"use client";

import { ReactNode } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { formatCurrency } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  color: string;
  link: string;
  subValue?: string;
  subText?: string;
  change?: number;
}

export default function StatCard({
  title,
  value,
  icon,
  color,
  link,
  subValue,
  subText,
  change,
}: StatCardProps) {
  // Define color classes based on the color prop
  const colorClasses = {
    blue: {
      bg: "bg-blue-500",
      light: "bg-blue-100",
      text: "text-blue-600",
      hover: "hover:bg-blue-600",
    },
    green: {
      bg: "bg-green-500",
      light: "bg-green-100",
      text: "text-green-600",
      hover: "hover:bg-green-600",
    },
    yellow: {
      bg: "bg-yellow-500",
      light: "bg-yellow-100",
      text: "text-yellow-600",
      hover: "hover:bg-yellow-600",
    },
    red: {
      bg: "bg-red-500",
      light: "bg-red-100",
      text: "text-red-600",
      hover: "hover:bg-red-600",
    },
    purple: {
      bg: "bg-purple-500",
      light: "bg-purple-100",
      text: "text-purple-600",
      hover: "hover:bg-purple-600",
    },
    indigo: {
      bg: "bg-indigo-500",
      light: "bg-indigo-100",
      text: "text-indigo-600",
      hover: "hover:bg-indigo-600",
    },
    teal: {
      bg: "bg-teal-500",
      light: "bg-teal-100",
      text: "text-teal-600",
      hover: "hover:bg-teal-600",
    },
  }[color] || {
    bg: "bg-gray-500",
    light: "bg-gray-100",
    text: "text-gray-600",
    hover: "hover:bg-gray-600",
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow duration-300"
    >
      <div className="p-5">
        <div className="flex items-center">
          <div className={`flex-shrink-0 ${colorClasses.bg} rounded-md p-3`}>
            {icon}
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                {title}
              </dt>
              <dd>
                <div className="text-xl font-bold text-gray-900">
                  {/* If value is a number and not a count (like products or customers count), format it as currency */}
                  {typeof value === 'number' &&
                   (title.toLowerCase().includes('sales') ||
                   title.toLowerCase().includes('purchases') ||
                   title.toLowerCase().includes('revenue') ||
                   title.toLowerCase().includes('expense') ||
                   title.toLowerCase().includes('profit') ||
                   title.toLowerCase().includes('balance')) ?
                    formatCurrency(value) : value}
                </div>
                {(subValue || subText) && (
                  <div className="flex items-center mt-1">
                    {subValue && (
                      <span className="text-sm text-gray-600 mr-1">
                        {subValue}
                      </span>
                    )}
                    {subText && (
                      <span className="text-xs text-gray-500">{subText}</span>
                    )}
                    {change !== undefined && (
                      <span
                        className={`ml-2 text-xs font-medium ${
                          change >= 0 ? "text-green-600" : "text-red-600"
                        }`}
                      >
                        {change >= 0 ? "+" : ""}
                        {change}%
                      </span>
                    )}
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className={`${colorClasses.light} px-5 py-3`}>
        <div className="text-sm">
          <Link
            href={link}
            className={`font-medium ${colorClasses.text} hover:underline flex items-center justify-between`}
          >
            <span>View all</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
