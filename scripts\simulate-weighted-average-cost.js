// Script to simulate weighted average cost calculation
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Simulating weighted average cost calculation...');

    // Get a product ID from command line arguments or use a default
    const productId = process.argv[2] || 'dcb15b30-2921-4514-883a-9fb2619b5fd2'; // Default to HP zbook
    const quantity = parseInt(process.argv[3] || '2');
    const unitPrice = parseFloat(process.argv[4] || '15000');

    // Get the product
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        inventory: true,
      },
    });

    if (!product) {
      console.error(`Product with ID ${productId} not found`);
      return;
    }

    console.log(`\nProduct: ${product.name} (ID: ${product.id})`);
    console.log(`Current cost price: ${product.costPrice}`);

    // Get inventory for this product
    const inventory = product.inventory[0];
    
    if (!inventory) {
      console.log('No inventory found for this product');
      return;
    }

    console.log(`Current inventory: ${inventory.quantity} units at ${inventory.costPrice}`);

    // Calculate weighted average cost
    const oldTotalValue = inventory.quantity * inventory.costPrice;
    const newItemValue = quantity * unitPrice;
    const newTotalQuantity = inventory.quantity + quantity;
    const weightedAverageCost = newTotalQuantity > 0 
      ? (oldTotalValue + newItemValue) / newTotalQuantity 
      : unitPrice;

    console.log(`\nSimulating purchase of ${quantity} units at ${unitPrice}:`);
    console.log(`- Old inventory value: ${inventory.quantity} units at ${inventory.costPrice} = ${oldTotalValue}`);
    console.log(`- New purchase value: ${quantity} units at ${unitPrice} = ${newItemValue}`);
    console.log(`- New total: ${newTotalQuantity} units at ${weightedAverageCost.toFixed(2)}`);

    // Ask if user wants to update the inventory
    if (process.argv.includes('--update')) {
      console.log(`\nUpdating inventory...`);
      
      // Update inventory
      await prisma.inventory.update({
        where: {
          id: inventory.id,
        },
        data: {
          quantity: newTotalQuantity,
          costPrice: weightedAverageCost,
        },
      });

      // Update product cost price
      await prisma.product.update({
        where: {
          id: product.id,
        },
        data: {
          costPrice: weightedAverageCost,
        },
      });

      console.log('Update completed');
    }
  } catch (error) {
    console.error('Error simulating weighted average cost:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
