import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const maintenanceId = params.id;
    const { method, amount, notes } = await request.json();

    // Validate input
    if (!method || !amount || amount <= 0) {
      return NextResponse.json(
        { error: "Invalid payment data" },
        { status: 400 }
      );
    }

    // Check if maintenance service exists
    const service = await db.maintenanceService.findUnique({
      where: { id: maintenanceId },
      include: {
        payments: true,
      },
    });

    if (!service) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Create payment
    const payment = await db.maintenancePayment.create({
      data: {
        maintenanceServiceId: maintenanceId,
        method,
        amount,
        notes,
        date: new Date(),
      },
    });

    // Calculate total paid amount
    const totalPaid = service.payments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    ) + amount;

    // Update maintenance service payment status
    let paymentStatus = "UNPAID";
    if (totalPaid >= (service.finalCost || 0)) {
      paymentStatus = "PAID";
    } else if (totalPaid > 0) {
      paymentStatus = "PARTIALLY_PAID";
    }

    await db.maintenanceService.update({
      where: { id: maintenanceId },
      data: {
        paymentMethod: method,
        paymentStatus,
        isPaid: paymentStatus === "PAID",
      },
    });

    return NextResponse.json(payment);
  } catch (error) {
    console.error("Error adding payment:", error);
    return NextResponse.json(
      { error: "Failed to add payment" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const maintenanceId = params.id;

    // Get all payments for the maintenance service
    const payments = await db.maintenancePayment.findMany({
      where: { maintenanceServiceId: maintenanceId },
      orderBy: { date: "desc" },
    });

    return NextResponse.json(payments);
  } catch (error) {
    console.error("Error fetching payments:", error);
    return NextResponse.json(
      { error: "Failed to fetch payments" },
      { status: 500 }
    );
  }
}
