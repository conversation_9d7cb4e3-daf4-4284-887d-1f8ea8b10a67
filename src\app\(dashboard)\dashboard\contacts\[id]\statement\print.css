@media print {
  /* Hide navigation, buttons, and other UI elements */
  .print-hidden,
  nav,
  header,
  footer,
  button,
  .sidebar,
  .navbar {
    display: none !important;
  }

  /* Set page size to A4 */
  @page {
    size: A4;
    margin: 1cm;
  }

  /* Basic styling for the printed page */
  body {
    font-family: Arial, sans-serif;
    font-size: 12pt;
    line-height: 1.3;
    background: white;
    color: black;
  }

  /* Statement header styling */
  .print-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .print-header h1 {
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .print-header .company-name {
    font-size: 16pt;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .print-header .company-info {
    font-size: 10pt;
    margin-bottom: 10px;
  }

  /* Contact and statement info */
  .print-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .print-info-section {
    width: 48%;
  }

  .print-info-section h2 {
    font-size: 14pt;
    font-weight: bold;
    margin-bottom: 10px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
  }

  .print-info-section p {
    margin: 5px 0;
  }

  /* Table styling */
  .print-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }

  .print-table th {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    font-weight: bold;
  }

  .print-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }

  .print-table .amount {
    text-align: right;
  }

  .print-table .total-row {
    font-weight: bold;
    background-color: #f9f9f9;
  }

  /* Footer */
  .print-footer {
    margin-top: 30px;
    text-align: center;
    font-size: 10pt;
    color: #666;
  }

  /* Force page breaks */
  .page-break {
    page-break-after: always;
  }

  /* Ensure table doesn't break across pages */
  .no-break {
    page-break-inside: avoid;
  }
}
