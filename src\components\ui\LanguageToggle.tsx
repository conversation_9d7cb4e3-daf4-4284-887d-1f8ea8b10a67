"use client";

import { useState } from "react";
import { Globe, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useApp } from "@/contexts/AppContext";
import { Language, getOppositeLanguage } from "@/lib/i18n";

export function LanguageToggle() {
  const { language, setLanguage } = useApp();

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
  };

  const getLanguageLabel = (lang: Language) => {
    return lang === 'ar' ? 'العربية' : 'English';
  };

  const getLanguageFlag = (lang: Language) => {
    return lang === 'ar' ? '🇸🇦' : '🇺🇸';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 px-2">
          <Globe className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">
            {getLanguageFlag(language)} {getLanguageLabel(language)}
          </span>
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        <DropdownMenuItem
          onClick={() => handleLanguageChange('ar')}
          className={`cursor-pointer ${language === 'ar' ? 'bg-blue-50 text-blue-600' : ''}`}
        >
          <span className="mr-2">🇸🇦</span>
          العربية
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleLanguageChange('en')}
          className={`cursor-pointer ${language === 'en' ? 'bg-blue-50 text-blue-600' : ''}`}
        >
          <span className="mr-2">🇺🇸</span>
          English
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
