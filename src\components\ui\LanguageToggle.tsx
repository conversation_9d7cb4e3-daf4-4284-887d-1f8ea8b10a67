"use client";

import { useState } from "react";
import { Globe, ChevronDown } from "lucide-react";
import { useApp } from "@/contexts/AppContext";
import { Language, getOppositeLanguage } from "@/lib/i18n";

export function LanguageToggle() {
  const { language, setLanguage } = useApp();

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
  };

  const getLanguageLabel = (lang: Language) => {
    return lang === 'ar' ? 'العربية' : 'English';
  };

  const getLanguageFlag = (lang: Language) => {
    return lang === 'ar' ? '🇸🇦' : '🇺🇸';
  };

  return (
    <button
      onClick={() => handleLanguageChange(getOppositeLanguage(language))}
      className="flex items-center h-8 px-2 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
      title={language === 'ar' ? 'Switch to English' : 'التبديل إلى العربية'}
    >
      <Globe className="h-4 w-4 mr-2" />
      <span className="hidden sm:inline text-sm">
        {getLanguageFlag(language)} {getLanguageLabel(language)}
      </span>
    </button>
  );
}
