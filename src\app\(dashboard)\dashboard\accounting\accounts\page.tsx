"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select-radix";
import { Loader2, Plus, Search, Edit, Trash2, Save, X, RefreshCw, FileSpreadsheet } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import AccountsExportImport from "./components/AccountsExportImport";
import OpeningBalanceImport from "./components/OpeningBalanceImport";

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
  parentId: string | null;
  balance: number;
  isActive: boolean;
}

export default function AccountsPage() {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<Account[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentAccount, setCurrentAccount] = useState<Account | null>(null);
  const [isInitializing, setIsInitializing] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    code: "",
    name: "",
    type: "ASSET",
    parentId: "",
  });

  // Fetch accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/accounting/accounts");
        if (response.ok) {
          const data = await response.json();
          setAccounts(data.data || []);
          setFilteredAccounts(data.data || []);
        }
      } catch (error) {
        console.error("Error fetching accounts:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAccounts();
  }, []);

  // Filter accounts based on search term and type
  useEffect(() => {
    let filtered = accounts;

    if (searchTerm) {
      filtered = filtered.filter(account =>
        account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account.code.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedType) {
      filtered = filtered.filter(account => account.type === selectedType);
    }

    setFilteredAccounts(filtered);
  }, [searchTerm, selectedType, accounts]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      code: "",
      name: "",
      type: "ASSET",
      parentId: "none",
    });
  };

  // Open add dialog
  const openAddDialog = () => {
    resetForm();
    setShowAddDialog(true);
  };

  // Open edit dialog
  const openEditDialog = (account: Account) => {
    setCurrentAccount(account);
    setFormData({
      code: account.code,
      name: account.name,
      type: account.type,
      parentId: account.parentId || "none",
    });
    setShowEditDialog(true);
  };

  // Create account
  const createAccount = async () => {
    try {
      // Convert "none" to null for parentId
      const parentId = formData.parentId === "none" ? null : formData.parentId || null;

      const response = await fetch("/api/accounting/accounts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: formData.code,
          name: formData.name,
          type: formData.type,
          parentId: parentId,
        }),
      });

      if (response.ok) {
        const newAccount = await response.json();
        setAccounts(prev => [...prev, newAccount.data]);
        setShowAddDialog(false);
        resetForm();
        toast.success(`Account "${formData.name}" created successfully! / تم إنشاء الحساب "${formData.name}" بنجاح!`);
      } else {
        const error = await response.json();
        toast.error(`Error creating account: ${error.error}`);
      }
    } catch (error) {
      console.error("Error creating account:", error);
      toast.error("Failed to create account. Please try again.");
    }
  };

  // Update account
  const updateAccount = async () => {
    if (!currentAccount) return;

    try {
      // Convert "none" to null for parentId
      const parentId = formData.parentId === "none" ? null : formData.parentId || null;

      const response = await fetch(`/api/accounting/accounts/${currentAccount.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: formData.code,
          name: formData.name,
          type: formData.type,
          parentId: parentId,
        }),
      });

      if (response.ok) {
        const updatedAccount = await response.json();
        setAccounts(prev =>
          prev.map(acc => acc.id === currentAccount.id ? updatedAccount.data : acc)
        );
        setShowEditDialog(false);
        setCurrentAccount(null);
        resetForm();
        toast.success(`Account "${formData.name}" updated successfully! / تم تحديث الحساب "${formData.name}" بنجاح!`);
      } else {
        const error = await response.json();
        toast.error(`Error updating account: ${error.error}`);
      }
    } catch (error) {
      console.error("Error updating account:", error);
      toast.error("Failed to update account. Please try again.");
    }
  };

  // Format account type for display
  const formatAccountType = (type: string) => {
    switch (type) {
      case "ASSET":
        return "Assets";
      case "LIABILITY":
        return "Liabilities";
      case "EQUITY":
        return "Equity";
      case "REVENUE":
        return "Revenue";
      case "EXPENSE":
        return "Expenses";
      default:
        return type;
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    // Format with Egyptian Pound symbol
    return `EGP ${amount.toFixed(2)}`;
  };

  // Initialize accounts
  const initializeAccounts = async () => {
    setIsInitializing(true);
    try {
      const response = await fetch("/api/accounting/accounts/initialize", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        // Refresh accounts list
        const accountsResponse = await fetch("/api/accounting/accounts");
        if (accountsResponse.ok) {
          const accountsData = await accountsResponse.json();
          setAccounts(accountsData.data || []);
          setFilteredAccounts(accountsData.data || []);
        }
        toast.success("Accounts initialized successfully! / تم تهيئة الحسابات بنجاح!");
      } else {
        const error = await response.json();
        toast.error(`Error initializing accounts: ${error.error}`);
      }
    } catch (error) {
      console.error("Error initializing accounts:", error);
      toast.error("Failed to initialize accounts. Please try again.");
    } finally {
      setIsInitializing(false);
    }
  };

  // Initialize accounting system
  const initializeAccountingSystem = async () => {
    setIsInitializing(true);
    try {
      const response = await fetch("/api/accounting/initialize", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        // Refresh accounts list
        const accountsResponse = await fetch("/api/accounting/accounts");
        if (accountsResponse.ok) {
          const accountsData = await accountsResponse.json();
          setAccounts(accountsData.data || []);
          setFilteredAccounts(accountsData.data || []);
        }
        toast.success("Accounting system initialized successfully! / تم تهيئة نظام المحاسبة بنجاح!");
      } else {
        const error = await response.json();
        toast.error(`Error initializing accounting system: ${error.error}`);
      }
    } catch (error) {
      console.error("Error initializing accounting system:", error);
      toast.error("Failed to initialize accounting system. Please try again.");
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Account Management</h1>
          <p className="text-gray-500 mt-1">إدارة الحسابات</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={initializeAccountingSystem}
            disabled={isInitializing}
            className="border-blue-200 text-blue-700 hover:bg-blue-50"
          >
            {isInitializing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Initialize System / تهيئة النظام
          </Button>
          <Button
            variant="outline"
            onClick={initializeAccounts}
            disabled={isInitializing}
            className="border-blue-200 text-blue-700 hover:bg-blue-50"
          >
            {isInitializing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Initialize Accounts / تهيئة الحسابات
          </Button>
          <Button
            onClick={openAddDialog}
            className="bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add New Account / إضافة حساب جديد
          </Button>
        </div>
      </div>

      {/* Export & Import Component */}
      <AccountsExportImport
        accounts={accounts}
        onImportComplete={() => {
          // Refresh accounts after import
          const fetchAccounts = async () => {
            setIsLoading(true);
            try {
              const response = await fetch("/api/accounting/accounts");
              if (response.ok) {
                const data = await response.json();
                setAccounts(data.data || []);
                setFilteredAccounts(data.data || []);
              }
            } catch (error) {
              console.error("Error fetching accounts:", error);
              toast.error("Failed to refresh accounts");
            } finally {
              setIsLoading(false);
            }
          };
          fetchAccounts();
        }}
      />

      {/* Opening Balance Import Component */}
      <OpeningBalanceImport
        accounts={accounts}
        onImportComplete={() => {
          // Refresh accounts after import
          const fetchAccounts = async () => {
            setIsLoading(true);
            try {
              const response = await fetch("/api/accounting/accounts");
              if (response.ok) {
                const data = await response.json();
                setAccounts(data.data || []);
                setFilteredAccounts(data.data || []);
              }
            } catch (error) {
              console.error("Error fetching accounts:", error);
              toast.error("Failed to refresh accounts");
            } finally {
              setIsLoading(false);
            }
          };
          fetchAccounts();
        }}
      />

      <Card>
        <CardHeader>
          <CardTitle>Accounts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search accounts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="w-full md:w-64">
              <Select value={selectedType || "all"} onValueChange={(value) => setSelectedType(value === "all" ? null : value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Account Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Account Types</SelectItem>
                  <SelectItem value="ASSET">Assets</SelectItem>
                  <SelectItem value="LIABILITY">Liabilities</SelectItem>
                  <SelectItem value="EQUITY">Equity</SelectItem>
                  <SelectItem value="REVENUE">Revenue</SelectItem>
                  <SelectItem value="EXPENSE">Expenses</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
              <span className="ml-2 text-gray-500">Loading accounts...</span>
            </div>
          ) : filteredAccounts.length > 0 ? (
            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-gray-100 border-b">
                      <th className="px-4 py-3 text-left">Account Code</th>
                      <th className="px-4 py-3 text-left">Account Name</th>
                      <th className="px-4 py-3 text-left">Type</th>
                      <th className="px-4 py-3 text-left">Balance</th>
                      <th className="px-4 py-3 text-center">Status</th>
                      <th className="px-4 py-3 text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAccounts.map(account => (
                      <tr key={account.id} className="border-b hover:bg-gray-50">
                        <td className="px-4 py-3 text-left">{account.code}</td>
                        <td className="px-4 py-3 text-left">{account.name}</td>
                        <td className="px-4 py-3 text-left">{formatAccountType(account.type)}</td>
                        <td className="px-4 py-3 text-left">{formatCurrency(account.balance)}</td>
                        <td className="px-4 py-3 text-center">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${account.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {account.isActive ? "Active" : "Inactive"}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <div className="flex justify-center space-x-2">
                            <Button variant="ghost" size="icon" onClick={() => openEditDialog(account)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="text-center p-4 text-gray-500">
              No matching accounts found.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Account Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Account</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="code">Account Code</Label>
                <Input
                  id="code"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  placeholder="e.g. 1001"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Account Type</Label>
                <Select value={formData.type} onValueChange={(value) => handleSelectChange("type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select account type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ASSET">Assets</SelectItem>
                    <SelectItem value="LIABILITY">Liabilities</SelectItem>
                    <SelectItem value="EQUITY">Equity</SelectItem>
                    <SelectItem value="REVENUE">Revenue</SelectItem>
                    <SelectItem value="EXPENSE">Expenses</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">Account Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Account name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="parentId">Parent Account (Optional)</Label>
              <Select value={formData.parentId} onValueChange={(value) => handleSelectChange("parentId", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select parent account (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No parent account</SelectItem>
                  {accounts
                    .filter(account => account.type === formData.type)
                    .map(account => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.code} - {account.name}
                      </SelectItem>
                    ))
                  }
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={createAccount}>
              Add Account
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Account Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Account</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-code">Account Code</Label>
                <Input
                  id="edit-code"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  placeholder="e.g. 1001"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">Account Type</Label>
                <Select value={formData.type} onValueChange={(value) => handleSelectChange("type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select account type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ASSET">Assets</SelectItem>
                    <SelectItem value="LIABILITY">Liabilities</SelectItem>
                    <SelectItem value="EQUITY">Equity</SelectItem>
                    <SelectItem value="REVENUE">Revenue</SelectItem>
                    <SelectItem value="EXPENSE">Expenses</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-name">Account Name</Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Account name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-parentId">Parent Account (Optional)</Label>
              <Select value={formData.parentId} onValueChange={(value) => handleSelectChange("parentId", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select parent account (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No parent account</SelectItem>
                  {accounts
                    .filter(account => account.type === formData.type && account.id !== currentAccount?.id)
                    .map(account => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.code} - {account.name}
                      </SelectItem>
                    ))
                  }
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={updateAccount}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
