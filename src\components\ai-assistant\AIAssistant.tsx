"use client";

import { useState, useEffect, useRef } from "react";
import { Bot, X, Maximize2, Minimize2, Send, Settings, MessageSquare, Plus } from "lucide-react";
import { useSession } from "next-auth/react";
import { AIMessage } from "./AIMessage";
import { AIAssistantSettings } from "./AIAssistantSettings";
import { AIConversationList } from "./AIConversationList";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  createdAt: string;
}

interface Conversation {
  id: string;
  title: string | null;
  createdAt: string;
  isActive: boolean;
}

export function AIAssistant() {
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [showConversations, setShowConversations] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Fetch active conversation on mount
  useEffect(() => {
    if (session?.user && isOpen) {
      fetchActiveConversation();
    }
  }, [session, isOpen]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const fetchActiveConversation = async () => {
    try {
      const response = await fetch("/api/ai-assistant/conversations/active");
      if (response.ok) {
        const data = await response.json();
        if (data.conversation) {
          setActiveConversation(data.conversation.id);
          setMessages(data.messages || []);
        } else {
          // Create a new conversation
          createNewConversation();
        }
      }
    } catch (error) {
      console.error("Error fetching active conversation:", error);
    }
  };

  const createNewConversation = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/ai-assistant/conversations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setActiveConversation(data.id);
        setMessages([
          {
            id: "welcome",
            role: "assistant",
            content: "مرحباً! أنا المساعد الذكي لنظام VERO ERP. كيف يمكنني مساعدتك اليوم؟",
            createdAt: new Date().toISOString(),
          },
        ]);
      }
    } catch (error) {
      console.error("Error creating conversation:", error);
      toast.error("حدث خطأ أثناء إنشاء محادثة جديدة");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !activeConversation || isLoading) return;

    const userMessage = {
      id: `temp-${Date.now()}`,
      role: "user" as const,
      content: message,
      createdAt: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setMessage("");
    setIsLoading(true);

    try {
      const response = await fetch(`/api/ai-assistant/conversations/${activeConversation}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: userMessage.content }),
      });

      if (response.ok) {
        const data = await response.json();
        setMessages((prev) => [
          ...prev.filter((m) => m.id !== userMessage.id),
          data.userMessage,
          data.assistantMessage,
        ]);
      } else {
        toast.error("حدث خطأ أثناء إرسال الرسالة");
        // Keep the user message but mark it as failed
        setMessages((prev) =>
          prev.map((m) =>
            m.id === userMessage.id
              ? { ...m, content: `${m.content} (فشل الإرسال)` }
              : m
          )
        );
      }
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("حدث خطأ أثناء إرسال الرسالة");
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (message.trim()) {
        handleSendMessage();
      }
    }
  };

  const toggleAssistant = () => {
    setIsOpen(!isOpen);
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      {/* Floating button with pulse effect */}
      {!isOpen && (
        <button
          onClick={toggleAssistant}
          className="fixed bottom-6 right-6 p-4 bg-gradient-to-r from-blue-500 to-teal-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 group"
          title="المساعد الذكي"
        >
          <Bot className="h-6 w-6" />
          <span className="absolute top-0 right-0 -mt-1 -mr-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></span>
          <span className="absolute top-0 right-0 -mt-1 -mr-1 w-3 h-3 bg-red-500 rounded-full"></span>

          {/* Tooltip */}
          <span className="absolute bottom-full right-0 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
            انقر هنا للتحدث مع المساعد الذكي
          </span>
        </button>
      )}

      {/* Assistant panel with animation */}
      {isOpen && (
        <div
          className={`fixed bottom-6 right-6 bg-white rounded-lg shadow-2xl z-50 transition-all duration-300 flex flex-col animate-fadeIn ${
            isExpanded
              ? "w-[80vw] h-[80vh] max-w-4xl"
              : "w-96 h-[500px]"
          }`}
          style={{
            boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.5), 0 8px 10px -6px rgba(59, 130, 246, 0.3)",
            borderTop: "3px solid #3b82f6"
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center">
              <Bot className="h-6 w-6 text-blue-500 mr-2" />
              <h3 className="font-medium">المساعد الذكي</h3>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
                title="الإعدادات"
              >
                <Settings className="h-4 w-4" />
              </button>
              <button
                onClick={() => setShowConversations(!showConversations)}
                className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
                title="المحادثات"
              >
                <MessageSquare className="h-4 w-4" />
              </button>
              <button
                onClick={toggleExpand}
                className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
                title={isExpanded ? "تصغير" : "تكبير"}
              >
                {isExpanded ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </button>
              <button
                onClick={toggleAssistant}
                className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
                title="إغلاق"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Content area */}
          <div className="flex flex-1 overflow-hidden">
            {/* Sidebar for settings or conversations */}
            {(showSettings || showConversations) && (
              <div className="w-64 border-r overflow-y-auto">
                {showSettings && (
                  <AIAssistantSettings onClose={() => setShowSettings(false)} />
                )}
                {showConversations && (
                  <AIConversationList
                    conversations={conversations}
                    activeConversationId={activeConversation}
                    onSelectConversation={(id) => {
                      setActiveConversation(id);
                      // Fetch messages for this conversation
                    }}
                    onNewConversation={createNewConversation}
                    onClose={() => setShowConversations(false)}
                  />
                )}
              </div>
            )}

            {/* Main chat area */}
            <div className="flex-1 flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((msg) => (
                  <AIMessage key={msg.id} message={msg} />
                ))}
                {isLoading && (
                  <AIMessage
                    message={{
                      id: "loading",
                      role: "assistant",
                      content: "جاري التفكير...",
                      createdAt: new Date().toISOString(),
                    }}
                    isLoading={true}
                  />
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Input area */}
              <div className="p-4 border-t">
                <div className="flex items-end space-x-reverse space-x-2 flex-row-reverse">
                  <Textarea
                    ref={textareaRef}
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="اكتب رسالتك هنا..."
                    className="flex-1 resize-none text-right"
                    rows={2}
                    disabled={isLoading}
                    dir="rtl"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!message.trim() || isLoading}
                    className="ml-2"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
