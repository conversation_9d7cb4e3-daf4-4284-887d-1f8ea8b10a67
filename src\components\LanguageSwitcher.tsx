"use client";

import { useState } from 'react';
import { Language } from '@/locales/translations';

interface LanguageSwitcherProps {
  initialLanguage: Language;
  onLanguageChange: (language: Language) => void;
}

export default function LanguageSwitcher({ 
  initialLanguage, 
  onLanguageChange 
}: LanguageSwitcherProps) {
  const [language, setLanguage] = useState<Language>(initialLanguage);

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'ar' : 'en';
    setLanguage(newLanguage);
    onLanguageChange(newLanguage);
  };

  return (
    <button
      onClick={toggleLanguage}
      className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      aria-label={`Switch to ${language === 'en' ? 'Arabic' : 'English'}`}
    >
      {language === 'en' ? 'العربية' : 'English'}
    </button>
  );
}
