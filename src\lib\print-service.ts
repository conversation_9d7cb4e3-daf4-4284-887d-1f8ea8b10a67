import axios from 'axios';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import QRCode from 'qrcode';
import JsBarcode from 'jsbarcode';

export interface PrintSettings {
  pageSize: string;
  orientation: string;
  marginTop: number;
  marginRight: number;
  marginBottom: number;
  marginLeft: number;
  headerHeight: number;
  footerHeight: number;
  showLogo: boolean;
  logoPosition: string;
  showBranch: boolean;
  showQRCode: boolean;
  showBarcode: boolean;
  fontSize: number;
  fontFamily: string;
  primaryColor: string;
  secondaryColor: string;
  footerText: string;
  termsText: string;
  customWidth?: number;
  customHeight?: number;
  companyLogo?: string;
  defaultTemplateSettings?: any;
  thermalPrinterSettings?: any;
  signatureSettings?: any;
  stampSettings?: any;
  multiLanguageSettings?: any;
  branchId?: string;
}

export interface PrintTemplate {
  id: string;
  name: string;
  type: string;
  content: string;
  isDefault: boolean;
  language: string;
  paperSize: string;
  orientation: string;
  branchId?: string;
  variables?: any;
  previewImage?: string;
}

export interface PrintData {
  [key: string]: any;
}

/**
 * Print Service
 * Central service for handling all printing functionality
 */
class PrintService {
  private settings: PrintSettings | null = null;
  private templates: Record<string, PrintTemplate> = {};
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;

  /**
   * Initialize the print service
   * Fetches settings and templates from the API
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    // If already initializing, return the existing promise
    if (this.initPromise) return this.initPromise;

    this.initPromise = new Promise<void>(async (resolve, reject) => {
      try {
        // Fetch print settings
        const settingsResponse = await axios.get('/api/print-settings');
        this.settings = settingsResponse.data;

        // Fetch all default templates
        const templatesResponse = await axios.get('/api/print-templates?default=true');
        const templates = templatesResponse.data;

        // Organize templates by type (use default templates for each type)
        templates.forEach((template: PrintTemplate) => {
          if (template.isDefault) {
            this.templates[template.type] = template;
          }
        });

        this.isInitialized = true;
        resolve();
      } catch (error) {
        console.error('Failed to initialize print service:', error);
        reject(error);
      }
    });

    return this.initPromise;
  }

  /**
   * Get print settings
   */
  async getSettings(): Promise<PrintSettings> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.settings as PrintSettings;
  }

  /**
   * Get template for a specific document type
   */
  async getTemplate(documentType: string, language: string = 'ar'): Promise<PrintTemplate | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Try to get template with specified language
    const templateKey = `${documentType}_${language}`;
    if (this.templates[templateKey]) {
      return this.templates[templateKey];
    }

    // Fall back to default language template
    if (this.templates[documentType]) {
      return this.templates[documentType];
    }

    // If not found in cache, try to fetch from API
    try {
      const response = await axios.get(`/api/print-templates?type=${documentType}&default=true&language=${language}`);
      const templates = response.data;

      if (templates && templates.length > 0) {
        const template = templates[0];
        this.templates[templateKey] = template;
        return template;
      }
    } catch (error) {
      console.error(`Failed to fetch template for ${documentType}:`, error);
    }

    return null;
  }

  /**
   * Generate HTML for printing
   */
  async generatePrintHTML(documentType: string, data: PrintData, language: string = 'ar'): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const template = await this.getTemplate(documentType, language);
    if (!template) {
      throw new Error(`No template found for document type: ${documentType}`);
    }

    let html = template.content;

    // Replace variables with actual data
    Object.keys(data).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      html = html.replace(regex, data[key] !== undefined ? data[key] : '');
    });

    return html;
  }

  /**
   * Generate QR code as data URL
   */
  async generateQRCode(text: string, size: number = 100): Promise<string> {
    return new Promise((resolve, reject) => {
      QRCode.toDataURL(text, { width: size, margin: 1 }, (err, url) => {
        if (err) reject(err);
        else resolve(url);
      });
    });
  }

  /**
   * Generate barcode as data URL
   */
  generateBarcode(text: string, options: any = {}): string {
    const canvas = document.createElement('canvas');
    JsBarcode(canvas, text, {
      format: 'CODE128',
      width: 2,
      height: 50,
      displayValue: true,
      ...options
    });
    return canvas.toDataURL('image/png');
  }

  /**
   * Print a document
   */
  async print(documentType: string, data: PrintData, options: {
    language?: string;
    copies?: number;
    printerName?: string;
  } = {}): Promise<void> {
    const { language = 'ar', copies = 1 } = options;

    try {
      // Initialize if needed
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Get settings and template
      const settings = await this.getSettings();
      const template = await this.getTemplate(documentType, language);

      if (!template) {
        throw new Error(`No template found for document type: ${documentType}`);
      }

      // Generate HTML
      const contentHtml = await this.generatePrintHTML(documentType, data, language);

      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Failed to open print window. Please check your popup blocker settings.');
      }

      // Get paper size
      const paperSize = template.paperSize || settings.pageSize;
      const orientation = template.orientation || settings.orientation;

      // Write the HTML to the new window
      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="${language === 'ar' ? 'rtl' : 'ltr'}">
        <head>
          <title>Print</title>
          <meta charset="UTF-8">
          <style>
            @media print {
              @page {
                size: ${paperSize === 'custom'
                  ? `${settings.customWidth}mm ${settings.customHeight}mm`
                  : paperSize};
                margin: ${settings.marginTop}mm ${settings.marginRight}mm ${settings.marginBottom}mm ${settings.marginLeft}mm;
                orientation: ${orientation};
              }

              body {
                font-family: ${settings.fontFamily}, sans-serif;
                font-size: ${settings.fontSize}pt;
                line-height: 1.3;
                background: white !important;
                color: black !important;
                margin: 0;
                padding: 0;
              }

              .print-hidden {
                display: none !important;
              }

              /* Basic styles for the document */
              .print-container {
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
              }

              /* Header styles */
              .header {
                text-align: center;
                margin-bottom: 20px;
              }

              /* Logo styles */
              .logo {
                text-align: ${settings.logoPosition};
                margin-bottom: 10px;
              }

              .logo img {
                max-height: 50px;
                max-width: 200px;
              }

              /* Footer styles */
              .footer {
                text-align: center;
                margin-top: 20px;
                color: ${settings.primaryColor};
                font-size: 10pt;
              }

              /* Terms styles */
              .terms {
                margin-top: 20px;
                padding-top: 10px;
                border-top: 1px solid ${settings.secondaryColor};
                font-size: 8pt;
              }

              /* Table styles */
              table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
              }

              th {
                background-color: ${settings.secondaryColor};
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
              }

              td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
              }

              /* RTL specific styles */
              html[dir="rtl"] th,
              html[dir="rtl"] td {
                text-align: right;
              }

              /* Thermal printer specific styles */
              ${paperSize === '80mm' ? `
                body {
                  font-size: 8pt;
                  width: 80mm;
                }

                table {
                  font-size: 8pt;
                }

                th, td {
                  padding: 4px;
                }
              ` : ''}

              /* Custom styles */
              .invoice-header, .receipt-header, .po-header {
                margin-bottom: 20px;
              }

              .customer-info, .supplier-info {
                margin-bottom: 15px;
              }

              .invoice-items, .receipt-items, .po-items {
                margin-bottom: 15px;
              }

              .invoice-summary, .receipt-summary, .po-summary {
                margin-top: 15px;
              }

              .summary-row {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
              }

              .summary-row.total {
                font-weight: bold;
                border-top: 2px solid #ddd;
                border-bottom: 2px solid #ddd;
              }

              .signatures {
                display: flex;
                justify-content: space-between;
                margin-top: 30px;
              }

              .signature-box {
                text-align: center;
                width: 45%;
              }

              .signature-line {
                margin-top: 10px;
                border-top: 1px solid #000;
              }
            }

            /* Non-print styles */
            body {
              font-family: ${settings.fontFamily}, sans-serif;
              font-size: ${settings.fontSize}pt;
              line-height: 1.3;
              margin: 0;
              padding: 20px;
              background-color: #f5f5f5;
            }

            .print-container {
              background-color: white;
              box-shadow: 0 0 10px rgba(0,0,0,0.1);
              padding: 20px;
              margin: 0 auto;
              max-width: ${paperSize === 'A4' ? '210mm' :
                          paperSize === 'A5' ? '148mm' :
                          paperSize === '80mm' ? '80mm' :
                          `${settings.customWidth}mm`};
            }

            .print-button {
              position: fixed;
              top: 20px;
              right: 20px;
              padding: 10px 20px;
              background-color: ${settings.primaryColor};
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            }

            .print-button:hover {
              background-color: #2980b9;
            }
          </style>
        </head>
        <body>
          <button class="print-button print-hidden" onclick="window.print()">Print</button>

          <div class="print-container">
            ${settings.showLogo && settings.companyLogo ? `
              <div class="logo">
                <img src="${settings.companyLogo}" alt="Company Logo" />
              </div>
            ` : ''}

            ${contentHtml}

            ${settings.footerText ? `
              <div class="footer">
                <p>${settings.footerText}</p>
              </div>
            ` : ''}

            ${settings.termsText ? `
              <div class="terms">
                <p>${settings.termsText}</p>
              </div>
            ` : ''}

            ${settings.showBarcode && data.barcode ? `
              <div style="text-align: center; margin-top: 20px;">
                <img src="${this.generateBarcode(data.barcode)}" alt="Barcode" />
              </div>
            ` : ''}
          </div>

          <script>
            // Auto print after loading
            window.onload = function() {
              // Set number of copies
              for (let i = 0; i < ${copies}; i++) {
                if (i > 0) {
                  setTimeout(() => window.print(), i * 1000);
                } else {
                  window.print();
                }
              }
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();

      // Log the print operation
      try {
        await axios.post('/api/print-logs', {
          documentType,
          documentId: data.id || 'unknown',
          templateId: template.id,
          copies,
          printerName: options.printerName,
          status: 'success'
        });
      } catch (error) {
        console.error('Failed to log print operation:', error);
      }
    } catch (error: any) {
      console.error('Failed to print document:', error);

      // Log the failed print operation
      try {
        await axios.post('/api/print-logs', {
          documentType,
          documentId: data.id || 'unknown',
          templateId: 'unknown',
          copies,
          printerName: options.printerName,
          status: 'error',
          errorMessage: error.message
        });
      } catch (logError) {
        console.error('Failed to log print error:', logError);
      }

      throw error;
    }
  }

  /**
   * Save document as PDF
   */
  async saveToPDF(documentType: string, data: PrintData, options: {
    language?: string;
    fileName?: string;
  } = {}): Promise<Blob> {
    const { language = 'ar', fileName = 'document.pdf' } = options;

    try {
      // Initialize if needed
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Get settings and template
      const settings = await this.getSettings();
      const template = await this.getTemplate(documentType, language);

      if (!template) {
        throw new Error(`No template found for document type: ${documentType}`);
      }

      // Generate HTML
      const contentHtml = await this.generatePrintHTML(documentType, data, language);

      // Create a temporary container
      const container = document.createElement('div');
      container.style.position = 'absolute';
      container.style.left = '-9999px';
      container.style.top = '-9999px';
      container.innerHTML = contentHtml;
      document.body.appendChild(container);

      // Convert to canvas
      const canvas = await html2canvas(container, {
        scale: 2,
        useCORS: true,
        allowTaint: true
      });

      // Remove the temporary container
      document.body.removeChild(container);

      // Get paper size
      const paperSize = template.paperSize || settings.pageSize;
      const orientation = template.orientation || settings.orientation;

      // Create PDF
      const pdf = new jsPDF({
        orientation: orientation as any,
        unit: 'mm',
        format: paperSize === 'custom' ? [settings.customWidth || 210, settings.customHeight || 297] : paperSize
      });

      // Add the canvas to the PDF
      const imgData = canvas.toDataURL('image/png');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const ratio = canvas.width / canvas.height;
      const imgWidth = pdfWidth;
      const imgHeight = imgWidth / ratio;

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      // Return the PDF as a blob
      return pdf.output('blob');
    } catch (error) {
      console.error('Failed to save document as PDF:', error);
      throw error;
    }
  }
}

// Export the class
export { PrintService };
