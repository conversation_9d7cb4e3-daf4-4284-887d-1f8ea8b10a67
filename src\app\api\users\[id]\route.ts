import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import bcrypt from "bcrypt";

// GET /api/users/:id - Get a user by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    const user = await db.user.findUnique({
      where: {
        id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        warehouses: {
          select: {
            warehouse: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        permissions: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Transform the warehouses array to make it easier to use in the frontend
    const transformedUser = {
      ...user,
      warehouses: user.warehouses.map(w => w.warehouse),
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 }
    );
  }
}

// PATCH /api/users/:id - Update a user
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    const data = await req.json();

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: {
        id,
      },
      include: {
        warehouses: true,
      },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if email is already used by another user
    if (data.email && data.email !== existingUser.email) {
      const userWithEmail = await db.user.findUnique({
        where: {
          email: data.email,
        },
      });

      if (userWithEmail && userWithEmail.id !== id) {
        return NextResponse.json(
          { error: "Email already exists" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      name: data.name,
      email: data.email,
      role: data.role,
      branchId: data.branchId || null,
      isActive: data.isActive,
    };

    // Hash the password if provided
    if (data.password) {
      updateData.password = await bcrypt.hash(data.password, 10);
    }

    // Update the user
    const user = await db.user.update({
      where: {
        id,
      },
      data: updateData,
    });

    // Update warehouses if provided
    if (data.warehouseIds) {
      // Delete existing warehouse connections
      await db.userWarehouse.deleteMany({
        where: {
          userId: id,
        },
      });

      // Add new warehouse connections
      if (data.warehouseIds.length > 0) {
        const warehouseConnections = data.warehouseIds.map((warehouseId: string) => ({
          userId: id,
          warehouseId,
        }));

        await db.userWarehouse.createMany({
          data: warehouseConnections,
        });
      }
    }

    // Update permissions if provided
    if (data.permissionIds) {
      await db.user.update({
        where: {
          id,
        },
        data: {
          permissions: {
            set: data.permissionIds.map((permId: string) => ({ id: permId })),
          },
        },
      });
    }

    // Return the user without the password
    const { password, ...userWithoutPassword } = user;
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    );
  }
}

// DELETE /api/users/:id - Delete a user
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admins can delete users
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only administrators can delete users" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Check if user exists
    const user = await db.user.findUnique({
      where: {
        id,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if this is the admin user (email contains "admin")
    if (user.email.toLowerCase().includes("admin") || user.role === "ADMIN") {
      return NextResponse.json(
        { error: "Cannot delete administrator user" },
        { status: 403 }
      );
    }

    // Delete user
    await db.user.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    );
  }
}