import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import * as XLSX from 'xlsx';

// GET /api/products/template - Get Excel template for product import
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all categories
    const categories = await db.category.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    // Get all warehouses
    const warehouses = await db.warehouse.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    // Get all component types from existing products
    const componentProducts = await db.product.findMany({
      where: {
        isComponent: true,
        componentType: {
          not: null
        }
      },
      select: {
        componentType: true
      },
      distinct: ['componentType']
    });

    const componentTypes = componentProducts
      .filter(p => p.componentType)
      .map(p => p.componentType as string);

    // Create template data
    const templateData = [
      {
        Name: 'Example Product',
        Description: 'This is an example product description',
        Category: categories.length > 0 ? categories[0].name : 'Computers',
        BasePrice: 1000,
        CostPrice: 800,
        IsCustomizable: 'No',
        IsComponent: 'No',
        ComponentType: '',
        Specifications: 'Processor: Intel Core i5, RAM: 8GB, Storage: 512GB SSD',
        ...warehouses.reduce((acc, warehouse) => {
          acc[warehouse.name] = 10; // Example inventory quantity
          return acc;
        }, {} as Record<string, number>)
      }
    ];

    // Create a worksheet with the template data
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // Add validation and instructions
    const validationSheet = XLSX.utils.aoa_to_sheet([
      ['Product Import Template Instructions'],
      [''],
      ['Required Fields:'],
      ['- Name: Product name (required)'],
      ['- Category: Product category (required)'],
      ['- BasePrice: Selling price (required)'],
      ['- CostPrice: Cost price (required)'],
      [''],
      ['Optional Fields:'],
      ['- Description: Product description'],
      ['- IsCustomizable: "Yes" or "No"'],
      ['- IsComponent: "Yes" or "No"'],
      ['- ComponentType: Only if IsComponent is "Yes" (e.g., RAM, SSD, HDD, NVME, GPU, CPU, or any custom type)'],
      ['- Specifications: Format as "Name1: Value1, Name2: Value2"'],
      [''],
      ['Warehouse Inventory:'],
      ['- Each warehouse has its own column'],
      ['- Enter the quantity for each warehouse'],
      [''],
      ['Available Categories:'],
      ...categories.map(category => [`- ${category.name}`]),
      [''],
      ['Available Component Types:'],
      ...(componentTypes.length > 0
        ? componentTypes.map(type => [`- ${type}`])
        : [['- RAM'], ['- SSD'], ['- HDD'], ['- NVME'], ['- GPU'], ['- CPU']]
      ),
      [''],
      ['Available Warehouses:'],
      ...warehouses.map(warehouse => [`- ${warehouse.name}`])
    ]);

    // Create a workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Products Template');
    XLSX.utils.book_append_sheet(workbook, validationSheet, 'Instructions');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });

    // Set headers for file download
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="products_import_template.xlsx"'
      }
    });
  } catch (error) {
    console.error("Error generating template:", error);
    return NextResponse.json(
      { error: "Failed to generate template" },
      { status: 500 }
    );
  }
}
