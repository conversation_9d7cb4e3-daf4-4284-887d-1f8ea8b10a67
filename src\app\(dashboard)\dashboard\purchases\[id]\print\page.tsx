"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Printer } from "lucide-react";
import { PrintableDocument } from "@/components/shared/PrintableDocument";
import { PrintButton } from "@/components/shared/PrintButton";
import { formatCurrency, formatDate } from "@/lib/utils";

export default function PurchasePrintPage() {
  const params = useParams();
  const router = useRouter();
  const purchaseId = params.id as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [purchase, setPurchase] = useState<any>(null);
  const [companyInfo, setCompanyInfo] = useState<any>({
    name: "VERO Company",
    address: "",
    phone: "",
    email: "",
    website: "",
    taxNumber: ""
  });

  // Fetch purchase details
  useEffect(() => {
    const fetchPurchaseDetails = async () => {
      setIsLoading(true);
      try {
        // Fetch purchase data
        const response = await fetch(`/api/purchases/${purchaseId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch purchase details");
        }
        const data = await response.json();
        setPurchase(data);
        
        // Fetch company info
        try {
          const settingsResponse = await fetch("/api/settings/company");
          if (settingsResponse.ok) {
            const settingsData = await settingsResponse.json();
            if (settingsData) {
              setCompanyInfo(settingsData);
            }
          }
        } catch (error) {
          // Use default company info if settings can't be fetched
          console.error("Error fetching company info:", error);
        }
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPurchaseDetails();
  }, [purchaseId]);

  // Prepare print data
  const preparePrintData = () => {
    if (!purchase) return null;
    
    // Generate items HTML
    const itemsHtml = purchase.items.map((item: any) => `
      <tr>
        <td>${item.product?.name || 'Unknown Product'}</td>
        <td style="text-align: right;">${item.quantity}</td>
        <td style="text-align: right;">${formatCurrency(item.unitPrice, purchase.currency)}</td>
        <td style="text-align: right;">${formatCurrency(item.totalPrice, purchase.currency)}</td>
      </tr>
    `).join('');
    
    // Generate items table
    const itemsTable = `
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Item</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Qty</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Price</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${itemsHtml}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Subtotal:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(purchase.subtotalAmount, purchase.currency)}</td>
          </tr>
          ${purchase.taxAmount > 0 ? `
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Tax:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(purchase.taxAmount, purchase.currency)}</td>
          </tr>
          ` : ''}
          ${purchase.discountAmount > 0 ? `
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Discount:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(purchase.discountAmount, purchase.currency)}</td>
          </tr>
          ` : ''}
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Total:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(purchase.totalAmount, purchase.currency)}</td>
          </tr>
        </tfoot>
      </table>
    `;
    
    // Prepare supplier details
    const supplierDetails = purchase.contact ? `
      ${purchase.contact.name || 'Unknown Supplier'}<br>
      ${purchase.contact.phone || ''}<br>
      ${purchase.contact.address || ''}
    ` : 'Unknown Supplier';
    
    // Prepare payment method
    const paymentMethod = purchase.paymentMethod || 'Not specified';
    
    // Prepare print data
    return {
      id: purchase.id,
      company_name: companyInfo.name,
      po_number: purchase.invoiceNumber,
      date: formatDate(purchase.date),
      supplier_details: supplierDetails,
      items: itemsTable,
      subtotal: formatCurrency(purchase.subtotalAmount, purchase.currency),
      tax: formatCurrency(purchase.taxAmount, purchase.currency),
      discount: formatCurrency(purchase.discountAmount, purchase.currency),
      total: formatCurrency(purchase.totalAmount, purchase.currency),
      payment_method: paymentMethod,
      payment_status: purchase.paymentStatus,
      barcode: purchase.invoiceNumber,
      branch: purchase.branch?.name || 'Main Branch',
      user: purchase.user?.name || 'Unknown User',
      notes: purchase.notes || ''
    };
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          {error}
        </div>
        <div className="mt-4">
          <Link
            href={`/dashboard/purchases/${purchaseId}`}
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Purchase Details
          </Link>
        </div>
      </div>
    );
  }

  const printData = preparePrintData();

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6 print:hidden">
        <Link
          href={`/dashboard/purchases/${purchaseId}`}
          className="flex items-center text-indigo-600 hover:text-indigo-900"
        >
          <ArrowLeft className="h-5 w-5 mr-1" />
          Back to Purchase Details
        </Link>
        
        {printData && (
          <PrintButton
            documentType="purchase_order"
            documentData={printData}
          />
        )}
      </div>

      {printData && (
        <PrintableDocument
          documentType="purchase_order"
          documentData={printData}
          showControls={true}
        />
      )}
    </div>
  );
}
