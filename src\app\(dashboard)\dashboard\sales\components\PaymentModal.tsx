"use client";

import { useState, Fragment, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";

interface PaymentMethodSetting {
  id: string;
  name: string;
  code: string;
  isActive: boolean;
}

interface PaymentMethod {
  method: string;
  amount: number;
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  totalAmount: number;
  onConfirm: (paymentMethods: PaymentMethod[]) => void;
}

export default function PaymentModal({
  isOpen,
  onClose,
  totalAmount,
  onConfirm,
}: PaymentModalProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState<PaymentMethodSetting[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>("CASH");
  const [paymentAmount, setPaymentAmount] = useState<number>(totalAmount);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch available payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/settings/payment-methods');

        if (response.ok) {
          const data = await response.json();
          // Filter only active payment methods
          const activeMethods = data.filter((method: PaymentMethodSetting) =>
            method.isActive && method.code !== 'CUSTOMER_ACCOUNT'
          );

          setAvailablePaymentMethods(activeMethods);

          // Set default selected payment method if available
          if (activeMethods.length > 0) {
            setSelectedPaymentMethod(activeMethods[0].code);
          }
        }
      } catch (error) {
        console.error('Error fetching payment methods:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchPaymentMethods();
    }
  }, [isOpen]);

  // Calculate total paid and remaining amount
  const totalPaid = paymentMethods.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingAmount = Math.max(0, totalAmount - totalPaid);

  // Add payment method
  const addPaymentMethod = () => {
    if (selectedPaymentMethod && paymentAmount > 0 && paymentAmount <= remainingAmount) {
      // Check if this payment method already exists
      const existingPaymentIndex = paymentMethods.findIndex(p => p.method === selectedPaymentMethod);

      if (existingPaymentIndex >= 0) {
        // Update existing payment method
        const updatedPaymentMethods = [...paymentMethods];
        updatedPaymentMethods[existingPaymentIndex].amount += paymentAmount;
        setPaymentMethods(updatedPaymentMethods);
      } else {
        // Add new payment method
        setPaymentMethods([...paymentMethods, { method: selectedPaymentMethod, amount: paymentAmount }]);
      }

      // Reset payment amount to remaining amount
      setPaymentAmount(remainingAmount > 0 ? remainingAmount - paymentAmount : 0);
    }
  };

  // Remove payment method
  const removePaymentMethod = (index: number) => {
    const updatedPaymentMethods = [...paymentMethods];
    const removedAmount = updatedPaymentMethods[index].amount;
    updatedPaymentMethods.splice(index, 1);
    setPaymentMethods(updatedPaymentMethods);

    // Update payment amount to include the removed amount
    setPaymentAmount(paymentAmount + removedAmount);
  };

  // Handle confirmation
  const handleConfirm = () => {
    // If there are no payment methods, add the total amount as CUSTOMER_ACCOUNT
    if (paymentMethods.length === 0) {
      onConfirm([{ method: "CUSTOMER_ACCOUNT", amount: totalAmount }]);
    } else if (remainingAmount > 0) {
      // If there's a remaining amount, add it as CUSTOMER_ACCOUNT
      onConfirm([...paymentMethods, { method: "CUSTOMER_ACCOUNT", amount: remainingAmount }]);
    } else {
      // Otherwise, just use the payment methods as is
      onConfirm(paymentMethods);
    }
    onClose();
  };

  // Reset state when modal is closed
  const handleClose = () => {
    setPaymentMethods([]);
    setSelectedPaymentMethod("CASH");
    setPaymentAmount(totalAmount);
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900"
                >
                  Record Payment
                </Dialog.Title>
                <div className="mt-4">
                  <div className="bg-gray-50 p-4 rounded-md mb-4">
                    <div className="flex justify-between text-sm font-bold">
                      <span>Total Amount:</span>
                      <span>{totalAmount.toFixed(2)} ج.م</span>
                    </div>
                    <div className="flex justify-between text-sm mt-2">
                      <span>Total Paid:</span>
                      <span>{totalPaid.toFixed(2)} ج.م</span>
                    </div>
                    <div className="flex justify-between text-sm mt-2 font-bold">
                      <span>Remaining:</span>
                      <span>{remainingAmount.toFixed(2)} ج.م</span>
                    </div>
                  </div>

                  {/* Payment Method Selection */}
                  {isLoading ? (
                    <div className="flex justify-center items-center py-4">
                      <svg className="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="ml-2 text-gray-600">Loading payment methods...</span>
                    </div>
                  ) : availablePaymentMethods.length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      No payment methods available. Please add payment methods in settings.
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2 mb-4">
                      {availablePaymentMethods.map((method) => (
                        <div
                          key={method.id}
                          className={`p-2 border rounded-md cursor-pointer flex items-center ${selectedPaymentMethod === method.code ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                          onClick={() => setSelectedPaymentMethod(method.code)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                          <span className="font-medium text-black">{method.name}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Payment Amount Input */}
                  <div className="flex items-center mb-4">
                    <div className="flex-1 mr-2">
                      <label htmlFor="paymentAmount" className="block text-sm font-medium text-black mb-1">
                        Payment Amount
                      </label>
                      <div className="relative rounded-md shadow-sm">
                        <input
                          type="number"
                          id="paymentAmount"
                          className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-3 pr-12 sm:text-sm border-gray-300 rounded-md text-black"
                          placeholder="0.00"
                          min="0"
                          max={remainingAmount}
                          step="0.01"
                          value={paymentAmount}
                          onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <span className="text-black sm:text-sm">ج.م</span>
                        </div>
                      </div>
                    </div>
                    <button
                      type="button"
                      className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      onClick={addPaymentMethod}
                      disabled={!selectedPaymentMethod || paymentAmount <= 0 || paymentAmount > remainingAmount}
                    >
                      Add Payment
                    </button>
                  </div>

                  {/* Payment Methods List */}
                  {paymentMethods.length > 0 && (
                    <div className="mt-4 border rounded-md overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                              Method
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                              Amount
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {paymentMethods.map((payment, index) => (
                            <tr key={index}>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-black">
                                {availablePaymentMethods.find(m => m.code === payment.method)?.name || payment.method}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                                {payment.amount.toFixed(2)} ج.م
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                                <button
                                  type="button"
                                  onClick={() => removePaymentMethod(index)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  Remove
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2"
                    onClick={handleClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2"
                    onClick={handleConfirm}
                  >
                    Confirm Payment
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
