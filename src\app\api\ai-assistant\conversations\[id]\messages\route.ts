import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { processAIAssistantMessage } from "@/lib/ai-assistant";

// GET /api/ai-assistant/conversations/[id]/messages - Get messages for a conversation
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const conversationId = params.id;
    
    // Verify the conversation belongs to the user
    const conversation = await db.aIAssistantConversation.findUnique({
      where: {
        id: conversationId,
        userId: session.user.id,
      },
    });
    
    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }
    
    // Get messages for this conversation
    const messages = await db.aIAssistantMessage.findMany({
      where: {
        conversationId,
        role: {
          not: "SYSTEM", // Exclude system messages
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });
    
    // Format messages for the frontend
    const formattedMessages = messages.map((message) => ({
      id: message.id,
      role: message.role.toLowerCase(),
      content: message.content,
      createdAt: message.createdAt.toISOString(),
    }));
    
    return NextResponse.json({ messages: formattedMessages });
  } catch (error) {
    console.error("Error fetching messages:", error);
    return NextResponse.json(
      { error: "Failed to fetch messages" },
      { status: 500 }
    );
  }
}

// POST /api/ai-assistant/conversations/[id]/messages - Add a message to a conversation
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const conversationId = params.id;
    
    // Verify the conversation belongs to the user
    const conversation = await db.aIAssistantConversation.findUnique({
      where: {
        id: conversationId,
        userId: session.user.id,
      },
    });
    
    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { content } = body;
    
    if (!content) {
      return NextResponse.json(
        { error: "Message content is required" },
        { status: 400 }
      );
    }
    
    // Create user message
    const userMessage = await db.aIAssistantMessage.create({
      data: {
        conversationId,
        role: "USER",
        content,
      },
    });
    
    // Get conversation history for context
    const conversationHistory = await db.aIAssistantMessage.findMany({
      where: {
        conversationId,
      },
      orderBy: {
        createdAt: "asc",
      },
    });
    
    // Process the message with AI
    const aiResponse = await processAIAssistantMessage(
      content,
      conversationHistory,
      session.user
    );
    
    // Create assistant message
    const assistantMessage = await db.aIAssistantMessage.create({
      data: {
        conversationId,
        role: "ASSISTANT",
        content: aiResponse,
      },
    });
    
    // Update conversation title if it's the first user message
    if (!conversation.title) {
      await db.aIAssistantConversation.update({
        where: {
          id: conversationId,
        },
        data: {
          title: content.substring(0, 50) + (content.length > 50 ? "..." : ""),
        },
      });
    }
    
    // Format messages for the frontend
    const formattedUserMessage = {
      id: userMessage.id,
      role: "user",
      content: userMessage.content,
      createdAt: userMessage.createdAt.toISOString(),
    };
    
    const formattedAssistantMessage = {
      id: assistantMessage.id,
      role: "assistant",
      content: assistantMessage.content,
      createdAt: assistantMessage.createdAt.toISOString(),
    };
    
    return NextResponse.json({
      userMessage: formattedUserMessage,
      assistantMessage: formattedAssistantMessage,
    });
  } catch (error) {
    console.error("Error adding message:", error);
    return NextResponse.json(
      { error: "Failed to add message" },
      { status: 500 }
    );
  }
}
