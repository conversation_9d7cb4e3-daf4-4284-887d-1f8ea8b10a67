import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/general-ledger - Get general ledger entries for an account
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const accountId = url.searchParams.get("accountId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    
    // Validate required parameters
    if (!accountId) {
      return NextResponse.json(
        { error: "Account ID is required" },
        { status: 400 }
      );
    }
    
    // Check if account exists
    const account = await db.account.findUnique({
      where: { id: accountId },
    });
    
    if (!account) {
      return NextResponse.json(
        { error: "Account not found" },
        { status: 404 }
      );
    }
    
    // Build date filters
    const dateFilter: any = {};
    if (startDate && endDate) {
      dateFilter.gte = new Date(startDate);
      dateFilter.lte = new Date(endDate);
    } else if (startDate) {
      dateFilter.gte = new Date(startDate);
    } else if (endDate) {
      dateFilter.lte = new Date(endDate);
    }
    
    // Get all journal entries where this account is either debited or credited
    const debitEntries = await db.journalEntry.findMany({
      where: {
        debitAccountId: accountId,
        ...(Object.keys(dateFilter).length > 0 ? { date: dateFilter } : {}),
      },
      include: {
        journal: {
          select: {
            id: true,
            code: true,
            name: true,
          },
        },
        debitAccount: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
          },
        },
        creditAccount: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
          },
        },
        contact: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        date: "asc",
      },
    });
    
    const creditEntries = await db.journalEntry.findMany({
      where: {
        creditAccountId: accountId,
        ...(Object.keys(dateFilter).length > 0 ? { date: dateFilter } : {}),
      },
      include: {
        journal: {
          select: {
            id: true,
            code: true,
            name: true,
          },
        },
        debitAccount: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
          },
        },
        creditAccount: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
          },
        },
        contact: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        date: "asc",
      },
    });
    
    // Combine and sort entries by date
    const allEntries = [...debitEntries, ...creditEntries].sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
    
    // Calculate opening balance (sum of entries before start date)
    let openingBalance = 0;
    
    if (startDate) {
      const startDateObj = new Date(startDate);
      
      // Get all entries before start date
      const previousDebitEntries = await db.journalEntry.findMany({
        where: {
          debitAccountId: accountId,
          date: {
            lt: startDateObj,
          },
        },
      });
      
      const previousCreditEntries = await db.journalEntry.findMany({
        where: {
          creditAccountId: accountId,
          date: {
            lt: startDateObj,
          },
        },
      });
      
      // Calculate opening balance based on account type
      const debitSum = previousDebitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      const creditSum = previousCreditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // For asset and expense accounts, debits increase the balance
      if (account.type === "ASSET" || account.type === "EXPENSE") {
        openingBalance = debitSum - creditSum;
      } 
      // For liability, equity, and revenue accounts, credits increase the balance
      else if (account.type === "LIABILITY" || account.type === "EQUITY" || account.type === "REVENUE") {
        openingBalance = creditSum - debitSum;
      }
    }
    
    return NextResponse.json({
      account,
      openingBalance,
      entries: allEntries,
    });
  } catch (error: any) {
    console.error("Error fetching general ledger:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch general ledger" },
      { status: 500 }
    );
  }
}
