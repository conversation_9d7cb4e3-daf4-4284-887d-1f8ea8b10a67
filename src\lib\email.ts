import nodemailer from 'nodemailer';

interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html: string;
}

// Create a transporter object
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
  secure: process.env.EMAIL_SERVER_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
});

/**
 * Send an email
 * @param options Email options
 * @returns Promise that resolves when the email is sent
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  try {
    // Validate email address
    if (!options.to || !validateEmail(options.to)) {
      throw new Error(`Invalid email address: ${options.to}`);
    }
    
    // Send email
    await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
    });
    
    console.log(`Email sent to ${options.to}`);
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/**
 * Validate an email address
 * @param email Email address to validate
 * @returns True if the email is valid, false otherwise
 */
function validateEmail(email: string): boolean {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

/**
 * Send a test email
 * @returns Promise that resolves when the test email is sent
 */
export async function sendTestEmail(): Promise<void> {
  await sendEmail({
    to: process.env.TEST_EMAIL || process.env.EMAIL_FROM || '<EMAIL>',
    subject: 'Test Email',
    text: 'This is a test email from the application.',
    html: '<p>This is a test email from the application.</p>',
  });
}
