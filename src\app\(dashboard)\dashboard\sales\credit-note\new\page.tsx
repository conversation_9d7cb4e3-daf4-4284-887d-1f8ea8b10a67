"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import ContactSearchInput from "@/components/contacts/ContactSearchInput";

export default function NewCreditNotePage() {
  const [isLoading, setIsLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState("");
  const [selectedProducts, setSelectedProducts] = useState([{ productId: "", quantity: 1, price: 0, reason: "" }]);
  const [invoices, setInvoices] = useState([]);
  const [selectedInvoice, setSelectedInvoice] = useState("");
  const [selectedInvoiceDetails, setSelectedInvoiceDetails] = useState(null);
  const [error, setError] = useState("");

  // Payment methods state
  interface PaymentMethodSetting {
    id: string;
    name: string;
    code: string;
    isActive: boolean;
  }

  const [paymentMethods, setPaymentMethods] = useState([]);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState<PaymentMethodSetting[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("CASH");
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentMethodsLoading, setPaymentMethodsLoading] = useState(false);
  const router = useRouter();

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      setPaymentMethodsLoading(true);
      const response = await fetch('/api/settings/payment-methods');

      if (response.ok) {
        const data = await response.json();
        // Filter only active payment methods
        const activeMethods = data.filter((method: PaymentMethodSetting) =>
          method.isActive && method.code !== 'SUPPLIER_ACCOUNT'
        );

        setAvailablePaymentMethods(activeMethods);

        // Set default selected payment method if available
        if (activeMethods.length > 0) {
          setSelectedPaymentMethod(activeMethods[0].code);
        }
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    } finally {
      setPaymentMethodsLoading(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch customers (using contacts API with customer type)
        const customersResponse = await fetch("/api/contacts?type=customer");
        if (customersResponse.ok) {
          const customersData = await customersResponse.json();
          setCustomers(customersData);
        } else {
          console.error("Error fetching customers:", await customersResponse.text());
        }

        // Fetch products
        const productsResponse = await fetch("/api/products");
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          setProducts(productsData);
        } else {
          console.error("Error fetching products:", await productsResponse.text());
        }

        // Fetch payment methods
        await fetchPaymentMethods();
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchInvoices = async () => {
      if (selectedCustomer) {
        try {
          // Fetch invoices for the selected customer
          const response = await fetch(`/api/sales?contactId=${selectedCustomer}`);
          if (response.ok) {
            const data = await response.json();
            setInvoices(data);
          } else {
            let errorMessage = "Unknown error";
            try {
              const errorText = await response.text();
              console.error("Error fetching invoices:", errorText);
              try {
                // Try to parse as JSON
                const errorData = JSON.parse(errorText);
                errorMessage = errorData.error || `HTTP Error: ${response.status}`;
              } catch (parseError) {
                // If not JSON, use the text
                errorMessage = errorText || `HTTP Error: ${response.status}`;
              }
            } catch (textError) {
              console.error("Error reading error response:", textError);
              errorMessage = `HTTP Error: ${response.status}`;
            }
            console.error(`Failed to fetch invoices: ${errorMessage}`);
          }
        } catch (error) {
          console.error("Error fetching invoices:", error);
          let errorMessage = "Unknown error";
          if (error instanceof Error) {
            errorMessage = error.message;
          } else if (error && typeof error === 'object') {
            errorMessage = JSON.stringify(error);
          } else if (error) {
            errorMessage = String(error);
          }
          console.error(`Failed to fetch invoices: ${errorMessage}`);
        }
      } else {
        setInvoices([]);
      }
    };

    fetchInvoices();
  }, [selectedCustomer]);

  // Fetch invoice details when an invoice is selected
  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      if (selectedInvoice) {
        // Clear any previous errors
        setError("");
        console.log(`Fetching invoice details for ID: ${selectedInvoice}`);

        try {
          // Fetch invoice details from API
          console.log(`Making API request to: /api/sales/${selectedInvoice}`);

          // Add a timeout to the fetch request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

          try {
            const response = await fetch(`/api/sales/${selectedInvoice}`, {
              signal: controller.signal,
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            // Clear the timeout
            clearTimeout(timeoutId);

            console.log(`API response status: ${response.status}`);

            if (response.ok) {
              const invoiceDetails = await response.json();
              console.log(`Successfully fetched invoice details:`, invoiceDetails);
              setSelectedInvoiceDetails(invoiceDetails);

              // If we have invoice details, populate the products from the invoice
              if (invoiceDetails && invoiceDetails.items && invoiceDetails.items.length > 0) {
                console.log(`Populating ${invoiceDetails.items.length} products from invoice`);
                const invoiceProducts = invoiceDetails.items.map(item => ({
                  productId: item.productId,
                  quantity: item.quantity,
                  price: item.unitPrice,
                  reason: "",
                  originalItem: item // Keep reference to original item
                }));
                setSelectedProducts(invoiceProducts);
              } else {
                console.log(`No items found in invoice details`);
              }
            } else {
              let errorMessage = "Unknown error";
              let responseText = "";

              try {
                responseText = await response.text();
                console.log(`Error response text:`, responseText);

                try {
                  const errorData = JSON.parse(responseText);
                  console.error("Error fetching invoice details:", errorData);
                  errorMessage = errorData.error || `HTTP Error: ${response.status}`;
                } catch (jsonError) {
                  console.error("Error parsing JSON response:", jsonError);
                  errorMessage = responseText || `HTTP Error: ${response.status}`;
                }
              } catch (textError) {
                console.error("Error reading response text:", textError);
                errorMessage = `HTTP Error: ${response.status}`;
              }

              // Set error state instead of using alert
              const fullErrorMessage = `Error fetching invoice details: ${errorMessage}`;
              setError(fullErrorMessage);
              console.error(fullErrorMessage);

              // Reset the selected invoice if it can't be found
              setSelectedInvoice("");
            }
          } catch (fetchError) {
            // Clear the timeout if fetch fails
            clearTimeout(timeoutId);

            // Handle fetch errors (like network errors)
            console.error("Fetch operation failed:", fetchError);

            let errorMessage = "Network error";
            if (fetchError.name === 'AbortError') {
              errorMessage = "Request timed out after 10 seconds";
            } else if (fetchError instanceof Error) {
              errorMessage = fetchError.message || "Network error";
            }

            setError(`Error fetching invoice details: ${errorMessage}`);
            setSelectedInvoice("");

            // Re-throw to be caught by the outer catch
            throw fetchError;
          }
        } catch (error) {
          console.error("Error fetching invoice details:", error);
          let errorMessage = "Unknown error";

          try {
            if (error instanceof Error) {
              errorMessage = error.message || "Error without message";
            } else if (error && typeof error === 'object') {
              // Handle empty objects
              if (Object.keys(error).length === 0) {
                errorMessage = "Empty error object";
              } else {
                // Try to safely stringify the error object
                try {
                  errorMessage = JSON.stringify(error, Object.getOwnPropertyNames(error));
                } catch (jsonError) {
                  errorMessage = "Error object could not be converted to string";
                }
              }
            } else if (error) {
              errorMessage = String(error);
            }
          } catch (errorHandlingError) {
            console.error("Error while handling error:", errorHandlingError);
            errorMessage = "Error occurred while processing error information";
          }

          // Set error state instead of using alert
          setError(`Error fetching invoice details: ${errorMessage}`);
          console.error(`Error fetching invoice details: ${errorMessage}`);

          // Reset the selected invoice if there's an error
          setSelectedInvoice("");
        }
      } else {
        setSelectedInvoiceDetails(null);
        setSelectedProducts([{ productId: "", quantity: 1, price: 0, reason: "" }]);
        // Clear any previous errors
        setError("");
      }
    };

    fetchInvoiceDetails();
  }, [selectedInvoice]);

  const handleCustomerChange = (e) => {
    setSelectedCustomer(e.target.value);
    setSelectedInvoice("");
  };

  const handleInvoiceChange = (e) => {
    setSelectedInvoice(e.target.value);
  };

  const handleProductChange = (index, field, value) => {
    const updatedProducts = [...selectedProducts];
    updatedProducts[index][field] = value;

    if (field === "productId" && value) {
      const product = products.find(p => p.id === value);
      if (product) {
        updatedProducts[index].price = product.sellingPrice;
      }
    }

    setSelectedProducts(updatedProducts);
  };

  const addProductRow = () => {
    setSelectedProducts([...selectedProducts, { productId: "", quantity: 1, price: 0, reason: "" }]);
  };

  const removeProductRow = (index) => {
    if (selectedProducts.length > 1) {
      const updatedProducts = [...selectedProducts];
      updatedProducts.splice(index, 1);
      setSelectedProducts(updatedProducts);
    }
  };

  // Calculate total amount
  const calculateTotal = () => {
    return selectedProducts.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  // Calculate total paid amount
  const calculateTotalPaid = () => {
    return paymentMethods.reduce((sum, payment) => sum + payment.amount, 0);
  };

  // Calculate remaining amount
  const calculateRemainingAmount = () => {
    const total = calculateTotal();
    const totalPaid = calculateTotalPaid();
    return Math.max(0, total - totalPaid);
  };

  // Add payment method
  const addPaymentMethod = () => {
    if (selectedPaymentMethod && paymentAmount > 0 && paymentAmount <= calculateRemainingAmount()) {
      // Check if this payment method already exists
      const existingPaymentIndex = paymentMethods.findIndex(p => p.method === selectedPaymentMethod);

      if (existingPaymentIndex >= 0) {
        // Update existing payment method
        const updatedPaymentMethods = [...paymentMethods];
        updatedPaymentMethods[existingPaymentIndex].amount += paymentAmount;
        setPaymentMethods(updatedPaymentMethods);
      } else {
        // Add new payment method
        setPaymentMethods([...paymentMethods, { method: selectedPaymentMethod, amount: paymentAmount }]);
      }

      // Reset payment amount to remaining amount
      setPaymentAmount(calculateRemainingAmount() - paymentAmount);
    }
  };

  // Remove payment method
  const removePaymentMethod = (index) => {
    const updatedPaymentMethods = [...paymentMethods];
    const removedAmount = updatedPaymentMethods[index].amount;
    updatedPaymentMethods.splice(index, 1);
    setPaymentMethods(updatedPaymentMethods);

    // Update payment amount to include the removed amount
    setPaymentAmount(paymentAmount + removedAmount);
  };

  // Get payment status text
  const getPaymentStatusText = () => {
    const totalPaid = calculateTotalPaid();
    const total = calculateTotal();

    if (totalPaid === 0) return 'Unpaid (Customer Account)';
    if (totalPaid < total) return 'Partially Paid (Remaining to Customer Account)';
    return 'Paid';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Clear any previous errors
    setError("");

    if (!selectedCustomer) {
      setError("Please select a customer");
      return;
    }

    if (!selectedInvoice) {
      setError("Please select an invoice");
      return;
    }

    if (selectedProducts.length === 0 || selectedProducts.some(p => !p.productId || !p.reason)) {
      setError("Please select products and provide reasons for return");
      return;
    }

    setIsLoading(true);

    try {
      // Calculate totals
      const subtotal = calculateTotal();
      const totalPaid = calculateTotalPaid();
      const remainingAmount = calculateRemainingAmount();

      // Get branch ID from the invoice details
      const branchId = selectedInvoiceDetails?.branchId;
      if (!branchId) {
        setError("Branch information is missing from the invoice");
        setIsLoading(false);
        return;
      }

      // Determine payment status based on payment methods
      let paymentStatus = "PAID";
      let primaryPaymentMethod = "CASH";

      // If there are no payment methods or remaining amount > 0, add CUSTOMER_ACCOUNT payment
      let finalPaymentMethods = [...paymentMethods];

      if (finalPaymentMethods.length === 0) {
        // If no payment methods, all goes to customer account
        finalPaymentMethods = [{ method: "CUSTOMER_ACCOUNT", amount: subtotal }];
        paymentStatus = "UNPAID";
        primaryPaymentMethod = "CUSTOMER_ACCOUNT";
      } else if (remainingAmount > 0) {
        // If there's remaining amount, add it as CUSTOMER_ACCOUNT
        finalPaymentMethods.push({ method: "CUSTOMER_ACCOUNT", amount: remainingAmount });
        paymentStatus = "PARTIALLY_PAID";
        // Set primary payment method to the first non-customer account method
        const nonCustomerAccountMethod = finalPaymentMethods.find(p => p.method !== "CUSTOMER_ACCOUNT");
        if (nonCustomerAccountMethod) {
          primaryPaymentMethod = nonCustomerAccountMethod.method;
        }
      }

      // Prepare data for API
      const creditNoteData = {
        contactId: selectedCustomer,
        branchId: branchId,
        originalInvoiceId: selectedInvoice,
        originalInvoiceNumber: selectedInvoiceDetails?.invoiceNumber,
        date: new Date().toISOString(),
        items: selectedProducts.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.price,
          total: item.price * item.quantity,
          reason: item.reason,
          originalItemId: item.originalItem?.id,
          // We don't need to specify warehouseId, the API will find the default warehouse
        })),
        subtotalAmount: subtotal,
        totalAmount: subtotal, // For now, total equals subtotal (no tax/discount handling yet)
        notes: `Credit note for invoice #${selectedInvoiceDetails?.invoiceNumber}`,
        paymentStatus: paymentStatus,
        paymentMethod: primaryPaymentMethod,
        payments: finalPaymentMethods
      };

      // Call the API to create a credit note
      const response = await fetch("/api/credit-notes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(creditNoteData),
      });

      if (response.ok) {
        const result = await response.json();
        // Use a more user-friendly approach instead of alert
        setError(""); // Clear any errors
        // Show success message and redirect
        alert("Credit note created successfully!");
        router.push("/dashboard/sales/credit-note");
      } else {
        let errorMessage = "Failed to create credit note";
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
          errorMessage = `HTTP Error: ${response.status}`;
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error creating credit note:", error);
      let errorMessage = "Unknown error";

      try {
        if (error instanceof Error) {
          errorMessage = error.message || "Error without message";
        } else if (error && typeof error === 'object') {
          // Handle empty objects
          if (Object.keys(error).length === 0) {
            errorMessage = "Empty error object";
          } else {
            // Try to safely stringify the error object
            try {
              errorMessage = JSON.stringify(error, Object.getOwnPropertyNames(error));
            } catch (jsonError) {
              errorMessage = "Error object could not be converted to string";
            }
          }
        } else if (error) {
          errorMessage = String(error);
        }
      } catch (errorHandlingError) {
        console.error("Error while handling error:", errorHandlingError);
        errorMessage = "Error occurred while processing error information";
      }
      setError(`Error creating credit note: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">New Credit Note</h1>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6">
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            {/* Customer Selection */}
            <div className="sm:col-span-3">
              <label htmlFor="customer" className="block text-sm font-medium text-gray-700">
                Customer
              </label>
              <div className="mt-1">
                <ContactSearchInput
                  onSelectContact={(contact) => handleCustomerChange({ target: { value: contact.id } })}
                  placeholder="Search customer by name or phone"
                  contactType="customer"
                  selectedContactId={selectedCustomer}
                  buttonLabel="Add New Customer"
                  className="w-full"
                />
              </div>
            </div>

            {/* Invoice Selection */}
            <div className="sm:col-span-3">
              <label htmlFor="invoice" className="block text-sm font-medium text-gray-700">
                Related Invoice
              </label>
              <div className="mt-1">
                <select
                  id="invoice"
                  name="invoice"
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  value={selectedInvoice}
                  onChange={handleInvoiceChange}
                  disabled={!selectedCustomer}
                >
                  <option value="">Select an invoice</option>
                  {invoices.map((invoice) => (
                    <option key={invoice.id} value={invoice.id}>
                      Invoice #{invoice.invoiceNumber} - {new Date(invoice.date).toLocaleDateString()} -
                      {invoice.paymentStatus === "PAID" ? " (مدفوعة)" : invoice.paymentStatus === "PARTIALLY_PAID" ? " (مدفوعة جزئياً)" : " (غير مدفوعة)"} -
                      {invoice.totalAmount} ج.م
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Invoice Details Section */}
            {selectedInvoiceDetails && (
              <div className="sm:col-span-6 mb-6">
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Invoice Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Invoice Number</p>
                      <p className="text-sm text-gray-900">{selectedInvoiceDetails.invoiceNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Date</p>
                      <p className="text-sm text-gray-900">{new Date(selectedInvoiceDetails.date).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Status</p>
                      <p className="text-sm text-gray-900">
                        {selectedInvoiceDetails.paymentStatus === "PAID" ? "مدفوعة" :
                         selectedInvoiceDetails.paymentStatus === "PARTIALLY_PAID" ? "مدفوعة جزئياً" : "غير مدفوعة"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Payment Method</p>
                      <p className="text-sm text-gray-900">
                        {selectedInvoiceDetails.paymentMethod === "CASH" ? "نقدي" :
                         selectedInvoiceDetails.paymentMethod === "VODAFONE_CASH" ? "فودافون كاش" :
                         selectedInvoiceDetails.paymentMethod === "BANK_TRANSFER" ? "تحويل بنكي" :
                         selectedInvoiceDetails.paymentMethod === "CREDIT_CARD" ? "بطاقة ائتمان" : selectedInvoiceDetails.paymentMethod}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Subtotal</p>
                      <p className="text-sm text-gray-900">{selectedInvoiceDetails.subtotalAmount} ج.م</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Total</p>
                      <p className="text-sm text-gray-900">{selectedInvoiceDetails.totalAmount} ج.م</p>
                    </div>
                  </div>

                  {/* Invoice Items */}
                  <div className="mt-4">
                    <h4 className="text-md font-medium text-gray-900 mb-2">Invoice Items</h4>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-100">
                          <tr>
                            <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Product
                            </th>
                            <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Quantity
                            </th>
                            <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Unit Price
                            </th>
                            <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {selectedInvoiceDetails.items.map((item) => (
                            <tr key={item.id}>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                {item.product?.name || "Unknown Product"}
                              </td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                {item.quantity}
                              </td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                {item.unitPrice} ج.م
                              </td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                {item.totalPrice} ج.م
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Products Section */}
            {/* Payment Methods Section */}
            <div className="sm:col-span-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
              <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                <div className="mb-4">
                  <div className="flex justify-between text-sm font-bold mb-2">
                    <span>Total Amount:</span>
                    <span>{calculateTotal().toFixed(2)} ج.م</span>
                  </div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Total Paid:</span>
                    <span>{calculateTotalPaid().toFixed(2)} ج.م</span>
                  </div>
                  <div className="flex justify-between text-sm font-bold">
                    <span>Remaining:</span>
                    <span>{calculateRemainingAmount().toFixed(2)} ج.م</span>
                  </div>
                </div>

                {/* Payment Method Selection */}
                {paymentMethodsLoading ? (
                  <div className="flex justify-center items-center py-4">
                    <svg className="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="ml-2 text-gray-600">Loading payment methods...</span>
                  </div>
                ) : availablePaymentMethods.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    No payment methods available. Please add payment methods in settings.
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {availablePaymentMethods.map((method) => (
                      <div
                        key={method.id}
                        className={`p-2 border rounded-md cursor-pointer flex items-center ${selectedPaymentMethod === method.code ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedPaymentMethod(method.code)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        <span className="font-medium text-black">{method.name}</span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Payment Amount Input */}
                <div className="flex items-center mb-4">
                  <div className="flex-1 mr-2">
                    <label htmlFor="paymentAmount" className="block text-sm font-medium text-black mb-1">
                      Payment Amount
                    </label>
                    <div className="relative rounded-md shadow-sm">
                      <input
                        type="number"
                        id="paymentAmount"
                        className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-3 pr-12 sm:text-sm border-gray-300 rounded-md text-black"
                        placeholder="0.00"
                        min="0"
                        max={calculateRemainingAmount()}
                        step="0.01"
                        value={paymentAmount}
                        onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <span className="text-black sm:text-sm">ج.م</span>
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    onClick={addPaymentMethod}
                    disabled={!selectedPaymentMethod || paymentAmount <= 0 || paymentAmount > calculateRemainingAmount()}
                  >
                    Add Payment
                  </button>
                </div>

                {/* Payment Methods List */}
                {paymentMethods.length > 0 && (
                  <div className="mt-4 border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                            Method
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                            Amount
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {paymentMethods.map((payment, index) => (
                          <tr key={index}>
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-black">
                              {payment.method === 'CUSTOMER_ACCOUNT' ? 'Customer Account' :
                                availablePaymentMethods.find(m => m.code === payment.method)?.name || payment.method}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                              {payment.amount.toFixed(2)} ج.م
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                              <button
                                type="button"
                                onClick={() => removePaymentMethod(index)}
                                className="text-red-600 hover:text-red-900"
                              >
                                Remove
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}

                {/* Payment Status */}
                {calculateRemainingAmount() < calculateTotal() && (
                  <div className="mt-4 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm font-medium text-yellow-800">
                      Payment Status: {getPaymentStatusText()}
                    </p>
                    {calculateRemainingAmount() > 0 && (
                      <p className="text-xs text-yellow-600 mt-1">
                        Remaining amount ({calculateRemainingAmount().toFixed(2)} ج.م) will be added to customer account.
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="sm:col-span-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Products to Return</h3>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reason
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {selectedProducts.map((product, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={product.productId}
                            onChange={(e) => handleProductChange(index, "productId", e.target.value)}
                            required
                          >
                            <option value="">Select a product</option>
                            {products.map((p) => (
                              <option key={p.id} value={p.id}>
                                {p.name}
                              </option>
                            ))}
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="number"
                            min="1"
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={product.quantity}
                            onChange={(e) => handleProductChange(index, "quantity", parseInt(e.target.value))}
                            required
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={product.price}
                            onChange={(e) => handleProductChange(index, "price", parseFloat(e.target.value))}
                            required
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={product.reason}
                            onChange={(e) => handleProductChange(index, "reason", e.target.value)}
                            required
                          >
                            <option value="">Select a reason</option>
                            <option value="damaged">Damaged</option>
                            <option value="wrong_item">Wrong Item</option>
                            <option value="quality_issue">Quality Issue</option>
                            <option value="other">Other</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            type="button"
                            className="text-red-600 hover:text-red-900"
                            onClick={() => removeProductRow(index)}
                            disabled={selectedProducts.length === 1}
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-4">
                <button
                  type="button"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  onClick={addProductRow}
                >
                  Add Product
                </button>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button
              type="button"
              className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              onClick={() => router.push("/dashboard/sales/credit-note")}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              disabled={isLoading}
            >
              {isLoading ? "Creating..." : "Create Credit Note"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
