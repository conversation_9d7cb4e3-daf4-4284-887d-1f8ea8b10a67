"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { format, subDays } from "date-fns";
import { ArrowDown, ArrowUp, Banknote, CreditCard, FileText, Filter, Calendar, Building } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ian<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";

// Mock report data
const mockReportData = {
  paidPurchases: 185000.75,
  unpaidPurchases: 67250.50,
  totalPurchases: 252251.25,
  purchasesBySupplier: [
    { name: "Supplier A", value: 120000 },
    { name: "Supplier B", value: 75000 },
    { name: "Supplier C", value: 45000 },
    { name: "Others", value: 12251.25 }
  ],
  purchasesByCategory: [
    { name: "Computers", value: 95000 },
    { name: "Components", value: 65000 },
    { name: "Accessories", value: 45000 },
    { name: "Software", value: 25000 },
    { name: "Others", value: 22251.25 }
  ],
  monthlyPurchases: [
    { name: "Jan", value: 25000 },
    { name: "Feb", value: 30000 },
    { name: "Mar", value: 28000 },
    { name: "Apr", value: 32000 },
    { name: "May", value: 35000 },
    { name: "Jun", value: 40000 },
    { name: "Jul", value: 38000 },
    { name: "Aug", value: 42000 },
    { name: "Sep", value: 45000 },
    { name: "Oct", value: 48000 },
    { name: "Nov", value: 52000 },
    { name: "Dec", value: 55000 }
  ]
};

// Colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export default function PurchasesReportsPage() {
  const [startDate, setStartDate] = useState<Date | undefined>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [selectedBranch, setSelectedBranch] = useState("all");
  const [selectedSupplier, setSelectedSupplier] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [reportData, setReportData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [branches, setBranches] = useState<any[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("summary");

  // Fetch branches, suppliers, and categories
  useEffect(() => {
    fetchBranches();
    fetchSuppliers();
    fetchCategories();
  }, []);

  // Fetch branches
  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches');
      if (response.ok) {
        const data = await response.json();
        setBranches([
          { id: "all", name: "All Branches" },
          ...data.map((branch: any) => ({ id: branch.id, name: branch.name }))
        ]);
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
      setBranches([
        { id: "all", name: "All Branches" },
        { id: "branch1", name: "Main Branch" },
        { id: "branch2", name: "Downtown Branch" }
      ]);
    }
  };

  // Fetch suppliers
  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/contacts?type=supplier');
      if (response.ok) {
        const data = await response.json();
        setSuppliers([
          { id: "all", name: "All Suppliers" },
          ...data.map((supplier: any) => ({ id: supplier.id, name: supplier.name }))
        ]);
      }
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      setSuppliers([
        { id: "all", name: "All Suppliers" },
        { id: "supplier1", name: "Supplier A" },
        { id: "supplier2", name: "Supplier B" }
      ]);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories([
          { id: "all", name: "All Categories" },
          ...data.map((category: any) => ({ id: category.id, name: category.name }))
        ]);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      setCategories([
        { id: "all", name: "All Categories" },
        { id: "category1", name: "Computers" },
        { id: "category2", name: "Components" }
      ]);
    }
  };

  // Generate report
  const generateReport = async () => {
    setIsLoading(true);

    try {
      // Format dates for API
      const formattedStartDate = startDate ? format(startDate, 'yyyy-MM-dd') : '';
      const formattedEndDate = endDate ? format(endDate, 'yyyy-MM-dd') : '';

      // Fetch report data from API
      const response = await fetch(
        `/api/reports/purchases?startDate=${formattedStartDate}&endDate=${formattedEndDate}&branchId=${selectedBranch}&supplierId=${selectedSupplier}&categoryId=${selectedCategory}`
      );

      if (response.ok) {
        const data = await response.json();
        setReportData(data.data || data);
      } else {
        console.error("Error fetching report:", await response.text());
        // Fallback to mock data if API fails
        setReportData(mockReportData);
      }
    } catch (error) {
      console.error("Error generating report:", error);
      // Fallback to mock data if API fails
      setReportData(mockReportData);
    } finally {
      setIsLoading(false);
    }
  };

  // Call generateReport when filters change
  useEffect(() => {
    if (startDate && endDate) {
      generateReport();
    }
  }, [startDate, endDate, selectedBranch, selectedSupplier, selectedCategory]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Purchase Reports</h2>
          <p className="text-muted-foreground">
            Analyze and track your purchase data
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" onClick={() => window.print()}>
            <FileText className="mr-2 h-4 w-4" />
            Print Report
          </Button>
          <Link href="/dashboard/purchases">
            <Button variant="outline">
              Back to Purchases
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Report Filters</CardTitle>
          <CardDescription>
            Filter your purchase data by date range, branch, supplier, and category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
              <DatePicker
                date={startDate}
                setDate={setStartDate}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
              <DatePicker
                date={endDate}
                setDate={setEndDate}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Branch</label>
              <Select value={selectedBranch} onValueChange={setSelectedBranch}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Branch" />
                </SelectTrigger>
                <SelectContent>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Supplier</label>
              <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Supplier" />
                </SelectTrigger>
                <SelectContent>
                  {suppliers.map((supplier) => (
                    <SelectItem key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="bySupplier">By Supplier</TabsTrigger>
          <TabsTrigger value="byCategory">By Category</TabsTrigger>
        </TabsList>

        {/* Summary Tab */}
        <TabsContent value="summary">
          <Card>
            <CardHeader>
              <CardTitle>Purchase Summary</CardTitle>
              <CardDescription>
                Overview of your purchase data for the selected period
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                </div>
              ) : reportData ? (
                <div className="space-y-6">
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white overflow-hidden rounded-lg border border-green-100 p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-green-100 rounded-md p-3 mr-4">
                          <CreditCard className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Paid Purchases</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.paidPurchases)}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white overflow-hidden rounded-lg border border-yellow-100 p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-yellow-100 rounded-md p-3 mr-4">
                          <Banknote className="h-6 w-6 text-yellow-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Unpaid Purchases</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.unpaidPurchases)}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white overflow-hidden rounded-lg border border-blue-100 p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-blue-100 rounded-md p-3 mr-4">
                          <FileText className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Total Purchases</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.totalPurchases)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Monthly Purchases Chart */}
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Purchase Trends</h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={reportData.monthlyPurchases}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip formatter={(value) => formatCurrency(value as number)} />
                          <Legend />
                          <Bar dataKey="value" name="Purchase Amount" fill="#3895e7" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available. Please adjust your filters or try again.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* By Supplier Tab */}
        <TabsContent value="bySupplier">
          <Card>
            <CardHeader>
              <CardTitle>Purchases by Supplier</CardTitle>
              <CardDescription>
                Breakdown of purchases by supplier for the selected period
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                </div>
              ) : reportData ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Pie Chart */}
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={reportData.purchasesBySupplier}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {reportData.purchasesBySupplier.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  {/* Table */}
                  <div>
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.purchasesBySupplier.map((supplier: any, index: number) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{supplier.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(supplier.value)}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {((supplier.value / reportData.totalPurchases) * 100).toFixed(2)}%
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available. Please adjust your filters or try again.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* By Category Tab */}
        <TabsContent value="byCategory">
          <Card>
            <CardHeader>
              <CardTitle>Purchases by Category</CardTitle>
              <CardDescription>
                Breakdown of purchases by product category for the selected period
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                </div>
              ) : reportData ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Pie Chart */}
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={reportData.purchasesByCategory}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {reportData.purchasesByCategory.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  {/* Table */}
                  <div>
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.purchasesByCategory.map((category: any, index: number) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{category.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(category.value)}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {((category.value / reportData.totalPurchases) * 100).toFixed(2)}%
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available. Please adjust your filters or try again.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
