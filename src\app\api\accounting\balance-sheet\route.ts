import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/balance-sheet - Get balance sheet
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const asOfDate = url.searchParams.get("asOfDate");
    
    // Validate required parameters
    if (!asOfDate) {
      return NextResponse.json(
        { error: "As of date is required" },
        { status: 400 }
      );
    }
    
    const asOfDateObj = new Date(asOfDate);
    
    // Get all asset accounts
    const assetAccounts = await db.account.findMany({
      where: {
        type: "ASSET",
        isActive: true,
      },
      orderBy: {
        code: "asc",
      },
    });
    
    // Get all liability accounts
    const liabilityAccounts = await db.account.findMany({
      where: {
        type: "LIABILITY",
        isActive: true,
      },
      orderBy: {
        code: "asc",
      },
    });
    
    // Get all equity accounts (excluding retained earnings)
    const equityAccounts = await db.account.findMany({
      where: {
        type: "EQUITY",
        isActive: true,
        NOT: {
          code: "3100", // Assuming 3100 is the code for Retained Earnings
        },
      },
      orderBy: {
        code: "asc",
      },
    });
    
    // Get all journal entries up to the specified date
    const journalEntries = await db.journalEntry.findMany({
      where: {
        date: {
          lte: asOfDateObj,
        },
      },
    });
    
    // Calculate balances for asset accounts
    const assetsWithBalances = assetAccounts.map(account => {
      // For asset accounts, debits increase the balance
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Net balance is debits minus credits for asset accounts
      const balance = totalDebits - totalCredits;
      
      return {
        ...account,
        balance,
      };
    });
    
    // Calculate balances for liability accounts
    const liabilitiesWithBalances = liabilityAccounts.map(account => {
      // For liability accounts, credits increase the balance
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Net balance is credits minus debits for liability accounts
      const balance = totalCredits - totalDebits;
      
      return {
        ...account,
        balance,
      };
    });
    
    // Calculate balances for equity accounts
    const equityWithBalances = equityAccounts.map(account => {
      // For equity accounts, credits increase the balance
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Net balance is credits minus debits for equity accounts
      const balance = totalCredits - totalDebits;
      
      return {
        ...account,
        balance,
      };
    });
    
    // Calculate retained earnings
    // Get all revenue accounts
    const revenueAccounts = await db.account.findMany({
      where: {
        type: "REVENUE",
        isActive: true,
      },
    });
    
    // Get all expense accounts
    const expenseAccounts = await db.account.findMany({
      where: {
        type: "EXPENSE",
        isActive: true,
      },
    });
    
    // Calculate total revenue
    let totalRevenue = 0;
    for (const account of revenueAccounts) {
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Net balance is credits minus debits for revenue accounts
      totalRevenue += (totalCredits - totalDebits);
    }
    
    // Calculate total expenses
    let totalExpenses = 0;
    for (const account of expenseAccounts) {
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Net balance is debits minus credits for expense accounts
      totalExpenses += (totalDebits - totalCredits);
    }
    
    // Retained earnings is revenue minus expenses
    const retainedEarnings = totalRevenue - totalExpenses;
    
    // Filter out accounts with zero balance
    const nonZeroAssets = assetsWithBalances.filter(account => account.balance !== 0);
    const nonZeroLiabilities = liabilitiesWithBalances.filter(account => account.balance !== 0);
    const nonZeroEquity = equityWithBalances.filter(account => account.balance !== 0);
    
    // Calculate totals
    const totalAssets = nonZeroAssets.reduce((sum, account) => sum + account.balance, 0);
    const totalLiabilities = nonZeroLiabilities.reduce((sum, account) => sum + account.balance, 0);
    const totalEquityWithoutRetained = nonZeroEquity.reduce((sum, account) => sum + account.balance, 0);
    const totalEquity = totalEquityWithoutRetained + retainedEarnings;
    
    return NextResponse.json({
      asOfDate: asOfDateObj,
      assets: nonZeroAssets,
      liabilities: nonZeroLiabilities,
      equity: nonZeroEquity,
      totalAssets,
      totalLiabilities,
      totalEquity,
      retainedEarnings,
    });
  } catch (error: any) {
    console.error("Error generating balance sheet:", error);
    return NextResponse.json(
      { error: error.message || "Failed to generate balance sheet" },
      { status: 500 }
    );
  }
}
