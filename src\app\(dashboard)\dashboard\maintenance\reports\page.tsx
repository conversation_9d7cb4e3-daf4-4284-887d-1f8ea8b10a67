"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { 
  Loader2, 
  ArrowLeft, 
  Calendar, 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON>,
  Clock,
  CheckCircle2,
  Truck,
  XCircle,
  Settings,
  AlertTriangle,
  Download
} from "lucide-react";
import { getStatusLabel, getStatusColor } from "@/lib/maintenance";
import { formatCurrency } from "@/lib/utils";
import { format, subDays, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns";

interface MaintenanceStats {
  totalCount: number;
  completedCount: number;
  inProgressCount: number;
  waitingForPartsCount: number;
  cancelledCount: number;
  overdueCount: number;
  averageCompletionDays: number;
  totalRevenue: number;
  statusBreakdown: {
    status: string;
    count: number;
  }[];
  monthlyStats: {
    month: string;
    count: number;
    revenue: number;
  }[];
  topDeviceTypes: {
    deviceType: string;
    count: number;
  }[];
  topBrands: {
    brand: string;
    count: number;
  }[];
}

export default function MaintenanceReportsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<MaintenanceStats | null>(null);
  const [dateRange, setDateRange] = useState<"month" | "quarter" | "year" | "all">("month");
  const [startDate, setStartDate] = useState<Date>(startOfMonth(new Date()));
  const [endDate, setEndDate] = useState<Date>(endOfMonth(new Date()));

  // Fetch maintenance statistics
  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      try {
        // Calculate date range
        let start = startDate;
        let end = endDate;

        if (dateRange === "month") {
          start = startOfMonth(new Date());
          end = endOfMonth(new Date());
        } else if (dateRange === "quarter") {
          start = subDays(new Date(), 90);
          end = new Date();
        } else if (dateRange === "year") {
          start = startOfYear(new Date());
          end = endOfYear(new Date());
        } else if (dateRange === "all") {
          start = new Date(2000, 0, 1); // Far in the past
          end = new Date(2100, 0, 1); // Far in the future
        }

        setStartDate(start);
        setEndDate(end);

        // Format dates for API
        const startStr = format(start, "yyyy-MM-dd");
        const endStr = format(end, "yyyy-MM-dd");

        const response = await fetch(`/api/maintenance/reports?startDate=${startStr}&endDate=${endStr}`);
        
        if (!response.ok) {
          throw new Error("Failed to fetch maintenance statistics");
        }
        
        const data = await response.json();
        setStats(data);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [dateRange]);

  // Handle date range change
  const handleDateRangeChange = (range: "month" | "quarter" | "year" | "all") => {
    setDateRange(range);
  };

  // Generate CSV data for export
  const exportToCSV = () => {
    if (!stats) return;

    // Create CSV content
    let csvContent = "data:text/csv;charset=utf-8,";
    
    // Add headers
    csvContent += "Metric,Value\r\n";
    
    // Add data
    csvContent += `Total Services,${stats.totalCount}\r\n`;
    csvContent += `Completed Services,${stats.completedCount}\r\n`;
    csvContent += `In Progress Services,${stats.inProgressCount}\r\n`;
    csvContent += `Waiting for Parts,${stats.waitingForPartsCount}\r\n`;
    csvContent += `Cancelled Services,${stats.cancelledCount}\r\n`;
    csvContent += `Overdue Services,${stats.overdueCount}\r\n`;
    csvContent += `Average Completion Days,${stats.averageCompletionDays.toFixed(1)}\r\n`;
    csvContent += `Total Revenue,${stats.totalRevenue.toFixed(2)}\r\n`;
    
    // Add status breakdown
    csvContent += "\r\nStatus Breakdown\r\n";
    csvContent += "Status,Count\r\n";
    stats.statusBreakdown.forEach(item => {
      csvContent += `${getStatusLabel(item.status)},${item.count}\r\n`;
    });
    
    // Add monthly stats
    csvContent += "\r\nMonthly Statistics\r\n";
    csvContent += "Month,Count,Revenue\r\n";
    stats.monthlyStats.forEach(item => {
      csvContent += `${item.month},${item.count},${item.revenue.toFixed(2)}\r\n`;
    });
    
    // Add device types
    csvContent += "\r\nTop Device Types\r\n";
    csvContent += "Device Type,Count\r\n";
    stats.topDeviceTypes.forEach(item => {
      csvContent += `${item.deviceType},${item.count}\r\n`;
    });
    
    // Add brands
    csvContent += "\r\nTop Brands\r\n";
    csvContent += "Brand,Count\r\n";
    stats.topBrands.forEach(item => {
      csvContent += `${item.brand},${item.count}\r\n`;
    });
    
    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `maintenance_report_${format(new Date(), "yyyy-MM-dd")}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            href="/dashboard/maintenance"
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Maintenance Reports</h1>
        </div>
        <button
          onClick={exportToCSV}
          disabled={isLoading || !stats}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium flex items-center"
        >
          <Download className="h-4 w-4 mr-2" />
          Export to CSV
        </button>
      </div>

      {/* Date range filter */}
      <div className="mb-6">
        <div className="flex space-x-2">
          <button
            onClick={() => handleDateRangeChange("month")}
            className={`px-3 py-1 rounded-md font-medium ${
              dateRange === "month"
                ? "bg-indigo-100 text-indigo-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            This Month
          </button>
          <button
            onClick={() => handleDateRangeChange("quarter")}
            className={`px-3 py-1 rounded-md font-medium ${
              dateRange === "quarter"
                ? "bg-indigo-100 text-indigo-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            Last 90 Days
          </button>
          <button
            onClick={() => handleDateRangeChange("year")}
            className={`px-3 py-1 rounded-md font-medium ${
              dateRange === "year"
                ? "bg-indigo-100 text-indigo-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            This Year
          </button>
          <button
            onClick={() => handleDateRangeChange("all")}
            className={`px-3 py-1 rounded-md font-medium ${
              dateRange === "all"
                ? "bg-indigo-100 text-indigo-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            All Time
          </button>
        </div>
        <p className="text-sm text-gray-500 mt-2">
          Showing data from {format(startDate, "dd/MM/yyyy")} to {format(endDate, "dd/MM/yyyy")}
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
      ) : stats ? (
        <div className="space-y-6">
          {/* Summary cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg shadow-md p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Services</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCount}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Completed</p>
                  <p className="text-2xl font-bold text-green-600">{stats.completedCount}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <CheckCircle2 className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                {stats.totalCount > 0 
                  ? `${((stats.completedCount / stats.totalCount) * 100).toFixed(1)}% completion rate` 
                  : "No services"}
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">In Progress</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.inProgressCount}</p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-full">
                  <Settings className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                {stats.totalCount > 0 
                  ? `${((stats.inProgressCount / stats.totalCount) * 100).toFixed(1)}% of total` 
                  : "No services"}
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Overdue</p>
                  <p className="text-2xl font-bold text-red-600">{stats.overdueCount}</p>
                </div>
                <div className="p-3 bg-red-100 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                {stats.totalCount > 0 
                  ? `${((stats.overdueCount / stats.totalCount) * 100).toFixed(1)}% of total` 
                  : "No services"}
              </p>
            </div>
          </div>

          {/* Additional metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h2>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <p className="text-sm font-medium text-gray-500">Average Completion Time</p>
                    <p className="text-sm font-medium text-gray-900">{stats.averageCompletionDays.toFixed(1)} days</p>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        stats.averageCompletionDays <= 7 
                          ? "bg-green-500" 
                          : stats.averageCompletionDays <= 14 
                            ? "bg-yellow-500" 
                            : "bg-red-500"
                      }`} 
                      style={{ width: `${Math.min((stats.averageCompletionDays / 30) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                    <p className="text-sm font-medium text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                  </div>
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-700 mb-3">Status Breakdown</h3>
                  <div className="space-y-2">
                    {stats.statusBreakdown.map((item) => (
                      <div key={item.status} className="flex items-center">
                        <span className={`inline-block w-3 h-3 rounded-full mr-2 ${getStatusColor(item.status)}`}></span>
                        <span className="text-sm text-gray-600">{getStatusLabel(item.status)}</span>
                        <span className="ml-auto text-sm font-medium text-gray-900">{item.count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Device Analysis</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3">Top Device Types</h3>
                  <div className="space-y-2">
                    {stats.topDeviceTypes.map((item, index) => (
                      <div key={index} className="flex items-center">
                        <span className="text-sm text-gray-600">{item.deviceType}</span>
                        <div className="flex-grow mx-4">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="h-2 rounded-full bg-blue-500" 
                              style={{ 
                                width: `${(item.count / (stats.topDeviceTypes[0]?.count || 1)) * 100}%` 
                              }}
                            ></div>
                          </div>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{item.count}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3">Top Brands</h3>
                  <div className="space-y-2">
                    {stats.topBrands.map((item, index) => (
                      <div key={index} className="flex items-center">
                        <span className="text-sm text-gray-600">{item.brand}</span>
                        <div className="flex-grow mx-4">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="h-2 rounded-full bg-purple-500" 
                              style={{ 
                                width: `${(item.count / (stats.topBrands[0]?.count || 1)) * 100}%` 
                              }}
                            ></div>
                          </div>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{item.count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Monthly trend */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Monthly Trends</h2>
            <div className="h-64">
              {/* This would be a chart in a real implementation */}
              <div className="h-full flex items-end space-x-2">
                {stats.monthlyStats.map((item, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div 
                      className="w-full bg-indigo-500 rounded-t-md" 
                      style={{ 
                        height: `${(item.count / (Math.max(...stats.monthlyStats.map(s => s.count)) || 1)) * 100}%` 
                      }}
                    ></div>
                    <p className="text-xs text-gray-500 mt-2">{item.month}</p>
                    <p className="text-xs font-medium text-gray-700">{item.count}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-50 p-6 text-center rounded-md">
          <p className="text-gray-500">No maintenance data available</p>
        </div>
      )}
    </div>
  );
}
