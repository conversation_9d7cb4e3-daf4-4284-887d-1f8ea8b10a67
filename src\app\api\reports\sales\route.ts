import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// GET /api/reports/sales - Get sales reports
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const reportType = url.searchParams.get("type") || "financial";
    const branchId = url.searchParams.get("branchId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const groupBy = url.searchParams.get("groupBy") || "day";

    // Build filter object
    const filter: any = {};

    if (branchId && branchId !== "all") {
      filter.branchId = branchId;
    }

    // Date range filter
    if (startDate && endDate) {
      const startDateTime = new Date(startDate);
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999); // Set to end of day

      filter.date = {
        gte: startDateTime,
        lte: endDateTime,
      };
    } else if (startDate) {
      filter.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      filter.date = {
        lte: endDateTime,
      };
    } else {
      // Default to last 30 days if no date range specified
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      filter.date = {
        gte: thirtyDaysAgo,
      };
    }

    // Generate report based on type
    let reportData;

    switch (reportType) {
      case "financial":
        // Financial report with paid/unpaid sales and returns

        // Get paid sales (PAID status)
        const paidSales = await db.sale.aggregate({
          where: {
            ...filter,
            paymentStatus: "PAID",
          },
          _sum: {
            totalAmount: true,
          },
        });

        // Get unpaid sales (UNPAID or PARTIALLY_PAID status)
        const unpaidSales = await db.sale.aggregate({
          where: {
            ...filter,
            paymentStatus: {
              in: ["UNPAID", "PARTIALLY_PAID"],
            },
          },
          _sum: {
            totalAmount: true,
          },
        });

        // Get paid credit notes (returns)
        const paidReturns = await db.creditNote.aggregate({
          where: {
            ...filter,
            paymentStatus: "PAID",
          },
          _sum: {
            totalAmount: true,
          },
        });

        // Get unpaid credit notes (returns)
        const unpaidReturns = await db.creditNote.aggregate({
          where: {
            ...filter,
            paymentStatus: {
              in: ["UNPAID", "PARTIALLY_PAID"],
            },
          },
          _sum: {
            totalAmount: true,
          },
        });

        reportData = {
          paidSales: paidSales._sum.totalAmount || 0,
          unpaidSales: unpaidSales._sum.totalAmount || 0,
          paidReturns: paidReturns._sum.totalAmount || 0,
          unpaidReturns: unpaidReturns._sum.totalAmount || 0,
          netSales: (paidSales._sum.totalAmount || 0) + (unpaidSales._sum.totalAmount || 0) -
                    (paidReturns._sum.totalAmount || 0) - (unpaidReturns._sum.totalAmount || 0),
        };
        break;

      case "summary":
        // Summary report with total sales, average sale value, etc.
        const sales = await db.sale.findMany({
          where: filter,
          include: {
            items: true,
          },
        });

        const totalSales = sales.length;
        const totalRevenue = sales.reduce((sum, sale) => sum + sale.totalAmount, 0);
        const averageSaleValue = totalSales > 0 ? totalRevenue / totalSales : 0;

        // Count unique customers
        const uniqueCustomers = new Set(sales.map(sale => sale.contactId)).size;

        // Calculate total items sold
        const totalItemsSold = sales.reduce((sum, sale) => {
          return sum + sale.items.reduce((itemSum, item) => itemSum + item.quantity, 0);
        }, 0);

        reportData = {
          totalSales,
          totalRevenue,
          averageSaleValue,
          uniqueCustomers,
          totalItemsSold,
        };
        break;

      case "byBranch":
        // Sales by branch
        const branchSales = await db.sale.groupBy({
          by: ["branchId"],
          where: filter,
          _sum: {
            totalAmount: true,
          },
          _count: {
            id: true,
          },
        });

        // Get branch details
        const branches = await db.branch.findMany();

        reportData = branchSales.map(branchSale => {
          const branch = branches.find(b => b.id === branchSale.branchId);
          return {
            branchId: branchSale.branchId,
            branchName: branch?.name || "Unknown",
            totalSales: branchSale._count.id,
            totalRevenue: branchSale._sum.totalAmount,
          };
        });
        break;

      case "byProduct":
        // Sales by product
        const productSales = await db.saleItem.groupBy({
          by: ["productId"],
          where: {
            sale: filter,
          },
          _sum: {
            quantity: true,
            totalPrice: true,
          },
        });

        // Get product details
        const products = await db.product.findMany();

        reportData = productSales.map(productSale => {
          const product = products.find(p => p.id === productSale.productId);
          return {
            productId: productSale.productId,
            productName: product?.name || "Unknown",
            quantitySold: productSale._sum.quantity,
            totalRevenue: productSale._sum.totalPrice,
          };
        });
        break;

      case "byTime":
        // Sales over time (daily, weekly, monthly)
        const salesByDate = await db.sale.findMany({
          where: filter,
          select: {
            id: true,
            date: true,
            totalAmount: true,
          },
          orderBy: {
            date: "asc",
          },
        });

        // Group by time period
        const groupedSales: Record<string, { count: number; total: number }> = {};

        salesByDate.forEach(sale => {
          let key: string;
          const date = new Date(sale.date);

          if (groupBy === "day") {
            key = date.toISOString().split("T")[0]; // YYYY-MM-DD
          } else if (groupBy === "week") {
            // Get the week number
            const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
            const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
            const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
            key = `${date.getFullYear()}-W${weekNumber}`;
          } else if (groupBy === "month") {
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
          } else {
            key = date.getFullYear().toString();
          }

          if (!groupedSales[key]) {
            groupedSales[key] = { count: 0, total: 0 };
          }

          groupedSales[key].count += 1;
          groupedSales[key].total += sale.totalAmount;
        });

        reportData = Object.entries(groupedSales).map(([period, data]) => ({
          period,
          salesCount: data.count,
          totalRevenue: data.total,
        }));
        break;

      default:
        return NextResponse.json(
          { error: "Invalid report type" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      reportType,
      dateRange: {
        startDate: startDate || "30 days ago",
        endDate: endDate || "today",
      },
      data: reportData,
    });
  } catch (error) {
    console.error("Error generating sales report:", error);
    return NextResponse.json(
      { error: "Failed to generate sales report" },
      { status: 500 }
    );
  }
}
