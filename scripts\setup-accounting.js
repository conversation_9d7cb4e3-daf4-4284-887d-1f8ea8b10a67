/**
 * <PERSON><PERSON><PERSON> to set up the accounting module
 * 
 * This script will:
 * 1. Create necessary accounting tables if they don't exist
 * 2. Initialize the accounting module with default accounts and journals
 */

const { PrismaClient } = require('@prisma/client');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Initialize Prisma client
const prisma = new PrismaClient();

// Default accounts to create
const defaultAccounts = [
  // Asset accounts (1000-1999)
  { code: '1000', name: 'Assets', type: 'ASSET' },
  { code: '1100', name: 'Cash', type: 'ASSET' },
  { code: '1200', name: 'Bank Accounts', type: 'ASSET' },
  { code: '1300', name: 'Accounts Receivable', type: 'ASSET' },
  { code: '1400', name: 'Inventory', type: 'ASSET' },
  { code: '1500', name: 'Fixed Assets', type: 'ASSET' },
  
  // Liability accounts (2000-2999)
  { code: '2000', name: 'Liabilities', type: 'LIABILITY' },
  { code: '2100', name: 'Accounts Payable', type: 'LIABILITY' },
  { code: '2200', name: 'Taxes Payable', type: 'LIABILITY' },
  { code: '2300', name: 'Loans Payable', type: 'LIABILITY' },
  
  // Equity accounts (3000-3999)
  { code: '3000', name: 'Equity', type: 'EQUITY' },
  { code: '3100', name: 'Retained Earnings', type: 'EQUITY' },
  { code: '3200', name: 'Owner\'s Capital', type: 'EQUITY' },
  
  // Revenue accounts (4000-4999)
  { code: '4000', name: 'Revenue', type: 'REVENUE' },
  { code: '4100', name: 'Sales Revenue', type: 'REVENUE' },
  { code: '4200', name: 'Service Revenue', type: 'REVENUE' },
  { code: '4300', name: 'Interest Income', type: 'REVENUE' },
  
  // Expense accounts (5000-5999)
  { code: '5000', name: 'Expenses', type: 'EXPENSE' },
  { code: '5100', name: 'Cost of Goods Sold', type: 'EXPENSE' },
  { code: '5200', name: 'Salaries Expense', type: 'EXPENSE' },
  { code: '5300', name: 'Rent Expense', type: 'EXPENSE' },
  { code: '5400', name: 'Utilities Expense', type: 'EXPENSE' },
  { code: '5500', name: 'Office Supplies Expense', type: 'EXPENSE' },
];

// Default journals to create
const defaultJournals = [
  { code: 'CASH', name: 'Cash Journal', type: 'CASH', paymentMethod: 'CASH' },
  { code: 'VFCASH', name: 'Vodafone Cash Journal', type: 'VODAFONE_CASH', paymentMethod: 'VODAFONE_CASH' },
  { code: 'BANK', name: 'Bank Transfer Journal', type: 'BANK_TRANSFER', paymentMethod: 'BANK_TRANSFER' },
  { code: 'VISA', name: 'Credit Card Journal', type: 'VISA', paymentMethod: 'VISA' },
  { code: 'CUST', name: 'Customer Account Journal', type: 'CUSTOMER_ACCOUNT', paymentMethod: 'CUSTOMER_ACCOUNT' },
  { code: 'GEN', name: 'General Journal', type: 'GENERAL', paymentMethod: null },
];

// SQL to create accounting tables
const accountingTablesSql = `
-- Create enum types if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'journaltype') THEN
        CREATE TYPE "JournalType" AS ENUM ('CASH', 'VODAFONE_CASH', 'BANK_TRANSFER', 'VISA', 'CUSTOMER_ACCOUNT', 'GENERAL');
    END IF;
END
$$;

-- Create Account table if it doesn't exist or update it
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'Account') THEN
        -- Create the Account table
        CREATE TABLE "Account" (
            "id" TEXT NOT NULL,
            "code" TEXT NOT NULL,
            "name" TEXT NOT NULL,
            "type" "AccountType" NOT NULL,
            "parentId" TEXT,
            "balance" DOUBLE PRECISION NOT NULL DEFAULT 0,
            "isActive" BOOLEAN NOT NULL DEFAULT true,
            "branchId" TEXT,
            "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updatedAt" TIMESTAMP(3) NOT NULL,
            CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
        );
        
        -- Add unique constraint to code
        ALTER TABLE "Account" ADD CONSTRAINT "Account_code_key" UNIQUE ("code");
    ELSE
        -- Update existing Account table
        ALTER TABLE "Account" 
        ADD COLUMN IF NOT EXISTS "code" TEXT,
        ADD COLUMN IF NOT EXISTS "parentId" TEXT,
        ADD COLUMN IF NOT EXISTS "isActive" BOOLEAN NOT NULL DEFAULT true,
        ADD COLUMN IF NOT EXISTS "branchId" TEXT;
        
        -- Add unique constraint to code if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM pg_constraint 
            WHERE conname = 'Account_code_key' AND conrelid = 'public."Account"'::regclass
        ) THEN
            -- Make sure all codes are unique first
            UPDATE "Account" SET "code" = "id" WHERE "code" IS NULL;
            ALTER TABLE "Account" ADD CONSTRAINT "Account_code_key" UNIQUE ("code");
        END IF;
    END IF;
END
$$;

-- Create Journal table if it doesn't exist
CREATE TABLE IF NOT EXISTS "Journal" (
  "id" TEXT NOT NULL,
  "code" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "type" "JournalType" NOT NULL,
  "paymentMethod" TEXT,
  "branchId" TEXT,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Journal_pkey" PRIMARY KEY ("id")
);

-- Add unique constraint to code if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'Journal_code_key' AND conrelid = 'public."Journal"'::regclass
    ) THEN
        ALTER TABLE "Journal" ADD CONSTRAINT "Journal_code_key" UNIQUE ("code");
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist, do nothing
END
$$;

-- Create JournalEntry table if it doesn't exist
CREATE TABLE IF NOT EXISTS "JournalEntry" (
  "id" TEXT NOT NULL,
  "journalId" TEXT NOT NULL,
  "entryNumber" TEXT NOT NULL,
  "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "description" TEXT NOT NULL,
  "debitAccountId" TEXT NOT NULL,
  "creditAccountId" TEXT NOT NULL,
  "amount" DOUBLE PRECISION NOT NULL,
  "contactId" TEXT,
  "reference" TEXT,
  "referenceType" TEXT,
  "isPosted" BOOLEAN NOT NULL DEFAULT false,
  "fiscalPeriodId" TEXT,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "JournalEntry_pkey" PRIMARY KEY ("id")
);

-- Add unique constraint to entryNumber and journalId if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'JournalEntry_entryNumber_journalId_key' AND conrelid = 'public."JournalEntry"'::regclass
    ) THEN
        ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_entryNumber_journalId_key" UNIQUE ("entryNumber", "journalId");
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist, do nothing
END
$$;

-- Create FiscalYear table if it doesn't exist
CREATE TABLE IF NOT EXISTS "FiscalYear" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "startDate" TIMESTAMP(3) NOT NULL,
  "endDate" TIMESTAMP(3) NOT NULL,
  "isClosed" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "FiscalYear_pkey" PRIMARY KEY ("id")
);

-- Create FiscalPeriod table if it doesn't exist
CREATE TABLE IF NOT EXISTS "FiscalPeriod" (
  "id" TEXT NOT NULL,
  "fiscalYearId" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "startDate" TIMESTAMP(3) NOT NULL,
  "endDate" TIMESTAMP(3) NOT NULL,
  "isClosed" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "FiscalPeriod_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints if they don't exist
ALTER TABLE "Account" 
ADD CONSTRAINT IF NOT EXISTS "Account_parentId_fkey" 
FOREIGN KEY ("parentId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Account" 
ADD CONSTRAINT IF NOT EXISTS "Account_branchId_fkey" 
FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Journal" 
ADD CONSTRAINT IF NOT EXISTS "Journal_branchId_fkey" 
FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_journalId_fkey" 
FOREIGN KEY ("journalId") REFERENCES "Journal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_debitAccountId_fkey" 
FOREIGN KEY ("debitAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_creditAccountId_fkey" 
FOREIGN KEY ("creditAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_contactId_fkey" 
FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_fiscalPeriodId_fkey" 
FOREIGN KEY ("fiscalPeriodId") REFERENCES "FiscalPeriod"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "FiscalPeriod" 
ADD CONSTRAINT IF NOT EXISTS "FiscalPeriod_fiscalYearId_fkey" 
FOREIGN KEY ("fiscalYearId") REFERENCES "FiscalYear"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "Account_code_idx" ON "Account"("code");
CREATE INDEX IF NOT EXISTS "Account_type_idx" ON "Account"("type");
CREATE INDEX IF NOT EXISTS "Account_parentId_idx" ON "Account"("parentId");
CREATE INDEX IF NOT EXISTS "Account_isActive_idx" ON "Account"("isActive");

CREATE INDEX IF NOT EXISTS "Journal_code_idx" ON "Journal"("code");
CREATE INDEX IF NOT EXISTS "Journal_type_idx" ON "Journal"("type");
CREATE INDEX IF NOT EXISTS "Journal_isActive_idx" ON "Journal"("isActive");

CREATE INDEX IF NOT EXISTS "JournalEntry_journalId_idx" ON "JournalEntry"("journalId");
CREATE INDEX IF NOT EXISTS "JournalEntry_date_idx" ON "JournalEntry"("date");
CREATE INDEX IF NOT EXISTS "JournalEntry_debitAccountId_idx" ON "JournalEntry"("debitAccountId");
CREATE INDEX IF NOT EXISTS "JournalEntry_creditAccountId_idx" ON "JournalEntry"("creditAccountId");
CREATE INDEX IF NOT EXISTS "JournalEntry_isPosted_idx" ON "JournalEntry"("isPosted");

CREATE INDEX IF NOT EXISTS "FiscalYear_isClosed_idx" ON "FiscalYear"("isClosed");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_fiscalYearId_idx" ON "FiscalPeriod"("fiscalYearId");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_isClosed_idx" ON "FiscalPeriod"("isClosed");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_startDate_endDate_idx" ON "FiscalPeriod"("startDate", "endDate");
`;

// Function to execute SQL
async function executeSql(sql) {
  try {
    console.log('Executing SQL to create accounting tables...');
    
    // Get database connection string from environment
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable is not set');
    }
    
    // Parse connection string to get credentials
    const match = databaseUrl.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }
    
    const [, user, password, host, port, database] = match;
    
    // Create a temporary SQL file
    const tempSqlFile = path.join(__dirname, 'temp_accounting.sql');
    fs.writeFileSync(tempSqlFile, sql);
    
    // Execute the SQL file using psql
    console.log('Executing SQL...');
    execSync(`psql -U ${user} -h ${host} -p ${port} -d ${database} -f ${tempSqlFile}`, {
      env: { ...process.env, PGPASSWORD: password },
    });
    
    // Remove the temporary file
    fs.unlinkSync(tempSqlFile);
    
    console.log('SQL executed successfully');
  } catch (error) {
    console.error('Error executing SQL:', error);
    throw error;
  }
}

// Function to initialize accounting module
async function initializeAccountingModule() {
  try {
    console.log('Initializing accounting module...');
    
    // Create default accounts
    for (const account of defaultAccounts) {
      // Check if account already exists
      const existingAccount = await prisma.account.findFirst({
        where: {
          OR: [
            { code: account.code },
            { name: account.name, type: account.type },
          ],
        },
      });
      
      if (!existingAccount) {
        // Create the account
        await prisma.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            isActive: true,
          },
        });
        console.log(`Created account: ${account.name} (${account.code})`);
      } else {
        console.log(`Account already exists: ${account.name} (${account.code})`);
      }
    }
    
    // Create default journals
    for (const journal of defaultJournals) {
      // Check if journal already exists
      const existingJournal = await prisma.journal.findFirst({
        where: {
          OR: [
            { code: journal.code },
            { name: journal.name, type: journal.type },
          ],
        },
      });
      
      if (!existingJournal) {
        // Create the journal
        await prisma.journal.create({
          data: {
            code: journal.code,
            name: journal.name,
            type: journal.type,
            paymentMethod: journal.paymentMethod,
            isActive: true,
          },
        });
        console.log(`Created journal: ${journal.name} (${journal.code})`);
      } else {
        console.log(`Journal already exists: ${journal.name} (${journal.code})`);
      }
    }
    
    // Create current fiscal year and period if they don't exist
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1); // January 1st of current year
    const endDate = new Date(currentYear, 11, 31); // December 31st of current year
    
    const existingFiscalYear = await prisma.fiscalYear.findFirst({
      where: {
        name: `FY${currentYear}`,
      },
    });
    
    let fiscalYearId;
    
    if (!existingFiscalYear) {
      const fiscalYear = await prisma.fiscalYear.create({
        data: {
          name: `FY${currentYear}`,
          startDate,
          endDate,
          isClosed: false,
        },
      });
      fiscalYearId = fiscalYear.id;
      console.log(`Created fiscal year: FY${currentYear}`);
    } else {
      fiscalYearId = existingFiscalYear.id;
      console.log(`Fiscal year already exists: FY${currentYear}`);
    }
    
    // Create 12 monthly periods for the fiscal year
    for (let month = 0; month < 12; month++) {
      const periodStartDate = new Date(currentYear, month, 1);
      const periodEndDate = new Date(currentYear, month + 1, 0); // Last day of the month
      
      const existingPeriod = await prisma.fiscalPeriod.findFirst({
        where: {
          fiscalYearId,
          name: `${currentYear}-${month + 1}`,
        },
      });
      
      if (!existingPeriod) {
        await prisma.fiscalPeriod.create({
          data: {
            fiscalYearId,
            name: `${currentYear}-${month + 1}`,
            startDate: periodStartDate,
            endDate: periodEndDate,
            isClosed: month < new Date().getMonth(), // Close past months
          },
        });
        console.log(`Created fiscal period: ${currentYear}-${month + 1}`);
      } else {
        console.log(`Fiscal period already exists: ${currentYear}-${month + 1}`);
      }
    }
    
    console.log('Accounting module initialized successfully');
  } catch (error) {
    console.error('Error initializing accounting module:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Starting accounting module setup...');
    
    // Execute SQL to create accounting tables
    await executeSql(accountingTablesSql);
    
    // Initialize accounting module with default data
    await initializeAccountingModule();
    
    console.log('Accounting module setup completed successfully');
  } catch (error) {
    console.error('Error during setup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
