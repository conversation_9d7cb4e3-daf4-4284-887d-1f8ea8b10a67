// <PERSON>ript to add finance permissions to the database
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Adding finance permissions...');
    
    // Create view_finance permission if it doesn't exist
    const viewFinancePermission = await prisma.permission.findFirst({
      where: {
        name: 'view_finance',
      },
    });
    
    if (!viewFinancePermission) {
      await prisma.permission.create({
        data: {
          name: 'view_finance',
          description: 'Permission to view finance data including payment methods and transactions',
        },
      });
      console.log('Created view_finance permission');
    } else {
      console.log('view_finance permission already exists');
    }
    
    // Create manage_finance permission if it doesn't exist
    const manageFinancePermission = await prisma.permission.findFirst({
      where: {
        name: 'manage_finance',
      },
    });
    
    if (!manageFinancePermission) {
      await prisma.permission.create({
        data: {
          name: 'manage_finance',
          description: 'Permission to manage finance data including payment methods and transactions',
        },
      });
      console.log('Created manage_finance permission');
    } else {
      console.log('manage_finance permission already exists');
    }
    
    // Assign finance permissions to admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        role: 'ADMIN',
      },
    });
    
    for (const user of adminUsers) {
      // Get the permissions
      const viewFinancePerm = await prisma.permission.findFirst({
        where: { name: 'view_finance' },
      });
      
      const manageFinancePerm = await prisma.permission.findFirst({
        where: { name: 'manage_finance' },
      });
      
      if (viewFinancePerm) {
        // Check if user already has the permission
        const hasViewPermission = await prisma.user.findFirst({
          where: {
            id: user.id,
            permissions: {
              some: {
                id: viewFinancePerm.id,
              },
            },
          },
        });
        
        if (!hasViewPermission) {
          // Add view_finance permission to user
          await prisma.user.update({
            where: { id: user.id },
            data: {
              permissions: {
                connect: { id: viewFinancePerm.id },
              },
            },
          });
          console.log(`Added view_finance permission to user ${user.email}`);
        } else {
          console.log(`User ${user.email} already has view_finance permission`);
        }
      }
      
      if (manageFinancePerm) {
        // Check if user already has the permission
        const hasManagePermission = await prisma.user.findFirst({
          where: {
            id: user.id,
            permissions: {
              some: {
                id: manageFinancePerm.id,
              },
            },
          },
        });
        
        if (!hasManagePermission) {
          // Add manage_finance permission to user
          await prisma.user.update({
            where: { id: user.id },
            data: {
              permissions: {
                connect: { id: manageFinancePerm.id },
              },
            },
          });
          console.log(`Added manage_finance permission to user ${user.email}`);
        } else {
          console.log(`User ${user.email} already has manage_finance permission`);
        }
      }
    }
    
    console.log('Finance permissions added successfully');
  } catch (error) {
    console.error('Error adding finance permissions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
main();
