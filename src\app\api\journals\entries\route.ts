import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/journals/entries - Get journal entries
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view journal entries
    const hasViewPermission = await hasPermission("view_accounts");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view journal entries" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const journalId = url.searchParams.get("journalId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const referenceType = url.searchParams.get("referenceType");
    const reference = url.searchParams.get("reference");
    const contactId = url.searchParams.get("contactId");
    const type = url.searchParams.get("type");
    const limitStr = url.searchParams.get("limit");
    const limit = limitStr ? parseInt(limitStr) : undefined;

    // Build filter
    const filter: any = {};

    if (journalId) {
      filter.journalId = journalId;
    }

    if (referenceType) {
      filter.referenceType = referenceType;
    }

    if (reference) {
      filter.reference = reference;
    }

    if (contactId) {
      filter.contactId = contactId;
    }

    if (type) {
      filter.type = type;
    }

    // Date range filter
    if (startDate || endDate) {
      filter.date = {};

      if (startDate) {
        filter.date.gte = new Date(startDate);
      }

      if (endDate) {
        filter.date.lte = new Date(endDate);
      }
    }

    // Get journal entries from database
    const entries = await db.journalEntry.findMany({
      where: filter,
      include: {
        journal: true,
        contact: true,
      },
      orderBy: {
        date: "desc",
      },
      ...(limit ? { take: limit } : {}),
    });

    return NextResponse.json(entries);
  } catch (error) {
    console.error("Error fetching journal entries:", error);
    return NextResponse.json(
      { error: "Failed to fetch journal entries" },
      { status: 500 }
    );
  }
}

// POST /api/journals/entries - Create a new journal entry
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add journal entries
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add journal entries" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.journalId || !data.description || !data.amount || !data.type) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the journal
    const journal = await db.journal.findUnique({
      where: {
        id: data.journalId,
      },
      include: {
        entries: {
          orderBy: {
            entryNumber: "desc",
          },
          take: 1,
        },
      },
    });

    if (!journal) {
      return NextResponse.json(
        { error: "Journal not found" },
        { status: 404 }
      );
    }

    // Generate entry number
    let entryNumber = "1";
    if (journal.entries.length > 0) {
      const lastEntryNumber = parseInt(journal.entries[0].entryNumber);
      entryNumber = (lastEntryNumber + 1).toString();
    }

    // Create the journal entry
    const entry = await db.journalEntry.create({
      data: {
        journalId: data.journalId,
        entryNumber,
        description: data.description,
        amount: data.amount,
        type: data.type,
        contactId: data.contactId || null,
        reference: data.reference || null,
        referenceType: data.referenceType || null,
        date: data.date ? new Date(data.date) : new Date(),
      },
    });

    return NextResponse.json(entry);
  } catch (error) {
    console.error("Error creating journal entry:", error);
    return NextResponse.json(
      { error: "Failed to create journal entry" },
      { status: 500 }
    );
  }
}
