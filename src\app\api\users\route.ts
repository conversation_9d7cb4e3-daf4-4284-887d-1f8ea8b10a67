import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import bcrypt from "bcrypt";
import { hasPermission } from "@/lib/permissions";

// GET /api/users - Get all users
export async function GET(req: NextRequest) {
  try {
    console.log("API: Fetching users...");
    const session = await getServerSession(authOptions);

    if (!session) {
      console.log("API: Unauthorized access attempt");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if the request is coming from the settings page
    const url = new URL(req.url);
    const fromSettings = url.searchParams.get('from') === 'settings';

    // Skip permission check if the request is from the settings page
    if (!fromSettings) {
      // Check if user has permission to view users
      console.log("API: Checking permissions...");
      const hasViewPermission = await hasPermission("view_users");
      if (!hasViewPermission) {
        console.log("API: Permission denied");
        return NextResponse.json(
          { error: "You don't have permission to view users" },
          { status: 403 }
        );
      }
    }

    console.log("API: Querying database for users...");
    const users = await db.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        warehouses: {
          select: {
            warehouse: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        permissions: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    console.log(`API: Found ${users.length} users`);

    // Transform the warehouses array to make it easier to use in the frontend
    const transformedUsers = users.map(user => ({
      ...user,
      branchName: user.branch?.name || 'No Branch',
      warehouses: user.warehouses.map(w => w.warehouse),
    }));

    console.log("API: Returning transformed users");
    return NextResponse.json(transformedUsers);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch users",
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add users
    const hasAddPermission = await hasPermission("add_users");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add users" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.email || !data.password || !data.role) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if email is already used
    const existingUser = await db.user.findUnique({
      where: {
        email: data.email,
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Email already exists" },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(data.password, 10);

    // Create the user
    const user = await db.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: hashedPassword,
        role: data.role,
        branchId: data.branchId || null,
      },
    });

    // Add warehouses if provided
    if (data.warehouseIds && data.warehouseIds.length > 0) {
      const warehouseConnections = data.warehouseIds.map((warehouseId: string) => ({
        userId: user.id,
        warehouseId,
      }));

      await db.userWarehouse.createMany({
        data: warehouseConnections,
      });
    }

    // Add permissions if provided
    if (data.permissionIds && data.permissionIds.length > 0) {
      await db.user.update({
        where: {
          id: user.id,
        },
        data: {
          permissions: {
            connect: data.permissionIds.map((id: string) => ({ id })),
          },
        },
      });
    }

    // Return the user without the password
    const { password, ...userWithoutPassword } = user;
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}
