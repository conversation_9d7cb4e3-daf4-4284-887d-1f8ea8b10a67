import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import { 
  initializeAccountingModule, 
  ensureAccountsExist, 
  ensureJournalsExist, 
  linkPaymentMethodsToAccounts 
} from "@/lib/accounting-integration";

// POST /api/accounting/sync - Synchronize accounting module with other modules
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to synchronize accounting module" },
        { status: 403 }
      );
    }

    // Initialize accounting module
    await initializeAccountingModule();

    // Get accounting settings
    const settings = await db.accountingSettings.findFirst();

    // Create default settings if they don't exist
    if (!settings) {
      await db.accountingSettings.create({
        data: {
          defaultCurrency: "EGP",
          defaultTaxRate: 14,
          fiscalYearStart: "01-01",
          fiscalYearEnd: "12-31",
          autoCreateAccounts: true,
          requireApproval: false,
          allowNegativeInventory: false,
          integrateWithSales: true,
          integrateWithPurchases: true,
          integrateWithInventory: true,
          integrateWithContacts: true,
        },
      });
    }

    // Get current fiscal year
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1); // January 1st
    const endDate = new Date(currentYear, 11, 31); // December 31st

    // Check if fiscal year exists
    let fiscalYear = await db.fiscalYear.findFirst({
      where: {
        startDate: {
          gte: new Date(currentYear, 0, 1),
          lt: new Date(currentYear, 0, 2),
        },
        endDate: {
          gte: new Date(currentYear, 11, 31),
          lt: new Date(currentYear, 11, 32),
        },
      },
    });

    // Create fiscal year if it doesn't exist
    if (!fiscalYear) {
      fiscalYear = await db.fiscalYear.create({
        data: {
          name: `Fiscal Year ${currentYear}`,
          startDate,
          endDate,
          isClosed: false,
        },
      });

      // Create fiscal periods (quarters)
      for (let i = 0; i < 4; i++) {
        const periodStartDate = new Date(currentYear, i * 3, 1);
        const periodEndDate = new Date(currentYear, (i + 1) * 3, 0);

        await db.fiscalPeriod.create({
          data: {
            fiscalYearId: fiscalYear.id,
            name: `Q${i + 1} ${currentYear}`,
            startDate: periodStartDate,
            endDate: periodEndDate,
            isClosed: false,
          },
        });
      }
    }

    // Synchronize payment methods with accounts
    await linkPaymentMethodsToAccounts();

    // Count entities
    const accountsCount = await db.account.count();
    const journalsCount = await db.journal.count();
    const paymentMethodsCount = await db.paymentMethodSettings.count();
    const fiscalYearsCount = await db.fiscalYear.count();
    const fiscalPeriodsCount = await db.fiscalPeriod.count();

    return NextResponse.json({
      success: true,
      message: "Accounting module synchronized successfully",
      counts: {
        accounts: accountsCount,
        journals: journalsCount,
        paymentMethods: paymentMethodsCount,
        fiscalYears: fiscalYearsCount,
        fiscalPeriods: fiscalPeriodsCount,
      },
    });
  } catch (error) {
    console.error("Error synchronizing accounting module:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to synchronize accounting module" },
      { status: 500 }
    );
  }
}

// GET /api/accounting/sync - Get accounting module synchronization status
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounting
    const hasViewPermission = await hasPermission("view_accounting");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view accounting module status" },
        { status: 403 }
      );
    }

    // Count entities
    const accountsCount = await db.account.count();
    const journalsCount = await db.journal.count();
    const paymentMethodsCount = await db.paymentMethodSettings.count();
    const fiscalYearsCount = await db.fiscalYear.count();
    const fiscalPeriodsCount = await db.fiscalPeriod.count();
    const journalEntriesCount = await db.journalEntry.count();
    const paymentVouchersCount = await db.paymentVoucher.count();
    const receiptVouchersCount = await db.receiptVoucher.count();

    // Check if payment methods are linked to accounts
    const paymentMethodsWithAccounts = await db.paymentMethodSettings.count({
      where: {
        accountId: {
          not: null,
        },
      },
    });

    // Check if payment methods are linked to journals
    const paymentMethodsWithJournals = await db.paymentMethodSettings.count({
      where: {
        journalId: {
          not: null,
        },
      },
    });

    // Get accounting settings
    const settings = await db.accountingSettings.findFirst();

    return NextResponse.json({
      success: true,
      isInitialized: accountsCount > 0 && journalsCount > 0 && fiscalYearsCount > 0,
      isFullySynchronized: 
        accountsCount > 0 && 
        journalsCount > 0 && 
        fiscalYearsCount > 0 && 
        paymentMethodsWithAccounts === paymentMethodsCount &&
        paymentMethodsWithJournals === paymentMethodsCount,
      counts: {
        accounts: accountsCount,
        journals: journalsCount,
        paymentMethods: paymentMethodsCount,
        paymentMethodsWithAccounts,
        paymentMethodsWithJournals,
        fiscalYears: fiscalYearsCount,
        fiscalPeriods: fiscalPeriodsCount,
        journalEntries: journalEntriesCount,
        paymentVouchers: paymentVouchersCount,
        receiptVouchers: receiptVouchersCount,
      },
      settings,
    });
  } catch (error) {
    console.error("Error getting accounting module status:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to get accounting module status" },
      { status: 500 }
    );
  }
}
