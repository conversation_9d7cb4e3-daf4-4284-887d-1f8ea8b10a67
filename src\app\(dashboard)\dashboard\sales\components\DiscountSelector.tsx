"use client";

import { useState, useEffect } from "react";
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Percent, DollarSign, Tag, ShoppingBag, Users, Search, AlertCircle } from "lucide-react";
import { format } from "date-fns";

interface DiscountSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  availableDiscounts: any[];
  selectedDiscounts: any[];
  onSelectDiscounts: (discounts: any[]) => void;
  scope: "INVOICE" | "ITEM";
  itemId?: string;
  subtotal: number;
  contactId?: string;
}

export default function DiscountSelector({
  isOpen,
  onClose,
  availableDiscounts,
  selectedDiscounts,
  onSelectDiscounts,
  scope,
  itemId,
  subtotal,
  contactId
}: DiscountSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredDiscounts, setFilteredDiscounts] = useState<any[]>([]);
  const [selectedTab, setSelectedTab] = useState<string>("all");
  const [tempSelectedDiscounts, setTempSelectedDiscounts] = useState<any[]>([]);
  
  // Initialize temp selected discounts from props
  useEffect(() => {
    setTempSelectedDiscounts([...selectedDiscounts]);
  }, [selectedDiscounts, isOpen]);
  
  // Filter discounts based on search term, tab, and scope
  useEffect(() => {
    let filtered = [...availableDiscounts];
    
    // Filter by scope
    filtered = filtered.filter(discount => discount.scope === scope);
    
    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(discount => 
        discount.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (discount.description && discount.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    // Filter by tab
    if (selectedTab === "percentage") {
      filtered = filtered.filter(discount => discount.type === "PERCENTAGE");
    } else if (selectedTab === "fixed") {
      filtered = filtered.filter(discount => discount.type === "FIXED_AMOUNT");
    }
    
    // Filter by item if scope is ITEM
    if (scope === "ITEM" && itemId) {
      filtered = filtered.filter(discount => 
        !discount.productId || discount.productId === itemId
      );
    }
    
    // Filter by customer if contactId is provided
    if (contactId) {
      filtered = filtered.filter(discount => {
        // Include all invoice discounts
        if (discount.scope === "INVOICE") return true;
        
        // For customer discounts, check if this customer is eligible
        if (discount.scope === "CUSTOMER") {
          // Check if this discount applies to this customer
          const isForThisCustomer = discount.customerDiscounts.some(
            (cd: any) => cd.contactId === contactId
          );
          return isForThisCustomer;
        }
        
        return true;
      });
    }
    
    // Filter by minimum amount
    filtered = filtered.filter(discount => 
      !discount.minAmount || subtotal >= discount.minAmount
    );
    
    // Filter by date range
    const now = new Date();
    filtered = filtered.filter(discount => 
      (!discount.startDate || new Date(discount.startDate) <= now) &&
      (!discount.endDate || new Date(discount.endDate) >= now)
    );
    
    setFilteredDiscounts(filtered);
  }, [availableDiscounts, searchTerm, selectedTab, scope, itemId, contactId, subtotal]);
  
  // Toggle discount selection
  const toggleDiscount = (discount: any) => {
    const isSelected = tempSelectedDiscounts.some(d => d.id === discount.id);
    
    if (isSelected) {
      setTempSelectedDiscounts(tempSelectedDiscounts.filter(d => d.id !== discount.id));
    } else {
      setTempSelectedDiscounts([...tempSelectedDiscounts, discount]);
    }
  };
  
  // Apply selected discounts
  const applyDiscounts = () => {
    onSelectDiscounts(tempSelectedDiscounts);
    onClose();
  };
  
  // Format discount value
  const formatDiscountValue = (discount: any) => {
    if (discount.type === "PERCENTAGE") {
      return `${discount.value}%`;
    } else {
      return `${discount.value.toFixed(2)} EGP`;
    }
  };
  
  // Calculate discount amount
  const calculateDiscountAmount = (discount: any) => {
    if (discount.type === "PERCENTAGE") {
      return subtotal * (discount.value / 100);
    } else {
      return Math.min(discount.value, subtotal);
    }
  };
  
  // Get scope icon
  const getScopeIcon = (discountScope: string) => {
    switch (discountScope) {
      case "ITEM":
        return <Tag className="h-4 w-4" />;
      case "INVOICE":
        return <ShoppingBag className="h-4 w-4" />;
      case "CUSTOMER":
        return <Users className="h-4 w-4" />;
      default:
        return <Tag className="h-4 w-4" />;
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>
            {scope === "INVOICE" ? "Select Invoice Discounts" : "Select Item Discounts"}
          </DialogTitle>
          <DialogDescription>
            Choose discounts to apply to this {scope === "INVOICE" ? "invoice" : "item"}.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex items-center gap-2 mb-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <Input
              placeholder="Search discounts..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="percentage">Percentage</TabsTrigger>
            <TabsTrigger value="fixed">Fixed Amount</TabsTrigger>
          </TabsList>
          
          <ScrollArea className="h-[300px] pr-4">
            {filteredDiscounts.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-center">
                <AlertCircle className="h-8 w-8 text-gray-400 mb-2" />
                <h3 className="text-lg font-medium">No discounts available</h3>
                <p className="text-sm text-gray-500 mt-1">
                  {searchTerm 
                    ? "Try adjusting your search" 
                    : `No ${scope.toLowerCase()} discounts are available`}
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredDiscounts.map((discount) => {
                  const isSelected = tempSelectedDiscounts.some(d => d.id === discount.id);
                  const discountAmount = calculateDiscountAmount(discount);
                  
                  return (
                    <div 
                      key={discount.id}
                      className={`border rounded-md p-3 cursor-pointer transition-all ${
                        isSelected 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => toggleDiscount(discount)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          <Checkbox checked={isSelected} />
                          <div>
                            <h4 className="font-medium">{discount.name}</h4>
                            <div className="flex items-center text-sm text-gray-500 mt-1">
                              {getScopeIcon(discount.scope)}
                              <span className="ml-1">
                                {discount.scope === 'ITEM' ? 'Item Discount' : 
                                 discount.scope === 'INVOICE' ? 'Invoice Discount' : 'Customer Discount'}
                              </span>
                            </div>
                            {discount.description && (
                              <p className="text-sm text-gray-500 mt-1">{discount.description}</p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className={`${
                            discount.type === 'PERCENTAGE' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                          } flex items-center`}>
                            {discount.type === 'PERCENTAGE' 
                              ? <Percent className="h-3 w-3 mr-1" /> 
                              : <DollarSign className="h-3 w-3 mr-1" />}
                            {formatDiscountValue(discount)}
                          </Badge>
                          <div className="text-sm font-medium mt-1">
                            -{discountAmount.toFixed(2)} EGP
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mt-2">
                        {discount.minAmount && (
                          <Badge variant="outline" className="text-xs">
                            Min: {discount.minAmount.toFixed(2)} EGP
                          </Badge>
                        )}
                        {discount.maxAmount && (
                          <Badge variant="outline" className="text-xs">
                            Max: {discount.maxAmount.toFixed(2)} EGP
                          </Badge>
                        )}
                        {discount.startDate && (
                          <Badge variant="outline" className="text-xs">
                            From: {format(new Date(discount.startDate), 'MMM d, yyyy')}
                          </Badge>
                        )}
                        {discount.endDate && (
                          <Badge variant="outline" className="text-xs">
                            Until: {format(new Date(discount.endDate), 'MMM d, yyyy')}
                          </Badge>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </ScrollArea>
        </Tabs>
        
        <DialogFooter className="mt-4">
          <div className="flex justify-between items-center w-full">
            <div className="text-sm">
              {tempSelectedDiscounts.length} discount(s) selected
            </div>
            <div className="space-x-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={applyDiscounts}>
                Apply Discounts
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
