// Consolidated Schema with Accounting Module

generator client {
  provider = "prisma-client-js"
}

generator ts_node {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and Authentication Models
model User {
  id          String          @id @default(uuid())
  email       String          @unique
  name        String
  password    String
  role        Role            @default(EMPLOYEE)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  branchId    String?
  isActive    Boolean         @default(true)
  purchases   Purchase[]
  sales       Sale[]
  creditNotes CreditNote[]
  branch      Branch?         @relation(fields: [branchId], references: [id])
  warehouses  UserWarehouse[]
  permissions Permission[]    @relation("PermissionToUser")
}

model UserWarehouse {
  id          String    @id @default(uuid())
  userId      String
  warehouseId String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)

  @@unique([userId, warehouseId])
}

model Permission {
  id          String   @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]   @relation("PermissionToUser")
}

// Branch and Warehouse Models
model Branch {
  id          String       @id @default(uuid())
  name        String
  address     String
  phone       String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  code        String       @unique
  isActive    Boolean      @default(true)
  purchases   Purchase[]
  sales       Sale[]
  creditNotes CreditNote[]
  users       User[]
  warehouses  Warehouse[]
  accounts    Account[]    // Accounts associated with this branch
  journals    Journal[]    // Journals associated with this branch
}

model Warehouse {
  id        String          @id @default(uuid())
  name      String
  branchId  String
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  isActive  Boolean         @default(true)
  inventory Inventory[]
  users     UserWarehouse[]
  branch    Branch          @relation(fields: [branchId], references: [id])
}

// Product and Inventory Models
model Product {
  id             String           @id @default(uuid())
  name           String
  description    String?
  basePrice      Float
  categoryId     String
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  componentType  String?
  isComponent    Boolean          @default(false)
  isCustomizable Boolean          @default(false)
  costPrice      Float            @default(0)
  inventory      Inventory[]
  category       Category         @relation(fields: [categoryId], references: [id])
  purchaseItems  PurchaseItem[]
  saleItems      SaleItem[]
  creditNoteItems CreditNoteItem[]
  specifications Specification[]
}

model Category {
  id          String    @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Specification {
  id        String   @id @default(uuid())
  name      String
  value     String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id])
}

model Inventory {
  id          String    @id @default(uuid())
  productId   String
  warehouseId String
  quantity    Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  costPrice   Float     @default(0)
  product     Product   @relation(fields: [productId], references: [id])
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])
}

// Sales Models
model Sale {
  id             String        @id @default(uuid())
  userId         String
  branchId       String
  date           DateTime      @default(now())
  status         SaleStatus    @default(PENDING)
  totalAmount    Float
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  applyTax       Boolean       @default(false)
  currency       String        @default("EGP")
  discountAmount Float         @default(0)
  notes          String?
  subtotalAmount Float
  taxAmount      Float         @default(0)
  taxRate        Float         @default(0)
  invoiceNumber  String        @unique
  paymentMethod  PaymentMethod @default(CASH)
  paymentStatus  PaymentStatus @default(UNPAID)
  contactId      String
  branch         Branch        @relation(fields: [branchId], references: [id])
  contact        Contact       @relation(fields: [contactId], references: [id])
  user           User          @relation(fields: [userId], references: [id])
  items          SaleItem[]
  payments       SalePayment[]
}

model SaleItem {
  id             String              @id @default(uuid())
  saleId         String
  productId      String
  quantity       Int
  unitPrice      Float
  totalPrice     Float
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  isCustomized   Boolean             @default(false)
  specifications String?
  product        Product             @relation(fields: [productId], references: [id])
  sale           Sale                @relation(fields: [saleId], references: [id])
  components     SaleItemComponent[]
}

model SaleItemComponent {
  id            String   @id @default(uuid())
  saleItemId    String
  componentId   String
  componentName String
  componentType String
  quantity      Int
  totalQuantity Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  saleItem      SaleItem @relation(fields: [saleItemId], references: [id])
}

model SalePayment {
  id        String        @id @default(uuid())
  saleId    String
  method    PaymentMethod
  amount    Float
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  sale      Sale          @relation(fields: [saleId], references: [id])
}

// Purchase Models
model Purchase {
  id             String          @id @default(uuid())
  userId         String
  branchId       String
  date           DateTime        @default(now())
  status         PurchaseStatus  @default(PENDING)
  totalAmount    Float
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  contactId      String
  currency       String          @default("EGP")
  discountAmount Float           @default(0)
  invoiceNumber  String          @unique
  notes          String?
  paymentMethod  PaymentMethod   @default(CASH)
  paymentStatus  PaymentStatus   @default(UNPAID)
  subtotalAmount Float
  taxAmount      Float           @default(0)
  branch         Branch          @relation(fields: [branchId], references: [id])
  contact        Contact         @relation(fields: [contactId], references: [id])
  user           User            @relation(fields: [userId], references: [id])
  items          PurchaseItem[]
  payments       PurchasePayment[]
}

model PurchaseItem {
  id         String   @id @default(uuid())
  purchaseId String
  productId  String
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  product    Product  @relation(fields: [productId], references: [id])
  purchase   Purchase @relation(fields: [purchaseId], references: [id])
}

model PurchasePayment {
  id        String        @id @default(uuid())
  purchaseId String
  method    PaymentMethod
  amount    Float
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  purchase  Purchase      @relation(fields: [purchaseId], references: [id])
}

// Contact Model
model Contact {
  id                 String        @id @default(uuid())
  name               String
  phone              String        @unique
  address            String?
  isCustomer         Boolean       @default(false)
  isSupplier         Boolean       @default(false)
  balance            Float         @default(0)
  creditLimit        Float         @default(0)
  creditPeriod       Int           @default(30) // Credit period in days
  lastReminderDate   DateTime?
  isActive           Boolean       @default(true)
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  openingBalance     Float         @default(0)
  openingBalanceDate DateTime      @default(now())
  purchases          Purchase[]
  sales              Sale[]
  creditNotes        CreditNote[]
  journalEntries     JournalEntry[]
}

// Credit Note Models
model CreditNote {
  id                  String           @id @default(uuid())
  userId              String
  contactId           String
  branchId            String
  originalInvoiceId   String?
  originalInvoiceNumber String?
  creditNoteNumber    String           @unique
  date                DateTime         @default(now())
  status              CreditNoteStatus @default(PENDING)
  paymentStatus       PaymentStatus    @default(UNPAID)
  paymentMethod       PaymentMethod?
  totalAmount         Float
  subtotalAmount      Float
  taxAmount           Float            @default(0)
  taxRate             Float            @default(0)
  discountAmount      Float            @default(0)
  applyTax            Boolean          @default(false)
  currency            String           @default("EGP")
  notes               String?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  user                User             @relation(fields: [userId], references: [id])
  contact             Contact          @relation(fields: [contactId], references: [id])
  branch              Branch           @relation(fields: [branchId], references: [id])
  items               CreditNoteItem[]
  payments            CreditNotePayment[]
}

model CreditNoteItem {
  id                  String     @id @default(uuid())
  creditNoteId        String
  productId           String
  quantity            Int
  unitPrice           Float
  totalPrice          Float
  reason              String
  originalItemId      String?
  createdAt           DateTime   @default(now())
  updatedAt           DateTime   @updatedAt
  creditNote          CreditNote @relation(fields: [creditNoteId], references: [id])
  product             Product    @relation(fields: [productId], references: [id])
}

model CreditNotePayment {
  id            String        @id @default(uuid())
  creditNoteId  String
  method        PaymentMethod
  amount        Float
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  creditNote    CreditNote    @relation(fields: [creditNoteId], references: [id])
}

// Accounting Module Models
// Account Model
model Account {
  id                String          @id @default(uuid())
  code              String          @unique // Account code (e.g., 1000, 2000)
  name              String
  type              AccountType
  parentId          String?         // Parent account ID for hierarchical structure
  parent            Account?        @relation("AccountHierarchy", fields: [parentId], references: [id])
  children          Account[]       @relation("AccountHierarchy")
  balance           Float           @default(0)
  isActive          Boolean         @default(true)
  branchId          String?         // Optional branch association
  branch            Branch?         @relation(fields: [branchId], references: [id])
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  debitEntries      JournalEntry[]  @relation("DebitAccount")
  creditEntries     JournalEntry[]  @relation("CreditAccount")

  @@index([code])
  @@index([type])
  @@index([parentId])
  @@index([isActive])
}

// Journal Model
model Journal {
  id                String          @id @default(uuid())
  code              String          @unique // Journal code (e.g., CASH-001)
  name              String
  type              JournalType
  paymentMethod     PaymentMethod?  // Associated payment method
  branchId          String?         // Optional branch association
  branch            Branch?         @relation(fields: [branchId], references: [id])
  isActive          Boolean         @default(true)
  entries           JournalEntry[]
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@index([code])
  @@index([type])
  @@index([isActive])
}

// Journal Entry Model
model JournalEntry {
  id                String          @id @default(uuid())
  journal           Journal         @relation(fields: [journalId], references: [id])
  journalId         String
  entryNumber       String          // Sequential number for each journal
  date              DateTime        @default(now())
  description       String
  debitAccountId    String          // Account to debit
  debitAccount      Account         @relation("DebitAccount", fields: [debitAccountId], references: [id])
  creditAccountId   String          // Account to credit
  creditAccount     Account         @relation("CreditAccount", fields: [creditAccountId], references: [id])
  amount            Float
  contactId         String?         // Optional contact association
  contact           Contact?        @relation(fields: [contactId], references: [id])
  reference         String?         // Reference to sale or purchase invoice
  referenceType     String?         // Type of reference: SALE, PURCHASE, PAYMENT, RECEIPT
  isPosted          Boolean         @default(false)
  fiscalPeriodId    String?
  fiscalPeriod      FiscalPeriod?   @relation(fields: [fiscalPeriodId], references: [id])
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@unique([entryNumber, journalId])
  @@index([journalId])
  @@index([date])
  @@index([debitAccountId])
  @@index([creditAccountId])
  @@index([isPosted])
  @@index([contactId])
}

// Fiscal Year Model
model FiscalYear {
  id                String          @id @default(uuid())
  name              String
  startDate         DateTime
  endDate           DateTime
  isClosed          Boolean         @default(false)
  periods           FiscalPeriod[]
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@index([isClosed])
}

// Fiscal Period Model
model FiscalPeriod {
  id                String          @id @default(uuid())
  fiscalYear        FiscalYear      @relation(fields: [fiscalYearId], references: [id])
  fiscalYearId      String
  name              String
  startDate         DateTime
  endDate           DateTime
  isClosed          Boolean         @default(false)
  journalEntries    JournalEntry[]
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@index([fiscalYearId])
  @@index([isClosed])
  @@index([startDate, endDate])
}

// Enums
enum Role {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum PaymentMethod {
  CASH
  VODAFONE_CASH
  BANK_TRANSFER
  CREDIT_CARD
  CUSTOMER_ACCOUNT
  SUPPLIER_ACCOUNT
}

enum PaymentStatus {
  PAID
  UNPAID
  PARTIALLY_PAID
}

enum SaleStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum PurchaseStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum CreditNoteStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

enum JournalType {
  CASH
  VODAFONE_CASH
  BANK_TRANSFER
  VISA
  CUSTOMER_ACCOUNT
  GENERAL
}
