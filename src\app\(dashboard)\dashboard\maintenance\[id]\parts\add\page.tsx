"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Loader2, ArrowLeft, Save, Search, Package } from "lucide-react";

interface Product {
  id: string;
  name: string;
  basePrice: number;
  costPrice: number;
}

interface Warehouse {
  id: string;
  name: string;
}

interface Inventory {
  id: string;
  productId: string;
  warehouseId: string;
  quantity: number;
  product: Product;
}

import React from "react";

export default function AddPartPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const id = unwrappedParams.id;

  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [inventory, setInventory] = useState<Inventory[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [showProductDropdown, setShowProductDropdown] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    partName: "",
    productId: "",
    quantity: 1,
    unitPrice: 0,
    isFromInventory: false,
    warehouseId: "",
  });

  // Fetch products and warehouses
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch products
        const productsResponse = await fetch("/api/products");
        if (!productsResponse.ok) {
          throw new Error("Failed to fetch products");
        }
        const productsData = await productsResponse.json();
        setProducts(productsData);

        // Fetch warehouses
        const warehousesResponse = await fetch("/api/warehouses");
        if (!warehousesResponse.ok) {
          throw new Error("Failed to fetch warehouses");
        }
        const warehousesData = await warehousesResponse.json();
        setWarehouses(warehousesData);

        // Fetch inventory if warehouses exist
        if (warehousesData.length > 0) {
          const inventoryResponse = await fetch(`/api/inventory?warehouseId=${warehousesData[0].id}`);
          if (!inventoryResponse.ok) {
            throw new Error("Failed to fetch inventory");
          }
          const inventoryData = await inventoryResponse.json();
          setInventory(inventoryData);

          // Set default warehouse
          setFormData(prev => ({
            ...prev,
            warehouseId: warehousesData[0].id,
          }));
        }
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter products based on search term
  useEffect(() => {
    if (searchTerm) {
      const filtered = products.filter(
        (product) => product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProducts(filtered);
      setShowProductDropdown(true);
    } else {
      setFilteredProducts([]);
      setShowProductDropdown(false);
    }
  }, [searchTerm, products]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox"
        ? (e.target as HTMLInputElement).checked
        : name === "quantity" || name === "unitPrice"
          ? parseFloat(value) || 0
          : value,
    }));
  };

  // Handle product selection
  const handleSelectProduct = (product: Product) => {
    setFormData((prev) => ({
      ...prev,
      productId: product.id,
      partName: product.name,
      unitPrice: product.basePrice,
    }));
    setSearchTerm(product.name);
    setShowProductDropdown(false);
  };

  // Handle warehouse change
  const handleWarehouseChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const warehouseId = e.target.value;
    setFormData((prev) => ({
      ...prev,
      warehouseId,
    }));

    // Fetch inventory for selected warehouse
    try {
      const response = await fetch(`/api/inventory?warehouseId=${warehouseId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch inventory");
      }
      const data = await response.json();
      setInventory(data);
    } catch (error: any) {
      setError(error.message);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.partName) {
        throw new Error("Part name is required");
      }

      if (formData.quantity <= 0) {
        throw new Error("Quantity must be greater than 0");
      }

      if (formData.isFromInventory && !formData.productId) {
        throw new Error("Please select a product from inventory");
      }

      // Check inventory if using product from inventory
      if (formData.isFromInventory && formData.productId) {
        const inventoryItem = inventory.find(item => item.productId === formData.productId);

        if (!inventoryItem) {
          throw new Error("Selected product is not in inventory");
        }

        if (inventoryItem.quantity < formData.quantity) {
          throw new Error(`Not enough quantity in inventory. Available: ${inventoryItem.quantity}`);
        }
      }

      // Prepare data for API
      const partData = {
        partName: formData.partName,
        quantity: formData.quantity,
        unitPrice: formData.unitPrice,
        isFromInventory: formData.isFromInventory,
        productId: formData.isFromInventory ? formData.productId : null,
        warehouseId: formData.isFromInventory ? formData.warehouseId : null,
      };

      // Submit to API
      const response = await fetch(`/api/maintenance/${id}/parts`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(partData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add part");
      }

      // Redirect back to maintenance service page
      router.push(`/dashboard/maintenance/${id}?tab=parts`);
    } catch (error: any) {
      setError(error.message);
      setIsSaving(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            href={`/dashboard/maintenance/${id}?tab=parts`}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Add Part</h1>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6 space-y-6">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="isFromInventory"
              name="isFromInventory"
              checked={formData.isFromInventory}
              onChange={(e) => setFormData({ ...formData, isFromInventory: e.target.checked })}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="isFromInventory" className="ml-2 block text-sm font-medium text-gray-700">
              Use part from inventory
            </label>
          </div>

          {formData.isFromInventory ? (
            <>
              <div>
                <label htmlFor="warehouseId" className="block text-sm font-medium text-gray-700 mb-1">
                  Warehouse
                </label>
                <select
                  id="warehouseId"
                  name="warehouseId"
                  value={formData.warehouseId}
                  onChange={handleWarehouseChange}
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                >
                  {warehouses.map((warehouse) => (
                    <option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="productSearch" className="block text-sm font-medium text-gray-700 mb-1">
                  Search Product <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="flex">
                    <div className="relative flex-grow">
                      <input
                        type="text"
                        id="productSearch"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder="Search product by name"
                        className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium pl-10"
                        autoComplete="off"
                      />
                      <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    </div>
                  </div>

                  {showProductDropdown && filteredProducts.length > 0 && (
                    <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md max-h-60 overflow-auto">
                      {filteredProducts.map((product) => {
                        const inventoryItem = inventory.find(item => item.productId === product.id);
                        const inStock = inventoryItem && inventoryItem.quantity > 0;

                        return (
                          <div
                            key={product.id}
                            className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${!inStock ? 'opacity-50' : ''}`}
                            onClick={() => inStock && handleSelectProduct(product)}
                          >
                            <div className="font-medium">{product.name}</div>
                            <div className="text-sm text-gray-500 flex items-center justify-between">
                              <span>Price: {product.basePrice.toFixed(2)}</span>
                              <span className={inStock ? 'text-green-600' : 'text-red-600'}>
                                {inStock ? `In stock: ${inventoryItem?.quantity}` : 'Out of stock'}
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div>
              <label htmlFor="partName" className="block text-sm font-medium text-gray-700 mb-1">
                Part Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="partName"
                name="partName"
                value={formData.partName}
                onChange={handleChange}
                placeholder="Enter part name"
                className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                required
              />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                Quantity <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                id="quantity"
                name="quantity"
                value={formData.quantity}
                onChange={handleChange}
                min="1"
                step="1"
                className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                required
              />
            </div>
            <div>
              <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700 mb-1">
                Unit Price <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                id="unitPrice"
                name="unitPrice"
                value={formData.unitPrice}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                required
              />
            </div>
          </div>

          <div>
            <p className="text-sm font-medium text-gray-700">Total Price</p>
            <p className="text-lg font-bold text-gray-900">
              {(formData.quantity * formData.unitPrice).toFixed(2)} ج.م
            </p>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 flex justify-end">
          <Link
            href={`/dashboard/maintenance/${id}?tab=parts`}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium mr-2"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSaving}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium flex items-center"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
