import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { format, addDays } from "date-fns";

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    const daysParam = url.searchParams.get("days") || "30";
    const days = parseInt(daysParam, 10);
    
    // Get current date in YYYY-MM-DD format
    const today = new Date();
    const todayFormatted = format(today, "MM-dd");
    
    // Get date in X days in YYYY-MM-DD format
    const futureDate = addDays(today, days);
    const futureDateFormatted = format(futureDate, "MM-dd");
    
    // Find customers with birthdays in the next X days
    // This is a simplified query that doesn't handle year wrap-around (Dec to Jan)
    const upcomingBirthdays = await prisma.$queryRaw`
      SELECT 
        c.id,
        c.name,
        c.phone,
        c.email,
        c.birthday,
        c."loyaltyPoints",
        c."loyaltyTier",
        c."isVIP",
        EXTRACT(DAY FROM (
          DATE(CONCAT(EXTRACT(YEAR FROM CURRENT_DATE)::text, '-', TO_CHAR(c.birthday, 'MM-DD'))) - 
          CURRENT_DATE
        )) as "daysUntilBirthday"
      FROM "Contact" c
      WHERE 
        c.birthday IS NOT NULL AND
        c."isCustomer" = true AND
        TO_CHAR(c.birthday, 'MM-DD') BETWEEN ${todayFormatted} AND ${futureDateFormatted}
      ORDER BY "daysUntilBirthday" ASC
    `;
    
    return NextResponse.json({
      upcomingBirthdays
    });
  } catch (error) {
    console.error("Error fetching upcoming birthdays:", error);
    return NextResponse.json(
      { error: "Failed to fetch upcoming birthdays" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const data = await req.json();
    
    if (!data.contactId) {
      return NextResponse.json(
        { error: "Contact ID is required" },
        { status: 400 }
      );
    }
    
    // Check if contact exists
    const contact = await prisma.contact.findUnique({
      where: { id: data.contactId }
    });
    
    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }
    
    // Check if birthday gift has already been sent this year
    const currentYear = new Date().getFullYear();
    const existingGift = await prisma.loyaltyBirthdayGift.findFirst({
      where: {
        contactId: data.contactId,
        year: currentYear
      }
    });
    
    if (existingGift) {
      return NextResponse.json(
        { error: "Birthday gift already sent for this year" },
        { status: 400 }
      );
    }
    
    // Determine gift based on loyalty tier
    let giftPoints = 0;
    let giftDescription = "";
    
    switch (contact.loyaltyTier) {
      case "PLATINUM":
        giftPoints = 1000;
        giftDescription = "Platinum tier birthday gift: 1000 points";
        break;
      case "GOLD":
        giftPoints = 500;
        giftDescription = "Gold tier birthday gift: 500 points";
        break;
      case "SILVER":
        giftPoints = 250;
        giftDescription = "Silver tier birthday gift: 250 points";
        break;
      default: // BRONZE
        giftPoints = 100;
        giftDescription = "Bronze tier birthday gift: 100 points";
    }
    
    // Add VIP bonus if applicable
    if (contact.isVIP) {
      giftPoints *= 2;
      giftDescription += " (VIP bonus: 2x points)";
    }
    
    // Create transaction in database
    const transaction = await prisma.$transaction(async (tx) => {
      // Add points to contact
      const updatedContact = await tx.contact.update({
        where: { id: data.contactId },
        data: {
          loyaltyPoints: { increment: giftPoints }
        }
      });
      
      // Record loyalty transaction
      const loyaltyTransaction = await tx.loyaltyTransaction.create({
        data: {
          loyaltyAccountId: contact.loyaltyAccountId,
          type: "BIRTHDAY_GIFT",
          points: giftPoints,
          description: giftDescription
        }
      });
      
      // Record birthday gift
      const birthdayGift = await tx.loyaltyBirthdayGift.create({
        data: {
          contactId: data.contactId,
          year: currentYear,
          points: giftPoints,
          description: giftDescription,
          loyaltyTransactionId: loyaltyTransaction.id
        }
      });
      
      return {
        updatedContact,
        loyaltyTransaction,
        birthdayGift
      };
    });
    
    return NextResponse.json({
      success: true,
      message: `Sent ${giftPoints} points as a birthday gift to ${contact.name}`,
      transaction
    });
  } catch (error) {
    console.error("Error sending birthday gift:", error);
    return NextResponse.json(
      { error: "Failed to send birthday gift" },
      { status: 500 }
    );
  }
}
