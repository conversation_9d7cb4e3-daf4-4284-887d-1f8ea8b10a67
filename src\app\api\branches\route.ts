import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/branches - Get all branches
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if the request is coming from the settings page
    const url = new URL(req.url);
    const fromSettings = url.searchParams.get('from') === 'settings';

    // Skip permission check if the request is from the settings page
    if (!fromSettings) {
      // Check if user has permission to view branches
      const hasViewPermission = await hasPermission("view_branches");
      if (!hasViewPermission) {
        return NextResponse.json(
          { error: "You don't have permission to view branches" },
          { status: 403 }
        );
      }
    }

    const branches = await db.branch.findMany({
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json(branches);
  } catch (error) {
    console.error("Error fetching branches:", error);
    return NextResponse.json(
      { error: "Failed to fetch branches" },
      { status: 500 }
    );
  }
}

// POST /api/branches - Create a new branch
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add branches
    const hasAddPermission = await hasPermission("add_branches");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add branches" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.code || !data.address || !data.phone) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if code is already used
    const existingBranch = await db.branch.findUnique({
      where: {
        code: data.code,
      },
    });

    if (existingBranch) {
      return NextResponse.json(
        { error: "Branch code already exists" },
        { status: 400 }
      );
    }

    // Create the branch
    const branch = await db.branch.create({
      data: {
        name: data.name,
        code: data.code,
        address: data.address,
        phone: data.phone,
      },
    });

    return NextResponse.json(branch);
  } catch (error) {
    console.error("Error creating branch:", error);
    return NextResponse.json(
      { error: "Failed to create branch" },
      { status: 500 }
    );
  }
}
