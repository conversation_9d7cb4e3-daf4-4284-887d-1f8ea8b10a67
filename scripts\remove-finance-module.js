const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to run shell commands
function runCommand(command) {
  try {
    console.log(`Running command: ${command}`);
    execSync(command, { stdio: 'inherit' });
  } catch (error) {
    console.error(`Error running command: ${command}`);
    console.error(error);
    process.exit(1);
  }
}

// Function to remove a directory and its contents
function removeDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`Directory does not exist: ${dirPath}`);
    return;
  }

  console.log(`Removing directory: ${dirPath}`);
  
  try {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`Directory removed: ${dirPath}`);
  } catch (error) {
    console.error(`Error removing directory ${dirPath}:`, error);
  }
}

// Main function
async function removeFinanceModule() {
  try {
    console.log('Starting removal of finance module...');
    
    // 1. Drop finance tables from the database
    console.log('Step 1: Dropping finance tables from the database...');
    
    // Run the SQL script to drop finance tables
    const sqlScriptPath = path.join(process.cwd(), 'scripts', 'drop-finance-tables.sql');
    if (fs.existsSync(sqlScriptPath)) {
      // This command assumes you have PostgreSQL client installed and configured
      runCommand(`psql -U openpg -d vero -f ${sqlScriptPath}`);
    } else {
      console.error('SQL script not found. Skipping database cleanup.');
    }
    
    // 2. Remove finance-related files from the codebase
    console.log('Step 2: Removing finance-related files from the codebase...');
    
    // Directories to remove
    const dirsToRemove = [
      'src/app/(dashboard)/dashboard/finance',
      'src/components/finance',
      'src/app/api/finance'
    ];
    
    // Remove directories
    dirsToRemove.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir);
      removeDirectory(fullPath);
    });
    
    // 3. Update the navigation to remove finance-related links
    console.log('Step 3: Updating navigation...');
    
    // The finance module has been successfully removed
    console.log('The finance module has been successfully removed.');
    
  } catch (error) {
    console.error('Error removing finance module:', error);
  }
}

// Run the main function
removeFinanceModule();
