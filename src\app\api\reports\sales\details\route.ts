import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// GET /api/reports/sales/details - Get detailed sales report data
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const reportType = url.searchParams.get("type") || "paidSales";
    const branchId = url.searchParams.get("branchId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");

    // Build filter object
    const filter: any = {};

    if (branchId && branchId !== "all") {
      filter.branchId = branchId;
    }

    // Date range filter
    if (startDate && endDate) {
      const startDateTime = new Date(startDate);
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999); // Set to end of day

      filter.date = {
        gte: startDateTime,
        lte: endDateTime,
      };
    } else if (startDate) {
      filter.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      filter.date = {
        lte: endDateTime,
      };
    } else {
      // Default to last 30 days if no date range specified
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      filter.date = {
        gte: thirtyDaysAgo,
      };
    }

    // Generate detailed report based on type
    let details = [];

    switch (reportType) {
      case "paidSales":
        // Get paid sales with contact details
        details = await db.sale.findMany({
          where: {
            ...filter,
            paymentStatus: "PAID",
          },
          include: {
            contact: {
              select: {
                id: true,
                name: true,
                phone: true,
                creditLimit: true,
                creditPeriod: true,
              },
            },
            payments: true,
          },
          orderBy: {
            date: "desc",
          },
        });

        // Transform data for frontend
        details = details.map(sale => ({
          id: sale.id,
          invoiceNumber: sale.invoiceNumber,
          date: sale.date,
          contactId: sale.contactId,
          contactName: sale.contact.name,
          totalAmount: sale.totalAmount,
          paymentStatus: sale.paymentStatus,
          paymentMethod: sale.paymentMethod,
          payments: sale.payments.map(payment => ({
            id: payment.id,
            method: payment.method,
            amount: payment.amount,
          })),
        }));
        break;

      case "unpaidSales":
        // Get unpaid sales with contact details
        details = await db.sale.findMany({
          where: {
            ...filter,
            paymentStatus: {
              in: ["UNPAID", "PARTIALLY_PAID"],
            },
          },
          include: {
            contact: {
              select: {
                id: true,
                name: true,
                phone: true,
                creditLimit: true,
                creditPeriod: true,
              },
            },
            payments: true,
          },
          orderBy: {
            date: "desc",
          },
        });

        // Transform data for frontend
        details = details.map(sale => ({
          id: sale.id,
          invoiceNumber: sale.invoiceNumber,
          date: sale.date,
          contactId: sale.contactId,
          contactName: sale.contact.name,
          totalAmount: sale.totalAmount,
          paymentStatus: sale.paymentStatus,
          creditPeriod: sale.contact.creditPeriod,
          payments: sale.payments.map(payment => ({
            id: payment.id,
            method: payment.method,
            amount: payment.amount,
          })),
        }));
        break;

      case "paidCreditNotes":
        // Get paid credit notes with contact details
        details = await db.creditNote.findMany({
          where: {
            ...filter,
            paymentStatus: "PAID",
          },
          include: {
            contact: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
            payments: true,
          },
          orderBy: {
            date: "desc",
          },
        });

        // Transform data for frontend
        details = details.map(creditNote => ({
          id: creditNote.id,
          creditNoteNumber: creditNote.creditNoteNumber,
          date: creditNote.date,
          contactId: creditNote.contactId,
          contactName: creditNote.contact.name,
          totalAmount: creditNote.totalAmount,
          paymentStatus: creditNote.paymentStatus,
          paymentMethod: creditNote.paymentMethod,
          payments: creditNote.payments.map(payment => ({
            id: payment.id,
            method: payment.method,
            amount: payment.amount,
          })),
        }));
        break;

      case "unpaidCreditNotes":
        // Get unpaid credit notes with contact details
        details = await db.creditNote.findMany({
          where: {
            ...filter,
            paymentStatus: {
              in: ["UNPAID", "PARTIALLY_PAID"],
            },
          },
          include: {
            contact: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
          },
          orderBy: {
            date: "desc",
          },
        });

        // Transform data for frontend
        details = details.map(creditNote => ({
          id: creditNote.id,
          creditNoteNumber: creditNote.creditNoteNumber,
          date: creditNote.date,
          contactId: creditNote.contactId,
          contactName: creditNote.contact.name,
          totalAmount: creditNote.totalAmount,
          paymentStatus: creditNote.paymentStatus,
        }));
        break;

      default:
        return NextResponse.json(
          { error: "Invalid report type" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      reportType,
      details,
    });
  } catch (error) {
    console.error("Error generating detailed sales report:", error);
    return NextResponse.json(
      { error: "Failed to generate detailed sales report" },
      { status: 500 }
    );
  }
}
