"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, Save, RefreshCw, Plus, AlertCircle, CheckCircle2, Link as LinkIcon } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

interface PaymentMethod {
  id: string;
  code: string;
  name: string;
  isActive: boolean;
  accountId: string | null;
  journalId: string | null;
  account: {
    id: string;
    name: string;
    code: string;
  } | null;
  journal: {
    id: string;
    name: string;
    code: string;
  } | null;
}

interface Account {
  id: string;
  name: string;
  code: string;
  type: string;
}

interface Journal {
  id: string;
  name: string;
  code: string;
  type: string;
}

export default function AccountingPaymentMethodsPage() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [journals, setJournals] = useState<Journal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLinking, setIsLinking] = useState(false);
  const [linkSuccess, setLinkSuccess] = useState(false);
  const [linkError, setLinkError] = useState<string | null>(null);
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [linkForm, setLinkForm] = useState({
    accountId: "",
    journalId: "",
  });

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch payment methods
        const paymentMethodsResponse = await fetch("/api/accounting/payment-methods");
        if (paymentMethodsResponse.ok) {
          const paymentMethodsData = await paymentMethodsResponse.json();
          setPaymentMethods(paymentMethodsData.data || []);
        }

        // Fetch accounts
        const accountsResponse = await fetch("/api/accounting/accounts");
        if (accountsResponse.ok) {
          const accountsData = await accountsResponse.json();
          setAccounts(accountsData.data || []);
        }

        // Fetch journals
        const journalsResponse = await fetch("/api/accounting/journals");
        if (journalsResponse.ok) {
          const journalsData = await journalsResponse.json();
          setJournals(journalsData.data || []);
        }
      } catch (error) {
        console.error("Error fetching payment methods data:", error);
        toast.error("Failed to load payment methods data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle link all payment methods
  const handleLinkAllPaymentMethods = async () => {
    if (!confirm("Are you sure you want to link all payment methods to their corresponding accounts and journals?")) {
      return;
    }

    setIsLinking(true);
    setLinkSuccess(false);
    setLinkError(null);

    try {
      const response = await fetch("/api/accounting/payment-methods", {
        method: "PATCH",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to link payment methods");
      }

      const data = await response.json();
      setLinkSuccess(true);
      toast.success("Payment methods linked successfully");

      // Refresh payment methods
      const paymentMethodsResponse = await fetch("/api/accounting/payment-methods");
      if (paymentMethodsResponse.ok) {
        const paymentMethodsData = await paymentMethodsResponse.json();
        setPaymentMethods(paymentMethodsData.data || []);
      }
    } catch (error) {
      console.error("Error linking payment methods:", error);
      setLinkError(error instanceof Error ? error.message : "An error occurred");
      toast.error(error instanceof Error ? error.message : "Failed to link payment methods");
    } finally {
      setIsLinking(false);
    }
  };

  // Open link dialog for a payment method
  const openLinkDialog = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setLinkForm({
      accountId: paymentMethod.accountId || "",
      journalId: paymentMethod.journalId || "",
    });
    setShowLinkDialog(true);
  };

  // Handle link form input changes
  const handleLinkFormChange = (field: string, value: string) => {
    setLinkForm(prev => ({ ...prev, [field]: value }));
  };

  // Handle link payment method
  const handleLinkPaymentMethod = async () => {
    if (!selectedPaymentMethod) return;

    setIsLinking(true);
    try {
      const response = await fetch("/api/accounting/payment-methods", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: selectedPaymentMethod.id,
          accountId: linkForm.accountId || null,
          journalId: linkForm.journalId || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to link payment method");
      }

      const data = await response.json();
      toast.success("Payment method linked successfully");

      // Update payment methods list
      setPaymentMethods(prev =>
        prev.map(pm =>
          pm.id === selectedPaymentMethod.id ? data.data : pm
        )
      );

      // Close dialog
      setShowLinkDialog(false);
    } catch (error) {
      console.error("Error linking payment method:", error);
      toast.error(error instanceof Error ? error.message : "Failed to link payment method");
    } finally {
      setIsLinking(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Payment Methods</h1>
        <div className="flex space-x-2">
          <Button
            onClick={handleLinkAllPaymentMethods}
            disabled={isLinking}
            className="flex items-center"
          >
            {isLinking ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Linking...
              </>
            ) : (
              <>
                <LinkIcon className="mr-2 h-4 w-4" />
                Link All Payment Methods
              </>
            )}
          </Button>
        </div>
      </div>

      {linkSuccess && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">
            Payment methods linked successfully to their corresponding accounts and journals.
          </AlertDescription>
        </Alert>
      )}

      {linkError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{linkError}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>Manage payment methods and their accounting links</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
              <span className="ml-2 text-gray-500">Loading payment methods...</span>
            </div>
          ) : paymentMethods.length > 0 ? (
            <div className="rounded-md border overflow-hidden">
              <table className="w-full text-sm">
                <thead>
                  <tr className="bg-gray-100 border-b">
                    <th className="px-4 py-3 text-left">Name</th>
                    <th className="px-4 py-3 text-left">Code</th>
                    <th className="px-4 py-3 text-left">Account</th>
                    <th className="px-4 py-3 text-left">Journal</th>
                    <th className="px-4 py-3 text-center">Status</th>
                    <th className="px-4 py-3 text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {paymentMethods.map(method => (
                    <tr key={method.id} className="border-b hover:bg-gray-50">
                      <td className="px-4 py-3 text-left">{method.name}</td>
                      <td className="px-4 py-3 text-left">{method.code}</td>
                      <td className="px-4 py-3 text-left">
                        {method.account ? `${method.account.name} (${method.account.code})` : "Not linked"}
                      </td>
                      <td className="px-4 py-3 text-left">
                        {method.journal ? `${method.journal.name} (${method.journal.code})` : "Not linked"}
                      </td>
                      <td className="px-4 py-3 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${method.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {method.isActive ? "Active" : "Inactive"}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="outline" size="sm" onClick={() => openLinkDialog(method)}>
                            <LinkIcon className="h-4 w-4 mr-1" />
                            Link
                          </Button>
                          <Link href={`/dashboard/accounting/payment-methods/${method.id}`}>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center p-4 text-gray-500">
              No payment methods found.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Link Payment Method Dialog */}
      <Dialog open={showLinkDialog} onOpenChange={setShowLinkDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Link Payment Method to Accounts</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="paymentMethod">Payment Method</Label>
              <Input
                id="paymentMethod"
                value={selectedPaymentMethod?.name || ""}
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="account">Account</Label>
              <Select value={linkForm.accountId} onValueChange={(value) => handleLinkFormChange("accountId", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an account" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">None</SelectItem>
                  {accounts.map(account => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name} ({account.code}) - {account.type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="journal">Journal</Label>
              <Select value={linkForm.journalId} onValueChange={(value) => handleLinkFormChange("journalId", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a journal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">None</SelectItem>
                  {journals.map(journal => (
                    <SelectItem key={journal.id} value={journal.id}>
                      {journal.name} ({journal.code}) - {journal.type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLinkDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleLinkPaymentMethod} disabled={isLinking}>
              {isLinking ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Linking...
                </>
              ) : (
                <>
                  <LinkIcon className="mr-2 h-4 w-4" />
                  Link Payment Method
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
