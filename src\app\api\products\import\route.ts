import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import * as XLSX from 'xlsx';

// POST /api/products/import - Import products from Excel
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add products
    const hasAddPermission = await hasPermission("add_products");
    if (!hasAddPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to import products" },
        { status: 403 }
      );
    }

    let file: File;
    let data: any[];

    try {
      console.log("Parsing form data...");
      // Parse form data
      const formData = await req.formData();
      file = formData.get("file") as File;

      if (!file) {
        console.error("No file found in form data");
        return NextResponse.json(
          { error: "No file uploaded. Please select a file before submitting." },
          { status: 400 }
        );
      }

      console.log(`File received: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);

      // Validate file type
      const fileName = file.name;
      if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
        console.error(`Invalid file type: ${file.type}`);
        return NextResponse.json(
          { error: "Invalid file type. Only Excel files (.xlsx, .xls) are allowed." },
          { status: 400 }
        );
      }

      // Validate file size
      if (file.size === 0) {
        console.error("File is empty");
        return NextResponse.json(
          { error: "The uploaded file is empty. Please select a valid Excel file." },
          { status: 400 }
        );
      }

      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        console.error(`File too large: ${file.size} bytes`);
        return NextResponse.json(
          { error: "The file is too large. Maximum file size is 10MB." },
          { status: 400 }
        );
      }

      console.log("Reading file content...");
      // Read the Excel file
      try {
        const fileBuffer = await file.arrayBuffer();
        console.log(`File buffer size: ${fileBuffer.byteLength} bytes`);

        const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
        console.log(`Workbook sheets: ${workbook.SheetNames.join(', ')}`);

        if (workbook.SheetNames.length === 0) {
          console.error("No sheets found in workbook");
          return NextResponse.json(
            { error: "The Excel file does not contain any sheets." },
            { status: 400 }
          );
        }

        // Get the first worksheet
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];

        // Convert worksheet to JSON
        data = XLSX.utils.sheet_to_json(worksheet);
        console.log(`Parsed ${data.length} rows from Excel file`);
      } catch (xlsxError) {
        console.error("Error parsing Excel file:", xlsxError);
        return NextResponse.json(
          {
            error: "Error processing Excel file. Please make sure the file is a valid Excel file.",
            details: xlsxError instanceof Error ? xlsxError.message : "Unknown error"
          },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error("Error processing form data:", error);
      return NextResponse.json(
        {
          error: "Error processing the uploaded file. Please try again.",
          details: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 400 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: "The Excel file is empty or has no valid data" },
        { status: 400 }
      );
    }

    // Validate the data structure
    const requiredFields = ['Name', 'Category', 'BasePrice', 'CostPrice'];
    const firstRow = data[0] as any;
    const missingFields = [];

    for (const field of requiredFields) {
      if (!(field in firstRow)) {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Get all categories
    const categories = await db.category.findMany();
    const categoryMap = new Map(categories.map(c => [c.name.toLowerCase(), c.id]));

    // Get all warehouses
    const warehouses = await db.warehouse.findMany();
    const warehouseMap = new Map(warehouses.map(w => [w.name.toLowerCase(), w.id]));

    // Process each product
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const row of data) {
      const product = row as any;

      try {
        // Find or create category
        let categoryId = null;
        const categoryName = product.Category;

        if (categoryName) {
          const categoryKey = categoryName.toLowerCase();
          if (categoryMap.has(categoryKey)) {
            categoryId = categoryMap.get(categoryKey);
          } else {
            // Create new category
            const newCategory = await db.category.create({
              data: {
                name: categoryName,
                description: '',
              },
            });
            categoryId = newCategory.id;
            categoryMap.set(categoryName.toLowerCase(), newCategory.id);
          }
        } else {
          throw new Error("Category is required");
        }

        // Create the product
        const newProduct = await db.product.create({
          data: {
            name: product.Name,
            description: product.Description || "",
            basePrice: parseFloat(product.BasePrice) || 0,
            costPrice: parseFloat(product.CostPrice) || 0,
            categoryId: categoryId,
            isCustomizable: product.IsCustomizable === 'Yes' || product.IsCustomizable === true,
            isComponent: product.IsComponent === 'Yes' || product.IsComponent === true,
            componentType: product.ComponentType || null,
          },
        });

        // Create specifications if provided
        if (product.Specifications) {
          const specs = product.Specifications.split(',').map((spec: string) => spec.trim());

          for (const spec of specs) {
            const [name, value] = spec.split(':').map((s: string) => s.trim());

            if (name && value) {
              await db.specification.create({
                data: {
                  name,
                  value,
                  productId: newProduct.id,
                },
              });
            }
          }
        }

        // Create inventory entries for warehouses
        for (const warehouse of warehouses) {
          const warehouseKey = warehouse.name;
          const quantity = parseInt(product[warehouseKey]) || 0;

          if (quantity > 0) {
            await db.inventory.create({
              data: {
                productId: newProduct.id,
                warehouseId: warehouse.id,
                quantity,
                costPrice: parseFloat(product.CostPrice) || 0,
              },
            });
          }
        }

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Error importing ${product.Name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      message: `Import completed. ${results.success} products imported successfully, ${results.failed} failed.`,
      results
    });
  } catch (error) {
    console.error("Error importing products:", error);
    return NextResponse.json(
      { error: "Failed to import products. Please try again or contact your system administrator." },
      { status: 500 }
    );
  }
}
