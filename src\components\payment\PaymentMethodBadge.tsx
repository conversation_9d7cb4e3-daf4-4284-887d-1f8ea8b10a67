"use client";

import React from 'react';
import PaymentMethodIcon from './PaymentMethodIcon';

interface PaymentMethodBadgeProps {
  methodCode: string;
  methodName?: string;
  iconName?: string | null;
  iconUrl?: string | null;
  color?: string | null;
  size?: 'sm' | 'md' | 'lg';
}

export default function PaymentMethodBadge({
  methodCode,
  methodName,
  iconName,
  iconUrl,
  color,
  size = 'md'
}: PaymentMethodBadgeProps) {
  // Default method names if not provided
  const getDefaultMethodName = (code: string) => {
    switch (code) {
      case 'CASH':
        return 'Cash';
      case 'VODAFONE_CASH':
        return 'Vodafone Cash';
      case 'BANK_TRANSFER':
        return 'Bank Transfer';
      case 'VISA':
      case 'CREDIT_CARD':
        return 'Credit Card';
      case 'CUSTOMER_ACCOUNT':
        return 'Customer Account';
      case 'SUPPLIER_ACCOUNT':
        return 'Supplier Account';
      default:
        return code;
    }
  };

  const displayName = methodName || getDefaultMethodName(methodCode);

  // Size classes
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5'
  };

  // Default colors if not provided
  const getDefaultColor = (code: string) => {
    switch (code) {
      case 'CASH':
        return '#3895e7';
      case 'VODAFONE_CASH':
        return '#e60000';
      case 'BANK_TRANSFER':
        return '#307aa8';
      case 'VISA':
      case 'CREDIT_CARD':
        return '#1a1f71';
      case 'CUSTOMER_ACCOUNT':
      case 'SUPPLIER_ACCOUNT':
        return '#6b7280';
      default:
        return '#3895e7';
    }
  };

  const badgeColor = color || getDefaultColor(methodCode);

  // Calculate lighter background color (20% opacity)
  const hexToRgb = (hex: string) => {
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    const fullHex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(fullHex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  };

  const rgb = hexToRgb(badgeColor);
  const bgColor = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.15)`;

  return (
    <div
      className={`inline-flex items-center rounded-full ${sizeClasses[size]} font-medium`}
      style={{ backgroundColor: bgColor, color: badgeColor }}
    >
      <span className="mr-1.5">
        <PaymentMethodIcon
          methodCode={methodCode}
          iconName={iconName}
          iconUrl={iconUrl}
          color={badgeColor}
          size={size === 'sm' ? 14 : size === 'md' ? 16 : 18}
        />
      </span>
      {displayName}
    </div>
  );
}
