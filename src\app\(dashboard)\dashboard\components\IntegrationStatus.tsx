"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  Link as LinkIcon,
  Database,
  ShoppingCart,
  Truck,
  Package,
  DollarSign,
  Users
} from "lucide-react";
import Link from "next/link";

interface ModuleStatus {
  name: string;
  status: "connected" | "disconnected" | "warning" | "loading";
  message: string;
  icon: React.ReactNode;
  href: string;
}

export default function IntegrationStatus() {
  const [modules, setModules] = useState<ModuleStatus[]>([
    {
      name: "Sales",
      status: "loading",
      message: "Checking connection...",
      icon: <ShoppingCart className="h-5 w-5" />,
      href: "/dashboard/sales"
    },
    {
      name: "Purchases",
      status: "loading",
      message: "Checking connection...",
      icon: <Truck className="h-5 w-5" />,
      href: "/dashboard/purchases"
    },
    {
      name: "Inventory",
      status: "loading",
      message: "Checking connection...",
      icon: <Package className="h-5 w-5" />,
      href: "/dashboard/inventory"
    },
    {
      name: "Accounting",
      status: "loading",
      message: "Checking connection...",
      icon: <DollarSign className="h-5 w-5" />,
      href: "/dashboard/accounting"
    },
    {
      name: "Contacts",
      status: "loading",
      message: "Checking connection...",
      icon: <Users className="h-5 w-5" />,
      href: "/dashboard/contacts"
    }
  ]);
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Check module status
  useEffect(() => {
    checkModulesStatus();
  }, []);
  
  const checkModulesStatus = async () => {
    setIsRefreshing(true);
    
    try {
      const response = await fetch("/api/system/modules-status");
      
      if (response.ok) {
        const data = await response.json();
        
        setModules(prevModules => 
          prevModules.map(module => {
            const moduleData = data.modules.find((m: any) => m.name.toLowerCase() === module.name.toLowerCase());
            
            if (moduleData) {
              return {
                ...module,
                status: moduleData.status,
                message: moduleData.message
              };
            }
            
            return module;
          })
        );
      } else {
        console.error("Failed to fetch module status");
      }
    } catch (error) {
      console.error("Error checking module status:", error);
    } finally {
      setIsRefreshing(false);
    }
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "connected":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Connected
          </Badge>
        );
      case "disconnected":
        return (
          <Badge className="bg-red-100 text-red-800">
            <XCircle className="mr-1 h-3 w-3" />
            Disconnected
          </Badge>
        );
      case "warning":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Warning
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800">
            <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
            Loading
          </Badge>
        );
    }
  };
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>System Integration Status</CardTitle>
            <CardDescription>
              Status of connections between system modules
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={checkModulesStatus}
            disabled={isRefreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {modules.map((module, index) => (
            <div key={module.name}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3 p-2 bg-gray-100 rounded-full">
                    {module.icon}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">{module.name}</h3>
                    <p className="text-xs text-gray-500">{module.message}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(module.status)}
                  <Link href={module.href}>
                    <Button variant="ghost" size="sm">
                      <LinkIcon className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
              {index < modules.length - 1 && <Separator className="my-4" />}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
