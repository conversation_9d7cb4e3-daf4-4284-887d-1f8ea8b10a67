import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/warehouses - Get all warehouses
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const fromSettings = url.searchParams.get('from') === 'settings';
    const branchId = url.searchParams.get("branchId");

    // Skip permission check if the request is from the settings page
    if (!fromSettings) {
      // Check if user has permission to view warehouses or products
      const hasViewPermission = await hasPermission("view_warehouses") || await hasPermission("view_products");
      if (!hasViewPermission) {
        return NextResponse.json(
          { error: "You don't have permission to view warehouses" },
          { status: 403 }
        );
      }
    }

    // Build the query
    const query: any = {
      include: {
        branch: true,
      },
      orderBy: {
        name: "asc",
      },
    };

    // Add branch filter if provided
    if (branchId) {
      query.where = {
        branchId,
      };
    }

    const warehouses = await db.warehouse.findMany(query);

    return NextResponse.json(warehouses);
  } catch (error) {
    console.error("Error fetching warehouses:", error);
    return NextResponse.json(
      { error: "Failed to fetch warehouses" },
      { status: 500 }
    );
  }
}

// POST /api/warehouses - Create a new warehouse
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add warehouses
    const hasAddPermission = await hasPermission("add_warehouses");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add warehouses" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.branchId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if branch exists
    const branch = await db.branch.findUnique({
      where: {
        id: data.branchId,
      },
    });

    if (!branch) {
      return NextResponse.json(
        { error: "Branch not found" },
        { status: 404 }
      );
    }

    // Create the warehouse
    const warehouse = await db.warehouse.create({
      data: {
        name: data.name,
        branchId: data.branchId,
      },
    });

    return NextResponse.json(warehouse);
  } catch (error) {
    console.error("Error creating warehouse:", error);
    return NextResponse.json(
      { error: "Failed to create warehouse" },
      { status: 500 }
    );
  }
}
