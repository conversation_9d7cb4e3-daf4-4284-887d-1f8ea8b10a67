import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/trial-balance - Get trial balance
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const asOfDate = url.searchParams.get("asOfDate");
    
    // Set default date to today if not provided
    const dateFilter = asOfDate ? new Date(asOfDate) : new Date();
    
    // Get all active accounts
    const accounts = await db.account.findMany({
      where: {
        isActive: true,
      },
      orderBy: [
        { type: "asc" },
        { code: "asc" },
      ],
    });
    
    // Get all journal entries up to the specified date
    const journalEntries = await db.journalEntry.findMany({
      where: {
        date: {
          lte: dateFilter,
        },
      },
    });
    
    // Calculate debit and credit balances for each account
    const accountsWithBalances = accounts.map(account => {
      // Get all entries where this account is debited
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Get all entries where this account is credited
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Calculate debit and credit balances based on account type
      let debitBalance = 0;
      let creditBalance = 0;
      
      // For asset and expense accounts, debits increase the balance
      if (account.type === "ASSET" || account.type === "EXPENSE") {
        const netBalance = totalDebits - totalCredits;
        if (netBalance > 0) {
          debitBalance = netBalance;
        } else {
          creditBalance = Math.abs(netBalance);
        }
      } 
      // For liability, equity, and revenue accounts, credits increase the balance
      else if (account.type === "LIABILITY" || account.type === "EQUITY" || account.type === "REVENUE") {
        const netBalance = totalCredits - totalDebits;
        if (netBalance > 0) {
          creditBalance = netBalance;
        } else {
          debitBalance = Math.abs(netBalance);
        }
      }
      
      return {
        ...account,
        debitBalance,
        creditBalance,
      };
    });
    
    // Filter out accounts with zero balance
    const nonZeroAccounts = accountsWithBalances.filter(account => 
      account.debitBalance > 0 || account.creditBalance > 0
    );
    
    return NextResponse.json({
      asOfDate: dateFilter,
      accounts: nonZeroAccounts,
    });
  } catch (error: any) {
    console.error("Error generating trial balance:", error);
    return NextResponse.json(
      { error: error.message || "Failed to generate trial balance" },
      { status: 500 }
    );
  }
}
