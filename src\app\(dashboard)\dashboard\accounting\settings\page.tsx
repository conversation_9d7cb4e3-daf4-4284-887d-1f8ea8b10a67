"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Plus, Save, Trash2, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import CurrencyForm from "./components/CurrencyForm";
import CurrencyList from "./components/CurrencyList";
import AccountingSystemForm from "./components/AccountingSystemForm";

export default function AccountingSettingsPage() {
  const [activeTab, setActiveTab] = useState("currencies");
  const [isLoading, setIsLoading] = useState(false);
  const [currencies, setCurrencies] = useState([]);
  const [showCurrencyForm, setShowCurrencyForm] = useState(false);
  const [editingCurrency, setEditingCurrency] = useState(null);

  // Fetch currencies
  const fetchCurrencies = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/accounting/currencies");
      if (response.ok) {
        const data = await response.json();
        setCurrencies(data.data || []);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to fetch currencies");
      }
    } catch (error) {
      console.error("Error fetching currencies:", error);
      toast.error("Failed to fetch currencies");
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on initial render
  useEffect(() => {
    fetchCurrencies();
  }, []);

  // Handle currency form submission
  const handleCurrencySubmit = async (currencyData) => {
    try {
      const response = await fetch("/api/accounting/currencies", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(currencyData),
      });

      if (response.ok) {
        toast.success("Currency saved successfully");
        setShowCurrencyForm(false);
        setEditingCurrency(null);
        fetchCurrencies();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to save currency");
      }
    } catch (error) {
      console.error("Error saving currency:", error);
      toast.error("Failed to save currency");
    }
  };

  // Handle edit currency
  const handleEditCurrency = (currency) => {
    setEditingCurrency(currency);
    setShowCurrencyForm(true);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Accounting Settings</h1>
        <Button onClick={() => fetchCurrencies()} disabled={isLoading}>
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
          Refresh
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="currencies">Currencies</TabsTrigger>
          <TabsTrigger value="chart-of-accounts">Chart of Accounts</TabsTrigger>
          <TabsTrigger value="fiscal-periods">Fiscal Periods</TabsTrigger>
          <TabsTrigger value="accounting-system">Accounting System</TabsTrigger>
        </TabsList>

        <TabsContent value="currencies" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Currency Management</h2>
            <Button onClick={() => {
              setEditingCurrency(null);
              setShowCurrencyForm(true);
            }}>
              <Plus className="h-4 w-4 mr-2" />
              Add Currency
            </Button>
          </div>

          {showCurrencyForm ? (
            <Card>
              <CardHeader>
                <CardTitle>{editingCurrency ? "Edit Currency" : "Add Currency"}</CardTitle>
              </CardHeader>
              <CardContent>
                <CurrencyForm 
                  initialData={editingCurrency} 
                  onSubmit={handleCurrencySubmit} 
                  onCancel={() => {
                    setShowCurrencyForm(false);
                    setEditingCurrency(null);
                  }}
                />
              </CardContent>
            </Card>
          ) : (
            <CurrencyList 
              currencies={currencies} 
              onEdit={handleEditCurrency} 
              isLoading={isLoading}
            />
          )}
        </TabsContent>

        <TabsContent value="chart-of-accounts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Chart of Accounts</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Configure your chart of accounts structure and settings.</p>
              {/* Chart of Accounts configuration will be implemented here */}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fiscal-periods" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Fiscal Periods</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Manage fiscal years and periods for financial reporting.</p>
              {/* Fiscal Periods management will be implemented here */}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accounting-system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Accounting System Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <AccountingSystemForm />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
