"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from "recharts";
import { 
  Award, 
  Gift, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Calendar, 
  Star,
  ShoppingBag,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  Percent
} from "lucide-react";

interface LoyaltyMetrics {
  totalPoints: number;
  pointsValue: number;
  activeCustomers: number;
  redemptionRate: number;
  pointsEarned: {
    current: number;
    previous: number;
    percentChange: number;
  };
  pointsRedeemed: {
    current: number;
    previous: number;
    percentChange: number;
  };
  tierDistribution: Array<{
    tier: string;
    count: number;
    percentage: number;
  }>;
  monthlyActivity: Array<{
    month: string;
    earned: number;
    redeemed: number;
    net: number;
  }>;
  topCustomers: Array<{
    id: string;
    name: string;
    loyaltyPoints: number;
    loyaltyTier: string;
    isVIP: boolean;
  }>;
  upcomingBirthdays: number;
}

export default function LoyaltyDashboardPage() {
  const [metrics, setMetrics] = useState<LoyaltyMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeFrame, setTimeFrame] = useState("month");
  
  // Colors for charts
  const TIER_COLORS = {
    BRONZE: '#cd7f32',
    SILVER: '#C0C0C0',
    GOLD: '#FFD700',
    PLATINUM: '#E5E4E2'
  };
  
  // Fetch loyalty metrics
  useEffect(() => {
    const fetchMetrics = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll simulate data
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock metrics data
        const mockMetrics: LoyaltyMetrics = {
          totalPoints: 267000,
          pointsValue: 2670,
          activeCustomers: 145,
          redemptionRate: 32,
          pointsEarned: {
            current: 28500,
            previous: 25800,
            percentChange: 10.5
          },
          pointsRedeemed: {
            current: 19500,
            previous: 17200,
            percentChange: 13.4
          },
          tierDistribution: [
            { tier: "BRONZE", count: 85, percentage: 58.6 },
            { tier: "SILVER", count: 38, percentage: 26.2 },
            { tier: "GOLD", count: 15, percentage: 10.3 },
            { tier: "PLATINUM", count: 7, percentage: 4.9 }
          ],
          monthlyActivity: [
            { month: "Jan", earned: 12500, redeemed: 8200, net: 4300 },
            { month: "Feb", earned: 14200, redeemed: 9500, net: 4700 },
            { month: "Mar", earned: 15800, redeemed: 10200, net: 5600 },
            { month: "Apr", earned: 13600, redeemed: 8900, net: 4700 },
            { month: "May", earned: 16200, redeemed: 11500, net: 4700 },
            { month: "Jun", earned: 18500, redeemed: 12800, net: 5700 },
            { month: "Jul", earned: 17200, redeemed: 11200, net: 6000 },
            { month: "Aug", earned: 19800, redeemed: 13500, net: 6300 },
            { month: "Sep", earned: 21500, redeemed: 14800, net: 6700 },
            { month: "Oct", earned: 23200, redeemed: 15900, net: 7300 },
            { month: "Nov", earned: 25800, redeemed: 17200, net: 8600 },
            { month: "Dec", earned: 28500, redeemed: 19500, net: 9000 }
          ],
          topCustomers: [
            { id: "1", name: "Ahmed Mohamed", loyaltyPoints: 12500, loyaltyTier: "PLATINUM", isVIP: true },
            { id: "2", name: "Laila Ibrahim", loyaltyPoints: 8700, loyaltyTier: "GOLD", isVIP: true },
            { id: "3", name: "Omar Ali", loyaltyPoints: 5200, loyaltyTier: "GOLD", isVIP: false },
            { id: "4", name: "Sara Hassan", loyaltyPoints: 4200, loyaltyTier: "SILVER", isVIP: false },
            { id: "5", name: "Mohamed Samir", loyaltyPoints: 3800, loyaltyTier: "SILVER", isVIP: true }
          ],
          upcomingBirthdays: 5
        };
        
        setMetrics(mockMetrics);
      } catch (error) {
        console.error("Error fetching loyalty metrics:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchMetrics();
  }, [timeFrame]);
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading loyalty dashboard...</p>
        </div>
      </div>
    );
  }
  
  if (!metrics) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-gray-500">No loyalty metrics available.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Loyalty Program Dashboard</h1>
        <div className="flex items-center space-x-2">
          <Tabs defaultValue={timeFrame} onValueChange={setTimeFrame}>
            <TabsList>
              <TabsTrigger value="week">Week</TabsTrigger>
              <TabsTrigger value="month">Month</TabsTrigger>
              <TabsTrigger value="quarter">Quarter</TabsTrigger>
              <TabsTrigger value="year">Year</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Loyalty Points</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{metrics.totalPoints.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Points in circulation</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Gift className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Points Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{metrics.pointsValue.toLocaleString()} ج.م</p>
                <p className="text-sm text-gray-500">Total liability</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Active Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{metrics.activeCustomers}</p>
                <p className="text-sm text-gray-500">In loyalty program</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Redemption Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{metrics.redemptionRate}%</p>
                <p className="text-sm text-gray-500">Points redeemed vs earned</p>
              </div>
              <div className="bg-amber-100 p-3 rounded-full">
                <Percent className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Points Earned & Redeemed */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-green-500" />
              Points Earned
            </CardTitle>
            <CardDescription>
              Points earned by customers in the current period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div>
                <p className="text-3xl font-bold">{metrics.pointsEarned.current.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Current period</p>
              </div>
              <div>
                <Badge className={metrics.pointsEarned.percentChange >= 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                  {metrics.pointsEarned.percentChange >= 0 ? (
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                  )}
                  {Math.abs(metrics.pointsEarned.percentChange)}% from previous
                </Badge>
                <p className="text-sm text-gray-500 mt-1 text-right">
                  Previous: {metrics.pointsEarned.previous.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ShoppingBag className="h-5 w-5 mr-2 text-red-500" />
              Points Redeemed
            </CardTitle>
            <CardDescription>
              Points redeemed by customers in the current period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div>
                <p className="text-3xl font-bold">{metrics.pointsRedeemed.current.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Current period</p>
              </div>
              <div>
                <Badge className={metrics.pointsRedeemed.percentChange >= 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                  {metrics.pointsRedeemed.percentChange >= 0 ? (
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                  )}
                  {Math.abs(metrics.pointsRedeemed.percentChange)}% from previous
                </Badge>
                <p className="text-sm text-gray-500 mt-1 text-right">
                  Previous: {metrics.pointsRedeemed.previous.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Activity</CardTitle>
            <CardDescription>
              Points earned and redeemed by month
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={metrics.monthlyActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value.toLocaleString()} points`, '']} />
                <Legend />
                <Area type="monotone" dataKey="earned" stackId="1" stroke="#8884d8" fill="#8884d8" name="Points Earned" />
                <Area type="monotone" dataKey="redeemed" stackId="2" stroke="#82ca9d" fill="#82ca9d" name="Points Redeemed" />
                <Line type="monotone" dataKey="net" stroke="#ff7300" name="Net Change" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Tier Distribution</CardTitle>
            <CardDescription>
              Breakdown of customers by loyalty tier
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={metrics.tierDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                  nameKey="tier"
                  label={({ tier, percentage }) => `${tier}: ${percentage.toFixed(1)}%`}
                >
                  {metrics.tierDistribution.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={TIER_COLORS[entry.tier as keyof typeof TIER_COLORS] || '#8884d8'} 
                    />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} customers`, 'Count']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
      
      {/* Top Customers & Upcoming Birthdays */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Top Loyalty Customers</CardTitle>
            <CardDescription>
              Customers with the highest loyalty points
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {metrics.topCustomers.map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-800 font-bold mr-3">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium">{customer.name}</p>
                      <div className="flex items-center mt-1">
                        <Badge 
                          style={{ 
                            backgroundColor: TIER_COLORS[customer.loyaltyTier as keyof typeof TIER_COLORS] || '#8884d8',
                            color: customer.loyaltyTier === 'BRONZE' || customer.loyaltyTier === 'PLATINUM' ? '#000' : '#fff'
                          }}
                          className="mr-2"
                        >
                          {customer.loyaltyTier}
                        </Badge>
                        {customer.isVIP && (
                          <Badge className="bg-yellow-100 text-yellow-800">
                            <Star className="h-3 w-3 mr-1" />
                            VIP
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">{customer.loyaltyPoints.toLocaleString()}</p>
                    <p className="text-xs text-gray-500">loyalty points</p>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" className="w-full">
                View All Customers
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-pink-500" />
              Upcoming Birthdays
            </CardTitle>
            <CardDescription>
              Customers with birthdays in the next 30 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-6 space-y-4">
              <div className="bg-pink-100 p-4 rounded-full">
                <Gift className="h-10 w-10 text-pink-500" />
              </div>
              <p className="text-3xl font-bold">{metrics.upcomingBirthdays}</p>
              <p className="text-gray-500 text-center">
                {metrics.upcomingBirthdays === 1 
                  ? "customer has a birthday" 
                  : "customers have birthdays"} in the next 30 days
              </p>
              <Button>
                Send Birthday Gifts
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
