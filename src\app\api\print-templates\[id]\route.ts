import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

/**
 * GET /api/print-templates/[id]
 * Retrieve a specific print template
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const id = params.id;
    
    const template = await db.printTemplate.findUnique({
      where: { id }
    });
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(template);
  } catch (error) {
    console.error('Error fetching print template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch print template' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/print-templates/[id]
 * Update a print template
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const id = params.id;
    const data = await request.json();
    
    // Validate the data
    if (!data.name || !data.type || !data.content) {
      return NextResponse.json(
        { error: 'Name, type, and content are required' },
        { status: 400 }
      );
    }
    
    // Check if template exists
    const existingTemplate = await db.printTemplate.findUnique({
      where: { id }
    });
    
    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // If this is set as default, unset any other default templates of the same type
    if (data.isDefault) {
      await db.printTemplate.updateMany({
        where: {
          type: data.type,
          isDefault: true,
          language: data.language || existingTemplate.language,
          branchId: data.branchId || existingTemplate.branchId,
          id: { not: id }
        },
        data: {
          isDefault: false
        }
      });
      
      // Update print settings if this is now the default template
      const settings = await db.printSettings.findFirst();
      if (settings && settings.defaultTemplateSettings) {
        const defaultTemplateSettings = settings.defaultTemplateSettings as any;
        defaultTemplateSettings[data.type] = {
          ...defaultTemplateSettings[data.type],
          defaultTemplateId: id
        };
        
        await db.printSettings.update({
          where: { id: settings.id },
          data: {
            defaultTemplateSettings
          }
        });
      }
    }
    
    // Update the template
    const template = await db.printTemplate.update({
      where: { id },
      data: {
        name: data.name,
        type: data.type,
        content: data.content,
        isDefault: data.isDefault || false,
        language: data.language || existingTemplate.language,
        paperSize: data.paperSize || existingTemplate.paperSize,
        orientation: data.orientation || existingTemplate.orientation,
        branchId: data.branchId,
        variables: data.variables,
        previewImage: data.previewImage
      }
    });
    
    return NextResponse.json(template);
  } catch (error) {
    console.error('Error updating print template:', error);
    return NextResponse.json(
      { error: 'Failed to update print template' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/print-templates/[id]
 * Delete a print template
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const id = params.id;
    
    // Check if template exists
    const existingTemplate = await db.printTemplate.findUnique({
      where: { id }
    });
    
    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // Check if this is the only default template for this type
    if (existingTemplate.isDefault) {
      const defaultTemplatesCount = await db.printTemplate.count({
        where: {
          type: existingTemplate.type,
          isDefault: true,
          language: existingTemplate.language,
          branchId: existingTemplate.branchId
        }
      });
      
      if (defaultTemplatesCount <= 1) {
        return NextResponse.json(
          { error: 'Cannot delete the only default template for this type' },
          { status: 400 }
        );
      }
      
      // Update print settings if this is the default template
      const settings = await db.printSettings.findFirst();
      if (settings && settings.defaultTemplateSettings) {
        const defaultTemplateSettings = settings.defaultTemplateSettings as any;
        if (defaultTemplateSettings[existingTemplate.type]?.defaultTemplateId === id) {
          // Find another template to set as default
          const anotherTemplate = await db.printTemplate.findFirst({
            where: {
              type: existingTemplate.type,
              id: { not: id }
            }
          });
          
          if (anotherTemplate) {
            // Set the other template as default
            await db.printTemplate.update({
              where: { id: anotherTemplate.id },
              data: { isDefault: true }
            });
            
            // Update settings
            defaultTemplateSettings[existingTemplate.type] = {
              ...defaultTemplateSettings[existingTemplate.type],
              defaultTemplateId: anotherTemplate.id
            };
            
            await db.printSettings.update({
              where: { id: settings.id },
              data: {
                defaultTemplateSettings
              }
            });
          }
        }
      }
    }
    
    // Delete the template
    await db.printTemplate.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting print template:', error);
    return NextResponse.json(
      { error: 'Failed to delete print template' },
      { status: 500 }
    );
  }
}
