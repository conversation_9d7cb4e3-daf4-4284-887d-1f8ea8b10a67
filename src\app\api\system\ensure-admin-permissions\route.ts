import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

// Define all required permissions for the system
const requiredPermissions = [
  { name: "view_users", description: "View users" },
  { name: "add_users", description: "Add new users" },
  { name: "edit_users", description: "Edit existing users" },
  { name: "delete_users", description: "Delete users" },
  { name: "view_branches", description: "View branches" },
  { name: "add_branches", description: "Add new branches" },
  { name: "edit_branches", description: "Edit existing branches" },
  { name: "delete_branches", description: "Delete branches" },
  { name: "view_warehouses", description: "View warehouses" },
  { name: "add_warehouses", description: "Add new warehouses" },
  { name: "edit_warehouses", description: "Edit existing warehouses" },
  { name: "delete_warehouses", description: "Delete warehouses" },
  { name: "view_products", description: "View products" },
  { name: "add_products", description: "Add new products" },
  { name: "edit_products", description: "Edit existing products" },
  { name: "delete_products", description: "Delete products" },
  { name: "view_inventory", description: "View inventory" },
  { name: "add_inventory", description: "Add inventory" },
  { name: "edit_inventory", description: "Edit inventory" },
  { name: "delete_inventory", description: "Delete inventory" },
  { name: "view_sales", description: "View sales" },
  { name: "add_sales", description: "Add new sales" },
  { name: "edit_sales", description: "Edit existing sales" },
  { name: "delete_sales", description: "Delete sales" },
  { name: "view_purchases", description: "View purchases" },
  { name: "add_purchases", description: "Add new purchases" },
  { name: "edit_purchases", description: "Edit existing purchases" },
  { name: "delete_purchases", description: "Delete purchases" },
  { name: "view_contacts", description: "View contacts" },
  { name: "add_contacts", description: "Add new contacts" },
  { name: "edit_contacts", description: "Edit existing contacts" },
  { name: "delete_contacts", description: "Delete contacts" },
  { name: "view_reports", description: "View reports" },
  { name: "view_settings", description: "View settings" },
  { name: "edit_settings", description: "Edit settings" },
  { name: "view_dashboard", description: "View dashboard" },
  { name: "view_accounting", description: "View accounting" },
  { name: "add_accounting", description: "Add accounting entries" },
  { name: "edit_accounting", description: "Edit accounting entries" },
  { name: "delete_accounting", description: "Delete accounting entries" },
  { name: "view_credit_notes", description: "View credit notes" },
  { name: "add_credit_notes", description: "Add credit notes" },
  { name: "edit_credit_notes", description: "Edit credit notes" },
  { name: "delete_credit_notes", description: "Delete credit notes" },
];

// POST /api/system/ensure-admin-permissions - Ensure admin user has all permissions
export async function POST(req: NextRequest) {
  try {
    console.log("Starting ensure-admin-permissions process");
    
    // Find the admin user by email
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    });

    if (!adminUser) {
      console.log("Admin user not found, attempting to create");
      return NextResponse.json(
        { error: "Admin user not found. Please restore admin user first." },
        { status: 404 }
      );
    }

    console.log(`Found admin user: ${adminUser.id}, ${adminUser.name}, ${adminUser.email}, role: ${adminUser.role}`);

    // Ensure the user has ADMIN role
    if (adminUser.role !== "ADMIN") {
      console.log("Updating user role to ADMIN");
      await db.user.update({
        where: {
          id: adminUser.id,
        },
        data: {
          role: "ADMIN",
        },
      });
    }

    // Ensure all required permissions exist
    const createdPermissions = [];
    const existingPermissions = [];

    for (const permission of requiredPermissions) {
      // Check if permission exists
      let existingPermission = await db.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        // Create the permission
        console.log(`Creating permission: ${permission.name}`);
        existingPermission = await db.permission.create({
          data: permission,
        });
        createdPermissions.push(permission.name);
      } else {
        existingPermissions.push(permission.name);
      }

      // Check if admin already has this permission
      const hasPermission = await db.user.findFirst({
        where: {
          id: adminUser.id,
          permissions: {
            some: {
              id: existingPermission.id,
            },
          },
        },
      });

      if (!hasPermission) {
        console.log(`Adding permission ${permission.name} to admin user`);
        await db.user.update({
          where: {
            id: adminUser.id,
          },
          data: {
            permissions: {
              connect: {
                id: existingPermission.id,
              },
            },
          },
        });
      }
    }

    // Get the updated admin user with permissions count
    const updatedAdminUser = await db.user.findUnique({
      where: {
        id: adminUser.id,
      },
      include: {
        _count: {
          select: {
            permissions: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Admin user permissions have been ensured",
      adminUser: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
        permissionsCount: updatedAdminUser?._count.permissions || 0,
      },
      created: createdPermissions,
      existing: existingPermissions,
    });
  } catch (error) {
    console.error("Error ensuring admin permissions:", error);
    return NextResponse.json(
      { error: "Failed to ensure admin permissions" },
      { status: 500 }
    );
  }
}

// GET /api/system/ensure-admin-permissions - Get admin user permissions status
export async function GET(req: NextRequest) {
  try {
    // Find the admin user by email
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
      include: {
        permissions: true,
      },
    });

    if (!adminUser) {
      return NextResponse.json(
        { error: "Admin user not found" },
        { status: 404 }
      );
    }

    // Get all permissions
    const allPermissions = await db.permission.findMany();
    
    // Get missing permissions
    const adminPermissionIds = adminUser.permissions.map(p => p.id);
    const missingPermissions = allPermissions.filter(
      p => !adminPermissionIds.includes(p.id)
    );

    return NextResponse.json({
      success: true,
      adminUser: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
      },
      permissions: {
        total: allPermissions.length,
        assigned: adminUser.permissions.length,
        missing: missingPermissions.length,
        missingList: missingPermissions.map(p => p.name),
      },
    });
  } catch (error) {
    console.error("Error getting admin permissions status:", error);
    return NextResponse.json(
      { error: "Failed to get admin permissions status" },
      { status: 500 }
    );
  }
}
