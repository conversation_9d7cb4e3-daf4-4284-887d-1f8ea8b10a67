"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar, 
  ShoppingBag, 
  CreditCard, 
  Package, 
  Clock,
  Star,
  Gift,
  Edit,
  Trash2,
  Plus
} from "lucide-react";
import { format } from "date-fns";
import ProductRecommendations from "@/components/product-recommendations";

interface Contact {
  id: string;
  name: string;
  phone: string;
  email: string | null;
  address: string | null;
  isCustomer: boolean;
  isSupplier: boolean;
  balance: number;
  paymentTerm: number | null;
  isActive: boolean;
  createdAt: string;
  birthday: string | null;
  isVIP: boolean;
  loyaltyPoints: number;
  loyaltyTier: string | null;
}

interface Transaction {
  id: string;
  type: "SALE" | "CREDIT_NOTE";
  reference: string;
  date: string;
  amount: number;
  status: string;
  paymentStatus: string;
}

export default function CustomerDetailPage() {
  const params = useParams();
  const router = useRouter();
  const contactId = params.id as string;
  
  const [contact, setContact] = useState<Contact | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch contact data
  useEffect(() => {
    const fetchContactData = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll simulate data
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock contact data
        const mockContact: Contact = {
          id: contactId,
          name: "Ahmed Mohamed",
          phone: "01012345678",
          email: "<EMAIL>",
          address: "123 Main St, Cairo, Egypt",
          isCustomer: true,
          isSupplier: false,
          balance: 1500,
          paymentTerm: 30,
          isActive: true,
          createdAt: "2023-01-15T10:30:00Z",
          birthday: "1985-05-20",
          isVIP: true,
          loyaltyPoints: 5200,
          loyaltyTier: "GOLD"
        };
        
        // Mock transactions
        const mockTransactions: Transaction[] = [
          {
            id: "1",
            type: "SALE",
            reference: "INV-001",
            date: "2023-11-25T14:30:00Z",
            amount: 2500,
            status: "COMPLETED",
            paymentStatus: "PAID"
          },
          {
            id: "2",
            type: "SALE",
            reference: "INV-002",
            date: "2023-11-15T10:15:00Z",
            amount: 1800,
            status: "COMPLETED",
            paymentStatus: "PAID"
          },
          {
            id: "3",
            type: "CREDIT_NOTE",
            reference: "CN-001",
            date: "2023-11-10T09:45:00Z",
            amount: 500,
            status: "COMPLETED",
            paymentStatus: "PAID"
          },
          {
            id: "4",
            type: "SALE",
            reference: "INV-003",
            date: "2023-10-28T16:20:00Z",
            amount: 3200,
            status: "COMPLETED",
            paymentStatus: "PAID"
          },
          {
            id: "5",
            type: "SALE",
            reference: "INV-004",
            date: "2023-10-15T11:30:00Z",
            amount: 1200,
            status: "COMPLETED",
            paymentStatus: "UNPAID"
          }
        ];
        
        setContact(mockContact);
        setRecentTransactions(mockTransactions);
      } catch (error) {
        console.error("Error fetching contact data:", error);
        setError("Failed to load customer data. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchContactData();
  }, [contactId]);
  
  // Handle adding product to cart
  const handleAddToCart = (productId: string) => {
    // In a real implementation, this would add the product to a cart
    // For now, we'll just navigate to the new sale page
    router.push(`/dashboard/sales/new?contactId=${contactId}&productId=${productId}`);
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading customer data...</p>
        </div>
      </div>
    );
  }
  
  if (error || !contact) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-xl font-medium text-gray-900 mb-2">Customer Not Found</p>
          <p className="text-gray-500 mb-4">{error || "The customer information could not be loaded."}</p>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          Customer Profile
        </h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => router.push(`/dashboard/customers/edit/${contact.id}`)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="default" onClick={() => router.push(`/dashboard/sales/new?contactId=${contact.id}`)}>
            <Plus className="h-4 w-4 mr-2" />
            New Sale
          </Button>
        </div>
      </div>
      
      {/* Customer Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-bold">{contact.name}</h3>
                {contact.isVIP && (
                  <Badge className="bg-yellow-100 text-yellow-800 mt-1">
                    <Star className="h-3 w-3 mr-1" />
                    VIP Customer
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center text-gray-600">
                <Phone className="h-4 w-4 mr-2" />
                <span>{contact.phone}</span>
              </div>
              
              {contact.email && (
                <div className="flex items-center text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  <span>{contact.email}</span>
                </div>
              )}
              
              {contact.address && (
                <div className="flex items-start text-gray-600">
                  <MapPin className="h-4 w-4 mr-2 mt-1" />
                  <span>{contact.address}</span>
                </div>
              )}
              
              {contact.birthday && (
                <div className="flex items-center text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>Birthday: {format(new Date(contact.birthday), "MMMM d, yyyy")}</span>
                </div>
              )}
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Customer Since</span>
                <span className="font-medium">{format(new Date(contact.createdAt), "MMMM d, yyyy")}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Payment Terms</span>
                <span className="font-medium">{contact.paymentTerm || 0} days</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Current Balance</span>
                <span className={`font-medium ${contact.balance > 0 ? "text-red-600" : "text-green-600"}`}>
                  {contact.balance.toFixed(2)} ج.م
                </span>
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Loyalty Tier</span>
                <Badge 
                  style={{ 
                    backgroundColor: contact.loyaltyTier === "GOLD" ? "#FFD700" : 
                                    contact.loyaltyTier === "SILVER" ? "#C0C0C0" : 
                                    contact.loyaltyTier === "PLATINUM" ? "#E5E4E2" : 
                                    "#cd7f32",
                    color: contact.loyaltyTier === "GOLD" ? "#000" : 
                           contact.loyaltyTier === "BRONZE" ? "#000" : 
                           contact.loyaltyTier === "PLATINUM" ? "#000" : "#fff"
                  }}
                >
                  {contact.loyaltyTier || "BRONZE"}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Loyalty Points</span>
                <div className="flex items-center">
                  <Gift className="h-4 w-4 mr-1 text-purple-500" />
                  <span className="font-medium">{contact.loyaltyPoints}</span>
                </div>
              </div>
              
              <Button 
                variant="outline" 
                className="w-full mt-2"
                onClick={() => router.push(`/dashboard/customers/${contact.id}/loyalty`)}
              >
                View Loyalty Details
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>
              Recent sales and credit notes for this customer
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentTransactions.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingBag className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                <p className="text-gray-500">No transactions found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div 
                    key={transaction.id} 
                    className="flex items-center justify-between border-b pb-4 cursor-pointer hover:bg-gray-50 p-2 rounded"
                    onClick={() => router.push(
                      transaction.type === "SALE" 
                        ? `/dashboard/sales/${transaction.id}` 
                        : `/dashboard/sales/credit-note/${transaction.id}`
                    )}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-full ${
                        transaction.type === 'SALE' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        {transaction.type === 'SALE' ? (
                          <ShoppingBag className="h-5 w-5 text-green-600" />
                        ) : (
                          <Package className="h-5 w-5 text-red-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {transaction.type === 'SALE' ? 'Sale' : 'Credit Note'}: {transaction.reference}
                        </p>
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          {format(new Date(transaction.date), 'MMM d, yyyy')}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${
                        transaction.type === 'SALE' ? 'text-gray-900' : 'text-red-600'
                      }`}>
                        {transaction.type === 'SALE' ? '' : '-'}{transaction.amount.toFixed(2)} ج.م
                      </p>
                      <Badge 
                        className={
                          transaction.paymentStatus === 'PAID' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }
                      >
                        {transaction.paymentStatus}
                      </Badge>
                    </div>
                  </div>
                ))}
                
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => router.push(`/dashboard/reports/customers?contactId=${contact.id}`)}
                >
                  View All Transactions
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Product Recommendations */}
      <div className="mb-6">
        <ProductRecommendations 
          contactId={contact.id} 
          onAddToCart={handleAddToCart}
          limit={4}
        />
      </div>
    </div>
  );
}
