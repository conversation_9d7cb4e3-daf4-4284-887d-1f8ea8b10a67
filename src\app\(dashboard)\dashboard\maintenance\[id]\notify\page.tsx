"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import React from "react";
import {
  Loader2,
  ArrowLeft,
  Send,
  MessageSquare,
  Mail,
  Phone,
  Check,
  AlertTriangle
} from "lucide-react";
import { getStatusLabel } from "@/lib/maintenance";
import { format } from "date-fns";

interface MaintenanceService {
  id: string;
  serviceNumber: string;
  deviceType: string;
  brand: string;
  model?: string;
  status: string;
  receivedDate: string;
  contact: {
    id: string;
    name: string;
    phone: string;
    email?: string;
  };
}

export default function NotifyCustomerPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const id = unwrappedParams.id;

  const router = useRouter();
  const [service, setService] = useState<MaintenanceService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [notificationType, setNotificationType] = useState<"sms" | "email" | "both">("sms");

  // Form state
  const [formData, setFormData] = useState({
    subject: "",
    message: "",
  });

  // Fetch maintenance service details
  useEffect(() => {
    const fetchServiceDetails = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/maintenance/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch maintenance service details");
        }

        const data = await response.json();
        setService(data);

        // Set default subject and message
        const statusText = getStatusLabel(data.status);
        const subject = `Update on your maintenance service #${data.serviceNumber}`;
        const message = generateDefaultMessage(data);

        setFormData({
          subject,
          message,
        });
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceDetails();
  }, [id]);

  // Generate default message based on service status
  const generateDefaultMessage = (service: MaintenanceService) => {
    const statusText = getStatusLabel(service.status);
    const deviceInfo = `${service.deviceType} ${service.brand} ${service.model || ""}`.trim();

    let message = `Dear ${service.contact.name},\n\n`;

    switch (service.status) {
      case "RECEIVED":
        message += `We have received your ${deviceInfo} for maintenance (Service #${service.serviceNumber}). We will diagnose the issue and update you soon.`;
        break;
      case "IN_PROGRESS":
        message += `Your ${deviceInfo} (Service #${service.serviceNumber}) is currently being worked on by our technicians. We will update you once the maintenance is complete.`;
        break;
      case "WAITING_FOR_PARTS":
        message += `Your ${deviceInfo} (Service #${service.serviceNumber}) requires additional parts that we are currently ordering. We will update you once the parts arrive and maintenance continues.`;
        break;
      case "COMPLETED":
        message += `Good news! The maintenance for your ${deviceInfo} (Service #${service.serviceNumber}) has been completed. You can pick it up at your convenience.`;
        break;
      case "DELIVERED":
        message += `This is a confirmation that your ${deviceInfo} (Service #${service.serviceNumber}) has been delivered to you. Thank you for choosing our service.`;
        break;
      default:
        message += `This is an update regarding your ${deviceInfo} (Service #${service.serviceNumber}). The current status is: ${statusText}.`;
    }

    message += `\n\nIf you have any questions, please contact us.\n\nThank you,\nVERO ERP Support Team`;

    return message;
  };

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle notification type change
  const handleNotificationTypeChange = (type: "sms" | "email" | "both") => {
    setNotificationType(type);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSending(true);
    setError(null);
    setSuccess(null);

    try {
      if (!service) {
        throw new Error("Service information not available");
      }

      // Validate based on notification type
      if ((notificationType === "email" || notificationType === "both") && !service.contact.email) {
        throw new Error("Customer email is not available for email notification");
      }

      // Prepare notification data
      const notificationData = {
        serviceId: service.id,
        contactId: service.contact.id,
        subject: formData.subject,
        message: formData.message,
        notificationType,
      };

      // Submit to API
      const response = await fetch("/api/maintenance/notify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(notificationData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send notification");
      }

      setSuccess("Notification sent successfully!");

      // Redirect after a short delay
      setTimeout(() => {
        router.push(`/dashboard/maintenance/${id}`);
      }, 2000);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsSending(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  if (!service) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 text-yellow-700 p-4 rounded-md">
          Maintenance service not found
        </div>
        <div className="mt-4">
          <Link
            href="/dashboard/maintenance"
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            href={`/dashboard/maintenance/${id}`}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Notify Customer</h1>
            <p className="text-sm text-gray-500">
              {service.serviceNumber} - {service.contact.name}
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md flex items-start">
          <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <div>{error}</div>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-100 text-green-700 rounded-md flex items-start">
          <Check className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <div>{success}</div>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6">
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Name</p>
                <p className="text-base font-medium text-gray-900">{service.contact.name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Phone</p>
                <p className="text-base font-medium text-gray-900 flex items-center">
                  <Phone className="h-4 w-4 mr-1 text-gray-400" />
                  {service.contact.phone}
                </p>
              </div>
              {service.contact.email && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Email</p>
                  <p className="text-base font-medium text-gray-900 flex items-center">
                    <Mail className="h-4 w-4 mr-1 text-gray-400" />
                    {service.contact.email}
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Notification Method</h2>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => handleNotificationTypeChange("sms")}
                className={`px-4 py-2 rounded-md flex items-center ${
                  notificationType === "sms"
                    ? "bg-indigo-100 text-indigo-800 border border-indigo-200"
                    : "bg-gray-100 text-gray-800 border border-gray-200"
                }`}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                SMS
              </button>
              <button
                type="button"
                onClick={() => handleNotificationTypeChange("email")}
                className={`px-4 py-2 rounded-md flex items-center ${
                  notificationType === "email"
                    ? "bg-indigo-100 text-indigo-800 border border-indigo-200"
                    : "bg-gray-100 text-gray-800 border border-gray-200"
                } ${!service.contact.email ? "opacity-50 cursor-not-allowed" : ""}`}
                disabled={!service.contact.email}
              >
                <Mail className="h-4 w-4 mr-2" />
                Email
              </button>
              <button
                type="button"
                onClick={() => handleNotificationTypeChange("both")}
                className={`px-4 py-2 rounded-md flex items-center ${
                  notificationType === "both"
                    ? "bg-indigo-100 text-indigo-800 border border-indigo-200"
                    : "bg-gray-100 text-gray-800 border border-gray-200"
                } ${!service.contact.email ? "opacity-50 cursor-not-allowed" : ""}`}
                disabled={!service.contact.email}
              >
                <MessageSquare className="h-4 w-4 mr-1" />
                <Mail className="h-4 w-4 mr-2" />
                Both
              </button>
            </div>
            {!service.contact.email && (
              <p className="text-sm text-yellow-600 mt-2 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-1" />
                Email notification is not available because customer email is not provided.
              </p>
            )}
          </div>

          <form onSubmit={handleSubmit}>
            {(notificationType === "email" || notificationType === "both") && (
              <div className="mb-4">
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                  Subject
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                />
              </div>
            )}

            <div className="mb-6">
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                rows={8}
                className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                required
              ></textarea>
              <p className="text-sm text-gray-500 mt-1">
                {notificationType === "sms" && "SMS messages should be concise and under 160 characters for best delivery."}
              </p>
            </div>

            <div className="flex justify-end">
              <Link
                href={`/dashboard/maintenance/${id}`}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium mr-2"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSending}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium flex items-center"
              >
                {isSending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Notification
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
