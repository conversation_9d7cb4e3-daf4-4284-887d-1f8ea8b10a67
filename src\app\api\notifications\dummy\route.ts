import { NextRequest, NextResponse } from "next/server";

// GET /api/notifications/dummy - Return dummy notifications
export async function GET(req: NextRequest) {
  try {
    // Create dummy notifications
    const notifications = [
      {
        id: "1",
        userId: "user-id",
        type: "SYSTEM",
        title: "System Notification",
        message: "This is a system notification",
        link: "/dashboard",
        read: false,
        metadata: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "2",
        userId: "user-id",
        type: "LOYALTY",
        title: "Loyalty Points",
        message: "You have earned 100 loyalty points",
        link: "/dashboard/loyalty",
        read: true,
        metadata: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    // Return dummy data
    return NextResponse.json({
      notifications,
      unreadCount: 1,
    });
  } catch (error) {
    console.error("Error in dummy notifications:", error);
    return NextResponse.json(
      { error: "Failed to get dummy notifications" },
      { status: 500 }
    );
  }
}
