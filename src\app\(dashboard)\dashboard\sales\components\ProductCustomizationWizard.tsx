"use client";

import { useState, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import {
  ChevronRight,
  ChevronLeft,
  Check,
  X,
  Monitor,
  Cpu,
  HardDrive,
  Save,
  Package,
  DollarSign,
  AlertTriangle,
  Minus,
  Plus
} from "lucide-react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

// Component types - using string type to support dynamic component types
type ComponentType = string;

// Component interface
interface Component {
  id: string;
  name: string;
  type: ComponentType;
  price: number;
  stock: number;
  capacity?: string; // For storage and RAM
  speed?: string; // For RAM and CPU
  description?: string;
  count?: number; // For multiple RAM sticks
  imageUrl?: string; // For component image
}

// Type for selected components map
interface SelectedComponentsMap {
  [key: string]: Component;
}

// Product interface
interface Product {
  id: string;
  name: string;
  basePrice: number;
  costPrice: number;
  isCustomizable: boolean;
  imageUrl?: string; // For product image
  availableComponents?: {
    [key in ComponentType]?: Component[];
  };
}

interface ProductCustomizationWizardProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  onConfirm: (customizedProduct: CustomizedProduct, existingItemId?: string) => void;
}

export interface CustomizedProduct {
  productId: string;
  productName: string;
  basePrice: number;
  costPrice: number;
  totalPrice: number;
  selectedComponents: SelectedComponentsMap;
  editingItemId?: string;
}

// Empty components data structure
const emptyComponents: { [key: string]: Component[] } = {};

// Step interface
interface Step {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

export default function ProductCustomizationWizard({
  isOpen,
  onClose,
  product,
  onConfirm,
}: ProductCustomizationWizardProps) {
  // Check if we're editing an existing item
  const isEditing = (product as any).editingItemId !== undefined;
  const editingItemId = (product as any).editingItemId;
  const currentComponents = (product as any).currentComponents;

  // State for available components from API
  const [apiComponents, setApiComponents] = useState<{ [key in ComponentType]?: Component[] }>(emptyComponents);
  const [isLoadingComponents, setIsLoadingComponents] = useState(false);

  // Define wizard steps
  const steps: Step[] = [
    {
      id: "product",
      title: "Base Product",
      description: "Review the base product specifications",
      icon: <Package className="h-6 w-6" />
    },
    {
      id: "processor",
      title: "Processor",
      description: "Select CPU and related components",
      icon: <Cpu className="h-6 w-6" />
    },
    {
      id: "memory",
      title: "Memory",
      description: "Configure RAM options",
      icon: <Save className="h-6 w-6" />
    },
    {
      id: "storage",
      title: "Storage",
      description: "Configure SSD and HDD options",
      icon: <HardDrive className="h-6 w-6" />
    },
    {
      id: "additional",
      title: "Additional",
      description: "Add any other components",
      icon: <Package className="h-6 w-6" />
    },
    {
      id: "pricing",
      title: "Pricing",
      description: "Review and set final pricing",
      icon: <DollarSign className="h-6 w-6" />
    }
  ];

  // Current step state
  const [currentStep, setCurrentStep] = useState(0);

  // Initialize selected components with values from existing item if available
  const [selectedComponents, setSelectedComponents] = useState<SelectedComponentsMap>(() => {
    // Log the current components for debugging
    console.log("Initializing with current components:", currentComponents);

    if (isEditing && currentComponents) {
      // Make sure we have a valid object with all properties
      const processedComponents: SelectedComponentsMap = {};

      try {
        // Handle if currentComponents is a string (JSON)
        let componentsObj = currentComponents;
        if (typeof currentComponents === 'string') {
          try {
            componentsObj = JSON.parse(currentComponents);
            console.log("Parsed components from JSON string:", componentsObj);
          } catch (e) {
            console.error("Failed to parse components JSON:", e);
          }
        }

        // Process each component to ensure it has all required properties
        if (componentsObj && typeof componentsObj === 'object') {
          Object.entries(componentsObj).forEach(([type, component]) => {
            if (component && typeof component === 'object') {
              processedComponents[type as ComponentType] = {
                ...component,
                type: type as ComponentType, // Ensure type is set correctly
                count: component.count || 1, // Ensure count is set for RAM
              };
            }
          });
        }
      } catch (error) {
        console.error("Error processing components:", error);
      }

      console.log("Processed components for editing:", processedComponents);
      return processedComponents;
    }

    return {};
  });

  // Set initial price based on existing item or product base price
  const initialPrice = isEditing && (product as any).totalPrice ? (product as any).totalPrice : product.basePrice;
  const [totalPrice, setTotalPrice] = useState(initialPrice);

  // Set available components from product, API, or use empty components
  const availableComponents = product.availableComponents || apiComponents || emptyComponents;

  // State for custom price - initialize from existing item if editing
  const [useCustomPrice, setUseCustomPrice] = useState(isEditing);
  const [customPrice, setCustomPrice] = useState(initialPrice);
  const [costPrice, setCostPrice] = useState(isEditing && (product as any).costPrice ? (product as any).costPrice : product.basePrice);

  // Fetch component types from API
  useEffect(() => {
    const fetchComponentTypes = async () => {
      setIsLoadingComponents(true);
      try {
        const response = await fetch('/api/component-types');
        if (response.ok) {
          const componentTypes = await response.json();
          console.log("Component types:", componentTypes);

          // Build components object
          const componentsByType: { [key: string]: Component[] } = {};

          // Initialize with empty arrays for each component type
          componentTypes.forEach((type: any) => {
            componentsByType[type.name] = [];
          });

          // Fetch all components in a single request
          const productsResponse = await fetch('/api/products?isComponent=true');
          if (productsResponse.ok) {
            const allComponents = await productsResponse.json();
            console.log("All components:", allComponents);

            // Group components by type
            allComponents.forEach((component: any) => {
              console.log("Processing component:", component.name, "Type:", component.componentType);

              // Handle case sensitivity and normalize component types
              let normalizedType = component.componentType;
              if (normalizedType) {
                // Convert to uppercase for standardization
                normalizedType = normalizedType.toUpperCase();

                // Map common variations to standard types
                const typeMapping: {[key: string]: string} = {
                  "SSD": "SSD",
                  "HDD": "HDD",
                  "NVME": "NVME",
                  "RAM": "RAM",
                  "CPU": "CPU",
                  "GPU": "GPU",
                  "SOLID STATE DRIVE": "SSD",
                  "HARD DISK DRIVE": "HDD",
                  "HARD DRIVE": "HDD",
                  "SOLID STATE": "SSD",
                  "MEMORY": "RAM",
                  "PROCESSOR": "CPU",
                  "GRAPHICS CARD": "GPU"
                };

                normalizedType = typeMapping[normalizedType] || normalizedType;

                // Check if this normalized type exists in our component types
                if (!componentsByType[normalizedType] && Object.keys(componentsByType).some(key => key.toUpperCase() === normalizedType)) {
                  // Find the correct case-sensitive key
                  const correctKey = Object.keys(componentsByType).find(key => key.toUpperCase() === normalizedType);
                  if (correctKey) {
                    normalizedType = correctKey;
                  }
                }
              }

              if (normalizedType && componentsByType[normalizedType]) {
                // Calculate total stock
                const totalStock = component.inventory.reduce((sum: number, inv: any) => sum + inv.quantity, 0);

                // Extract capacity and speed from specifications
                let capacity, speed;
                if (component.specifications && Array.isArray(component.specifications)) {
                  capacity = component.specifications.find((spec: any) =>
                    spec.name.toLowerCase() === 'capacity')?.value;
                  speed = component.specifications.find((spec: any) =>
                    spec.name.toLowerCase() === 'speed')?.value;
                }

                // Add component to the appropriate type array
                componentsByType[normalizedType].push({
                  id: component.id,
                  name: component.name,
                  type: normalizedType,
                  price: component.basePrice,
                  stock: totalStock,
                  capacity,
                  speed,
                  description: component.description
                });

                console.log(`Added component ${component.name} to ${normalizedType} group`);
              } else {
                console.log(`Could not add component ${component.name} - type ${component.componentType} not found in available types`);
              }
            });
          }

          console.log("Grouped components by type:", componentsByType);
          setApiComponents(componentsByType);
        }
      } catch (error) {
        console.error("Error fetching component types:", error);
      } finally {
        setIsLoadingComponents(false);
      }
    };

    if (isOpen && !product.availableComponents) {
      fetchComponentTypes();
    }
  }, [isOpen, product.availableComponents]);

  // Update total price when components change
  useEffect(() => {
    let newCostPrice = product.basePrice;

    Object.values(selectedComponents).forEach((component) => {
      if (component) {
        newCostPrice += component.price;
      }
    });

    setCostPrice(newCostPrice);

    if (!useCustomPrice) {
      setTotalPrice(newCostPrice);
      setCustomPrice(newCostPrice);
    }
  }, [selectedComponents, product.basePrice, useCustomPrice]);

  // Handle custom price change
  const handleCustomPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value)) {
      setCustomPrice(value);
      setTotalPrice(value);
    }
  };

  // Handle custom price blur (validate when focus leaves the field)
  const handleCustomPriceBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (isNaN(value) || value < costPrice) {
      alert(`Price cannot be lower than cost price (${costPrice.toFixed(2)} ج.م)`);
      setCustomPrice(costPrice);
      setTotalPrice(costPrice);
    }
  };

  // Calculate total capacity for RAM
  const calculateTotalCapacity = (component: Component) => {
    if (component.type === "RAM" && component.capacity && component.count) {
      // Extract the number from the capacity string (e.g., "8GB" -> 8)
      const capacityMatch = component.capacity.match(/(\d+)/);
      if (capacityMatch && capacityMatch[1]) {
        const capacityValue = parseInt(capacityMatch[1]);
        const unit = component.capacity.replace(/\d+/g, '');
        return `${capacityValue * component.count}${unit}`;
      }
    }
    return component.capacity;
  };

  // Store original components for restoration if needed
  const [originalComponents, setOriginalComponents] = useState<SelectedComponentsMap>({});

  // Initialize original components when the modal opens
  useEffect(() => {
    if (isOpen && isEditing && currentComponents) {
      console.log("Storing original components for potential restoration:", currentComponents);
      setOriginalComponents(selectedComponents);
    }
  }, [isOpen, isEditing, currentComponents]);

  // Handle component selection
  const handleComponentChange = (type: ComponentType, componentId: string) => {
    console.log(`Changing component ${type} to ID: ${componentId}`);

    // Check if we're clicking on the already selected component
    if (selectedComponents[type]?.id === componentId) {
      console.log(`Removing ${type} component because it was clicked again`);
      // If clicking on the same component, remove it (toggle behavior)
      const newSelectedComponents = { ...selectedComponents };
      delete newSelectedComponents[type];
      setSelectedComponents(newSelectedComponents);
      return;
    }

    if (componentId === "") {
      // Remove component if empty selection
      console.log(`Removing ${type} component because empty ID was provided`);
      const newSelectedComponents = { ...selectedComponents };
      delete newSelectedComponents[type];
      setSelectedComponents(newSelectedComponents);
      return;
    }

    const component = availableComponents[type]?.find(c => c.id === componentId);

    if (component) {
      console.log(`Setting ${type} component to:`, component.name);
      // For RAM, set default count to 1
      if (type === "RAM") {
        setSelectedComponents({
          ...selectedComponents,
          [type]: {
            ...component,
            count: 1
          },
        });
      } else {
        setSelectedComponents({
          ...selectedComponents,
          [type]: component,
        });
      }
    }
  };

  // Handle RAM count change
  const handleRamCountChange = (count: number) => {
    if (selectedComponents["RAM"]) {
      setSelectedComponents({
        ...selectedComponents,
        "RAM": {
          ...selectedComponents["RAM"],
          count: count
        }
      });
    }
  };

  // Handle confirmation
  const handleConfirm = () => {
    console.log("Confirming with selected components:", selectedComponents);

    // Components are optional now, so we don't need to validate
    // if (Object.keys(selectedComponents).length === 0) {
    //   alert("Please select at least one component before confirming.");
    //   return;
    // }

    const customizedProduct: CustomizedProduct = {
      productId: product.id,
      productName: product.name,
      basePrice: product.basePrice,
      costPrice: costPrice,
      totalPrice,
      selectedComponents,
    };

    if (isEditing && editingItemId) {
      console.log("Confirming edit for item:", editingItemId);
      customizedProduct.editingItemId = editingItemId;
    }

    // Call the onConfirm callback with the customized product
    onConfirm(customizedProduct, editingItemId);

    // Close the modal
    onClose();
  };

  // Navigate to next step
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Check if current step is valid
  const isStepValid = () => {
    // Add validation logic for each step
    switch (steps[currentStep].id) {
      case "processor":
        // Processor step is optional
        return true;
      case "memory":
        // Memory step is optional
        return true;
      case "storage":
        // Storage step is optional
        return true;
      case "additional":
        // Additional components step is optional
        return true;
      case "pricing":
        // Pricing step requires a valid price
        return totalPrice >= costPrice;
      default:
        return true;
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={async () => {
        // When the modal is closed without confirmation, we need to restore any components that were deducted
        console.log("Modal closed without confirmation");

        // If we're editing an existing item and have components, we need to restore them
        if (isEditing && editingItemId) {
          console.log("Restoring components for item:", editingItemId);

          try {
            // Get the sale ID from the editingItemId (format: "item-{timestamp}")
            // Since we don't have direct access to the sale ID, we'll use the current URL
            const urlParts = window.location.pathname.split('/');
            const saleId = urlParts[urlParts.indexOf('sales') + 1];

            if (saleId) {
              console.log(`Calling restore-inventory API for sale ${saleId}`);

              // Determine which components need to be restored
              // We need to restore any components that were added or modified
              const componentsToRestore: any[] = [];

              // Check for components that were added or modified
              Object.entries(selectedComponents).forEach(([type, component]: [string, any]) => {
                const originalComponent = originalComponents[type];

                // If this component wasn't in the original set or has a different count
                if (!originalComponent ||
                    originalComponent.id !== component.id ||
                    (originalComponent.count || 1) !== (component.count || 1)) {

                  const count = component.count || 1;
                  componentsToRestore.push({
                    id: component.id,
                    type,
                    name: component.name,
                    count: count,
                    warehouseId: (product as any).warehouseId, // Get warehouseId from product
                  });
                }
              });

              // Only call the API if we have components to restore
              if (componentsToRestore.length > 0) {
                console.log("Components to restore:", componentsToRestore);

                // Call the API to restore inventory
                const response = await fetch(`/api/sales/${saleId}/restore-inventory`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    components: componentsToRestore
                  }),
                });

                if (!response.ok) {
                  console.error("Failed to restore inventory:", await response.text());
                } else {
                  console.log("Inventory restored successfully");
                }
              } else {
                console.log("No components need to be restored");
              }
            }
          } catch (error) {
            console.error("Error restoring inventory:", error);
          }
        }

        onClose();
      }}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900"
                >
                  {isEditing ? `Edit ${product.name}` : `Customize ${product.name}`}
                </Dialog.Title>

                {/* Wizard Steps */}
                <div className="mt-4 mb-6">
                  <div className="flex justify-between items-center">
                    {steps.map((step, index) => (
                      <div key={step.id} className="flex flex-col items-center">
                        <div
                          className={`flex items-center justify-center w-10 h-10 rounded-full ${
                            index < currentStep
                              ? 'bg-green-100 text-green-600'
                              : index === currentStep
                                ? 'bg-blue-100 text-blue-600'
                                : 'bg-gray-100 text-gray-400'
                          }`}
                        >
                          {index < currentStep ? (
                            <Check className="h-5 w-5" />
                          ) : (
                            step.icon
                          )}
                        </div>
                        <span className={`text-xs mt-1 ${
                          index === currentStep ? 'font-medium text-blue-600' : 'text-gray-500'
                        }`}>
                          {step.title}
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="relative mt-2">
                    <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 rounded"></div>
                    <div
                      className="absolute top-0 left-0 h-1 bg-blue-500 rounded transition-all duration-300"
                      style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
                    ></div>
                  </div>
                </div>

                {/* Step Content */}
                <div className="mt-6">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentStep}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="min-h-[300px]"
                    >
                      {/* Base Product Step */}
                      {steps[currentStep].id === "product" && (
                        <div className="grid grid-cols-2 gap-6">
                          <div className="bg-gray-50 p-4 rounded-lg flex items-center justify-center">
                            {product.imageUrl ? (
                              <img
                                src={product.imageUrl}
                                alt={product.name}
                                className="max-h-[200px] object-contain"
                              />
                            ) : (
                              <Monitor className="h-32 w-32 text-gray-300" />
                            )}
                          </div>
                          <div>
                            <h4 className="text-lg font-medium">{product.name}</h4>
                            <p className="text-sm text-gray-500 mt-2">Base configuration</p>

                            <div className="mt-4 space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Base Price:</span>
                                <span className="text-sm font-medium">{product.basePrice.toFixed(2)} ج.م</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Customizable:</span>
                                <span className="text-sm font-medium">{product.isCustomizable ? "Yes" : "No"}</span>
                              </div>
                            </div>

                            <div className="mt-6">
                              <p className="text-sm text-gray-600">
                                You can customize this product by adding or changing components in the next steps.
                                Each component you select will affect the final price.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Processor Step */}
                      {steps[currentStep].id === "processor" && (
                        <div className="space-y-6">
                          {/* CPU Selection */}
                          {availableComponents["CPU"]?.length > 0 && (
                            <div>
                              <div className="flex justify-between items-center mb-2">
                                <label className="block text-sm font-medium text-gray-700">CPU</label>
                                {selectedComponents["CPU"] && (
                                  <button
                                    type="button"
                                    className="text-xs text-red-600 hover:text-red-800 flex items-center"
                                    onClick={() => {
                                      // Remove CPU component
                                      const newSelectedComponents = { ...selectedComponents };
                                      delete newSelectedComponents["CPU"];
                                      setSelectedComponents(newSelectedComponents);
                                    }}
                                  >
                                    <X className="h-3 w-3 mr-1" />
                                    Remove
                                  </button>
                                )}
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {availableComponents["CPU"].map((component) => (
                                  <div
                                    key={component.id}
                                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                                      selectedComponents["CPU"]?.id === component.id
                                        ? 'border-blue-500 bg-blue-50'
                                        : 'border-gray-200 hover:border-blue-300'
                                    }`}
                                    onClick={() => handleComponentChange("CPU", component.id)}
                                  >
                                    <div className="flex justify-between items-start">
                                      <div>
                                        <h5 className="font-medium text-sm">{component.name}</h5>
                                        {component.speed && (
                                          <p className="text-xs text-gray-500 mt-1">Speed: {component.speed}</p>
                                        )}
                                      </div>
                                      <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                                    </div>
                                    {component.stock <= 0 && (
                                      <div className="mt-2 text-xs text-red-600 flex items-center">
                                        <AlertTriangle className="h-3 w-3 mr-1" />
                                        Out of stock
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* GPU Selection */}
                          {availableComponents["GPU"]?.length > 0 && (
                            <div className="mt-6">
                              <div className="flex justify-between items-center mb-2">
                                <label className="block text-sm font-medium text-gray-700">Graphics Card</label>
                                {selectedComponents["GPU"] && (
                                  <button
                                    type="button"
                                    className="text-xs text-red-600 hover:text-red-800 flex items-center"
                                    onClick={() => {
                                      // Remove GPU component
                                      const newSelectedComponents = { ...selectedComponents };
                                      delete newSelectedComponents["GPU"];
                                      setSelectedComponents(newSelectedComponents);
                                    }}
                                  >
                                    <X className="h-3 w-3 mr-1" />
                                    Remove
                                  </button>
                                )}
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {availableComponents["GPU"].map((component) => (
                                  <div
                                    key={component.id}
                                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                                      selectedComponents["GPU"]?.id === component.id
                                        ? 'border-blue-500 bg-blue-50'
                                        : 'border-gray-200 hover:border-blue-300'
                                    }`}
                                    onClick={() => handleComponentChange("GPU", component.id)}
                                  >
                                    <div className="flex justify-between items-start">
                                      <div>
                                        <h5 className="font-medium text-sm">{component.name}</h5>
                                        {component.description && (
                                          <p className="text-xs text-gray-500 mt-1">{component.description}</p>
                                        )}
                                      </div>
                                      <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                                    </div>
                                    {component.stock <= 0 && (
                                      <div className="mt-2 text-xs text-red-600 flex items-center">
                                        <AlertTriangle className="h-3 w-3 mr-1" />
                                        Out of stock
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Memory Step */}
                      {steps[currentStep].id === "memory" && (
                        <div className="space-y-6">
                          {/* RAM Selection */}
                          {availableComponents["RAM"]?.length > 0 && (
                            <div>
                              <div className="flex justify-between items-center mb-2">
                                <label className="block text-sm font-medium text-gray-700">RAM</label>
                                {selectedComponents["RAM"] && (
                                  <div className="flex items-center space-x-4">
                                    <button
                                      type="button"
                                      className="text-xs text-red-600 hover:text-red-800 flex items-center"
                                      onClick={() => {
                                        // Remove RAM component
                                        const newSelectedComponents = { ...selectedComponents };
                                        delete newSelectedComponents["RAM"];
                                        setSelectedComponents(newSelectedComponents);
                                      }}
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      Remove
                                    </button>

                                    <div className="flex items-center space-x-2">
                                      <span className="text-xs text-gray-500">Number of sticks:</span>
                                      <div className="flex border rounded overflow-hidden">
                                        <button
                                          className="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600"
                                          onClick={() => handleRamCountChange(Math.max(1, (selectedComponents["RAM"]?.count || 1) - 1))}
                                        >
                                          <Minus className="h-3 w-3" />
                                        </button>
                                        <span className="px-3 py-1 text-sm">{selectedComponents["RAM"]?.count || 1}</span>
                                        <button
                                          className="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600"
                                          onClick={() => handleRamCountChange((selectedComponents["RAM"]?.count || 1) + 1)}
                                        >
                                          <Plus className="h-3 w-3" />
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {availableComponents["RAM"].map((component) => (
                                  <div
                                    key={component.id}
                                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                                      selectedComponents["RAM"]?.id === component.id
                                        ? 'border-blue-500 bg-blue-50'
                                        : 'border-gray-200 hover:border-blue-300'
                                    }`}
                                    onClick={() => handleComponentChange("RAM", component.id)}
                                  >
                                    <div className="flex justify-between items-start">
                                      <div>
                                        <h5 className="font-medium text-sm">{component.name}</h5>
                                        {component.capacity && (
                                          <p className="text-xs text-gray-500 mt-1">Capacity: {component.capacity}</p>
                                        )}
                                        {component.speed && (
                                          <p className="text-xs text-gray-500">Speed: {component.speed}</p>
                                        )}
                                      </div>
                                      <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                                    </div>
                                    {component.stock <= 0 && (
                                      <div className="mt-2 text-xs text-red-600 flex items-center">
                                        <AlertTriangle className="h-3 w-3 mr-1" />
                                        Out of stock
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Storage Step */}
                      {steps[currentStep].id === "storage" && (
                        <div className="space-y-6">
                          {/* Storage Selection */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* SSD Selection */}
                            {availableComponents["SSD"]?.length > 0 && (
                              <div>
                                <div className="flex justify-between items-center mb-2">
                                  <label className="block text-sm font-medium text-gray-700">SSD</label>
                                  {selectedComponents["SSD"] && (
                                    <button
                                      type="button"
                                      className="text-xs text-red-600 hover:text-red-800 flex items-center"
                                      onClick={() => {
                                        // Remove SSD component
                                        const newSelectedComponents = { ...selectedComponents };
                                        delete newSelectedComponents["SSD"];
                                        setSelectedComponents(newSelectedComponents);
                                      }}
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      Remove
                                    </button>
                                  )}
                                </div>
                                <div className="space-y-3">
                                  {availableComponents["SSD"].map((component) => (
                                    <div
                                      key={component.id}
                                      className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                                        selectedComponents["SSD"]?.id === component.id
                                          ? 'border-blue-500 bg-blue-50'
                                          : 'border-gray-200 hover:border-blue-300'
                                      }`}
                                      onClick={() => handleComponentChange("SSD", component.id)}
                                    >
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <h5 className="font-medium text-sm">{component.name}</h5>
                                          {component.capacity && (
                                            <p className="text-xs text-gray-500 mt-1">Capacity: {component.capacity}</p>
                                          )}
                                        </div>
                                        <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* HDD Selection */}
                            {availableComponents["HDD"]?.length > 0 && (
                              <div>
                                <div className="flex justify-between items-center mb-2">
                                  <label className="block text-sm font-medium text-gray-700">HDD</label>
                                  {selectedComponents["HDD"] && (
                                    <button
                                      type="button"
                                      className="text-xs text-red-600 hover:text-red-800 flex items-center"
                                      onClick={() => {
                                        // Remove HDD component
                                        const newSelectedComponents = { ...selectedComponents };
                                        delete newSelectedComponents["HDD"];
                                        setSelectedComponents(newSelectedComponents);
                                      }}
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      Remove
                                    </button>
                                  )}
                                </div>
                                <div className="space-y-3">
                                  {availableComponents["HDD"].map((component) => (
                                    <div
                                      key={component.id}
                                      className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                                        selectedComponents["HDD"]?.id === component.id
                                          ? 'border-blue-500 bg-blue-50'
                                          : 'border-gray-200 hover:border-blue-300'
                                      }`}
                                      onClick={() => handleComponentChange("HDD", component.id)}
                                    >
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <h5 className="font-medium text-sm">{component.name}</h5>
                                          {component.capacity && (
                                            <p className="text-xs text-gray-500 mt-1">Capacity: {component.capacity}</p>
                                          )}
                                        </div>
                                        <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* NVME Selection */}
                            {availableComponents["NVME"]?.length > 0 && (
                              <div>
                                <div className="flex justify-between items-center mb-2">
                                  <label className="block text-sm font-medium text-gray-700">NVME</label>
                                  {selectedComponents["NVME"] && (
                                    <button
                                      type="button"
                                      className="text-xs text-red-600 hover:text-red-800 flex items-center"
                                      onClick={() => {
                                        // Remove NVME component
                                        const newSelectedComponents = { ...selectedComponents };
                                        delete newSelectedComponents["NVME"];
                                        setSelectedComponents(newSelectedComponents);
                                      }}
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      Remove
                                    </button>
                                  )}
                                </div>
                                <div className="space-y-3">
                                  {availableComponents["NVME"].map((component) => (
                                    <div
                                      key={component.id}
                                      className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                                        selectedComponents["NVME"]?.id === component.id
                                          ? 'border-blue-500 bg-blue-50'
                                          : 'border-gray-200 hover:border-blue-300'
                                      }`}
                                      onClick={() => handleComponentChange("NVME", component.id)}
                                    >
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <h5 className="font-medium text-sm">{component.name}</h5>
                                          {component.capacity && (
                                            <p className="text-xs text-gray-500 mt-1">Capacity: {component.capacity}</p>
                                          )}
                                        </div>
                                        <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Additional Components Step */}
                      {steps[currentStep].id === "additional" && (
                        <div className="space-y-6">
                          {/* Filter out CPU, GPU, RAM, SSD, HDD, NVME which are handled in other steps */}
                          {Object.entries(availableComponents)
                            .filter(([type]) => !["CPU", "GPU", "RAM", "SSD", "HDD", "NVME"].includes(type))
                            .map(([type, components]) => (
                              <div key={type}>
                                <div className="flex justify-between items-center mb-2">
                                  <label className="block text-sm font-medium text-gray-700">{type}</label>
                                  {selectedComponents[type as ComponentType] && (
                                    <button
                                      type="button"
                                      className="text-xs text-red-600 hover:text-red-800 flex items-center"
                                      onClick={() => {
                                        // Remove this component type
                                        const newSelectedComponents = { ...selectedComponents };
                                        delete newSelectedComponents[type as ComponentType];
                                        setSelectedComponents(newSelectedComponents);
                                      }}
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      Remove
                                    </button>
                                  )}
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  {components.map((component) => (
                                    <div
                                      key={component.id}
                                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                                        selectedComponents[type as ComponentType]?.id === component.id
                                          ? 'border-blue-500 bg-blue-50'
                                          : 'border-gray-200 hover:border-blue-300'
                                      }`}
                                      onClick={() => handleComponentChange(type as ComponentType, component.id)}
                                    >
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <h5 className="font-medium text-sm">{component.name}</h5>
                                          {component.description && (
                                            <p className="text-xs text-gray-500 mt-1">{component.description}</p>
                                          )}
                                        </div>
                                        <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                        </div>
                      )}

                      {/* Pricing Step */}
                      {steps[currentStep].id === "pricing" && (
                        <div className="space-y-6">
                          <div className="bg-gray-50 p-4 rounded-md">
                            <h4 className="text-sm font-medium text-gray-700 mb-3">Price Summary</h4>

                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Base Price:</span>
                                <span className="text-sm font-medium">{product.basePrice.toFixed(2)} ج.م</span>
                              </div>

                              {Object.entries(selectedComponents).map(([type, component]) => (
                                <div key={type} className="flex justify-between">
                                  <span className="text-sm text-gray-600">
                                    {type}
                                    {type === "RAM" && component.count && component.count > 1 && (
                                      <span className="text-xs text-gray-500 ml-1">
                                        ({component.count} sticks)
                                      </span>
                                    )}:
                                  </span>
                                  <span className="text-sm font-medium">
                                    {type === "RAM" && component.count && component.count > 1
                                      ? (component.price * component.count).toFixed(2)
                                      : component.price.toFixed(2)} ج.م
                                  </span>
                                </div>
                              ))}

                              <div className="border-t border-gray-200 pt-2 mt-2">
                                <div className="flex justify-between font-medium">
                                  <span className="text-sm">Cost Price:</span>
                                  <span className="text-sm">{costPrice.toFixed(2)} ج.م</span>
                                </div>
                              </div>
                            </div>

                            <div className="mt-4">
                              <div className="flex items-center">
                                <input
                                  id="custom-price-toggle"
                                  name="custom-price-toggle"
                                  type="checkbox"
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  checked={useCustomPrice}
                                  onChange={(e) => setUseCustomPrice(e.target.checked)}
                                />
                                <label htmlFor="custom-price-toggle" className="ml-2 block text-sm text-gray-700">
                                  Set custom price
                                </label>
                              </div>

                              {useCustomPrice && (
                                <div className="mt-3">
                                  <label htmlFor="custom-price" className="block text-sm font-medium text-gray-700">
                                    Custom Price (min: {costPrice.toFixed(2)} ج.م)
                                  </label>
                                  <div className="mt-1 relative rounded-md shadow-sm">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                      <span className="text-gray-500 sm:text-sm">ج.م</span>
                                    </div>
                                    <input
                                      type="number"
                                      name="custom-price"
                                      id="custom-price"
                                      step="0.01"
                                      className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-12 pr-12 sm:text-sm border-gray-300 rounded-md"
                                      value={customPrice}
                                      onChange={handleCustomPriceChange}
                                      onBlur={handleCustomPriceBlur}
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-blue-800">Final Price:</span>
                              <span className="text-lg font-bold text-blue-800">{totalPrice.toFixed(2)} ج.م</span>
                            </div>

                            {useCustomPrice && totalPrice > costPrice && (
                              <div className="mt-2 text-xs text-blue-600">
                                Markup: {((totalPrice - costPrice) / costPrice * 100).toFixed(1)}%
                                ({(totalPrice - costPrice).toFixed(2)} ج.م)
                              </div>
                            )}
                          </div>

                          <div className="mt-4">
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Product Preview</h4>
                            <div className="border rounded-md p-4">
                              <h5 className="font-medium">{product.name} (Customized)</h5>
                              <div className="mt-2 space-y-1 text-sm">
                                {Object.entries(selectedComponents).map(([type, component]) => (
                                  <div key={type}>
                                    <span className="font-medium">{type}:</span> {component.name}
                                    {type === "RAM" && component.count && component.count > 1 && (
                                      <span className="text-xs text-gray-500 ml-1">
                                        ({component.count} sticks, total {calculateTotalCapacity(component)})
                                      </span>
                                    )}
                                    {(type === "SSD" || type === "HDD") && component.capacity && (
                                      <span className="text-xs text-gray-500 ml-1">
                                        ({component.capacity})
                                      </span>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  </AnimatePresence>
                </div>

                {/* Navigation Buttons */}
                <div className="mt-8 flex justify-between">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    onClick={prevStep}
                    disabled={currentStep === 0}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </button>

                  {currentStep < steps.length - 1 ? (
                    <button
                      type="button"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      onClick={nextStep}
                      disabled={!isStepValid()}
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </button>
                  ) : (
                    <button
                      type="button"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      onClick={handleConfirm}
                      disabled={!isStepValid()}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      {isEditing ? 'Update' : 'Confirm'}
                    </button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
