-- <PERSON><PERSON><PERSON> to completely remove finance module tables
-- This script removes all tables related to the finance module

-- Temporarily disable foreign key constraints
SET session_replication_role = 'replica';

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS "PaymentMethodSettings" CASCADE;
DROP TABLE IF EXISTS "PaymentMethodBalance" CASCADE;
DROP TABLE IF EXISTS "PaymentTransaction" CASCADE;
DROP TABLE IF EXISTS "FinancialReport" CASCADE;
DROP TABLE IF EXISTS "ExpenseCategory" CASCADE;
DROP TABLE IF EXISTS "Expense" CASCADE;
DROP TABLE IF EXISTS "FinancialPeriod" CASCADE;
DROP TABLE IF EXISTS "FinancialYear" CASCADE;
DROP TABLE IF EXISTS "FinanceSettings" CASCADE;

-- Restore foreign key constraints
SET session_replication_role = 'origin';

-- Confirmation message
SELECT 'All finance tables have been successfully removed.' AS "Message";
