import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/journals - Get all journals
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const type = url.searchParams.get("type");
    const isActive = url.searchParams.get("isActive") === "true";
    const search = url.searchParams.get("search");
    
    // Build query filters
    const filters: any = {};
    if (type) filters.type = type;
    if (url.searchParams.has("isActive")) filters.isActive = isActive;
    
    if (search) {
      filters.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
      ];
    }
    
    // Get journals
    const journals = await db.journal.findMany({
      where: filters,
      orderBy: [
        { code: "asc" },
      ],
      include: {
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });
    
    return NextResponse.json({
      data: journals,
    });
  } catch (error: any) {
    console.error("Error fetching journals:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch journals" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/journals - Create a new journal
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to create journals
    const hasCreatePermission = await hasPermission("manage_accounts");
    if (!hasCreatePermission) {
      return NextResponse.json(
        { error: "You don't have permission to create journals" },
        { status: 403 }
      );
    }
    
    // Parse request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: "Journal name is required" }, { status: 400 });
    }
    
    if (!data.code) {
      return NextResponse.json({ error: "Journal code is required" }, { status: 400 });
    }
    
    if (!data.type) {
      return NextResponse.json({ error: "Journal type is required" }, { status: 400 });
    }
    
    // Check if journal with same code already exists
    const existingJournal = await db.journal.findFirst({
      where: {
        code: data.code,
      },
    });
    
    if (existingJournal) {
      return NextResponse.json(
        { error: "A journal with this code already exists" },
        { status: 400 }
      );
    }
    
    // Create journal
    const journal = await db.journal.create({
      data: {
        name: data.name,
        code: data.code,
        type: data.type,
        paymentMethod: data.paymentMethod || null,
        branchId: data.branchId || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    });
    
    return NextResponse.json(journal, { status: 201 });
  } catch (error: any) {
    console.error("Error creating journal:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create journal" },
      { status: 500 }
    );
  }
}
