"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Loader2, Download, Search, RefreshCw } from "lucide-react";
import { format } from "date-fns";

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
}

interface JournalEntry {
  id: string;
  date: string;
  description: string;
  amount: number;
  debitAccount: Account;
  creditAccount: Account;
  journal: {
    id: string;
    code: string;
    name: string;
  };
  reference: string | null;
  referenceType: string | null;
  contact: {
    id: string;
    name: string;
  } | null;
}

export default function GeneralLedgerPage() {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string>("");
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().getFullYear(), 0, 1)); // January 1st of current year
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingAccounts, setIsLoadingAccounts] = useState(false);

  // Fetch accounts on component mount
  useEffect(() => {
    fetchAccounts();
  }, []);

  // Fetch accounts
  const fetchAccounts = async () => {
    setIsLoadingAccounts(true);
    try {
      const response = await fetch("/api/accounting/accounts");
      if (response.ok) {
        const data = await response.json();
        setAccounts(data.data || []);
      }
    } catch (error) {
      console.error("Error fetching accounts:", error);
    } finally {
      setIsLoadingAccounts(false);
    }
  };

  // Fetch ledger entries
  const fetchLedger = async () => {
    if (!selectedAccountId) {
      return;
    }

    setIsLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("accountId", selectedAccountId);
      
      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }
      
      if (endDate) {
        params.append("endDate", endDate.toISOString());
      }

      const response = await fetch(`/api/accounting/general-ledger?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setEntries(data.entries || []);
      }
    } catch (error) {
      console.error("Error fetching ledger:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    if (entries.length === 0) return;

    // Get selected account
    const selectedAccount = accounts.find(a => a.id === selectedAccountId);
    if (!selectedAccount) return;

    // Create CSV content
    let csvContent = "Date,Journal,Reference,Description,Debit,Credit,Balance\n";
    
    let runningBalance = 0;
    entries.forEach(entry => {
      const isDebit = entry.debitAccount.id === selectedAccountId;
      const amount = entry.amount;
      
      // Update running balance
      if (isDebit) {
        runningBalance += amount;
      } else {
        runningBalance -= amount;
      }
      
      csvContent += [
        format(new Date(entry.date), "yyyy-MM-dd"),
        entry.journal.code,
        entry.reference || "",
        entry.description,
        isDebit ? amount.toFixed(2) : "",
        !isDebit ? amount.toFixed(2) : "",
        runningBalance.toFixed(2)
      ].join(",") + "\n";
    });
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `general_ledger_${selectedAccount.code}_${format(new Date(), "yyyyMMdd")}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Calculate account balance
  const calculateBalance = () => {
    if (entries.length === 0) return 0;
    
    return entries.reduce((balance, entry) => {
      if (entry.debitAccount.id === selectedAccountId) {
        return balance + entry.amount;
      } else if (entry.creditAccount.id === selectedAccountId) {
        return balance - entry.amount;
      }
      return balance;
    }, 0);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">General Ledger</h1>
        <Button variant="outline" onClick={exportToCSV} disabled={entries.length === 0 || isLoading}>
          <Download className="h-4 w-4 mr-2" />
          Export to CSV
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Account</label>
              <Select value={selectedAccountId} onValueChange={setSelectedAccountId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an account" />
                </SelectTrigger>
                <SelectContent>
                  {accounts.map(account => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.code} - {account.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <DatePicker date={startDate} setDate={setStartDate} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <DatePicker date={endDate} setDate={setEndDate} />
            </div>

            <div className="flex items-end">
              <Button onClick={fetchLedger} disabled={!selectedAccountId || isLoading}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {selectedAccountId && entries.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>
              {accounts.find(a => a.id === selectedAccountId)?.code} - {accounts.find(a => a.id === selectedAccountId)?.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-gray-100 border-b">
                      <th className="px-4 py-3 text-left">Date</th>
                      <th className="px-4 py-3 text-left">Journal</th>
                      <th className="px-4 py-3 text-left">Reference</th>
                      <th className="px-4 py-3 text-left">Description</th>
                      <th className="px-4 py-3 text-right">Debit</th>
                      <th className="px-4 py-3 text-right">Credit</th>
                      <th className="px-4 py-3 text-right">Balance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {entries.map((entry, index) => {
                      const isDebit = entry.debitAccount.id === selectedAccountId;
                      const runningBalance = entries
                        .slice(0, index + 1)
                        .reduce((balance, e) => {
                          if (e.debitAccount.id === selectedAccountId) {
                            return balance + e.amount;
                          } else if (e.creditAccount.id === selectedAccountId) {
                            return balance - e.amount;
                          }
                          return balance;
                        }, 0);

                      return (
                        <tr key={entry.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{format(new Date(entry.date), "yyyy-MM-dd")}</td>
                          <td className="px-4 py-3">{entry.journal.code}</td>
                          <td className="px-4 py-3">{entry.reference || "-"}</td>
                          <td className="px-4 py-3">{entry.description}</td>
                          <td className="px-4 py-3 text-right">{isDebit ? entry.amount.toFixed(2) : ""}</td>
                          <td className="px-4 py-3 text-right">{!isDebit ? entry.amount.toFixed(2) : ""}</td>
                          <td className="px-4 py-3 text-right font-medium">{runningBalance.toFixed(2)}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-100 font-medium">
                      <td colSpan={4} className="px-4 py-3 text-right">Total</td>
                      <td className="px-4 py-3 text-right">
                        {entries
                          .filter(e => e.debitAccount.id === selectedAccountId)
                          .reduce((sum, e) => sum + e.amount, 0)
                          .toFixed(2)}
                      </td>
                      <td className="px-4 py-3 text-right">
                        {entries
                          .filter(e => e.creditAccount.id === selectedAccountId)
                          .reduce((sum, e) => sum + e.amount, 0)
                          .toFixed(2)}
                      </td>
                      <td className="px-4 py-3 text-right">{calculateBalance().toFixed(2)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
