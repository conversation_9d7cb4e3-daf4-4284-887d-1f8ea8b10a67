// This is a draft schema for the printing system
// To be merged with the main schema.prisma file

model PrintSettings {
  id                       String    @id @default(uuid())
  pageSize                 String    @default("A4")
  orientation              String    @default("portrait")
  marginTop                Int       @default(10)
  marginRight              Int       @default(10)
  marginBottom             Int       @default(10)
  marginLeft               Int       @default(10)
  headerHeight             Int       @default(30)
  footerHeight             Int       @default(20)
  showLogo                 Boolean   @default(true)
  logoPosition             String    @default("left")
  showBranch               Boolean   @default(true)
  showQRCode               Boolean   @default(true)
  showBarcode              Boolean   @default(true)
  fontSize                 Int       @default(12)
  fontFamily               String    @default("Arial")
  primaryColor             String    @default("#3895e7")
  secondaryColor           String    @default("#f3f4f6")
  footerText               String    @default("Thank you for your business!")
  termsText                String    @default("Terms and conditions apply.")
  customWidth              Int?
  customHeight             Int?
  companyLogo              String?   @db.Text
  defaultTemplateSettings  Json?     // Default settings for each document type
  thermalPrinterSettings   Json?     // Thermal printer settings
  signatureSettings        Json?     // Electronic signature settings
  stampSettings            Json?     // Stamp settings
  multiLanguageSettings    Json?     // Multi-language settings
  branchId                 String?   // Optional branch association
  branch                   Branch?   @relation(fields: [branchId], references: [id])
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
}

model PrintTemplate {
  id                       String    @id @default(uuid())
  name                     String
  type                     String    // invoice, receipt, report, label, purchase_order, delivery_note, maintenance_receipt, etc.
  content                  String    @db.Text
  isDefault                Boolean   @default(false)
  language                 String    @default("ar")  // Template language
  paperSize                String    @default("A4")  // Paper size
  orientation              String    @default("portrait")  // Paper orientation
  branchId                 String?   // Optional branch association
  branch                   Branch?   @relation(fields: [branchId], references: [id])
  variables                Json?     // Template variables
  previewImage             String?   @db.Text  // Template preview image
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
  printLogs                PrintLog[]
}

model PrintLog {
  id                       String    @id @default(uuid())
  documentType             String    // Document type
  documentId               String    // Document ID
  userId                   String    // User who printed
  user                     User      @relation(fields: [userId], references: [id])
  templateId               String    // Template used
  template                 PrintTemplate @relation(fields: [templateId], references: [id])
  printedAt                DateTime  @default(now())  // Print time
  copies                   Int       @default(1)  // Number of copies
  printerName              String?   // Printer name
  status                   String    @default("success")  // Print status
  errorMessage             String?   // Error message (if any)
}

// Add indexes for better performance
model PrintTemplate {
  @@index([type, isDefault])
  @@index([branchId])
}

model PrintLog {
  @@index([documentType, documentId])
  @@index([userId])
  @@index([printedAt])
}
