import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/maintenance/[id]/status-history - Get status history for a maintenance service
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view maintenance services
    const canViewMaintenance = await hasPermission("view_maintenance") || session.user.role === "ADMIN";

    if (!canViewMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to view maintenance status history" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Get status history for the maintenance service
    const statusHistory = await db.maintenanceStatusHistory.findMany({
      where: { maintenanceServiceId: id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(statusHistory);
  } catch (error) {
    console.error("Error fetching maintenance status history:", error);
    return NextResponse.json(
      { error: "Failed to fetch maintenance status history" },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/[id]/status-history - Add a status history entry to a maintenance service
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit maintenance services
    const canEditMaintenance = await hasPermission("edit_maintenance") || session.user.role === "ADMIN";

    if (!canEditMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to add maintenance status history" },
        { status: 403 }
      );
    }

    const id = params.id;
    const data = await req.json();

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!data.status) {
      return NextResponse.json(
        { error: "Status is required" },
        { status: 400 }
      );
    }

    // Create the status history entry
    const statusHistory = await db.maintenanceStatusHistory.create({
      data: {
        maintenanceServiceId: id,
        status: data.status,
        notes: data.notes || null,
        userId: session.user.id,
      },
    });

    // Update the maintenance service status
    await db.maintenanceService.update({
      where: { id },
      data: {
        status: data.status,
        // Update date fields based on status
        ...(data.status === "COMPLETED" && !existingService.completionDate
          ? { completionDate: new Date() }
          : {}),
        ...(data.status === "DELIVERED" && !existingService.deliveryDate
          ? { deliveryDate: new Date() }
          : {}),
      },
    });

    return NextResponse.json(statusHistory);
  } catch (error) {
    console.error("Error adding maintenance status history:", error);
    return NextResponse.json(
      { error: "Failed to add maintenance status history" },
      { status: 500 }
    );
  }
}
