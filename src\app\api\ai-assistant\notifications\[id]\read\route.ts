import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// POST /api/ai-assistant/notifications/[id]/read - Mark notification as read
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const notificationId = params.id;
    
    // Verify the notification belongs to the user
    const notification = await db.aIAssistantNotification.findUnique({
      where: {
        id: notificationId,
        userId: session.user.id,
      },
    });
    
    if (!notification) {
      return NextResponse.json(
        { error: "Notification not found" },
        { status: 404 }
      );
    }
    
    // Mark notification as read
    await db.aIAssistantNotification.update({
      where: {
        id: notificationId,
      },
      data: {
        isRead: true,
      },
    });
    
    return NextResponse.json({
      success: true,
      message: "Notification marked as read",
    });
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return NextResponse.json(
      { error: "Failed to mark notification as read" },
      { status: 500 }
    );
  }
}
