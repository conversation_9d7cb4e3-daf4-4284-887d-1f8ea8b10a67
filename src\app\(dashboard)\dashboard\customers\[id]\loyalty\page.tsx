"use client";

import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts";
import {
  Award,
  CreditCard,
  DollarSign,
  Gift,
  Plus,
  Minus,
  ShoppingBag,
  RefreshCw,
  TrendingUp,
  Star,
  Calendar
} from "lucide-react";
import { format } from "date-fns";
import { useMediaQuery } from "@/hooks/use-media-query";
import MobileLoyaltyView from "./mobile-view";

interface LoyaltyData {
  contact: {
    id: string;
    name: string;
    phone: string;
    isVIP: boolean;
    loyaltyPoints: number;
    loyaltyTier: string;
  };
  loyaltyInfo: {
    currentPoints: number;
    availableValue: number;
    tier: string;
    isVIP: boolean;
  };
  recentTransactions: Array<{
    type: string;
    reference: string;
    date: string;
    points: number;
    description: string;
  }>;
}

export default function CustomerLoyaltyPage() {
  const params = useParams();
  const router = useRouter();
  const contactId = params.id as string;
  const isMobile = useMediaQuery("(max-width: 768px)");

  const [isLoading, setIsLoading] = useState(true);
  const [loyaltyData, setLoyaltyData] = useState<LoyaltyData | null>(null);
  const [pointsToAdd, setPointsToAdd] = useState<number>(0);
  const [pointsToRedeem, setPointsToRedeem] = useState<number>(0);
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch loyalty data
  useEffect(() => {
    const fetchLoyaltyData = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/loyalty?contactId=${contactId}`);
        if (response.ok) {
          const data = await response.json();
          setLoyaltyData(data);
        } else {
          console.error("Failed to fetch loyalty data");
        }
      } catch (error) {
        console.error("Error fetching loyalty data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLoyaltyData();
  }, [contactId]);

  // Handle adding points
  const handleAddPoints = async () => {
    if (pointsToAdd <= 0) return;

    setIsUpdating(true);
    try {
      const response = await fetch("/api/loyalty", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contactId,
          operation: "ADD_POINTS",
          points: pointsToAdd
        }),
      });

      if (response.ok) {
        // Send email notification if contact has email
        if (loyaltyData?.contact?.email) {
          await fetch('/api/loyalty/email-notification', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contactId,
              type: 'LOYALTY_POINTS',
              points: pointsToAdd,
              reason: 'manual adjustment'
            }),
          });
        }

        // Refresh data
        const updatedResponse = await fetch(`/api/loyalty?contactId=${contactId}`);
        if (updatedResponse.ok) {
          const data = await updatedResponse.json();
          setLoyaltyData(data);
          setPointsToAdd(0);
        }
      } else {
        console.error("Failed to add points");
      }
    } catch (error) {
      console.error("Error adding points:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle redeeming points
  const handleRedeemPoints = async () => {
    if (pointsToRedeem <= 0 || !loyaltyData || pointsToRedeem > loyaltyData.loyaltyInfo.currentPoints) return;

    setIsUpdating(true);
    try {
      const response = await fetch("/api/loyalty", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contactId,
          operation: "REDEEM_POINTS",
          points: pointsToRedeem
        }),
      });

      if (response.ok) {
        // Send email notification if contact has email
        if (loyaltyData?.contact?.email) {
          await fetch('/api/loyalty/email-notification', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contactId,
              type: 'LOYALTY_POINTS',
              points: -pointsToRedeem,
              reason: 'points redemption'
            }),
          });
        }

        // Refresh data
        const updatedResponse = await fetch(`/api/loyalty?contactId=${contactId}`);
        if (updatedResponse.ok) {
          const data = await updatedResponse.json();
          setLoyaltyData(data);
          setPointsToRedeem(0);
        }
      } else {
        console.error("Failed to redeem points");
      }
    } catch (error) {
      console.error("Error redeeming points:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle toggling VIP status
  const handleToggleVIP = async () => {
    if (!loyaltyData) return;

    setIsUpdating(true);
    try {
      const response = await fetch("/api/loyalty", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contactId,
          operation: "SET_VIP",
          isVIP: !loyaltyData.loyaltyInfo.isVIP
        }),
      });

      if (response.ok) {
        // Refresh data
        const updatedResponse = await fetch(`/api/loyalty?contactId=${contactId}`);
        if (updatedResponse.ok) {
          const data = await updatedResponse.json();
          setLoyaltyData(data);
        }
      } else {
        console.error("Failed to update VIP status");
      }
    } catch (error) {
      console.error("Error updating VIP status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Get tier color
  const getTierColor = (tier: string) => {
    switch (tier) {
      case "PLATINUM":
        return "#8884d8";
      case "GOLD":
        return "#ffc658";
      case "SILVER":
        return "#82ca9d";
      default:
        return "#8884d8";
    }
  };

  // Get tier benefits
  const getTierBenefits = (tier: string) => {
    switch (tier) {
      case "PLATINUM":
        return [
          "10% discount on all purchases",
          "Priority customer service",
          "Free delivery on all orders",
          "Exclusive access to new products",
          "Birthday gift"
        ];
      case "GOLD":
        return [
          "7% discount on all purchases",
          "Free delivery on orders over 500 EGP",
          "Birthday gift"
        ];
      case "SILVER":
        return [
          "5% discount on all purchases",
          "Free delivery on orders over 1000 EGP"
        ];
      default:
        return [
          "2% discount on all purchases"
        ];
    }
  };

  // Get next tier info
  const getNextTierInfo = (currentTier: string, currentPoints: number) => {
    switch (currentTier) {
      case "PLATINUM":
        return {
          tier: "PLATINUM",
          pointsNeeded: 0,
          progress: 100
        };
      case "GOLD":
        const platinumPoints = 10000;
        const pointsToplatinum = platinumPoints - currentPoints;
        const platinumProgress = (currentPoints / platinumPoints) * 100;
        return {
          tier: "PLATINUM",
          pointsNeeded: pointsToplatinum,
          progress: platinumProgress
        };
      case "SILVER":
        const goldPoints = 5000;
        const pointsToGold = goldPoints - currentPoints;
        const goldProgress = (currentPoints / goldPoints) * 100;
        return {
          tier: "GOLD",
          pointsNeeded: pointsToGold,
          progress: goldProgress
        };
      default:
        const silverPoints = 1000;
        const pointsToSilver = silverPoints - currentPoints;
        const silverProgress = (currentPoints / silverPoints) * 100;
        return {
          tier: "SILVER",
          pointsNeeded: pointsToSilver,
          progress: silverProgress
        };
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading loyalty data...</p>
        </div>
      </div>
    );
  }

  if (!loyaltyData) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-gray-500">No loyalty data available for this customer.</p>
        </div>
      </div>
    );
  }

  const { contact, loyaltyInfo, recentTransactions } = loyaltyData;
  const nextTierInfo = getNextTierInfo(loyaltyInfo.tier, loyaltyInfo.currentPoints);

  // Mobile view
  if (isMobile) {
    return (
      <div className="container mx-auto py-4 px-2">
        <MobileLoyaltyView
          loyaltyData={loyaltyData}
          isUpdating={isUpdating}
          pointsToAdd={pointsToAdd}
          setPointsToAdd={setPointsToAdd}
          pointsToRedeem={pointsToRedeem}
          setPointsToRedeem={setPointsToRedeem}
          handleAddPoints={handleAddPoints}
          handleRedeemPoints={handleRedeemPoints}
          handleToggleVIP={handleToggleVIP}
        />
      </div>
    );
  }

  // Desktop view
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          Loyalty Program: {contact.name}
        </h1>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">VIP Status</span>
          <Switch
            checked={loyaltyInfo.isVIP}
            onCheckedChange={handleToggleVIP}
            disabled={isUpdating}
          />
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Current Points</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{loyaltyInfo.currentPoints}</p>
                <p className="text-sm text-gray-500">Loyalty points</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Gift className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Points Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{loyaltyInfo.availableValue.toFixed(2)} ج.م</p>
                <p className="text-sm text-gray-500">Available to redeem</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Loyalty Tier</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{loyaltyInfo.tier}</p>
                <p className="text-sm text-gray-500">Current tier</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Award className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Next Tier</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{nextTierInfo.pointsNeeded}</p>
                <p className="text-sm text-gray-500">Points needed for {nextTierInfo.tier}</p>
              </div>
              <div className="bg-amber-100 p-3 rounded-full">
                <TrendingUp className="h-6 w-6 text-amber-600" />
              </div>
            </div>
            {nextTierInfo.pointsNeeded > 0 && (
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${nextTierInfo.progress}%` }}
                ></div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Tabs defaultValue="transactions" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
              <TabsTrigger value="benefits">Tier Benefits</TabsTrigger>
            </TabsList>

            <TabsContent value="transactions">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Transactions</CardTitle>
                  <CardDescription>
                    Recent point earning and redemption activities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {recentTransactions.length === 0 ? (
                    <div className="text-center py-6">
                      <ShoppingBag className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                      <p className="text-gray-500">No transactions found</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {recentTransactions.map((transaction, index) => (
                        <div key={index} className="flex items-center justify-between border-b pb-4">
                          <div className="flex items-start space-x-3">
                            <div className={`p-2 rounded-full ${
                              transaction.type === 'SALE' ? 'bg-green-100' : 'bg-red-100'
                            }`}>
                              {transaction.type === 'SALE' ? (
                                <Plus className={`h-5 w-5 ${
                                  transaction.type === 'SALE' ? 'text-green-600' : 'text-red-600'
                                }`} />
                              ) : (
                                <Minus className={`h-5 w-5 ${
                                  transaction.type === 'SALE' ? 'text-green-600' : 'text-red-600'
                                }`} />
                              )}
                            </div>
                            <div>
                              <p className="font-medium">
                                {transaction.type === 'SALE' ? 'Purchase' : 'Credit Note'}
                              </p>
                              <p className="text-sm text-gray-500">
                                {transaction.reference}
                              </p>
                              <p className="text-xs text-gray-400">
                                {format(new Date(transaction.date), 'MMM d, yyyy')}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`font-medium ${
                              transaction.type === 'SALE' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.type === 'SALE' ? '+' : '-'}{Math.round(transaction.points)} points
                            </p>
                            <p className="text-sm text-gray-500">
                              {(transaction.points * 0.01).toFixed(2)} ج.م
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="benefits">
              <Card>
                <CardHeader>
                  <CardTitle>
                    <div className="flex items-center">
                      <span>{loyaltyInfo.tier} Tier Benefits</span>
                      <Badge
                        className="ml-2"
                        style={{ backgroundColor: getTierColor(loyaltyInfo.tier) }}
                      >
                        Current
                      </Badge>
                    </div>
                  </CardTitle>
                  <CardDescription>
                    Benefits available at your current loyalty tier
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {getTierBenefits(loyaltyInfo.tier).map((benefit, index) => (
                      <li key={index} className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-500 mr-2" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>

                  {loyaltyInfo.tier !== 'PLATINUM' && (
                    <>
                      <Separator className="my-4" />
                      <div>
                        <h4 className="font-medium mb-2">Next Tier: {nextTierInfo.tier}</h4>
                        <p className="text-sm text-gray-500 mb-2">
                          Need {nextTierInfo.pointsNeeded} more points to reach {nextTierInfo.tier}
                        </p>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                          <div
                            className="bg-blue-600 h-2.5 rounded-full"
                            style={{ width: `${nextTierInfo.progress}%` }}
                          ></div>
                        </div>
                        <ul className="space-y-2">
                          {getTierBenefits(nextTierInfo.tier)
                            .filter(benefit => !getTierBenefits(loyaltyInfo.tier).includes(benefit))
                            .map((benefit, index) => (
                              <li key={index} className="flex items-center text-gray-400">
                                <Star className="h-4 w-4 text-gray-300 mr-2" />
                                <span>{benefit}</span>
                              </li>
                            ))}
                        </ul>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Manage Points</CardTitle>
              <CardDescription>
                Add or redeem loyalty points
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="add-points">Add Points</Label>
                  <div className="flex mt-1">
                    <Input
                      id="add-points"
                      type="number"
                      min="0"
                      value={pointsToAdd || ''}
                      onChange={(e) => setPointsToAdd(parseInt(e.target.value) || 0)}
                      className="rounded-r-none"
                    />
                    <Button
                      onClick={handleAddPoints}
                      disabled={isUpdating || pointsToAdd <= 0}
                      className="rounded-l-none"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Value: {(pointsToAdd * 0.01).toFixed(2)} ج.م
                  </p>
                </div>

                <div>
                  <Label htmlFor="redeem-points">Redeem Points</Label>
                  <div className="flex mt-1">
                    <Input
                      id="redeem-points"
                      type="number"
                      min="0"
                      max={loyaltyInfo.currentPoints}
                      value={pointsToRedeem || ''}
                      onChange={(e) => setPointsToRedeem(parseInt(e.target.value) || 0)}
                      className="rounded-r-none"
                    />
                    <Button
                      onClick={handleRedeemPoints}
                      disabled={isUpdating || pointsToRedeem <= 0 || pointsToRedeem > loyaltyInfo.currentPoints}
                      className="rounded-l-none"
                      variant="destructive"
                    >
                      <Minus className="h-4 w-4 mr-1" />
                      Redeem
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Value: {(pointsToRedeem * 0.01).toFixed(2)} ج.م
                  </p>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">VIP Status</span>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={loyaltyInfo.isVIP}
                      onCheckedChange={handleToggleVIP}
                      disabled={isUpdating}
                    />
                    <span className="text-sm text-gray-500">
                      {loyaltyInfo.isVIP ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  VIP customers receive special treatment and exclusive offers
                </p>

                <Separator className="my-4" />

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-sm font-medium">Birthday Gift</span>
                    <p className="text-xs text-gray-500">
                      Send a special gift on customer's birthday
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/dashboard/customers/${contact.id}/loyalty/birthday-gift`)}
                  >
                    <Gift className="h-4 w-4 mr-1" />
                    Send Gift
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
