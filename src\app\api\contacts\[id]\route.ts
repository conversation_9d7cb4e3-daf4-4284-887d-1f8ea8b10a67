import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/contacts/:id - Get a contact by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Get the contact
    const contact = await db.contact.findUnique({
      where: {
        id,
      },
    });

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    // Check permissions based on contact type
    if (contact.isCustomer && !(await hasPermission("view_customers"))) {
      return NextResponse.json(
        { error: "You don't have permission to view customers" },
        { status: 403 }
      );
    }

    if (contact.isSupplier && !(await hasPermission("view_suppliers"))) {
      return NextResponse.json(
        { error: "You don't have permission to view suppliers" },
        { status: 403 }
      );
    }

    return NextResponse.json(contact);
  } catch (error) {
    console.error("Error fetching contact:", error);
    return NextResponse.json(
      { error: "Failed to fetch contact" },
      { status: 500 }
    );
  }
}

// PATCH /api/contacts/:id - Update a contact
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;
    const data = await req.json();

    // Get the existing contact
    const existingContact = await db.contact.findUnique({
      where: {
        id,
      },
    });

    if (!existingContact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    // Check permissions based on contact type
    if (existingContact.isCustomer && !(await hasPermission("edit_customers"))) {
      return NextResponse.json(
        { error: "You don't have permission to edit customers" },
        { status: 403 }
      );
    }

    if (existingContact.isSupplier && !(await hasPermission("edit_suppliers"))) {
      return NextResponse.json(
        { error: "You don't have permission to edit suppliers" },
        { status: 403 }
      );
    }

    // Check if phone is already used by another contact
    if (data.phone && data.phone !== existingContact.phone) {
      const contactWithPhone = await db.contact.findFirst({
        where: {
          phone: data.phone,
          id: { not: id },
        },
      });

      if (contactWithPhone) {
        return NextResponse.json(
          { error: "Phone number already exists" },
          { status: 400 }
        );
      }
    }

    // Update the contact
    const contact = await db.contact.update({
      where: {
        id,
      },
      data: {
        name: data.name,
        phone: data.phone,
        address: data.address,
        isCustomer: data.isCustomer,
        isSupplier: data.isSupplier,
        isActive: data.isActive,
        creditLimit: data.isCustomer ? (data.creditLimit || 0) : 0,
        creditPeriod: data.isCustomer ? (data.creditPeriod || 30) : 0,
        openingBalance: data.openingBalance || 0,
        openingBalanceDate: data.openingBalanceDate ? new Date(data.openingBalanceDate) : undefined,
      },
    });

    return NextResponse.json(contact);
  } catch (error) {
    console.error("Error updating contact:", error);
    return NextResponse.json(
      { error: "Failed to update contact" },
      { status: 500 }
    );
  }
}

// DELETE /api/contacts/:id - Delete a contact
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Get the existing contact
    const existingContact = await db.contact.findUnique({
      where: {
        id,
      },
      include: {
        sales: { take: 1 },
        purchases: { take: 1 },
      },
    });

    if (!existingContact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    // Check permissions based on contact type
    if (existingContact.isCustomer && !(await hasPermission("delete_customers"))) {
      return NextResponse.json(
        { error: "You don't have permission to delete customers" },
        { status: 403 }
      );
    }

    if (existingContact.isSupplier && !(await hasPermission("delete_suppliers"))) {
      return NextResponse.json(
        { error: "You don't have permission to delete suppliers" },
        { status: 403 }
      );
    }

    // Check if contact has related records
    if (existingContact.sales.length > 0 || existingContact.purchases.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete contact with related sales or purchases" },
        { status: 400 }
      );
    }

    // Delete the contact
    await db.contact.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting contact:", error);
    return NextResponse.json(
      { error: "Failed to delete contact" },
      { status: 500 }
    );
  }
}
