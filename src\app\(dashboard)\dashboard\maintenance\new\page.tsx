"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Loader2, ArrowLeft, Save } from "lucide-react";
import { getPriorityLabel, getPriorityColor } from "@/lib/maintenance";
import ContactSearchInput from "@/components/contacts/ContactSearchInput";

interface Contact {
  id: string;
  name: string;
  phone: string;
  isCustomer: boolean;
}

export default function NewMaintenancePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // No longer need these states as they're handled by the ContactSearchInput component

  // Form state
  const [formData, setFormData] = useState({
    contactId: "",
    contactName: "",
    branchId: "",
    deviceType: "",
    brand: "",
    model: "",
    serialNumber: "",
    deviceSpecs: "",
    problemDescription: "",
    priority: "MEDIUM",
    initialDiagnosis: "",
    estimatedCost: "",
    isWarranty: false,
    warrantyDetails: "",
    estimatedCompletionDate: "",
    customerSignature: null,
  });

  // Branch state
  const [branches, setBranches] = useState([]);

  // Contacts are now fetched by the ContactSearchInput component

  // Contact filtering is now handled by the ContactSearchInput component

  // Fetch branches
  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (response.ok) {
          const data = await response.json();
          setBranches(data);

          // Set default branch if available
          if (data.length > 0) {
            setFormData(prev => ({
              ...prev,
              branchId: data[0].id
            }));
          }
        }
      } catch (error) {
        console.error("Error fetching branches:", error);
      }
    };

    fetchBranches();
  }, []);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox"
        ? (e.target as HTMLInputElement).checked
        : value,
    }));
  };

  // Handle contact selection from the ContactSearchInput component
  const handleSelectContact = (contact: Contact) => {
    setFormData((prev) => ({
      ...prev,
      contactId: contact.id,
      contactName: contact.name,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.contactId) {
        throw new Error("Please select a customer");
      }

      if (!formData.branchId) {
        throw new Error("Branch is required");
      }

      if (!formData.deviceType) {
        throw new Error("Device type is required");
      }

      if (!formData.brand) {
        throw new Error("Brand is required");
      }

      if (!formData.problemDescription) {
        throw new Error("Problem description is required");
      }

      if (!formData.initialDiagnosis) {
        throw new Error("Initial diagnosis is required");
      }

      if (!formData.estimatedCost) {
        throw new Error("Estimated cost is required");
      }

      // Prepare data for API
      const maintenanceData = {
        contactId: formData.contactId,
        branchId: formData.branchId,
        deviceType: formData.deviceType,
        brand: formData.brand,
        model: formData.model || null,
        serialNumber: formData.serialNumber || null,
        deviceSpecs: formData.deviceSpecs || null,
        problemDescription: formData.problemDescription,
        priority: formData.priority,
        initialDiagnosis: formData.initialDiagnosis,
        estimatedCost: formData.estimatedCost ? parseFloat(formData.estimatedCost) : 0,
        isWarranty: formData.isWarranty,
        warrantyDetails: formData.isWarranty ? formData.warrantyDetails : null,
        estimatedCompletionDate: formData.estimatedCompletionDate || null,
        customerSignature: formData.customerSignature,
        status: "RECEIVED", // Initial status
        // Set 15-day deadline for device pickup
        pickupDeadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      };

      // Submit to API
      const response = await fetch("/api/maintenance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(maintenanceData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create maintenance service");
      }

      const data = await response.json();

      // Redirect to the new maintenance service page
      router.push(`/dashboard/maintenance/${data.id}`);
    } catch (error: any) {
      setError(error.message);
      setIsSaving(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            href="/dashboard/maintenance"
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">New Maintenance Service</h1>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6 space-y-6">
          {/* Customer Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <label htmlFor="contactName" className="block text-sm font-medium text-gray-700 mb-1">
                  Customer <span className="text-red-500">*</span>
                </label>
                <ContactSearchInput
                  onSelectContact={handleSelectContact}
                  placeholder="Search customer by name or phone"
                  contactType="customer"
                  selectedContactId={formData.contactId}
                  buttonLabel="Add New Customer"
                />
              </div>
              <div>
                <label htmlFor="branchId" className="block text-sm font-medium text-gray-700 mb-1">
                  Branch <span className="text-red-500">*</span>
                </label>
                <select
                  id="branchId"
                  name="branchId"
                  value={formData.branchId}
                  onChange={handleChange}
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                >
                  <option value="">Select Branch</option>
                  {branches.map((branch: any) => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Device Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Device Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="deviceType" className="block text-sm font-medium text-gray-700 mb-1">
                  Device Type <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="deviceType"
                  name="deviceType"
                  value={formData.deviceType}
                  onChange={handleChange}
                  placeholder="e.g. Laptop, Desktop, Printer"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                />
              </div>
              <div>
                <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-1">
                  Brand <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="brand"
                  name="brand"
                  value={formData.brand}
                  onChange={handleChange}
                  placeholder="e.g. HP, Dell, Lenovo"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                />
              </div>
              <div>
                <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                  Model
                </label>
                <input
                  type="text"
                  id="model"
                  name="model"
                  value={formData.model}
                  onChange={handleChange}
                  placeholder="e.g. ThinkPad X1, Inspiron 15"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
              <div>
                <label htmlFor="serialNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Serial Number
                </label>
                <input
                  type="text"
                  id="serialNumber"
                  name="serialNumber"
                  value={formData.serialNumber}
                  onChange={handleChange}
                  placeholder="Device serial number"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
              <div className="md:col-span-2">
                <label htmlFor="deviceSpecs" className="block text-sm font-medium text-gray-700 mb-1">
                  Device Specifications
                </label>
                <textarea
                  id="deviceSpecs"
                  name="deviceSpecs"
                  value={formData.deviceSpecs}
                  onChange={handleChange}
                  rows={2}
                  placeholder="RAM, Storage, Processor, etc."
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                ></textarea>
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Service Information</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="problemDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Problem Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="problemDescription"
                  name="problemDescription"
                  value={formData.problemDescription}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Describe the problem with the device"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <select
                    id="priority"
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  >
                    <option value="LOW">Low</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="HIGH">High</option>
                    <option value="URGENT">Urgent</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="estimatedCompletionDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Completion Date
                  </label>
                  <input
                    type="date"
                    id="estimatedCompletionDate"
                    name="estimatedCompletionDate"
                    value={formData.estimatedCompletionDate}
                    onChange={handleChange}
                    className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="initialDiagnosis" className="block text-sm font-medium text-gray-700 mb-1">
                  Initial Diagnosis
                </label>
                <textarea
                  id="initialDiagnosis"
                  name="initialDiagnosis"
                  value={formData.initialDiagnosis}
                  onChange={handleChange}
                  rows={2}
                  placeholder="Initial diagnosis of the problem"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="estimatedCost" className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Cost
                  </label>
                  <input
                    type="number"
                    id="estimatedCost"
                    name="estimatedCost"
                    value={formData.estimatedCost}
                    onChange={handleChange}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  />
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isWarranty"
                    name="isWarranty"
                    checked={formData.isWarranty}
                    onChange={(e) => setFormData({ ...formData, isWarranty: e.target.checked })}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isWarranty" className="ml-2 block text-sm font-medium text-gray-700">
                    Under Warranty
                  </label>
                </div>
              </div>

              {formData.isWarranty && (
                <div>
                  <label htmlFor="warrantyDetails" className="block text-sm font-medium text-gray-700 mb-1">
                    Warranty Details
                  </label>
                  <textarea
                    id="warrantyDetails"
                    name="warrantyDetails"
                    value={formData.warrantyDetails}
                    onChange={handleChange}
                    rows={2}
                    placeholder="Warranty details, expiration date, etc."
                    className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  ></textarea>
                </div>
              )}

              {/* Customer Signature */}
              <div className="mt-6">
                <h3 className="text-md font-semibold text-gray-900 mb-2">Customer Signature</h3>
                <p className="text-sm text-gray-500 mb-4">
                  By signing below, the customer acknowledges that they have read and agreed to the terms of service.
                  The device must be picked up within 15 days of notification, otherwise additional storage fees may apply.
                </p>

                <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Customer Signature
                    </label>
                    <button
                      type="button"
                      className="text-sm text-indigo-600 hover:text-indigo-800"
                      onClick={() => {
                        // Clear signature logic would go here
                        setFormData(prev => ({
                          ...prev,
                          customerSignature: null
                        }));
                      }}
                    >
                      Clear
                    </button>
                  </div>

                  <div className="border border-gray-300 rounded-md bg-white h-40 flex items-center justify-center">
                    {/* Signature pad would go here - for now just a placeholder */}
                    <p className="text-gray-400">Signature Pad Placeholder</p>
                  </div>

                  <div className="mt-2 flex items-center">
                    <input
                      type="checkbox"
                      id="signatureConsent"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label htmlFor="signatureConsent" className="ml-2 block text-sm text-gray-700">
                      I agree to the terms and conditions
                    </label>
                  </div>
                </div>

                <div className="mt-4">
                  <button
                    type="button"
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
                    onClick={() => {
                      // Print receipt logic would go here
                      alert("Receipt printing functionality will be implemented");
                    }}
                  >
                    Print Receipt
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 flex justify-end">
          <Link
            href="/dashboard/maintenance"
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium mr-2"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSaving}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium flex items-center"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </button>
        </div>
      </form>

      {/* The ContactSearchInput component now handles adding new customers */}
    </div>
  );
}
