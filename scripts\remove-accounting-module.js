const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to execute commands and display output
function runCommand(command) {
  console.log(`Executing: ${command}`);
  try {
    const output = execSync(command, { encoding: 'utf8' });
    console.log(output);
    return output;
  } catch (error) {
    console.error(`<PERSON><PERSON><PERSON> executing command: ${command}`);
    console.error(error.message);
    throw error;
  }
}

// Main function
async function removeAccountingModule() {
  try {
    console.log('Starting removal of accounting module...');

    // 1. Remove tables from the database
    console.log('Step 1: Removing database tables...');

    // Execute SQL script to remove tables
    try {
      // Try to use psql if available
      runCommand('psql -U openpg -d vero_erp -f scripts/drop-accounting-tables.sql');
    } catch (error) {
      console.log('Could not use psql, trying with Prisma...');

      // If psql is not available, use Prisma to remove tables
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      // Disable foreign key constraints
      await prisma.$executeRaw`SET session_replication_role = 'replica'`;

      // Remove tables in reverse dependency order
      console.log('Removing ReconciliationItem...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "ReconciliationItem" CASCADE`;

      console.log('Removing AccountReconciliation...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "AccountReconciliation" CASCADE`;

      console.log('Removing FinancialReport...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "FinancialReport" CASCADE`;

      console.log('Removing GeneralLedgerEntry...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "GeneralLedgerEntry" CASCADE`;

      console.log('Removing JournalEntry...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "JournalEntry" CASCADE`;

      console.log('Removing Journal...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "Journal" CASCADE`;

      console.log('Removing Transaction...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "Transaction" CASCADE`;

      console.log('Removing FiscalPeriod...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "FiscalPeriod" CASCADE`;

      console.log('Removing FiscalYear...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "FiscalYear" CASCADE`;

      console.log('Removing AccountingSettings...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "AccountingSettings" CASCADE`;

      console.log('Removing Account...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "Account" CASCADE`;

      // Restore foreign key constraints
      await prisma.$executeRaw`SET session_replication_role = 'origin'`;

      await prisma.$disconnect();
    }

    // 2. Update Prisma schema
    console.log('Step 2: Updating Prisma schema...');

    // Create a backup of the current schema
    fs.copyFileSync(
      path.join(process.cwd(), 'prisma', 'schema.prisma'),
      path.join(process.cwd(), 'prisma', 'schema.prisma.bak')
    );

    // Replace schema with version without accounting
    fs.copyFileSync(
      path.join(process.cwd(), 'prisma', 'schema_without_accounting.prisma'),
      path.join(process.cwd(), 'prisma', 'schema.prisma')
    );

    // 3. Generate Prisma client with new schema
    console.log('Step 3: Generating Prisma client...');
    runCommand('npx prisma generate');

    // 4. Remove source code files related to accounting module
    console.log('Step 4: Removing source code files...');

    // Directories to remove
    const dirsToRemove = [
      'src/app/(dashboard)/dashboard/accounting',
      'src/components/accounting',
      'src/app/api/accounting'
    ];

    // Remove directories
    dirsToRemove.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir);
      if (fs.existsSync(fullPath)) {
        console.log(`Removing directory: ${dir}`);
        fs.rmSync(fullPath, { recursive: true, force: true });
      }
    });

    console.log('The accounting module has been completely removed.');

  } catch (error) {
    console.error('Error removing accounting module:', error);
  }
}

removeAccountingModule();
