import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import cache from "@/lib/cache";
import { getSalesByDateRange, getTopSellingProducts } from "@/lib/sales-queries";

// GET /api/sales/dashboard - Get sales dashboard data
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    
    // Get query parameters
    const branchId = url.searchParams.get("branchId") || undefined;
    const startDate = url.searchParams.get("startDate") 
      ? new Date(url.searchParams.get("startDate") as string) 
      : undefined;
    const endDate = url.searchParams.get("endDate") 
      ? new Date(url.searchParams.get("endDate") as string) 
      : undefined;
    const timeFrame = url.searchParams.get("timeFrame") || "month";
    
    // Generate cache key
    const cacheKey = `sales_dashboard_${branchId || 'all'}_${startDate?.toISOString() || ''}_${endDate?.toISOString() || ''}_${timeFrame}`;
    
    // Check if we have cached data
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached dashboard data for key: ${cacheKey}`);
      return NextResponse.json(cachedData);
    }
    
    console.log(`Cache miss for key: ${cacheKey}, fetching from database`);
    
    // Fetch all required data in parallel
    const [
      salesByDate,
      topProducts,
      salesSummary,
      paymentMethodStats,
      customerStats
    ] = await Promise.all([
      // Sales by date for charts
      getSalesByDateRange(
        timeFrame as 'day' | 'week' | 'month',
        { branchId, startDate, endDate }
      ),
      
      // Top selling products
      getTopSellingProducts(10, { branchId, startDate, endDate }),
      
      // Sales summary statistics
      prisma.sale.groupBy({
        by: ['status', 'paymentStatus'],
        where: {
          ...(branchId ? { branchId } : {}),
          ...(startDate || endDate ? {
            date: {
              ...(startDate ? { gte: startDate } : {}),
              ...(endDate ? { lte: endDate } : {})
            }
          } : {})
        },
        _count: {
          id: true
        },
        _sum: {
          totalAmount: true,
          subtotalAmount: true,
          discountAmount: true,
          taxAmount: true
        }
      }),
      
      // Payment method statistics
      prisma.sale.groupBy({
        by: ['paymentMethod'],
        where: {
          ...(branchId ? { branchId } : {}),
          ...(startDate || endDate ? {
            date: {
              ...(startDate ? { gte: startDate } : {}),
              ...(endDate ? { lte: endDate } : {})
            }
          } : {})
        },
        _count: {
          id: true
        },
        _sum: {
          totalAmount: true
        }
      }),
      
      // Top customers
      prisma.sale.groupBy({
        by: ['contactId'],
        where: {
          ...(branchId ? { branchId } : {}),
          ...(startDate || endDate ? {
            date: {
              ...(startDate ? { gte: startDate } : {}),
              ...(endDate ? { lte: endDate } : {})
            }
          } : {})
        },
        _count: {
          id: true
        },
        _sum: {
          totalAmount: true
        },
        orderBy: {
          _sum: {
            totalAmount: 'desc'
          }
        },
        take: 5
      })
    ]);
    
    // Process sales summary statistics
    const summary = {
      totalSales: 0,
      totalAmount: 0,
      averageOrderValue: 0,
      totalDiscount: 0,
      totalTax: 0,
      byStatus: {} as Record<string, { count: number, amount: number }>,
      byPaymentStatus: {} as Record<string, { count: number, amount: number }>
    };
    
    salesSummary.forEach(stat => {
      // Add to total counts
      summary.totalSales += stat._count.id;
      summary.totalAmount += stat._sum.totalAmount || 0;
      summary.totalDiscount += stat._sum.discountAmount || 0;
      summary.totalTax += stat._sum.taxAmount || 0;
      
      // Group by status
      if (!summary.byStatus[stat.status]) {
        summary.byStatus[stat.status] = { count: 0, amount: 0 };
      }
      summary.byStatus[stat.status].count += stat._count.id;
      summary.byStatus[stat.status].amount += stat._sum.totalAmount || 0;
      
      // Group by payment status
      if (!summary.byPaymentStatus[stat.paymentStatus]) {
        summary.byPaymentStatus[stat.paymentStatus] = { count: 0, amount: 0 };
      }
      summary.byPaymentStatus[stat.paymentStatus].count += stat._count.id;
      summary.byPaymentStatus[stat.paymentStatus].amount += stat._sum.totalAmount || 0;
    });
    
    // Calculate average order value
    summary.averageOrderValue = summary.totalSales > 0 
      ? summary.totalAmount / summary.totalSales 
      : 0;
    
    // Process payment method statistics
    const paymentMethods = paymentMethodStats.map(stat => ({
      method: stat.paymentMethod,
      count: stat._count.id,
      amount: stat._sum.totalAmount || 0,
      percentage: summary.totalAmount > 0 
        ? ((stat._sum.totalAmount || 0) / summary.totalAmount) * 100 
        : 0
    }));
    
    // Process top customers
    const topCustomerIds = customerStats.map(stat => stat.contactId);
    const customerDetails = await prisma.contact.findMany({
      where: {
        id: {
          in: topCustomerIds
        }
      },
      select: {
        id: true,
        name: true,
        phone: true,
        loyaltyPoints: true,
        loyaltyTier: true
      }
    });
    
    const topCustomers = customerStats.map(stat => {
      const customer = customerDetails.find(c => c.id === stat.contactId);
      return {
        id: stat.contactId,
        name: customer?.name || 'Unknown',
        phone: customer?.phone || 'N/A',
        orderCount: stat._count.id,
        totalSpent: stat._sum.totalAmount || 0,
        loyaltyPoints: customer?.loyaltyPoints || 0,
        loyaltyTier: customer?.loyaltyTier || 'NONE'
      };
    });
    
    // Prepare the response data
    const responseData = {
      summary,
      salesByDate,
      topProducts,
      paymentMethods,
      topCustomers
    };
    
    // Cache the result for 5 minutes (300000 ms)
    cache.set(cacheKey, responseData, 5 * 60 * 1000);
    console.log(`Cached dashboard data for key: ${cacheKey}`);
    
    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error fetching sales dashboard data:", error);
    return NextResponse.json(
      { error: "Failed to fetch sales dashboard data" },
      { status: 500 }
    );
  }
}
