"use client";

import { useState } from "react";
import { Bot, Send } from "lucide-react";
import { Button } from "@/components/ui/button";

export function BasicAIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    { id: 1, text: "مرحباً! كيف يمكنني مساعدتك؟", isUser: false }
  ]);
  const [inputValue, setInputValue] = useState("");

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage = { id: Date.now(), text: inputValue, isUser: true };
    setMessages([...messages, userMessage]);

    // Store the input for processing
    const userInput = inputValue.trim();

    // Clear input
    setInputValue("");

    // Simulate AI response with different responses based on input
    setTimeout(() => {
      let responseText = "";

      // Check for greetings
      if (
        userInput.includes("مرحبا") ||
        userInput.includes("السلام عليكم") ||
        userInput.includes("صباح الخير") ||
        userInput.includes("مساء الخير") ||
        userInput.includes("اهلا")
      ) {
        responseText = "مرحباً بك! كيف يمكنني مساعدتك اليوم؟";
      }
      // Check for sales related questions
      else if (
        userInput.includes("مبيعات") ||
        userInput.includes("فاتورة") ||
        userInput.includes("بيع")
      ) {
        responseText = "يمكنك إنشاء فاتورة مبيعات جديدة من خلال الانتقال إلى قسم المبيعات والنقر على 'فاتورة جديدة'. هل تحتاج إلى مساعدة إضافية؟";
      }
      // Check for inventory related questions
      else if (
        userInput.includes("مخزون") ||
        userInput.includes("منتج") ||
        userInput.includes("بضاعة")
      ) {
        responseText = "يمكنك إدارة المخزون من خلال قسم المخزون في القائمة الجانبية. هناك يمكنك عرض المنتجات وإضافة منتجات جديدة وتعديل الكميات.";
      }
      // Check for customer related questions
      else if (
        userInput.includes("عميل") ||
        userInput.includes("زبون") ||
        userInput.includes("مشتري")
      ) {
        responseText = "يمكنك إدارة العملاء من خلال قسم جهات الاتصال. يمكنك إضافة عملاء جديد وعرض بيانات العملاء الحاليين وتعديلها.";
      }
      // Check for accounting related questions
      else if (
        userInput.includes("محاسبة") ||
        userInput.includes("حساب") ||
        userInput.includes("مالية")
      ) {
        responseText = "يمكنك الوصول إلى النظام المحاسبي من خلال قسم المحاسبة. هناك يمكنك عرض دفتر الأستاذ العام وميزان المراجعة وإنشاء سندات القبض والدفع.";
      }
      // Check for help request
      else if (
        userInput.includes("مساعدة") ||
        userInput.includes("ساعدني") ||
        userInput.includes("كيف")
      ) {
        responseText = "أنا هنا لمساعدتك! يمكنني تقديم المساعدة في استخدام نظام VERO ERP. يمكنك سؤالي عن المبيعات، المشتريات، المخزون، العملاء، المحاسبة، وغيرها من وظائف النظام.";
      }
      // Default response
      else {
        responseText = "شكراً لرسالتك! يمكنني مساعدتك في استخدام نظام VERO ERP. هل لديك سؤال محدد عن المبيعات، المشتريات، المخزون، العملاء، أو المحاسبة؟";
      }

      const aiResponse = {
        id: Date.now() + 1,
        text: responseText,
        isUser: false
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  return (
    <>
      {/* Chat button with pulse effect */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 right-6 p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 animate-pulse"
        >
          <Bot className="h-6 w-6" />
          <span className="absolute top-0 right-0 -mt-1 -mr-1 w-3 h-3 bg-red-500 rounded-full"></span>
        </button>
      )}

      {/* Chat window with animation */}
      {isOpen && (
        <div
          className="fixed bottom-6 right-6 w-80 h-96 bg-white rounded-lg shadow-xl flex flex-col z-50 border border-gray-200 animate-fadeIn"
          style={{
            boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.5), 0 8px 10px -6px rgba(59, 130, 246, 0.3)",
            borderTop: "3px solid #3b82f6"
          }}
        >
          {/* Header */}
          <div className="p-4 border-b bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg flex justify-between">
            <div className="flex items-center">
              <Bot className="h-5 w-5 mr-2" />
              <h3 className="font-medium">المساعد الذكي</h3>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white hover:text-gray-200 w-6 h-6 flex items-center justify-center rounded-full hover:bg-blue-700"
            >
              ×
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.isUser ? "justify-end" : "justify-start"
                }`}
              >
                {!message.isUser && (
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <Bot className="h-5 w-5 text-blue-600" />
                  </div>
                )}
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.isUser
                      ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md"
                      : "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-800 shadow-sm"
                  }`}
                  dir="rtl"
                >
                  <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                </div>
                {message.isUser && (
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center ml-2">
                    <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Input */}
          <div className="p-4 border-t">
            <div className="flex items-center bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                placeholder="اكتب رسالتك هنا..."
                className="flex-1 px-4 py-2 bg-transparent border-none focus:outline-none text-right"
                dir="rtl"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim()}
                className="m-1 rounded-lg"
                size="sm"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
