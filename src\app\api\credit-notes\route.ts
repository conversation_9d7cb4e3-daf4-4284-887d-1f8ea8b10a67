import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { generateCreditNoteNumber } from "@/lib/utils";
import {
  createCreditNoteJournalEntries,
  TransactionType,
  PaymentInfo
} from "@/lib/accounting";

// GET /api/credit-notes - Get all credit notes
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const contactId = url.searchParams.get("contactId");
    const status = url.searchParams.get("status");
    const limit = url.searchParams.get("limit") ? parseInt(url.searchParams.get("limit")!) : 100;
    const page = url.searchParams.get("page") ? parseInt(url.searchParams.get("page")!) : 1;
    const skip = (page - 1) * limit;

    // Build query filters
    const filters: any = {};
    if (contactId) filters.contactId = contactId;
    if (status) filters.status = status;

    // Get credit notes with pagination
    const creditNotes = await db.creditNote.findMany({
      where: filters,
      include: {
        Contact: true,
        Branch: true,
        User: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        CreditNoteItem: {
          include: {
            Product: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await db.creditNote.count({
      where: filters,
    });

    return NextResponse.json({
      data: creditNotes,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error: any) {
    console.error("Error fetching credit notes:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch credit notes" },
      { status: 500 }
    );
  }
}

// POST /api/credit-notes - Create a new credit note
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user ID from session
    const userId = session.user.id;

    // Parse request body
    const data = await req.json();

    // Validate required fields
    if (!data.contactId) {
      return NextResponse.json({ error: "Customer is required" }, { status: 400 });
    }

    if (!data.branchId) {
      return NextResponse.json({ error: "Branch is required" }, { status: 400 });
    }

    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json({ error: "At least one item is required" }, { status: 400 });
    }

    // Generate credit note number
    const branch = await db.branch.findUnique({
      where: { id: data.branchId },
      select: { code: true },
    });

    if (!branch) {
      return NextResponse.json({ error: "Branch not found" }, { status: 404 });
    }

    const creditNoteNumber = await generateCreditNoteNumber(branch.code);

    // Create credit note with items in a transaction
    const creditNote = await db.$transaction(async (tx) => {
      // Create credit note
      const newCreditNote = await tx.creditNote.create({
        data: {
          userId,
          contactId: data.contactId,
          branchId: data.branchId,
          originalInvoiceId: data.originalInvoiceId,
          originalInvoiceNumber: data.originalInvoiceNumber,
          creditNoteNumber,
          date: data.date ? new Date(data.date) : new Date(),
          status: data.status || "PENDING",
          paymentStatus: data.paymentStatus || "UNPAID",
          paymentMethod: data.paymentMethod,
          totalAmount: data.totalAmount,
          subtotalAmount: data.subtotalAmount,
          taxAmount: data.taxAmount || 0,
          taxRate: data.taxRate || 0,
          discountAmount: data.discountAmount || 0,
          applyTax: data.applyTax || false,
          currency: data.currency || "EGP",
          notes: data.notes,
        },
      });

      // Create payment methods if provided
      if (data.payments && Array.isArray(data.payments) && data.payments.length > 0) {
        for (const payment of data.payments) {
          await tx.creditNotePayment.create({
            data: {
              creditNoteId: newCreditNote.id,
              method: payment.method,
              amount: payment.amount,
            },
          });

          // If payment method is CUSTOMER_ACCOUNT, update customer balance
          if (payment.method === "CUSTOMER_ACCOUNT") {
            const contact = await tx.contact.findUnique({
              where: { id: data.contactId },
            });

            if (contact) {
              await tx.contact.update({
                where: { id: data.contactId },
                data: {
                  balance: contact.balance - payment.amount,
                },
              });
            }
          }
        }
      }

      // Create credit note items
      for (const item of data.items) {
        await tx.creditNoteItem.create({
          data: {
            creditNoteId: newCreditNote.id,
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.total || item.unitPrice * item.quantity,
            reason: item.reason,
            originalItemId: item.originalItemId,
          },
        });

        // Update inventory - add items back to stock
        // Find the warehouse for the branch
        const warehouse = await tx.warehouse.findFirst({
          where: {
            branchId: data.branchId,
            isDefault: true,
          },
        });

        if (!warehouse) {
          throw new Error(`No default warehouse found for branch ${data.branchId}`);
        }

        const inventory = await tx.inventory.findFirst({
          where: {
            productId: item.productId,
            warehouseId: warehouse.id,
          },
        });

        if (inventory) {
          await tx.inventory.update({
            where: { id: inventory.id },
            data: {
              quantity: inventory.quantity + item.quantity,
            },
          });
        }

        // If this is a component, update component inventory too
        if (item.originalItemId) {
          const originalItem = await tx.saleItem.findUnique({
            where: { id: item.originalItemId },
            include: { components: true },
          });

          if (originalItem && originalItem.components) {
            for (const component of originalItem.components) {
              // Use the same warehouse we found earlier
              const componentInventory = await tx.inventory.findFirst({
                where: {
                  productId: component.componentId,
                  warehouseId: warehouse.id,
                },
              });

              if (componentInventory) {
                await tx.inventory.update({
                  where: { id: componentInventory.id },
                  data: {
                    quantity: componentInventory.quantity + component.totalQuantity,
                  },
                });
              }
            }
          }
        }
      }

      return newCreditNote;
    });

    // Create journal entries for the credit note using the accounting service
    try {
      // Get contact name
      const contact = await db.contact.findUnique({
        where: {
          id: data.contactId,
        },
        select: {
          name: true,
        },
      });

      // Prepare payment data for journal entries
      const payments: PaymentInfo[] = [];

      // If we have payment data in the request
      if (data.payments && Array.isArray(data.payments) && data.payments.length > 0) {
        // Use the payment data from the request
        payments.push(...data.payments);
      } else if (data.paymentMethod) {
        // Use the single payment method
        payments.push({
          method: data.paymentMethod,
          amount: data.totalAmount,
        });
      }

      // Only create journal entries if we have valid payment methods
      if (payments.length > 0) {
        await createCreditNoteJournalEntries(
          payments,
          data.totalAmount,
          data.subtotalAmount,
          {
            transactionType: TransactionType.CREDIT_NOTE,
            referenceId: creditNote.id,
            referenceNumber: creditNote.creditNoteNumber,
            description: `Credit Note ${creditNote.creditNoteNumber}`,
            contactId: data.contactId,
            contactName: contact?.name,
            branchId: data.branchId,
            date: new Date(),
          }
        );
      }
    } catch (journalError) {
      console.error("Error creating journal entries for credit note:", journalError);
      // Don't fail the credit note creation if journal entries fail
    }

    return NextResponse.json(creditNote, { status: 201 });
  } catch (error: any) {
    console.error("Error creating credit note:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create credit note" },
      { status: 500 }
    );
  }
}
