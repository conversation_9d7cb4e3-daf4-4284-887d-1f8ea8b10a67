const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('Creating default permissions...');

  // Define default permissions
  const defaultPermissions = [
    // User permissions
    { name: 'view_users', description: 'View users' },
    { name: 'add_users', description: 'Add new users' },
    { name: 'edit_users', description: 'Edit existing users' },
    { name: 'delete_users', description: 'Delete users' },

    // Branch permissions
    { name: 'view_branches', description: 'View branches' },
    { name: 'add_branches', description: 'Add new branches' },
    { name: 'edit_branches', description: 'Edit existing branches' },
    { name: 'delete_branches', description: 'Delete branches' },

    // Warehouse permissions
    { name: 'view_warehouses', description: 'View warehouses' },
    { name: 'add_warehouses', description: 'Add new warehouses' },
    { name: 'edit_warehouses', description: 'Edit existing warehouses' },
    { name: 'delete_warehouses', description: 'Delete warehouses' },

    // Product permissions
    { name: 'view_products', description: 'View products' },
    { name: 'add_products', description: 'Add new products' },
    { name: 'edit_products', description: 'Edit existing products' },
    { name: 'delete_products', description: 'Delete products' },

    // Inventory permissions
    { name: 'view_inventory', description: 'View inventory' },
    { name: 'adjust_inventory', description: 'Adjust inventory levels' },

    // Sales permissions
    { name: 'view_sales', description: 'View sales' },
    { name: 'add_sales', description: 'Create new sales' },
    { name: 'edit_sales', description: 'Edit existing sales' },
    { name: 'delete_sales', description: 'Delete sales' },

    // Purchase permissions
    { name: 'view_purchases', description: 'View purchases' },
    { name: 'add_purchases', description: 'Create new purchases' },
    { name: 'edit_purchases', description: 'Edit existing purchases' },
    { name: 'delete_purchases', description: 'Delete purchases' },

    // Customer permissions
    { name: 'view_customers', description: 'View customers' },
    { name: 'add_customers', description: 'Add new customers' },
    { name: 'edit_customers', description: 'Edit existing customers' },
    { name: 'delete_customers', description: 'Delete customers' },

    // Supplier permissions
    { name: 'view_suppliers', description: 'View suppliers' },
    { name: 'add_suppliers', description: 'Add new suppliers' },
    { name: 'edit_suppliers', description: 'Edit existing suppliers' },
    { name: 'delete_suppliers', description: 'Delete suppliers' },

    // Account permissions
    { name: 'view_accounts', description: 'View accounts' },
    { name: 'add_accounts', description: 'Add new accounts' },
    { name: 'edit_accounts', description: 'Edit existing accounts' },
    { name: 'delete_accounts', description: 'Delete accounts' },

    // Report permissions
    { name: 'view_reports', description: 'View financial and inventory reports' },
  ];

  // Create permissions
  for (const permission of defaultPermissions) {
    // Check if permission already exists
    const existingPermission = await prisma.permission.findFirst({
      where: {
        name: permission.name,
      },
    });

    if (!existingPermission) {
      await prisma.permission.create({
        data: permission,
      });
      console.log(`Created permission: ${permission.name}`);
    } else {
      console.log(`Permission already exists: ${permission.name}`);
    }
  }

  // Assign all permissions to admin users
  const adminUsers = await prisma.user.findMany({
    where: {
      role: 'ADMIN',
    },
  });

  const allPermissions = await prisma.permission.findMany();

  for (const admin of adminUsers) {
    await prisma.user.update({
      where: {
        id: admin.id,
      },
      data: {
        permissions: {
          connect: allPermissions.map(p => ({ id: p.id })),
        },
      },
    });
    console.log(`Assigned all permissions to admin: ${admin.name}`);
  }

  console.log('Default permissions created successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
