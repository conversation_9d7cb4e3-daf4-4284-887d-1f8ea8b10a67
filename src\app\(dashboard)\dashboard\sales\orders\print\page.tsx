"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils";
import { Printer, Calendar, Search } from "lucide-react";

export default function PrintSalesPage() {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [selectedBranch, setSelectedBranch] = useState("all");
  const [branches, setBranches] = useState([
    { id: "all", name: "All Branches" }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [sales, setSales] = useState([]);
  const [error, setError] = useState<string | null>(null);

  // Set default dates (last 30 days)
  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    setEndDate(today.toISOString().split("T")[0]);
    setStartDate(thirtyDaysAgo.toISOString().split("T")[0]);

    // Fetch branches
    fetchBranches();
  }, []);

  // Fetch branches
  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches');
      if (response.ok) {
        const data = await response.json();
        setBranches([
          { id: "all", name: "All Branches" },
          ...data.map((branch: any) => ({ id: branch.id, name: branch.name }))
        ]);
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
    }
  };

  // Generate report
  const generateReport = async () => {
    if (!startDate || !endDate) {
      setError("Please select both start and end dates");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('startDate', startDate);
      params.append('endDate', endDate);
      params.append('limit', '500'); // Get more results for printing

      if (selectedBranch !== 'all') {
        params.append('branchId', selectedBranch);
      }

      // Fetch data with parameters
      const response = await fetch(`/api/sales?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch sales: ${response.statusText}`);
      }

      const data = await response.json();
      setSales(data.sales);
    } catch (error) {
      console.error("Error generating report:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
      setSales([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Print report
  const printReport = () => {
    window.print();
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div className="flex items-center">
            <Printer className="h-6 w-6 text-indigo-500 mr-2" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Print Sales Report
            </h3>
          </div>
        </div>

        {/* Report Filters */}
        <div className="px-4 py-5 sm:px-6 border-t border-gray-200">
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            {/* Branch */}
            <div className="sm:col-span-2">
              <label htmlFor="branch" className="block text-sm font-medium text-gray-700">
                Branch
              </label>
              <select
                id="branch"
                name="branch"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                value={selectedBranch}
                onChange={(e) => setSelectedBranch(e.target.value)}
              >
                {branches.map((branch) => (
                  <option key={branch.id} value={branch.id}>
                    {branch.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range */}
            <div className="sm:col-span-2">
              <label htmlFor="start-date" className="block text-sm font-medium text-gray-700">
                Start Date
              </label>
              <input
                type="date"
                name="start-date"
                id="start-date"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="end-date" className="block text-sm font-medium text-gray-700">
                End Date
              </label>
              <input
                type="date"
                name="end-date"
                id="end-date"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>

          <div className="mt-4 flex space-x-3">
            <button
              type="button"
              onClick={generateReport}
              disabled={isLoading || !startDate || !endDate}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Search className="h-4 w-4 mr-2" />
              {isLoading ? "Loading..." : "Generate Report"}
            </button>
            
            {sales.length > 0 && (
              <button
                type="button"
                onClick={printReport}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Report
              </button>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="px-4 py-3 bg-red-50 border-t border-b border-red-200 text-red-700">
            <p>{error}</p>
          </div>
        )}

        {/* Report Results */}
        {sales.length > 0 && (
          <div className="px-4 py-5 sm:px-6 border-t border-gray-200">
            <div className="print:text-black">
              <h2 className="text-xl font-bold mb-4 print:text-black">Sales Report</h2>
              <p className="mb-2 print:text-black">
                <span className="font-medium">Period:</span> {new Date(startDate).toLocaleDateString()} to {new Date(endDate).toLocaleDateString()}
              </p>
              <p className="mb-4 print:text-black">
                <span className="font-medium">Branch:</span> {branches.find(b => b.id === selectedBranch)?.name || "All Branches"}
              </p>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 print:text-black">
                  <thead className="bg-gray-50 print:bg-white">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider print:text-black">
                        Invoice #
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider print:text-black">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider print:text-black">
                        Customer
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider print:text-black">
                        Branch
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider print:text-black">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider print:text-black">
                        Payment
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider print:text-black">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sales.map((sale: any) => (
                      <tr key={sale.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-indigo-600 print:text-black">
                          {sale.invoiceNumber}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 print:text-black">
                          {sale.date ? new Date(sale.date).toLocaleDateString() : ''}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 print:text-black">
                          {sale.customer?.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 print:text-black">
                          {sale.branch?.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 print:text-black">
                          {sale.status}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 print:text-black">
                          {sale.paymentStatus}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 print:text-black">
                          {sale.totalAmount?.toFixed(2)} ج.م
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-50 print:bg-white">
                      <td colSpan={6} className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right print:text-black">
                        Total:
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 print:text-black">
                        {formatCurrency(sales.reduce((sum: number, sale: any) => sum + (sale.totalAmount || 0), 0))}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
