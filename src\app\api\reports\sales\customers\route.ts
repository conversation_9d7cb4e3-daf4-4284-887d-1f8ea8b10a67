import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import cache from "@/lib/cache";
import { format, subDays, parseISO, subMonths } from "date-fns";

// GET /api/reports/sales/customers - Get customer sales report data
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    
    // Get query parameters
    const loyaltyTier = url.searchParams.get("loyaltyTier") || undefined;
    const search = url.searchParams.get("search") || "";
    const startDate = url.searchParams.get("startDate") 
      ? new Date(url.searchParams.get("startDate") as string) 
      : subDays(new Date(), 30);
    const endDate = url.searchParams.get("endDate") 
      ? new Date(url.searchParams.get("endDate") as string) 
      : new Date();
    
    // Generate cache key
    const cacheKey = `customer_sales_report_${loyaltyTier || 'all'}_${search}_${startDate.toISOString()}_${endDate.toISOString()}`;
    
    // Check if we have cached data
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached customer report data for key: ${cacheKey}`);
      return NextResponse.json(cachedData);
    }
    
    console.log(`Cache miss for key: ${cacheKey}, fetching from database`);
    
    // Build where clause for sales
    const saleWhere: any = {
      date: {
        gte: startDate,
        lte: endDate
      }
    };
    
    // Build where clause for contacts
    const contactWhere: any = {
      isCustomer: true
    };
    
    if (loyaltyTier && loyaltyTier !== "all") {
      contactWhere.loyaltyTier = loyaltyTier;
    }
    
    if (search) {
      contactWhere.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          phone: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ];
    }
    
    // Calculate previous period for retention metrics
    const previousPeriodStart = new Date(startDate);
    previousPeriodStart.setDate(previousPeriodStart.getDate() - (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const previousPeriodEnd = new Date(startDate);
    previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);
    
    // Fetch all required data in parallel
    const [
      topCustomers,
      customersByLoyaltyTier,
      purchaseFrequency,
      newCustomers,
      returningCustomers,
      previousPeriodCustomers
    ] = await Promise.all([
      // Top customers
      prisma.$queryRaw`
        SELECT 
          c.id,
          c.name,
          c.phone,
          c."loyaltyTier",
          c."loyaltyPoints",
          COUNT(s.id) as "orderCount",
          SUM(s."totalAmount") as "totalSpent",
          CASE 
            WHEN COUNT(s.id) > 0 
            THEN SUM(s."totalAmount") / COUNT(s.id) 
            ELSE 0 
          END as "averageOrderValue"
        FROM "Contact" c
        JOIN "Sale" s ON c.id = s."contactId"
        WHERE 
          s.date >= ${startDate} AND 
          s.date <= ${endDate}
          ${loyaltyTier && loyaltyTier !== "all" ? prisma.$raw`AND c."loyaltyTier" = ${loyaltyTier}` : prisma.$raw``}
          ${search ? prisma.$raw`AND (c.name ILIKE ${`%${search}%`} OR c.phone ILIKE ${`%${search}%`})` : prisma.$raw``}
        GROUP BY c.id, c.name, c.phone, c."loyaltyTier", c."loyaltyPoints"
        ORDER BY "totalSpent" DESC
        LIMIT 20
      `,
      
      // Customers by loyalty tier
      prisma.$queryRaw`
        SELECT 
          c."loyaltyTier" as tier,
          COUNT(DISTINCT c.id) as "customerCount",
          COUNT(s.id) as "orderCount",
          SUM(s."totalAmount") as "totalSpent",
          CASE 
            WHEN COUNT(DISTINCT c.id) > 0 
            THEN SUM(s."totalAmount") / COUNT(DISTINCT c.id) 
            ELSE 0 
          END as "averageSpend"
        FROM "Contact" c
        JOIN "Sale" s ON c.id = s."contactId"
        WHERE 
          s.date >= ${startDate} AND 
          s.date <= ${endDate}
          ${search ? prisma.$raw`AND (c.name ILIKE ${`%${search}%`} OR c.phone ILIKE ${`%${search}%`})` : prisma.$raw``}
        GROUP BY c."loyaltyTier"
        ORDER BY "totalSpent" DESC
      `,
      
      // Purchase frequency
      prisma.$queryRaw`
        WITH CustomerPurchases AS (
          SELECT 
            c.id,
            COUNT(s.id) as purchase_count
          FROM "Contact" c
          JOIN "Sale" s ON c.id = s."contactId"
          WHERE 
            s.date >= ${startDate} AND 
            s.date <= ${endDate}
            ${loyaltyTier && loyaltyTier !== "all" ? prisma.$raw`AND c."loyaltyTier" = ${loyaltyTier}` : prisma.$raw``}
            ${search ? prisma.$raw`AND (c.name ILIKE ${`%${search}%`} OR c.phone ILIKE ${`%${search}%`})` : prisma.$raw``}
          GROUP BY c.id
        )
        SELECT 
          CASE 
            WHEN purchase_count = 1 THEN 'One-time'
            WHEN purchase_count = 2 THEN 'Twice'
            WHEN purchase_count = 3 THEN 'Three times'
            WHEN purchase_count = 4 THEN 'Four times'
            WHEN purchase_count >= 5 THEN 'Five or more'
          END as frequency,
          COUNT(*) as "customerCount",
          SUM(
            (SELECT SUM(s."totalAmount") 
             FROM "Sale" s 
             WHERE s."contactId" = cp.id AND s.date >= ${startDate} AND s.date <= ${endDate})
          ) as "totalRevenue"
        FROM CustomerPurchases cp
        GROUP BY frequency
        ORDER BY 
          CASE 
            WHEN frequency = 'One-time' THEN 1
            WHEN frequency = 'Twice' THEN 2
            WHEN frequency = 'Three times' THEN 3
            WHEN frequency = 'Four times' THEN 4
            WHEN frequency = 'Five or more' THEN 5
          END
      `,
      
      // New customers (first purchase in this period)
      prisma.$queryRaw`
        SELECT COUNT(DISTINCT c.id) as count
        FROM "Contact" c
        JOIN "Sale" s ON c.id = s."contactId"
        WHERE 
          s.date >= ${startDate} AND 
          s.date <= ${endDate}
          ${loyaltyTier && loyaltyTier !== "all" ? prisma.$raw`AND c."loyaltyTier" = ${loyaltyTier}` : prisma.$raw``}
          ${search ? prisma.$raw`AND (c.name ILIKE ${`%${search}%`} OR c.phone ILIKE ${`%${search}%`})` : prisma.$raw``}
          AND NOT EXISTS (
            SELECT 1 FROM "Sale" s2
            WHERE s2."contactId" = c.id AND s2.date < ${startDate}
          )
      `,
      
      // Returning customers (had purchases before this period)
      prisma.$queryRaw`
        SELECT COUNT(DISTINCT c.id) as count
        FROM "Contact" c
        JOIN "Sale" s ON c.id = s."contactId"
        WHERE 
          s.date >= ${startDate} AND 
          s.date <= ${endDate}
          ${loyaltyTier && loyaltyTier !== "all" ? prisma.$raw`AND c."loyaltyTier" = ${loyaltyTier}` : prisma.$raw``}
          ${search ? prisma.$raw`AND (c.name ILIKE ${`%${search}%`} OR c.phone ILIKE ${`%${search}%`})` : prisma.$raw``}
          AND EXISTS (
            SELECT 1 FROM "Sale" s2
            WHERE s2."contactId" = c.id AND s2.date < ${startDate}
          )
      `,
      
      // Customers who purchased in previous period
      prisma.$queryRaw`
        SELECT COUNT(DISTINCT c.id) as count
        FROM "Contact" c
        JOIN "Sale" s ON c.id = s."contactId"
        WHERE 
          s.date >= ${previousPeriodStart} AND 
          s.date <= ${previousPeriodEnd}
          ${loyaltyTier && loyaltyTier !== "all" ? prisma.$raw`AND c."loyaltyTier" = ${loyaltyTier}` : prisma.$raw``}
          ${search ? prisma.$raw`AND (c.name ILIKE ${`%${search}%`} OR c.phone ILIKE ${`%${search}%`})` : prisma.$raw``}
      `
    ]);
    
    // Calculate total revenue for percentage calculation
    const totalRevenue = purchaseFrequency.reduce((sum: number, frequency: any) => sum + parseFloat(frequency.totalRevenue), 0);
    
    // Add percentage to purchase frequency
    const formattedFrequency = purchaseFrequency.map((frequency: any) => ({
      ...frequency,
      percentage: totalRevenue > 0 ? parseFloat(frequency.totalRevenue) / totalRevenue : 0
    }));
    
    // Calculate retention metrics
    const newCustomerCount = newCustomers[0]?.count || 0;
    const returningCustomerCount = returningCustomers[0]?.count || 0;
    const previousPeriodCustomerCount = previousPeriodCustomers[0]?.count || 0;
    
    const totalCustomers = newCustomerCount + returningCustomerCount;
    const newPercentage = totalCustomers > 0 ? (newCustomerCount / totalCustomers) * 100 : 0;
    const returningPercentage = totalCustomers > 0 ? (returningCustomerCount / totalCustomers) * 100 : 0;
    
    // Calculate retention rate (customers who returned from previous period)
    const retentionRate = previousPeriodCustomerCount > 0 
      ? (returningCustomerCount / previousPeriodCustomerCount) * 100 
      : 0;
    
    // Prepare the response data
    const responseData = {
      topCustomers,
      customersByLoyaltyTier,
      purchaseFrequency: formattedFrequency,
      customerRetention: {
        newCustomers: newCustomerCount,
        returningCustomers: returningCustomerCount,
        totalCustomers,
        newPercentage: newPercentage.toFixed(1),
        returningPercentage: returningPercentage.toFixed(1),
        retentionRate: retentionRate.toFixed(1)
      }
    };
    
    // Cache the result for 5 minutes (300000 ms)
    cache.set(cacheKey, responseData, 5 * 60 * 1000);
    console.log(`Cached customer report data for key: ${cacheKey}`);
    
    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error fetching customer sales report data:", error);
    return NextResponse.json(
      { error: "Failed to fetch customer sales report data" },
      { status: 500 }
    );
  }
}
