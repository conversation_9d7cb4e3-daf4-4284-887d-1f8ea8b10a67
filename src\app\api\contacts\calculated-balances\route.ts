import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/contacts/calculated-balances - Get calculated balances for all contacts
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check permissions
    const hasViewCustomersPermission = await hasPermission("view_customers");
    const hasViewSuppliersPermission = await hasPermission("view_suppliers");
    const hasViewFinancePermission = await hasPermission("view_finance");
    const isAdmin = session.user.role === "ADMIN";

    if (!hasViewCustomersPermission && !hasViewSuppliersPermission && !hasViewFinancePermission && !isAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to view contacts" },
        { status: 403 }
      );
    }

    // Get all contacts
    const contacts = await db.contact.findMany({
      where: {
        isActive: true,
      },
      select: {
        id: true,
        openingBalance: true,
      },
    });

    // Calculate balances for each contact
    const balances: Record<string, number> = {};

    for (const contact of contacts) {
      try {
        // Calculate balance for this contact
        const balance = await calculateContactBalance(contact.id);
        balances[contact.id] = balance;
      } catch (error) {
        console.error(`Error calculating balance for contact ${contact.id}:`, error);
        // Use opening balance as fallback
        balances[contact.id] = contact.openingBalance;
      }
    }

    return NextResponse.json(balances);
  } catch (error) {
    console.error("Error fetching calculated balances:", error);
    return NextResponse.json(
      { error: "Failed to fetch calculated balances" },
      { status: 500 }
    );
  }
}

// Helper function to calculate the correct balance for a contact
async function calculateContactBalance(contactId: string): Promise<number> {
  // Get the contact
  const contact = await db.contact.findUnique({
    where: {
      id: contactId,
    },
  });

  if (!contact) {
    throw new Error("Contact not found");
  }

  // Start with opening balance
  let balance = contact.openingBalance;

  // Get all sales for this contact
  const sales = await db.sale.findMany({
    where: {
      contactId,
    },
  });

  // Get all transactions for this contact
  const transactions = await db.transaction.findMany({
    where: {
      contactId,
    },
  });

  // Calculate balance from sales
  for (const sale of sales) {
    // Check if this sale already has a transaction
    const hasTransaction = transactions.some(
      t => t.reference === sale.invoiceNumber && t.referenceType === "SALE"
    );

    // Only add sales that don't have corresponding transactions
    if (!hasTransaction) {
      if (contact.isCustomer) {
        balance += sale.totalAmount;
      } else if (contact.isSupplier) {
        balance -= sale.totalAmount;
      }
    }
  }

  // Calculate balance from transactions
  for (const transaction of transactions) {
    // Skip transactions that are already included in sales
    if (transaction.referenceType === "SALE" && sales.some(s => s.invoiceNumber === transaction.reference)) {
      continue;
    }

    if (transaction.referenceType === "RECEIPT" && transaction.type === "DEBIT") {
      // Customer payment received - decrease balance
      balance -= transaction.amount;
    } else if (transaction.referenceType === "PAYMENT" && transaction.type === "CREDIT") {
      // Payment to supplier - decrease balance
      balance -= transaction.amount;
    } else if (transaction.type === "DEBIT") {
      // Other debit transaction
      balance += transaction.amount;
    } else if (transaction.type === "CREDIT") {
      // Other credit transaction
      balance -= transaction.amount;
    }
  }

  return balance;
}
