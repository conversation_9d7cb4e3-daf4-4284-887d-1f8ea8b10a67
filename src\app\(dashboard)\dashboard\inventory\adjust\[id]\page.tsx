"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { use } from "react";

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  warehouseId: string;
  warehouseName: string;
  quantity: number;
  categoryId: string;
  categoryName: string;
}

export default function AdjustInventoryPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const resolvedParams = use(params);
  const inventoryId = resolvedParams.id;

  const [inventoryItem, setInventoryItem] = useState<InventoryItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [quantity, setQuantity] = useState<number>(0);
  const [reason, setReason] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch inventory item
  useEffect(() => {
    const fetchInventoryItem = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/inventory/${inventoryId}`);

        if (!response.ok) {
          throw new Error("Failed to fetch inventory item");
        }

        const data = await response.json();
        setInventoryItem(data);
        setQuantity(data.quantity);
      } catch (error) {
        console.error("Error fetching inventory item:", error);
        setError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    fetchInventoryItem();
  }, [inventoryId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inventoryItem) {
      return;
    }

    if (quantity < 0) {
      setError("Quantity cannot be negative");
      return;
    }

    if (quantity === inventoryItem.quantity) {
      setError("New quantity is the same as current quantity");
      return;
    }

    if (!reason.trim()) {
      setError("Please provide a reason for the adjustment");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: inventoryItem.productId,
          warehouseId: inventoryItem.warehouseId,
          quantity: quantity,
          reason: reason,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to adjust inventory');
      }

      const data = await response.json();

      const changeText = quantity > inventoryItem.quantity
        ? `increased by ${quantity - inventoryItem.quantity}`
        : `decreased by ${inventoryItem.quantity - quantity}`;

      setSuccess(`Successfully adjusted inventory. Quantity ${changeText} units.`);

      // Update the inventory item with new quantity
      setInventoryItem({
        ...inventoryItem,
        quantity: quantity,
      });

    } catch (error) {
      console.error("Error adjusting inventory:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
        <p className="mt-2 text-black">Loading inventory item...</p>
      </div>
    );
  }

  if (error && !inventoryItem) {
    return (
      <div className="text-center py-10">
        <p className="text-red-600">{error}</p>
        <button
          onClick={() => router.push("/dashboard/inventory")}
          className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Inventory
        </button>
      </div>
    );
  }

  if (!inventoryItem) {
    return (
      <div className="text-center py-10">
        <p className="text-black font-medium">Inventory item not found.</p>
        <button
          onClick={() => router.push("/dashboard/inventory")}
          className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Inventory
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-black sm:text-3xl sm:truncate">
            Adjust Inventory: {inventoryItem.productName}
          </h2>
          <p className="mt-1 text-sm text-black">
            Warehouse: {inventoryItem.warehouseName} | Current Quantity: {inventoryItem.quantity}
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/dashboard/inventory"
            className="inline-flex items-center px-4 py-2 border-2 border-gray-300 rounded-md shadow-sm text-sm font-medium text-black bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Inventory
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          {success && (
            <div className="mb-4 bg-green-50 border-l-4 border-green-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-700">
                    {success}
                  </p>
                </div>
              </div>
            </div>
          )}

          {error && (
            <div className="mb-4 bg-red-50 border-l-4 border-red-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">
                    {error}
                  </p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <label htmlFor="current-quantity" className="block text-sm font-medium text-black">
                  Current Quantity
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="current-quantity"
                    id="current-quantity"
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 bg-gray-100 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    value={inventoryItem.quantity}
                    disabled
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="new-quantity" className="block text-sm font-medium text-black">
                  New Quantity
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="new-quantity"
                    id="new-quantity"
                    min="0"
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    value={quantity}
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    required
                  />
                </div>
              </div>

              <div className="sm:col-span-6">
                <label htmlFor="reason" className="block text-sm font-medium text-black">
                  Reason for Adjustment
                </label>
                <div className="mt-1">
                  <textarea
                    id="reason"
                    name="reason"
                    rows={3}
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    required
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Please provide a detailed reason for this inventory adjustment.
                </p>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => router.push('/dashboard/inventory')}
                className="mr-3 inline-flex items-center px-4 py-2 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-black bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || quantity === inventoryItem.quantity}
                className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(isSubmitting || quantity === inventoryItem.quantity) ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isSubmitting ? 'Adjusting...' : 'Adjust Inventory'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
