"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Gift,
  Calendar,
  Star,
  Award,
  Check,
  X,
  AlertTriangle,
  Cake,
  PartyPopper,
  MailOpen
} from "lucide-react";
import { format } from "date-fns";

interface CustomerData {
  id: string;
  name: string;
  phone: string;
  email: string;
  birthday: string;
  loyaltyPoints: number;
  loyaltyTier: string;
  isVIP: boolean;
  daysUntilBirthday: number;
}

interface BirthdayGift {
  id: string;
  contactId: string;
  year: number;
  points: number;
  description: string;
  createdAt: string;
}

export default function CustomerBirthdayGiftPage() {
  const params = useParams();
  const router = useRouter();
  const contactId = params.id as string;

  const [isLoading, setIsLoading] = useState(true);
  const [customer, setCustomer] = useState<CustomerData | null>(null);
  const [previousGifts, setPreviousGifts] = useState<BirthdayGift[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [giftSent, setGiftSent] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch customer data
  useEffect(() => {
    const fetchCustomerData = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll simulate data

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock customer data
        const mockCustomer: CustomerData = {
          id: contactId,
          name: "Ahmed Mohamed",
          phone: "01012345678",
          email: "<EMAIL>",
          birthday: "1985-12-15",
          loyaltyPoints: 5200,
          loyaltyTier: "GOLD",
          isVIP: true,
          daysUntilBirthday: 3
        };

        // Mock previous gifts
        const mockPreviousGifts: BirthdayGift[] = [
          {
            id: "1",
            contactId: contactId,
            year: 2022,
            points: 500,
            description: "Gold tier birthday gift: 500 points (VIP bonus: 2x points)",
            createdAt: "2022-12-15T10:30:00Z"
          },
          {
            id: "2",
            contactId: contactId,
            year: 2021,
            points: 250,
            description: "Silver tier birthday gift: 250 points",
            createdAt: "2021-12-15T09:15:00Z"
          }
        ];

        setCustomer(mockCustomer);
        setPreviousGifts(mockPreviousGifts);
      } catch (error) {
        console.error("Error fetching customer data:", error);
        setError("Failed to load customer data. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomerData();
  }, [contactId]);

  // Calculate gift points based on loyalty tier and VIP status
  const calculateGiftPoints = () => {
    if (!customer) return 0;

    let basePoints = 0;

    switch (customer.loyaltyTier) {
      case "PLATINUM":
        basePoints = 1000;
        break;
      case "GOLD":
        basePoints = 500;
        break;
      case "SILVER":
        basePoints = 250;
        break;
      default: // BRONZE
        basePoints = 100;
    }

    // Double points for VIP customers
    return customer.isVIP ? basePoints * 2 : basePoints;
  };

  // Handle sending birthday gift
  const handleSendGift = async () => {
    if (!customer) return;

    setIsSending(true);
    setError(null);

    try {
      // Send birthday gift points
      const giftResponse = await fetch('/api/loyalty', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactId: customer.id,
          operation: 'ADD_POINTS',
          points: calculateGiftPoints()
        }),
      });

      if (!giftResponse.ok) {
        throw new Error('Failed to add birthday gift points');
      }

      // Send email notification
      if (customer.email) {
        await fetch('/api/loyalty/email-notification', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contactId: customer.id,
            type: 'BIRTHDAY_GIFT',
            points: calculateGiftPoints()
          }),
        });
      }

      setGiftSent(true);

      // Add to previous gifts
      const newGift: BirthdayGift = {
        id: `${Date.now()}`,
        contactId: customer.id,
        year: new Date().getFullYear(),
        points: calculateGiftPoints(),
        description: `${customer.loyaltyTier} tier birthday gift: ${calculateGiftPoints()} points${customer.isVIP ? " (VIP bonus: 2x points)" : ""}`,
        createdAt: new Date().toISOString()
      };

      setPreviousGifts([newGift, ...previousGifts]);
    } catch (error) {
      console.error("Error sending birthday gift:", error);
      setError("Failed to send birthday gift. Please try again.");
    } finally {
      setIsSending(false);
    }
  };

  // Get tier color
  const getTierColor = (tier: string) => {
    switch (tier) {
      case "PLATINUM":
        return "#E5E4E2";
      case "GOLD":
        return "#FFD700";
      case "SILVER":
        return "#C0C0C0";
      default:
        return "#cd7f32";
    }
  };

  // Check if birthday is today
  const isBirthdayToday = () => {
    if (!customer) return false;

    const today = format(new Date(), "MM-dd");
    const birthday = format(new Date(customer.birthday), "MM-dd");

    return today === birthday;
  };

  // Check if gift already sent this year
  const isGiftAlreadySentThisYear = () => {
    if (!customer || previousGifts.length === 0) return false;

    const currentYear = new Date().getFullYear();
    return previousGifts.some(gift => gift.year === currentYear);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading customer data...</p>
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <p className="text-xl font-medium text-gray-900 mb-2">Customer Not Found</p>
          <p className="text-gray-500 mb-4">The customer information could not be loaded.</p>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          Birthday Gift: {customer.name}
        </h1>
        <Button variant="outline" onClick={() => router.back()}>
          Back to Loyalty
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Cake className="h-6 w-6 mr-2 text-pink-500" />
                Birthday Gift Details
              </CardTitle>
              <CardDescription>
                Send a special birthday gift to reward customer loyalty
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row md:items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center mb-4 md:mb-0">
                    <Calendar className="h-10 w-10 text-blue-500 mr-4" />
                    <div>
                      <p className="text-sm text-blue-600 font-medium">Birthday</p>
                      <p className="text-xl font-bold">{format(new Date(customer.birthday), "MMMM d, yyyy")}</p>
                      {isBirthdayToday() ? (
                        <Badge className="mt-1 bg-pink-100 text-pink-800">
                          <PartyPopper className="h-3 w-3 mr-1" />
                          Today!
                        </Badge>
                      ) : (
                        <p className="text-sm text-gray-500">
                          {customer.daysUntilBirthday} days from now
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Award className="h-10 w-10 mr-4" style={{ color: getTierColor(customer.loyaltyTier) }} />
                    <div>
                      <p className="text-sm text-gray-600 font-medium">Loyalty Tier</p>
                      <div className="flex items-center">
                        <p className="text-xl font-bold">{customer.loyaltyTier}</p>
                        {customer.isVIP && (
                          <Badge className="ml-2 bg-yellow-100 text-yellow-800">
                            <Star className="h-3 w-3 mr-1" />
                            VIP
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        {customer.loyaltyPoints.toLocaleString()} points
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Birthday Gift</h3>

                  <div className="p-4 border rounded-lg bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Gift className="h-8 w-8 text-pink-500 mr-3" />
                        <div>
                          <p className="font-medium">Loyalty Points Gift</p>
                          <p className="text-sm text-gray-500">Based on loyalty tier and VIP status</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold">{calculateGiftPoints().toLocaleString()}</p>
                        <p className="text-sm text-gray-500">points</p>
                      </div>
                    </div>

                    {customer.isVIP && (
                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-500 mr-1 flex-shrink-0" />
                          <p className="text-yellow-700">
                            VIP bonus: 2x points (included in total)
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {isGiftAlreadySentThisYear() && (
                    <div className="p-4 border border-yellow-300 bg-yellow-50 rounded-lg">
                      <div className="flex items-start">
                        <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                        <div>
                          <p className="font-medium text-yellow-800">Birthday gift already sent this year</p>
                          <p className="text-sm text-yellow-700">
                            A birthday gift of {previousGifts[0].points.toLocaleString()} points was already sent on {format(new Date(previousGifts[0].createdAt), "MMMM d, yyyy")}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {giftSent && (
                    <div className="p-4 border border-green-300 bg-green-50 rounded-lg">
                      <div className="flex items-start">
                        <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                        <div>
                          <p className="font-medium text-green-800">Birthday gift sent successfully!</p>
                          <p className="text-sm text-green-700">
                            {calculateGiftPoints().toLocaleString()} points have been added to {customer.name}'s loyalty account.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {error && (
                    <div className="p-4 border border-red-300 bg-red-50 rounded-lg">
                      <div className="flex items-start">
                        <X className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                        <div>
                          <p className="font-medium text-red-800">Error sending gift</p>
                          <p className="text-sm text-red-700">{error}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button
                onClick={handleSendGift}
                disabled={isSending || giftSent || isGiftAlreadySentThisYear()}
              >
                {isSending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <Gift className="h-4 w-4 mr-2" />
                    Send Birthday Gift
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MailOpen className="h-5 w-5 mr-2" />
                Previous Birthday Gifts
              </CardTitle>
              <CardDescription>
                History of birthday gifts sent to this customer
              </CardDescription>
            </CardHeader>
            <CardContent>
              {previousGifts.length === 0 ? (
                <div className="text-center py-8">
                  <Gift className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                  <p className="text-gray-500">No previous birthday gifts</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {previousGifts.map((gift) => (
                    <div key={gift.id} className="border rounded-lg p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <Badge>{gift.year}</Badge>
                          <p className="mt-1 font-medium">{gift.points.toLocaleString()} points</p>
                          <p className="text-xs text-gray-500 mt-1">
                            {gift.description}
                          </p>
                        </div>
                        <p className="text-xs text-gray-500">
                          {format(new Date(gift.createdAt), "MMM d, yyyy")}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
