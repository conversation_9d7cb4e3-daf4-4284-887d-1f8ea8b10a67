import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import fs from "fs";
import path from "path";

// Function to check if user is admin
async function isAdmin(req: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return false;
  }
  
  // Check if user has admin role
  return session.user.role === "ADMIN";
}

// POST /api/system/database/upload - Upload a backup file
export async function POST(req: NextRequest) {
  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get("backupFile") as File;
    
    if (!file) {
      return NextResponse.json(
        { error: "No file uploaded" },
        { status: 400 }
      );
    }
    
    // Validate file type
    const fileName = file.name;
    if (!fileName.endsWith(".sql") && !fileName.endsWith(".gz") && !fileName.endsWith(".zip")) {
      return NextResponse.json(
        { error: "Invalid file type. Only .sql, .gz, and .zip files are allowed." },
        { status: 400 }
      );
    }
    
    // Create backup directory if it doesn't exist
    const backupDir = path.join(process.cwd(), "backups");
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    // Generate a unique filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileExtension = path.extname(fileName);
    const fileNameWithoutExt = path.basename(fileName, fileExtension);
    const newFileName = `${fileNameWithoutExt}-${timestamp}${fileExtension}`;
    const filePath = path.join(backupDir, newFileName);
    
    // Convert file to buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    
    // Write file to disk
    fs.writeFileSync(filePath, fileBuffer);
    
    return NextResponse.json({
      message: "File uploaded successfully",
      filename: newFileName,
    });
  } catch (error) {
    console.error("Error in database upload POST:", error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
