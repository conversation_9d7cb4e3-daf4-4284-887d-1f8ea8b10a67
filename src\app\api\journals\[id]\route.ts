import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/journals/:id - Get a journal by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view journals
    const hasViewPermission = await hasPermission("view_journals");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view journals" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");

    // Build entry filter
    const entryFilter: any = {};

    // Date range filter
    if (startDate || endDate) {
      entryFilter.date = {};

      if (startDate) {
        entryFilter.date.gte = new Date(startDate);
      }

      if (endDate) {
        entryFilter.date.lte = new Date(endDate);
      }
    }

    // Get journal from database
    const journal = await db.journal.findUnique({
      where: {
        id,
      },
      include: {
        branch: true,
        entries: {
          where: entryFilter,
          include: {
            contact: true,
          },
          orderBy: {
            date: "desc",
          },
        },
      },
    });

    if (!journal) {
      return NextResponse.json(
        { error: "Journal not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(journal);
  } catch (error) {
    console.error("Error fetching journal:", error);
    return NextResponse.json(
      { error: "Failed to fetch journal" },
      { status: 500 }
    );
  }
}

// PATCH /api/journals/:id - Update a journal
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit journals
    const hasEditPermission = await hasPermission("edit_journals");
    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to edit journals" },
        { status: 403 }
      );
    }

    const { id } = params;
    const data = await req.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Update the journal
    const journal = await db.journal.update({
      where: {
        id,
      },
      data: {
        name: data.name,
        description: data.description,
        branchId: data.branchId || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    });

    return NextResponse.json(journal);
  } catch (error) {
    console.error("Error updating journal:", error);
    return NextResponse.json(
      { error: "Failed to update journal" },
      { status: 500 }
    );
  }
}

// DELETE /api/journals/:id - Delete a journal
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to delete journals
    const hasDeletePermission = await hasPermission("delete_journals");
    if (!hasDeletePermission) {
      return NextResponse.json(
        { error: "You don't have permission to delete journals" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Check if journal has entries
    const journalWithEntries = await db.journal.findUnique({
      where: {
        id,
      },
      include: {
        entries: {
          take: 1,
        },
      },
    });

    if (journalWithEntries?.entries.length) {
      return NextResponse.json(
        { error: "Cannot delete journal with entries" },
        { status: 400 }
      );
    }

    // Delete the journal
    await db.journal.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting journal:", error);
    return NextResponse.json(
      { error: "Failed to delete journal" },
      { status: 500 }
    );
  }
}
