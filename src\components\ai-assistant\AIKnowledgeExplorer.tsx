"use client";

import { useState, useEffect } from "react";
import { Book, Search, RefreshCw, Layers, Database, Info } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

interface ModuleInfo {
  name: string;
  description: string;
  features: string[];
  workflows: {
    name: string;
    description: string;
  }[];
  commonIssues: {
    issue: string;
    description: string;
  }[];
}

interface TableInfo {
  name: string;
  arabicName: string;
  description: string;
  fields: {
    name: string;
    arabicName: string;
    type: string;
    description: string;
  }[];
}

interface WorkflowInfo {
  name: string;
  description: string;
  steps: string[];
  requiredPermissions: string[];
}

interface SearchResult {
  type: string;
  data: any;
  module?: string;
}

export function AIKnowledgeExplorer() {
  const [activeTab, setActiveTab] = useState("modules");
  const [isLoading, setIsLoading] = useState(false);
  const [modules, setModules] = useState<ModuleInfo[]>([]);
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [workflows, setWorkflows] = useState<WorkflowInfo[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedModule, setSelectedModule] = useState<ModuleInfo | null>(null);
  const [selectedTable, setSelectedTable] = useState<TableInfo | null>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowInfo | null>(null);

  // Fetch system knowledge
  const fetchSystemKnowledge = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/ai-assistant/knowledge?action=all");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setModules(data.data.modules || []);
          setTables(data.data.databaseSchema?.tables || []);
          
          // Extract all workflows from modules
          const allWorkflows: WorkflowInfo[] = [];
          data.data.modules.forEach((module: ModuleInfo) => {
            if (module.workflows) {
              allWorkflows.push(...module.workflows);
            }
          });
          setWorkflows(allWorkflows);
        } else {
          toast.error(data.error || "Failed to fetch system knowledge");
        }
      } else {
        toast.error("Failed to fetch system knowledge");
      }
    } catch (error) {
      console.error("Error fetching system knowledge:", error);
      toast.error("Error fetching system knowledge");
    } finally {
      setIsLoading(false);
    }
  };

  // Search knowledge base
  const searchKnowledgeBase = async () => {
    if (!searchTerm.trim()) return;
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/ai-assistant/knowledge?action=search&query=${encodeURIComponent(searchTerm)}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setSearchResults(data.data);
        } else {
          toast.error(data.error || "Failed to search knowledge base");
        }
      } else {
        toast.error("Failed to search knowledge base");
      }
    } catch (error) {
      console.error("Error searching knowledge base:", error);
      toast.error("Error searching knowledge base");
    } finally {
      setIsLoading(false);
    }
  };

  // Update knowledge base
  const updateKnowledgeBase = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/ai-assistant/knowledge/update", {
        method: "POST",
      });
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast.success("Knowledge base updated successfully");
          fetchSystemKnowledge();
        } else {
          toast.error(data.error || "Failed to update knowledge base");
        }
      } else {
        toast.error("Failed to update knowledge base");
      }
    } catch (error) {
      console.error("Error updating knowledge base:", error);
      toast.error("Error updating knowledge base");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle module selection
  const handleModuleSelect = (module: ModuleInfo) => {
    setSelectedModule(module);
    setSelectedTable(null);
    setSelectedWorkflow(null);
  };

  // Handle table selection
  const handleTableSelect = (table: TableInfo) => {
    setSelectedTable(table);
    setSelectedModule(null);
    setSelectedWorkflow(null);
  };

  // Handle workflow selection
  const handleWorkflowSelect = (workflow: WorkflowInfo) => {
    setSelectedWorkflow(workflow);
    setSelectedModule(null);
    setSelectedTable(null);
  };

  // Handle search result selection
  const handleSearchResultSelect = (result: SearchResult) => {
    if (result.type === "module") {
      handleModuleSelect(result.data);
      setActiveTab("modules");
    } else if (result.type === "table") {
      handleTableSelect(result.data);
      setActiveTab("database");
    } else if (result.type === "workflow") {
      handleWorkflowSelect(result.data);
      setActiveTab("workflows");
    }
  };

  // Fetch data when component mounts
  useEffect(() => {
    fetchSystemKnowledge();
  }, []);

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    searchKnowledgeBase();
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Book className="h-5 w-5 mr-2 text-blue-600" />
          مستكشف المعرفة
        </CardTitle>
        <CardDescription>
          استكشاف معلومات النظام والوحدات والعمليات
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Search bar */}
        <form onSubmit={handleSearchSubmit} className="mb-4 flex space-x-2">
          <Input
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="ابحث في قاعدة المعرفة..."
            className="flex-1"
          />
          <Button type="submit" disabled={isLoading || !searchTerm.trim()}>
            <Search className="h-4 w-4 mr-2" />
            بحث
          </Button>
          <Button type="button" onClick={updateKnowledgeBase} variant="outline" disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </form>

        {/* Search results */}
        {searchResults.length > 0 && (
          <div className="mb-4 border rounded-lg p-4 bg-gray-50">
            <h3 className="font-medium text-gray-900 mb-2">نتائج البحث عن "{searchTerm}"</h3>
            <div className="space-y-2">
              {searchResults.map((result, index) => (
                <div
                  key={index}
                  className="p-2 border rounded hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleSearchResultSelect(result)}
                >
                  <div className="flex items-center">
                    {result.type === "module" && <Layers className="h-4 w-4 mr-2 text-blue-500" />}
                    {result.type === "table" && <Database className="h-4 w-4 mr-2 text-green-500" />}
                    {result.type === "workflow" && <RefreshCw className="h-4 w-4 mr-2 text-orange-500" />}
                    {result.type === "issue" && <Info className="h-4 w-4 mr-2 text-red-500" />}
                    <div>
                      <p className="font-medium">
                        {result.type === "module" && result.data.name}
                        {result.type === "table" && result.data.arabicName}
                        {result.type === "workflow" && result.data.name}
                        {result.type === "issue" && result.data.issue}
                        {result.module && ` (${result.module})`}
                      </p>
                      <p className="text-sm text-gray-500">
                        {result.type === "module" && result.data.description}
                        {result.type === "table" && result.data.description}
                        {result.type === "workflow" && result.data.description}
                        {result.type === "issue" && result.data.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="modules" className="flex items-center">
              <Layers className="h-4 w-4 mr-2" />
              الوحدات
            </TabsTrigger>
            <TabsTrigger value="database" className="flex items-center">
              <Database className="h-4 w-4 mr-2" />
              قاعدة البيانات
            </TabsTrigger>
            <TabsTrigger value="workflows" className="flex items-center">
              <RefreshCw className="h-4 w-4 mr-2" />
              العمليات
            </TabsTrigger>
          </TabsList>

          {isLoading && !selectedModule && !selectedTable && !selectedWorkflow ? (
            <div className="flex justify-center items-center p-8">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          ) : (
            <>
              {/* Modules tab */}
              <TabsContent value="modules" className="space-y-4">
                {selectedModule ? (
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-gray-900 text-lg">{selectedModule.name}</h3>
                      <Button variant="ghost" size="sm" onClick={() => setSelectedModule(null)}>
                        العودة للقائمة
                      </Button>
                    </div>
                    <p className="text-gray-600 mt-2">{selectedModule.description}</p>
                    
                    <h4 className="font-medium text-gray-900 mt-4">الميزات:</h4>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {selectedModule.features.map((feature, index) => (
                        <li key={index} className="text-gray-600">{feature}</li>
                      ))}
                    </ul>
                    
                    <h4 className="font-medium text-gray-900 mt-4">العمليات:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                      {selectedModule.workflows.map((workflow, index) => (
                        <div key={index} className="border rounded p-2 bg-white">
                          <p className="font-medium">{workflow.name}</p>
                          <p className="text-sm text-gray-500">{workflow.description}</p>
                        </div>
                      ))}
                    </div>
                    
                    <h4 className="font-medium text-gray-900 mt-4">المشاكل الشائعة:</h4>
                    {selectedModule.commonIssues.length > 0 ? (
                      <div className="space-y-2 mt-2">
                        {selectedModule.commonIssues.map((issue, index) => (
                          <div key={index} className="border rounded p-2 bg-white">
                            <p className="font-medium text-red-500">{issue.issue}</p>
                            <p className="text-sm text-gray-500">{issue.description}</p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 mt-2">لا توجد مشاكل شائعة مسجلة لهذه الوحدة.</p>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {modules.map((module, index) => (
                      <div
                        key={index}
                        className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleModuleSelect(module)}
                      >
                        <h3 className="font-medium text-gray-900">{module.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{module.description}</p>
                        <div className="flex items-center mt-2 text-xs text-gray-400">
                          <span className="mr-2">{module.features.length} ميزة</span>
                          <span className="mr-2">•</span>
                          <span className="mr-2">{module.workflows.length} عملية</span>
                          <span className="mr-2">•</span>
                          <span>{module.commonIssues.length} مشكلة شائعة</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>

              {/* Database tab */}
              <TabsContent value="database" className="space-y-4">
                {selectedTable ? (
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-gray-900 text-lg">
                        {selectedTable.arabicName} ({selectedTable.name})
                      </h3>
                      <Button variant="ghost" size="sm" onClick={() => setSelectedTable(null)}>
                        العودة للقائمة
                      </Button>
                    </div>
                    <p className="text-gray-600 mt-2">{selectedTable.description}</p>
                    
                    <h4 className="font-medium text-gray-900 mt-4">الحقول:</h4>
                    <div className="overflow-x-auto mt-2">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="border p-2 text-right">الاسم</th>
                            <th className="border p-2 text-right">الاسم العربي</th>
                            <th className="border p-2 text-right">النوع</th>
                            <th className="border p-2 text-right">الوصف</th>
                          </tr>
                        </thead>
                        <tbody>
                          {selectedTable.fields.map((field, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="border p-2">{field.name}</td>
                              <td className="border p-2">{field.arabicName}</td>
                              <td className="border p-2">{field.type}</td>
                              <td className="border p-2">{field.description}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {tables.map((table, index) => (
                      <div
                        key={index}
                        className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleTableSelect(table)}
                      >
                        <h3 className="font-medium text-gray-900">
                          {table.arabicName} ({table.name})
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">{table.description}</p>
                        <div className="flex items-center mt-2 text-xs text-gray-400">
                          <span>{table.fields.length} حقل</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>

              {/* Workflows tab */}
              <TabsContent value="workflows" className="space-y-4">
                {selectedWorkflow ? (
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-gray-900 text-lg">{selectedWorkflow.name}</h3>
                      <Button variant="ghost" size="sm" onClick={() => setSelectedWorkflow(null)}>
                        العودة للقائمة
                      </Button>
                    </div>
                    <p className="text-gray-600 mt-2">{selectedWorkflow.description}</p>
                    
                    <h4 className="font-medium text-gray-900 mt-4">خطوات العملية:</h4>
                    <ol className="list-decimal list-inside mt-2 space-y-2">
                      {selectedWorkflow.steps.map((step, index) => (
                        <li key={index} className="text-gray-600">{step}</li>
                      ))}
                    </ol>
                    
                    <h4 className="font-medium text-gray-900 mt-4">الصلاحيات المطلوبة:</h4>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedWorkflow.requiredPermissions.map((permission, index) => (
                        <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                          {permission}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {workflows.map((workflow, index) => (
                      <div
                        key={index}
                        className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleWorkflowSelect(workflow)}
                      >
                        <h3 className="font-medium text-gray-900">{workflow.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{workflow.description}</p>
                        <div className="flex items-center mt-2 text-xs text-gray-400">
                          <span>{workflow.steps.length} خطوة</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
            </>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
