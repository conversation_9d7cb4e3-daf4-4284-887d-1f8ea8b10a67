const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyEmptyTables() {
  console.log('Verificando que las tablas contables estén vacías...');

  try {
    const accountCount = await prisma.account.count();
    console.log(`Cuentas: ${accountCount}`);

    const transactionCount = await prisma.transaction.count();
    console.log(`Transacciones: ${transactionCount}`);

    const journalCount = await prisma.journal.count();
    console.log(`Diarios: ${journalCount}`);

    const journalEntryCount = await prisma.journalEntry.count();
    console.log(`Asientos de diario: ${journalEntryCount}`);

    const generalLedgerEntryCount = await prisma.generalLedgerEntry.count();
    console.log(`Entradas del libro mayor: ${generalLedgerEntryCount}`);

    const fiscalYearCount = await prisma.fiscalYear.count();
    console.log(`Años fiscales: ${fiscalYearCount}`);

    const fiscalPeriodCount = await prisma.fiscalPeriod.count();
    console.log(`Períodos fiscales: ${fiscalPeriodCount}`);

    const accountingSettingsCount = await prisma.accountingSettings.count();
    console.log(`Configuraciones contables: ${accountingSettingsCount}`);

    const financialReportCount = await prisma.financialReport.count();
    console.log(`Informes financieros: ${financialReportCount}`);

    const accountReconciliationCount = await prisma.accountReconciliation.count();
    console.log(`Conciliaciones de cuentas: ${accountReconciliationCount}`);

    const reconciliationItemCount = await prisma.reconciliationItem.count();
    console.log(`Elementos de conciliación: ${reconciliationItemCount}`);

    console.log('Verificación completada.');
  } catch (error) {
    console.error('Error al verificar tablas vacías:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyEmptyTables();
