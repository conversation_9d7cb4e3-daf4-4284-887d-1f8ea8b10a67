import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/maintenance/[id] - Get a specific maintenance service
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view maintenance services
    const canViewMaintenance = await hasPermission("view_maintenance") || session.user.role === "ADMIN";

    if (!canViewMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to view maintenance services" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Get the maintenance service with related data
    const maintenanceService = await db.maintenanceService.findUnique({
      where: { id },
      include: {
        contact: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        branch: true,
        parts: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                basePrice: true,
                costPrice: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
        statusHistory: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        payments: {
          orderBy: {
            date: "desc",
          },
        },
        invoice: {
          select: {
            id: true,
            invoiceNumber: true,
            date: true,
            totalAmount: true,
            paymentStatus: true,
          },
        },
      },
    });

    if (!maintenanceService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(maintenanceService);
  } catch (error) {
    console.error("Error fetching maintenance service:", error);
    return NextResponse.json(
      { error: "Failed to fetch maintenance service" },
      { status: 500 }
    );
  }
}

// PATCH /api/maintenance/[id] - Update a maintenance service
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit maintenance services
    const canEditMaintenance = await hasPermission("edit_maintenance") || session.user.role === "ADMIN";

    if (!canEditMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to edit maintenance services" },
        { status: 403 }
      );
    }

    const id = params.id;
    const data = await req.json();

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    // Only update fields that are provided
    if (data.deviceType !== undefined) updateData.deviceType = data.deviceType;
    if (data.brand !== undefined) updateData.brand = data.brand;
    if (data.model !== undefined) updateData.model = data.model;
    if (data.serialNumber !== undefined) updateData.serialNumber = data.serialNumber;
    if (data.problemDescription !== undefined) updateData.problemDescription = data.problemDescription;
    if (data.initialDiagnosis !== undefined) updateData.initialDiagnosis = data.initialDiagnosis;
    if (data.technicalNotes !== undefined) updateData.technicalNotes = data.technicalNotes;
    if (data.estimatedCost !== undefined) updateData.estimatedCost = data.estimatedCost;
    if (data.finalCost !== undefined) updateData.finalCost = data.finalCost;
    if (data.isPaid !== undefined) updateData.isPaid = data.isPaid;
    if (data.isWarranty !== undefined) updateData.isWarranty = data.isWarranty;
    if (data.warrantyDetails !== undefined) updateData.warrantyDetails = data.warrantyDetails;
    if (data.priority !== undefined) updateData.priority = data.priority;
    if (data.estimatedCompletionDate !== undefined) {
      updateData.estimatedCompletionDate = data.estimatedCompletionDate ? new Date(data.estimatedCompletionDate) : null;
    }

    // New fields
    if (data.estimatedHours !== undefined) updateData.estimatedHours = data.estimatedHours;
    if (data.actualHours !== undefined) updateData.actualHours = data.actualHours;
    if (data.technicianId !== undefined) updateData.technicianId = data.technicianId;
    if (data.customerSignature !== undefined) updateData.customerSignature = data.customerSignature;
    if (data.customerRating !== undefined) updateData.customerRating = data.customerRating;
    if (data.customerFeedback !== undefined) updateData.customerFeedback = data.customerFeedback;

    // Payment and notification fields
    if (data.notificationMethod !== undefined) updateData.notificationMethod = data.notificationMethod;
    if (data.paymentMethod !== undefined) updateData.paymentMethod = data.paymentMethod;
    if (data.paymentStatus !== undefined) updateData.paymentStatus = data.paymentStatus;

    // Handle status change
    let statusChanged = false;
    if (data.status !== undefined && data.status !== existingService.status) {
      updateData.status = data.status;
      statusChanged = true;

      // Update date fields based on status
      if (data.status === "COMPLETED" && !existingService.completionDate) {
        updateData.completionDate = new Date();
      } else if (data.status === "DELIVERED" && !existingService.deliveryDate) {
        updateData.deliveryDate = new Date();
      }
    }

    // Update the maintenance service
    const updatedService = await db.maintenanceService.update({
      where: { id },
      data: updateData,
    });

    // Create status history entry if status changed
    if (statusChanged) {
      await db.maintenanceStatusHistory.create({
        data: {
          maintenanceServiceId: id,
          status: data.status,
          notes: data.statusNotes || `Status changed to ${data.status}`,
          userId: session.user.id,
        },
      });
    }

    return NextResponse.json(updatedService);
  } catch (error) {
    console.error("Error updating maintenance service:", error);
    return NextResponse.json(
      { error: "Failed to update maintenance service" },
      { status: 500 }
    );
  }
}

// DELETE /api/maintenance/[id] - Delete a maintenance service
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to delete maintenance services
    const canDeleteMaintenance = await hasPermission("delete_maintenance") || session.user.role === "ADMIN";

    if (!canDeleteMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to delete maintenance services" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Delete the maintenance service (cascade will delete related records)
    await db.maintenanceService.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Maintenance service deleted successfully" });
  } catch (error) {
    console.error("Error deleting maintenance service:", error);
    return NextResponse.json(
      { error: "Failed to delete maintenance service" },
      { status: 500 }
    );
  }
}
