"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Language, getLanguageDirection } from '@/lib/i18n';

interface AppContextType {
  // Language
  language: Language;
  setLanguage: (language: Language) => void;
  direction: 'ltr' | 'rtl';
  
  // Theme
  isDarkMode: boolean;
  setIsDarkMode: (isDark: boolean) => void;
  toggleDarkMode: () => void;
  
  // Current selections
  currentBranch: string | null;
  setCurrentBranch: (branchId: string | null) => void;
  currentWarehouse: string | null;
  setCurrentWarehouse: (warehouseId: string | null) => void;
  currentPOS: string | null;
  setCurrentPOS: (posId: string | null) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  // Language state
  const [language, setLanguageState] = useState<Language>('ar');
  const [direction, setDirection] = useState<'ltr' | 'rtl'>('rtl');
  
  // Theme state
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  // Current selections state
  const [currentBranch, setCurrentBranch] = useState<string | null>(null);
  const [currentWarehouse, setCurrentWarehouse] = useState<string | null>(null);
  const [currentPOS, setCurrentPOS] = useState<string | null>(null);

  // Load saved preferences from localStorage
  useEffect(() => {
    // Load language preference
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
      setLanguageState(savedLanguage);
      setDirection(getLanguageDirection(savedLanguage));
    }

    // Load theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      setIsDarkMode(true);
      document.documentElement.classList.add('dark');
    } else {
      setIsDarkMode(false);
      document.documentElement.classList.remove('dark');
    }

    // Load current selections
    const savedBranch = localStorage.getItem('currentBranch');
    if (savedBranch) {
      setCurrentBranch(savedBranch);
    }

    const savedWarehouse = localStorage.getItem('currentWarehouse');
    if (savedWarehouse) {
      setCurrentWarehouse(savedWarehouse);
    }

    const savedPOS = localStorage.getItem('currentPOS');
    if (savedPOS) {
      setCurrentPOS(savedPOS);
    }
  }, []);

  // Update document direction when language changes
  useEffect(() => {
    document.documentElement.dir = direction;
    document.documentElement.lang = language;
  }, [direction, language]);

  // Language functions
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    setDirection(getLanguageDirection(newLanguage));
    localStorage.setItem('language', newLanguage);
  };

  // Theme functions
  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  const setIsDarkModeState = (isDark: boolean) => {
    setIsDarkMode(isDark);
    
    if (isDark) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  // Current selections functions
  const setBranch = (branchId: string | null) => {
    setCurrentBranch(branchId);
    if (branchId) {
      localStorage.setItem('currentBranch', branchId);
    } else {
      localStorage.removeItem('currentBranch');
    }
  };

  const setWarehouse = (warehouseId: string | null) => {
    setCurrentWarehouse(warehouseId);
    if (warehouseId) {
      localStorage.setItem('currentWarehouse', warehouseId);
    } else {
      localStorage.removeItem('currentWarehouse');
    }
  };

  const setPOS = (posId: string | null) => {
    setCurrentPOS(posId);
    if (posId) {
      localStorage.setItem('currentPOS', posId);
    } else {
      localStorage.removeItem('currentPOS');
    }
  };

  const value: AppContextType = {
    // Language
    language,
    setLanguage,
    direction,
    
    // Theme
    isDarkMode,
    setIsDarkMode: setIsDarkModeState,
    toggleDarkMode,
    
    // Current selections
    currentBranch,
    setCurrentBranch: setBranch,
    currentWarehouse,
    setCurrentWarehouse: setWarehouse,
    currentPOS,
    setCurrentPOS: setPOS,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

// Hook for translations
export function useTranslation() {
  const { language } = useApp();
  
  const t = (key: string): string => {
    // This is a simplified version - you can expand this
    // to use the full translation system from i18n.ts
    return key;
  };
  
  return { t, language };
}
