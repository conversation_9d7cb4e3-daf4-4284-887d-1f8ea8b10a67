import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/currencies - Get all currencies
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const isActive = url.searchParams.get("isActive") === "true";
    const search = url.searchParams.get("search");
    
    // Build query filters
    const filters: any = {};
    if (url.searchParams.has("isActive")) filters.isActive = isActive;
    
    if (search) {
      filters.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
      ];
    }
    
    // Get currencies
    const currencies = await db.currency.findMany({
      where: filters,
      orderBy: [
        { isBaseCurrency: "desc" },
        { code: "asc" },
      ],
    });
    
    return NextResponse.json({
      data: currencies,
    });
  } catch (error: any) {
    console.error("Error fetching currencies:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch currencies" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/currencies - Create a new currency
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to create currencies
    const hasCreatePermission = await hasPermission("manage_accounts");
    if (!hasCreatePermission) {
      return NextResponse.json(
        { error: "You don't have permission to create currencies" },
        { status: 403 }
      );
    }
    
    // Parse request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: "Currency name is required" }, { status: 400 });
    }
    
    if (!data.code) {
      return NextResponse.json({ error: "Currency code is required" }, { status: 400 });
    }
    
    if (!data.symbol) {
      return NextResponse.json({ error: "Currency symbol is required" }, { status: 400 });
    }
    
    if (data.exchangeRate === undefined) {
      return NextResponse.json({ error: "Exchange rate is required" }, { status: 400 });
    }
    
    // Check if currency with same code already exists
    const existingCurrency = await db.currency.findFirst({
      where: {
        code: data.code,
      },
    });
    
    if (existingCurrency) {
      return NextResponse.json(
        { error: "A currency with this code already exists" },
        { status: 400 }
      );
    }
    
    // If this is set as base currency, update all other currencies
    if (data.isBaseCurrency) {
      await db.currency.updateMany({
        where: {
          isBaseCurrency: true,
        },
        data: {
          isBaseCurrency: false,
        },
      });
    }
    
    // Create currency
    const currency = await db.currency.create({
      data: {
        name: data.name,
        code: data.code,
        symbol: data.symbol,
        exchangeRate: data.exchangeRate,
        isBaseCurrency: data.isBaseCurrency || false,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    });
    
    return NextResponse.json(currency, { status: 201 });
  } catch (error: any) {
    console.error("Error creating currency:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create currency" },
      { status: 500 }
    );
  }
}
