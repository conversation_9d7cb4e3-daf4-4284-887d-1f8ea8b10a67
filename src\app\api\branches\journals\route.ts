import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// POST /api/branches/journals - Create cash journals for all branches
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add journals
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add journals" },
        { status: 403 }
      );
    }

    // Get all branches
    const branches = await db.branch.findMany({
      where: {
        isActive: true,
      },
    });

    if (branches.length === 0) {
      return NextResponse.json(
        { error: "No active branches found" },
        { status: 404 }
      );
    }

    // Create cash journal for each branch
    const createdJournals = await Promise.all(
      branches.map(async (branch) => {
        // Check if branch already has a cash journal
        const existingJournal = await db.journal.findFirst({
          where: {
            branchId: branch.id,
            paymentMethod: "CASH",
          },
        });

        if (existingJournal) {
          return {
            branch: branch.name,
            journal: existingJournal,
            status: "already_exists",
          };
        }

        // Create new cash journal for branch
        const journal = await db.journal.create({
          data: {
            name: `${branch.name} Cash Journal`,
            description: `Cash journal for ${branch.name} branch`,
            paymentMethod: "CASH",
            branchId: branch.id,
            isActive: true,
          },
        });

        return {
          branch: branch.name,
          journal,
          status: "created",
        };
      })
    );

    return NextResponse.json(
      { message: "Branch cash journals created successfully", journals: createdJournals },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating branch cash journals:", error);
    return NextResponse.json(
      { error: "Failed to create branch cash journals" },
      { status: 500 }
    );
  }
}

// GET /api/branches/journals - Get all branch journals
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view journals
    const hasViewPermission = await hasPermission("view_accounts");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view journals" },
        { status: 403 }
      );
    }

    // Get all branch journals
    const journals = await db.journal.findMany({
      where: {
        branchId: {
          not: null,
        },
        isActive: true,
      },
      include: {
        branch: true,
        entries: true,
      },
      orderBy: {
        branch: {
          name: "asc",
        },
      },
    });

    return NextResponse.json(journals);
  } catch (error) {
    console.error("Error fetching branch journals:", error);
    return NextResponse.json(
      { error: "Failed to fetch branch journals" },
      { status: 500 }
    );
  }
}
