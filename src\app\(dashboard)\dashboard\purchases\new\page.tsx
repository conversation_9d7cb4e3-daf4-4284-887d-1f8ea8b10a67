"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import ContactSearchInput from "@/components/contacts/ContactSearchInput";

// Define types for data
interface Contact {
  id: string;
  name: string;
  phone: string;
  isSupplier: boolean;
}

interface Branch {
  id: string;
  name: string;
  code: string;
}

interface Specification {
  id: string;
  name: string;
  value: string;
}

interface InventoryItem {
  warehouseId: string;
  warehouseName: string;
  quantity: number;
}

interface Product {
  id: string;
  name: string;
  basePrice: number;
  specifications: Specification[];
  inventory: InventoryItem[];
}

interface Warehouse {
  id: string;
  name: string;
  branchId: string;
}

export default function NewPurchasePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedContact, setSelectedContact] = useState("");
  const [selectedBranch, setSelectedBranch] = useState("");
  const [items, setItems] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [unitPrice, setUnitPrice] = useState(1);
  const [notes, setNotes] = useState("");
  const [availableWarehouses, setAvailableWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState("");
  const [applyTax, setApplyTax] = useState(false);
  const [discount, setDiscount] = useState(0);
  const [purchaseDate, setPurchaseDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [dueDate, setDueDate] = useState<string>("");
  // Generate invoice number based on branch
  const [invoiceNumber, setInvoiceNumber] = useState("DRAFT-0000");
  // Payment methods
  interface PaymentMethodSetting {
    id: string;
    name: string;
    code: string;
    isActive: boolean;
  }

  interface PaymentDetail {
    method: string;
    amount: number;
  }

  const [paymentMethods, setPaymentMethods] = useState<PaymentDetail[]>([]);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState<PaymentMethodSetting[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('CASH');
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentMethodsLoading, setPaymentMethodsLoading] = useState(false);

  // State for real data
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [dataLoading, setDataLoading] = useState(true);

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      setPaymentMethodsLoading(true);
      const response = await fetch('/api/settings/payment-methods');

      if (response.ok) {
        const data = await response.json();
        // Filter only active payment methods
        const activeMethods = data.filter((method: PaymentMethodSetting) =>
          method.isActive && method.code !== 'CUSTOMER_ACCOUNT' && method.code !== 'SUPPLIER_ACCOUNT'
        );

        setAvailablePaymentMethods(activeMethods);

        // Set default selected payment method if available
        if (activeMethods.length > 0) {
          setSelectedPaymentMethod(activeMethods[0].code);
        }
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    } finally {
      setPaymentMethodsLoading(false);
    }
  };

  // Fetch real data from API
  useEffect(() => {
    const fetchData = async () => {
      setDataLoading(true);
      try {
        // Fetch suppliers (contacts that are suppliers)
        const contactsResponse = await fetch('/api/contacts?type=supplier');
        if (contactsResponse.ok) {
          const contactsData = await contactsResponse.json();
          setContacts(contactsData);
        }

        // Fetch branches
        const branchesResponse = await fetch('/api/branches');
        if (branchesResponse.ok) {
          const branchesData = await branchesResponse.json();
          setBranches(branchesData);
        }

        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          setProducts(productsData);
        }

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        if (warehousesResponse.ok) {
          const warehousesData = await warehousesResponse.json();
          setWarehouses(warehousesData);
        }

        // Fetch payment methods
        await fetchPaymentMethods();
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setDataLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get next invoice number when branch changes
  useEffect(() => {
    if (selectedBranch) {
      // Find the branch in our real data
      const branch = branches.find(b => b.id === selectedBranch);
      if (branch) {
        // Use the branch code from the database
        const branchCode = branch.code || branch.name.charAt(0).toUpperCase();

        // Set a temporary invoice number immediately
        const tempInvoiceNumber = `P-${branchCode}-0001`;
        setInvoiceNumber(tempInvoiceNumber);

        // Try to fetch the next invoice number from the API
        try {
          fetch(`/api/invoices/next?branchId=${selectedBranch}&type=purchase`)
            .then(response => {
              if (!response.ok) {
                // If the response is not OK, use the fallback
                console.log(`API returned status ${response.status}. Using fallback invoice number.`);
                return { invoiceNumber: tempInvoiceNumber };
              }
              return response.json();
            })
            .then(data => {
              if (data && data.invoiceNumber) {
                setInvoiceNumber(data.invoiceNumber);
              } else {
                // Fallback if the response doesn't contain an invoice number
                console.log('API response missing invoice number. Using fallback.');
                setInvoiceNumber(tempInvoiceNumber);
              }
            })
            .catch(error => {
              console.error('Error fetching invoice number:', error);
              // Fallback to a default format if API fails
              setInvoiceNumber(tempInvoiceNumber);
            });
        } catch (error) {
          console.error('Exception in fetch operation:', error);
          // Fallback if the fetch operation throws an exception
          setInvoiceNumber(tempInvoiceNumber);
        }
      } else {
        // If branch not found, use a generic invoice number
        setInvoiceNumber(`P-INV-${Date.now().toString().slice(-6)}`);
      }
    } else {
      // If no branch is selected, use a draft number
      setInvoiceNumber("P-DRAFT-0000");
    }
  }, [selectedBranch, branches]);

  // Calculate total
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const taxRate = 0.14; // 14% tax

  // Apply tax first
  const taxAmount = applyTax ? subtotal * taxRate : 0;
  const subtotalWithTax = subtotal + taxAmount;

  // Then apply discount
  const discountAmount = discount > 0 ? (discount > subtotalWithTax ? subtotalWithTax : discount) : 0;
  const total = subtotalWithTax - discountAmount;

  // Calculate total paid amount and remaining amount
  const totalPaid = paymentMethods.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingAmount = Math.max(0, total - totalPaid);

  // Add payment method
  const addPaymentMethod = () => {
    if (selectedPaymentMethod && paymentAmount > 0 && paymentAmount <= remainingAmount) {
      // Check if this payment method already exists
      const existingPaymentIndex = paymentMethods.findIndex(p => p.method === selectedPaymentMethod);

      if (existingPaymentIndex >= 0) {
        // Update existing payment method
        const updatedPaymentMethods = [...paymentMethods];
        updatedPaymentMethods[existingPaymentIndex].amount += paymentAmount;
        setPaymentMethods(updatedPaymentMethods);
      } else {
        // Add new payment method
        setPaymentMethods([...paymentMethods, { method: selectedPaymentMethod, amount: paymentAmount }]);
      }

      // Reset payment amount
      setPaymentAmount(remainingAmount > 0 ? remainingAmount : 0);
    }
  };

  // Remove payment method
  const removePaymentMethod = (index: number) => {
    const updatedPaymentMethods = [...paymentMethods];
    updatedPaymentMethods.splice(index, 1);
    setPaymentMethods(updatedPaymentMethods);
  };

  // Determine payment status based on total paid amount
  const getPaymentStatus = () => {
    if (totalPaid === 0) return 'UNPAID';
    if (totalPaid < total) return 'PARTIALLY_PAID';
    return 'PAID';
  };

  // Get payment status display text
  const getPaymentStatusText = () => {
    if (totalPaid === 0) return 'Unpaid (Supplier Account)';
    if (totalPaid < total) return 'Partially Paid (Remaining to Supplier Account)';
    return 'Paid';
  };

  // Update available warehouses when branch changes
  useEffect(() => {
    if (selectedBranch) {
      const branchWarehouses = warehouses.filter(wh => wh.branchId === selectedBranch);
      setAvailableWarehouses(branchWarehouses);
      if (branchWarehouses.length > 0) {
        setSelectedWarehouse(branchWarehouses[0].id);
      } else {
        setSelectedWarehouse("");
      }
    } else {
      setAvailableWarehouses([]);
      setSelectedWarehouse("");
    }
  }, [selectedBranch, warehouses]);

  // Update unit price when product changes
  useEffect(() => {
    if (selectedProduct) {
      const product = products.find(p => p.id === selectedProduct);
      if (product) {
        // Set a default price of 1 if the product's base price is 0 or negative
        const price = product.basePrice > 0 ? product.basePrice : 1;
        setUnitPrice(price);
      }
    } else {
      setUnitPrice(1); // Default to 1 instead of 0
    }
  }, [selectedProduct, products]);

  // Update payment amount when total or payment methods change
  useEffect(() => {
    // Set payment amount to remaining amount
    setPaymentAmount(remainingAmount);
  }, [total, paymentMethods.length]);

  // Add item to purchase
  const addItem = () => {
    if (!selectedProduct || !selectedWarehouse) return;

    // Ensure quantity and unit price are valid
    let finalQuantity = quantity;
    let finalUnitPrice = unitPrice;

    if (finalQuantity <= 0) {
      finalQuantity = 1;
      setQuantity(1);
    }

    if (finalUnitPrice <= 0) {
      finalUnitPrice = 1;
      setUnitPrice(1);
    }

    const product = products.find(p => p.id === selectedProduct);
    if (!product) return;

    // For purchases, we don't need to check stock as we're adding to inventory

    // Create a new item
    const newItem = {
      id: `item-${Date.now()}`,
      productId: product.id,
      productName: product.name,
      unitPrice: finalUnitPrice,
      quantity: finalQuantity,
      total: finalUnitPrice * finalQuantity,
      warehouseId: selectedWarehouse,
      warehouseName: availableWarehouses.find(w => w.id === selectedWarehouse)?.name || "Unknown",
      specifications: product.specifications,
    };

    setItems([...items, newItem]);
    setSelectedProduct("");
    setQuantity(1);
    setUnitPrice(1); // Reset to 1 instead of 0
  };

  // Remove item from purchase
  const removeItem = (itemId: string) => {
    setItems(items.filter(item => item.id !== itemId));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedContact || !selectedBranch || items.length === 0) {
      alert("Please fill in all required fields and add at least one item.");
      return;
    }

    setIsLoading(true);

    // Make sure we have a valid invoice number
    const finalInvoiceNumber = invoiceNumber || `P-DRAFT-${Date.now().toString().slice(-6)}`;

    // Prepare purchase data
    const purchaseData = {
      invoiceNumber: finalInvoiceNumber,
      contactId: selectedContact,
      branchId: selectedBranch,
      items: items.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.total,
        warehouseId: item.warehouseId,
      })),
      subtotal,
      discount: discountAmount,
      taxAmount,
      total,
      payments: paymentMethods.length > 0
        ? (remainingAmount > 0
            ? [...paymentMethods, { method: 'SUPPLIER_ACCOUNT', amount: remainingAmount }]
            : paymentMethods)
        : [{ method: 'SUPPLIER_ACCOUNT', amount: total }],
      paymentStatus: getPaymentStatus(),
      notes,
      date: purchaseDate ? new Date(purchaseDate).toISOString() : new Date().toISOString(),
      dueDate: dueDate ? new Date(dueDate).toISOString() : null,
      status: "COMPLETED", // Mark as completed since we're creating a final invoice
      currency: "EGP", // Egyptian Pound
    };

    console.log("Purchase data:", purchaseData);

    // Send data to API
    try {
      const response = await fetch('/api/purchases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(purchaseData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create purchase');
      }

      setIsLoading(false);
      // Redirect to purchases list
      router.push("/dashboard/purchases");
    } catch (error) {
      console.error('Error creating purchase:', error);
      alert(`Error creating purchase: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-black sm:text-3xl sm:truncate">
            New Purchase
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/dashboard/purchases"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </Link>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              {/* Supplier Selection */}
              <div className="sm:col-span-3">
                <label htmlFor="contact" className="block text-base font-bold text-black">
                  Supplier <span className="text-red-600">*</span>
                </label>
                <div className="mt-2">
                  <ContactSearchInput
                    onSelectContact={(contact) => setSelectedContact(contact.id)}
                    placeholder="Search supplier by name or phone"
                    contactType="supplier"
                    selectedContactId={selectedContact}
                    buttonLabel="Add New Supplier"
                    className="w-full"
                  />
                </div>
              </div>

              {/* Branch Selection */}
              <div className="sm:col-span-3">
                <label htmlFor="branch" className="block text-base font-bold text-black">
                  Branch <span className="text-red-600">*</span>
                </label>
                <div className="mt-2">
                  <div className="relative bg-white border-2 border-gray-300 rounded-lg shadow-sm">
                    <select
                      id="branch"
                      name="branch"
                      className="block w-full py-3 pl-3 pr-10 text-base font-medium text-black bg-white focus:ring-black focus:border-black rounded-lg"
                      value={selectedBranch}
                      onChange={(e) => setSelectedBranch(e.target.value)}
                      required
                    >
                      <option value="" className="text-black font-medium">Select Branch</option>
                      {branches.map((branch) => (
                        <option key={branch.id} value={branch.id} className="text-black font-medium">
                          {branch.name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Date */}
              <div className="sm:col-span-3">
                <label htmlFor="date" className="block text-base font-bold text-black">
                  Date
                </label>
                <div className="mt-2">
                  <div className="relative bg-white border-2 border-gray-300 rounded-lg shadow-sm">
                    <input
                      type="date"
                      name="date"
                      id="date"
                      className="block w-full py-3 pl-3 pr-10 text-base font-medium text-black bg-white focus:ring-black focus:border-black rounded-lg"
                      value={purchaseDate}
                      onChange={(e) => setPurchaseDate(e.target.value)}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Due Date */}
              <div className="sm:col-span-3">
                <label htmlFor="dueDate" className="block text-base font-bold text-black">
                  Due Date
                </label>
                <div className="mt-2">
                  <div className="relative bg-white border-2 border-gray-300 rounded-lg shadow-sm">
                    <input
                      type="date"
                      name="dueDate"
                      id="dueDate"
                      className="block w-full py-3 pl-3 pr-10 text-base font-medium text-black bg-white focus:ring-black focus:border-black rounded-lg"
                      value={dueDate}
                      onChange={(e) => setDueDate(e.target.value)}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Invoice Number */}
              <div className="sm:col-span-3">
                <label htmlFor="invoice-number" className="block text-base font-bold text-black">
                  Invoice Number
                </label>
                <div className="mt-2">
                  <div className="relative bg-white border-2 border-gray-300 rounded-lg shadow-sm">
                    <input
                      type="text"
                      name="invoice-number"
                      id="invoice-number"
                      className="block w-full py-3 pl-3 pr-10 text-base font-medium text-black bg-white focus:ring-black focus:border-black rounded-lg"
                      value={invoiceNumber || "P-DRAFT-0000"}
                      readOnly
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add Items */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md mt-6">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-black mb-4">
              Items
            </h3>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              {/* Product Selection */}
              <div className="sm:col-span-2">
                <label htmlFor="product" className="block text-sm font-medium text-black">
                  Product
                </label>
                <div className="mt-1">
                  <select
                    id="product"
                    name="product"
                    className="shadow-sm focus:ring-black focus:border-black block w-full sm:text-sm border-gray-300 rounded-md bg-white text-black font-medium"
                    value={selectedProduct}
                    onChange={(e) => setSelectedProduct(e.target.value)}
                  >
                    <option value="" className="text-black font-medium">Select Product</option>
                    {products.map((product) => (
                      <option key={product.id} value={product.id} className="text-black font-medium">
                        {product.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Warehouse Selection */}
              <div className="sm:col-span-1">
                <label htmlFor="warehouse" className="block text-sm font-medium text-black">
                  Warehouse
                </label>
                <div className="mt-1">
                  <select
                    id="warehouse"
                    name="warehouse"
                    className="shadow-sm focus:ring-black focus:border-black block w-full sm:text-sm border-gray-300 rounded-md bg-white text-black font-medium"
                    value={selectedWarehouse}
                    onChange={(e) => setSelectedWarehouse(e.target.value)}
                    disabled={!selectedBranch}
                  >
                    <option value="" className="text-black font-medium">Select Warehouse</option>
                    {availableWarehouses.map((warehouse) => (
                      <option key={warehouse.id} value={warehouse.id} className="text-black font-medium">
                        {warehouse.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Unit Price */}
              <div className="sm:col-span-1">
                <label htmlFor="unitPrice" className="block text-sm font-medium text-black">
                  Unit Price
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="unitPrice"
                    id="unitPrice"
                    min="0.01"
                    step="0.01"
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md text-black"
                    value={unitPrice}
                    onChange={(e) => setUnitPrice(parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              {/* Quantity */}
              <div className="sm:col-span-1">
                <label htmlFor="quantity" className="block text-sm font-medium text-black">
                  Quantity
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="quantity"
                    id="quantity"
                    min="1"
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md text-black"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              {/* Add Button */}
              <div className="sm:col-span-1 flex items-end">
                <button
                  type="button"
                  onClick={addItem}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  disabled={!selectedProduct || !selectedWarehouse}
                >
                  Add
                </button>
              </div>
            </div>

            {/* Items Table */}
            <div className="mt-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Product
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Warehouse
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Total
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {items.length > 0 ? (
                      items.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 text-sm font-medium text-black">
                            <div>
                              {item.productName}
                            </div>
                            {item.specifications && (
                              <ul className="mt-1 text-xs text-gray-700 list-disc list-inside">
                                {item.specifications.map((spec: any, index: number) => (
                                  <li key={index}>
                                    <span className="font-medium">{spec.name}:</span> {spec.value}
                                  </li>
                                ))}
                              </ul>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            {item.warehouseName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            <div className="flex items-center">
                              <input
                                type="number"
                                min={0}
                                step="0.01"
                                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-24 sm:text-sm border-gray-300 rounded-md text-black"
                                value={item.unitPrice}
                                onChange={(e) => {
                                  const newPrice = parseFloat(e.target.value);
                                  if (!isNaN(newPrice) && newPrice > 0) {
                                    const updatedItems = items.map(i => {
                                      if (i.id === item.id) {
                                        return {
                                          ...i,
                                          unitPrice: newPrice,
                                          total: newPrice * i.quantity
                                        };
                                      }
                                      return i;
                                    });
                                    setItems(updatedItems);
                                  }
                                }}
                              />
                              <span className="ml-2 text-black">ج.م</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            <div className="flex items-center">
                              <div className="relative w-20">
                                <div className="flex items-center bg-white border border-gray-300 rounded-md">
                                  <button
                                    type="button"
                                    className="px-1 py-0.5 text-black hover:text-gray-700 focus:outline-none"
                                    onClick={() => {
                                      if (item.quantity > 1) {
                                        const updatedItems = items.map(i => {
                                          if (i.id === item.id) {
                                            return {
                                              ...i,
                                              quantity: i.quantity - 1,
                                              total: i.unitPrice * (i.quantity - 1)
                                            };
                                          }
                                          return i;
                                        });
                                        setItems(updatedItems);
                                      }
                                    }}
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                  </button>
                                  <input
                                    id={`quantity-${item.id}`}
                                    type="number"
                                    min="1"
                                    className="block w-8 text-center py-1 text-sm font-medium text-black border-0 focus:ring-0 focus:outline-none"
                                    value={item.quantity}
                                    onChange={(e) => {
                                      const newQuantity = parseInt(e.target.value);
                                      if (!isNaN(newQuantity) && newQuantity > 0) {
                                        const updatedItems = items.map(i => {
                                          if (i.id === item.id) {
                                            return {
                                              ...i,
                                              quantity: newQuantity,
                                              total: i.unitPrice * newQuantity
                                            };
                                          }
                                          return i;
                                        });
                                        setItems(updatedItems);
                                      }
                                    }}
                                  />
                                  <button
                                    type="button"
                                    className="px-1 py-0.5 text-black hover:text-gray-700 focus:outline-none"
                                    onClick={() => {
                                      const updatedItems = items.map(i => {
                                        if (i.id === item.id) {
                                          return {
                                            ...i,
                                            quantity: i.quantity + 1,
                                            total: i.unitPrice * (i.quantity + 1)
                                          };
                                        }
                                        return i;
                                      });
                                      setItems(updatedItems);
                                    }}
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            {item.total.toFixed(2)} ج.م
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            <button
                              type="button"
                              onClick={() => removeItem(item.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="px-6 py-4 whitespace-nowrap text-sm text-black text-center">
                          No items added yet
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Totals and Notes */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md mt-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              {/* Notes */}
              <div className="sm:col-span-4">
                <label htmlFor="notes" className="block text-sm font-medium text-black">
                  Notes
                </label>
                <div className="mt-1">
                  <textarea
                    id="notes"
                    name="notes"
                    rows={3}
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md text-black"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>
              </div>

              {/* Totals */}
              <div className="sm:col-span-2">
                <div className="bg-gray-50 p-4 rounded-lg border-2 border-gray-300 shadow-sm">
                  <div className="flex justify-between py-2 text-base">
                    <span className="font-bold text-black">Subtotal:</span>
                    <span className="text-black font-bold">{subtotal.toFixed(2)} ج.م</span>
                  </div>

                  {/* Tax */}
                  <div className="py-2">
                    <div className="flex justify-between text-sm mb-1">
                      <div className="flex items-center">
                        <input
                          id="apply-tax"
                          name="apply-tax"
                          type="checkbox"
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          checked={applyTax}
                          onChange={(e) => setApplyTax(e.target.checked)}
                        />
                        <label htmlFor="apply-tax" className="ml-2 font-medium text-black">
                          Apply Tax (14%):
                        </label>
                      </div>
                      <span className="text-black font-medium">{taxAmount.toFixed(2)} ج.م</span>
                    </div>
                    {applyTax && (
                      <div className="text-xs text-indigo-600 ml-6">
                        14% of {subtotal.toFixed(2)} ج.م
                      </div>
                    )}
                  </div>

                  {/* Subtotal with Tax */}
                  <div className="flex justify-between py-2 text-sm border-t border-gray-200 mt-2 pt-2">
                    <span className="font-medium text-black">Subtotal with Tax:</span>
                    <span className="text-black font-medium">{subtotalWithTax.toFixed(2)} ج.م</span>
                  </div>

                  {/* Discount */}
                  <div className="py-2">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="font-medium text-black">Discount:</span>
                      <span className="text-black font-medium">{discountAmount.toFixed(2)} ج.م</span>
                    </div>
                    <div className="flex items-center bg-white border border-gray-300 rounded-md">
                      <div className="flex-grow relative">
                        <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                          <span className="text-black text-xs">ج.م</span>
                        </div>
                        <input
                          type="number"
                          min="0"
                          max={subtotalWithTax}
                          step="0.01"
                          className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-8 pr-8 py-1 text-sm font-medium text-black border-0"
                          value={discount}
                          onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
                        />
                        {discount > 0 && (
                          <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                            <button
                              type="button"
                              className="text-black hover:text-gray-600 focus:outline-none"
                              onClick={() => setDiscount(0)}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                    {discount > 0 && (
                      <div className="mt-1 text-xs text-green-600">
                        {((discountAmount / subtotalWithTax) * 100).toFixed(1)}% off
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between py-3 text-lg font-bold border-t-2 border-gray-300 mt-3 pt-3 bg-gray-50 rounded-lg p-3">
                    <span className="text-black">Total:</span>
                    <span className="text-black text-xl">{total.toFixed(2)} ج.م</span>
                  </div>

                  {/* Payment Methods */}
                  <div className="py-3 mt-4 border-t border-gray-200">
                    <h4 className="text-base font-bold text-black mb-2">Payment Methods:</h4>

                    {/* Payment Method Selection */}
                    {paymentMethodsLoading ? (
                      <div className="flex justify-center items-center py-4">
                        <svg className="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="ml-2 text-gray-600">Loading payment methods...</span>
                      </div>
                    ) : availablePaymentMethods.length === 0 ? (
                      <div className="text-center py-4 text-gray-500">
                        No payment methods available. Please add payment methods in settings.
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-2 mb-4">
                        {availablePaymentMethods.map((method) => (
                          <div
                            key={method.id}
                            className={`p-2 border rounded-md cursor-pointer flex items-center ${selectedPaymentMethod === method.code ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                            onClick={() => setSelectedPaymentMethod(method.code)}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium text-black">{method.name}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Payment Amount Input */}
                    <div className="flex items-center mb-4">
                      <div className="flex-1 mr-2">
                        <label htmlFor="paymentAmount" className="block text-sm font-medium text-black mb-1">
                          Payment Amount
                        </label>
                        <div className="relative rounded-md shadow-sm">
                          <input
                            type="number"
                            id="paymentAmount"
                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-3 pr-12 sm:text-sm border-gray-300 rounded-md text-black"
                            placeholder="0.00"
                            min="0"
                            max={remainingAmount}
                            step="0.01"
                            value={paymentAmount}
                            onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <span className="text-black sm:text-sm">ج.م</span>
                          </div>
                        </div>
                      </div>
                      <button
                        type="button"
                        className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        onClick={addPaymentMethod}
                        disabled={!selectedPaymentMethod || paymentAmount <= 0 || paymentAmount > remainingAmount}
                      >
                        Add Payment
                      </button>
                    </div>

                    {/* Payment Methods List */}
                    {paymentMethods.length > 0 && (
                      <div className="mt-4 border rounded-md overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                                Method
                              </th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                                Amount
                              </th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {paymentMethods.map((payment, index) => (
                              <tr key={index}>
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-black">
                                  {availablePaymentMethods.find(m => m.code === payment.method)?.name || payment.method}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                                  {payment.amount.toFixed(2)} ج.م
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                                  <button
                                    type="button"
                                    onClick={() => removePaymentMethod(index)}
                                    className="text-red-600 hover:text-red-900"
                                  >
                                    Remove
                                  </button>
                                </td>
                              </tr>
                            ))}
                            <tr className="bg-gray-50">
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                Total Paid
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                {totalPaid.toFixed(2)} ج.م
                              </td>
                              <td></td>
                            </tr>
                            <tr>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                Remaining
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                {remainingAmount.toFixed(2)} ج.م
                              </td>
                              <td></td>
                            </tr>
                            {remainingAmount > 0 && (
                              <tr className="bg-yellow-50">
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                  To Supplier Account
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                  {remainingAmount.toFixed(2)} ج.م
                                </td>
                                <td></td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    )}

                    {/* Payment Status */}
                    <div className="mt-4 flex items-center">
                      <span className="text-sm font-medium text-black mr-2">Payment Status:</span>
                      <span className={`text-sm font-bold px-2 py-1 rounded-full ${
                        getPaymentStatus() === 'PAID' ? 'bg-green-100 text-green-800' :
                        getPaymentStatus() === 'PARTIALLY_PAID' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {getPaymentStatusText()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="pt-5">
          <div className="flex justify-end">
            <Link
              href="/dashboard/purchases"
              className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-black hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-3"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${isLoading ? 'bg-indigo-400' : 'bg-indigo-600 hover:bg-indigo-700'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                'Create Purchase'
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}