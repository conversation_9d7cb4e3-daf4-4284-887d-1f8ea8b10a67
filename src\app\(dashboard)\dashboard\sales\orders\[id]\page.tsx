"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Edit, Printer, ArrowLeft, CreditCard } from "lucide-react";

export default function SaleDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const saleId = params.id as string;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sale, setSale] = useState<any>(null);

  // Fetch sale data from API
  useEffect(() => {
    const fetchSale = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/sales/${saleId}`);

        if (response.ok) {
          const data = await response.json();
          setSale(data);
          setError(null);
        } else {
          const errorData = await response.json();
          setError(errorData.error || "Failed to fetch sale");
          setSale(null);
        }
      } catch (err: any) {
        console.error("Error fetching sale:", err);
        setError(err.message || "An error occurred while fetching the sale");
        setSale(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSale();
  }, [saleId]);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    // Format with Egyptian Pound symbol
    return `${amount.toFixed(2)} ج.م`;
  };

  // Get payment status badge class
  const getPaymentStatusClass = (status: string) => {
    switch (status) {
      case "PAID":
        return "bg-green-100 text-green-800";
      case "PARTIALLY_PAID":
        return "bg-yellow-100 text-yellow-800";
      case "UNPAID":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500">Loading sale details...</span>
      </div>
    );
  }

  // Show error state
  if (error || !sale) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6">
        <h2 className="text-xl font-semibold text-gray-900">Sale not found</h2>
        <p className="mt-2 text-gray-600">{error || `The sale with ID ${saleId} could not be found.`}</p>
        <div className="mt-4">
          <Link
            href="/dashboard/sales/orders"
            className="text-blue-600 hover:text-blue-900"
          >
            Back to Sales
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/sales/orders">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Sale Details</h1>
          <div className="ml-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusClass(sale.paymentStatus)}`}>
              {sale.paymentStatus}
            </span>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => router.push(`/dashboard/sales/${saleId}/edit`)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline">
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      {/* Invoice Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice #{sale.invoiceNumber}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Customer</h3>
              <div className="mt-1">
                <p className="text-base font-medium">{sale.contact?.name || "Unknown Customer"}</p>
                <p className="text-sm text-gray-600">{sale.contact?.phone || "No phone"}</p>
                <p className="text-sm text-gray-600">{sale.contact?.address || "No address"}</p>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Invoice Details</h3>
              <div className="mt-1 space-y-1">
                <p className="text-sm"><span className="font-medium">Date:</span> {formatDate(sale.date)}</p>
                <p className="text-sm"><span className="font-medium">Branch:</span> {sale.branch.name}</p>
                <p className="text-sm"><span className="font-medium">Created By:</span> {sale.user?.name || "Unknown User"}</p>
                <p className="text-sm"><span className="font-medium">Payment Method:</span> {sale.paymentMethod}</p>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Amount</h3>
              <div className="mt-1 space-y-1">
                <p className="text-sm"><span className="font-medium">Subtotal:</span> {formatCurrency(sale.subtotalAmount)}</p>
                {sale.discountAmount > 0 && (
                  <p className="text-sm"><span className="font-medium">Discount:</span> {formatCurrency(sale.discountAmount)}</p>
                )}
                {sale.taxAmount > 0 && (
                  <p className="text-sm"><span className="font-medium">Tax:</span> {formatCurrency(sale.taxAmount)}</p>
                )}
                <p className="text-base font-bold"><span className="font-medium">Total:</span> {formatCurrency(sale.totalAmount)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sale Items */}
      <Card>
        <CardHeader>
          <CardTitle>Sale Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Specifications
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Unit Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sale.items.map((item: any) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.product?.name || "Unknown Product"}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {item.specifications ? (
                        <ul className="list-disc list-inside">
                          {JSON.parse(item.specifications || '[]').map((spec: any, index: number) => (
                            <li key={index}>
                              <span className="font-medium">{spec.name}:</span> {spec.value}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <span>No specifications</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(item.unitPrice)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(item.totalPrice || (item.unitPrice * item.quantity))}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Notes */}
      {sale.notes && (
        <Card className="mt-4">
          <CardHeader>
            <CardTitle>Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-700">{sale.notes}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
