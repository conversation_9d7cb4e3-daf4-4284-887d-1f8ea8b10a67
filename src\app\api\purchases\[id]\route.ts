import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

// GET /api/purchases/:id - Get purchase by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Skip authentication for now
    // const session = await getServerSession(authOptions);
    // if (!session) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const id = params.id;

    const purchase = await prisma.purchase.findUnique({
      where: { id },
      include: {
        contact: true,
        branch: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!purchase) {
      return NextResponse.json({ error: 'Purchase not found' }, { status: 404 });
    }

    return NextResponse.json(purchase);
  } catch (error) {
    console.error('Error fetching purchase:', error);
    return NextResponse.json(
      { error: 'Failed to fetch purchase' },
      { status: 500 }
    );
  }
}

// PUT /api/purchases/:id - Update purchase
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Skip authentication for now
    // const session = await getServerSession(authOptions);
    // if (!session) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const id = params.id;
    const data = await request.json();

    // Check if purchase exists
    const existingPurchase = await prisma.purchase.findUnique({
      where: { id },
      include: {
        items: true,
      },
    });

    if (!existingPurchase) {
      return NextResponse.json({ error: 'Purchase not found' }, { status: 404 });
    }

    // Update purchase in a transaction
    const updatedPurchase = await prisma.$transaction(async (tx) => {
      // Delete existing items
      await tx.purchaseItem.deleteMany({
        where: { purchaseId: id },
      });

      // Get the contact to update balance
      const contact = existingPurchase.contactId ? await tx.contact.findUnique({
        where: { id: existingPurchase.contactId }
      }) : null;

      // Calculate balance change based on payment status change
      let balanceChange = 0;

      // If changing from UNPAID to PAID, decrease supplier balance
      if (existingPurchase.paymentStatus === "UNPAID" && data.paymentStatus === "PAID") {
        balanceChange = -existingPurchase.totalAmount;
      }
      // If changing from PAID to UNPAID, increase supplier balance
      else if (existingPurchase.paymentStatus === "PAID" && data.paymentStatus === "UNPAID") {
        balanceChange = existingPurchase.totalAmount;
      }

      // Update purchase
      const purchase = await tx.purchase.update({
        where: { id },
        data: {
          invoiceNumber: data.invoiceNumber,
          contactId: data.contactId,
          branchId: data.branchId,
          date: data.date,
          status: data.status,
          paymentMethod: data.paymentMethod,
          paymentStatus: data.paymentStatus,
          subtotalAmount: data.subtotal,
          discountAmount: data.discount,
          taxAmount: data.taxAmount,
          totalAmount: data.total,
          notes: data.notes,
          currency: data.currency || 'EGP',
          items: {
            create: data.items.map((item: any) => ({
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.total,
            })),
          },
        },
        include: {
          contact: true,
          branch: true,
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      // Update inventory for each item
      for (const item of data.items) {
        // Update inventory
        const inventory = await tx.inventory.findFirst({
          where: {
            productId: item.productId,
            warehouseId: item.warehouseId,
          },
        });

        if (inventory) {
          // Calculate weighted average cost
          const oldTotalValue = inventory.quantity * inventory.costPrice;
          const newItemValue = item.quantity * item.unitPrice;
          const newTotalQuantity = inventory.quantity + item.quantity;
          const weightedAverageCost = newTotalQuantity > 0
            ? (oldTotalValue + newItemValue) / newTotalQuantity
            : item.unitPrice;

          console.log(`Updating purchase - Calculating weighted average cost for product ${item.productId}:`);
          console.log(`- Old inventory: ${inventory.quantity} units at ${inventory.costPrice} = ${oldTotalValue}`);
          console.log(`- New purchase: ${item.quantity} units at ${item.unitPrice} = ${newItemValue}`);
          console.log(`- New total: ${newTotalQuantity} units at ${weightedAverageCost}`);

          // Update existing inventory with weighted average cost
          await tx.inventory.update({
            where: {
              id: inventory.id,
            },
            data: {
              quantity: newTotalQuantity,
              costPrice: weightedAverageCost,
            },
          });

          // Also update the product's cost price
          await tx.product.update({
            where: {
              id: item.productId,
            },
            data: {
              costPrice: weightedAverageCost,
            },
          });
        } else {
          // Create new inventory entry
          await tx.inventory.create({
            data: {
              productId: item.productId,
              warehouseId: item.warehouseId,
              quantity: item.quantity,
              costPrice: item.unitPrice,
            },
          });

          // Update the product's cost price
          await tx.product.update({
            where: {
              id: item.productId,
            },
            data: {
              costPrice: item.unitPrice,
            },
          });
        }
      }

      // Update contact balance if needed
      if (contact && balanceChange !== 0) {
        await tx.contact.update({
          where: { id: existingPurchase.contactId },
          data: {
            balance: {
              increment: balanceChange
            }
          }
        });
      }

      return purchase;
    });

    return NextResponse.json(updatedPurchase);
  } catch (error) {
    console.error('Error updating purchase:', error);
    return NextResponse.json(
      { error: 'Failed to update purchase' },
      { status: 500 }
    );
  }
}

// DELETE /api/purchases/:id - Delete purchase
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Skip authentication for now
    // const session = await getServerSession(authOptions);
    // if (!session) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const id = params.id;

    // Check if purchase exists
    const existingPurchase = await prisma.purchase.findUnique({
      where: { id },
    });

    if (!existingPurchase) {
      return NextResponse.json({ error: 'Purchase not found' }, { status: 404 });
    }

    // Delete purchase in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete purchase items
      await tx.purchaseItem.deleteMany({
        where: { purchaseId: id },
      });

      // Delete purchase
      await tx.purchase.delete({
        where: { id },
      });
    });

    return NextResponse.json({ message: 'Purchase deleted successfully' });
  } catch (error) {
    console.error('Error deleting purchase:', error);
    return NextResponse.json(
      { error: 'Failed to delete purchase' },
      { status: 500 }
    );
  }
}
