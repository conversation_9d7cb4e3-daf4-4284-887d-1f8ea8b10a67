import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

/**
 * GET /api/print-settings
 * Retrieve print settings
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the print settings
    const settings = await db.printSettings.findFirst();
    
    // If no settings exist, create default settings
    if (!settings) {
      const defaultSettings = await db.printSettings.create({
        data: {
          pageSize: 'A4',
          orientation: 'portrait',
          marginTop: 10,
          marginRight: 10,
          marginBottom: 10,
          marginLeft: 10,
          headerHeight: 30,
          footerHeight: 20,
          showLogo: true,
          logoPosition: 'left',
          showBranch: true,
          showQRCode: true,
          showBarcode: true,
          fontSize: 12,
          fontFamily: 'Arial',
          primaryColor: '#3895e7',
          secondaryColor: '#f3f4f6',
          footerText: 'Thank you for your business!',
          termsText: 'Terms and conditions apply.',
          defaultTemplateSettings: {
            invoice: { defaultTemplateId: null, copies: 1 },
            receipt: { defaultTemplateId: null, copies: 1 },
            purchase_order: { defaultTemplateId: null, copies: 1 },
            delivery_note: { defaultTemplateId: null, copies: 1 },
            maintenance_receipt: { defaultTemplateId: null, copies: 1 },
            maintenance_report: { defaultTemplateId: null, copies: 1 },
            customer_statement: { defaultTemplateId: null, copies: 1 },
            label: { defaultTemplateId: null, copies: 1 },
          },
          thermalPrinterSettings: {
            enabled: false,
            printerName: '',
            paperWidth: 80,
            paperHeight: 297,
            dpi: 203,
            printSpeed: 'normal',
            printDensity: 'medium',
          },
          signatureSettings: {
            showSignatures: true,
            managerSignature: null,
            accountantSignature: null,
            customerSignature: null,
          },
          stampSettings: {
            showStamp: true,
            stampImage: null,
            stampPosition: 'bottom-right',
          },
          multiLanguageSettings: {
            enabled: false,
            defaultLanguage: 'ar',
            availableLanguages: ['ar', 'en'],
          }
        }
      });
      
      return NextResponse.json(defaultSettings);
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching print settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch print settings' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/print-settings
 * Save print settings
 */
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const data = await request.json();
    
    // Get existing settings
    const existingSettings = await db.printSettings.findFirst();
    
    // Update or create settings
    let settings;
    if (existingSettings) {
      settings = await db.printSettings.update({
        where: { id: existingSettings.id },
        data: {
          pageSize: data.pageSize,
          orientation: data.orientation,
          marginTop: data.marginTop,
          marginRight: data.marginRight,
          marginBottom: data.marginBottom,
          marginLeft: data.marginLeft,
          headerHeight: data.headerHeight,
          footerHeight: data.footerHeight,
          showLogo: data.showLogo,
          logoPosition: data.logoPosition,
          showBranch: data.showBranch,
          showQRCode: data.showQRCode,
          showBarcode: data.showBarcode,
          fontSize: data.fontSize,
          fontFamily: data.fontFamily,
          primaryColor: data.primaryColor,
          secondaryColor: data.secondaryColor,
          footerText: data.footerText,
          termsText: data.termsText,
          customWidth: data.customWidth,
          customHeight: data.customHeight,
          companyLogo: data.companyLogo,
          defaultTemplateSettings: data.defaultTemplateSettings,
          thermalPrinterSettings: data.thermalPrinterSettings,
          signatureSettings: data.signatureSettings,
          stampSettings: data.stampSettings,
          multiLanguageSettings: data.multiLanguageSettings,
          branchId: data.branchId,
        }
      });
    } else {
      settings = await db.printSettings.create({
        data: {
          pageSize: data.pageSize,
          orientation: data.orientation,
          marginTop: data.marginTop,
          marginRight: data.marginRight,
          marginBottom: data.marginBottom,
          marginLeft: data.marginLeft,
          headerHeight: data.headerHeight,
          footerHeight: data.footerHeight,
          showLogo: data.showLogo,
          logoPosition: data.logoPosition,
          showBranch: data.showBranch,
          showQRCode: data.showQRCode,
          showBarcode: data.showBarcode,
          fontSize: data.fontSize,
          fontFamily: data.fontFamily,
          primaryColor: data.primaryColor,
          secondaryColor: data.secondaryColor,
          footerText: data.footerText,
          termsText: data.termsText,
          customWidth: data.customWidth,
          customHeight: data.customHeight,
          companyLogo: data.companyLogo,
          defaultTemplateSettings: data.defaultTemplateSettings,
          thermalPrinterSettings: data.thermalPrinterSettings,
          signatureSettings: data.signatureSettings,
          stampSettings: data.stampSettings,
          multiLanguageSettings: data.multiLanguageSettings,
          branchId: data.branchId,
        }
      });
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error saving print settings:', error);
    return NextResponse.json(
      { error: 'Failed to save print settings' },
      { status: 500 }
    );
  }
}
