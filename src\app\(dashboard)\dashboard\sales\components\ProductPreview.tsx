"use client";

import { useState, useEffect } from "react";
import { 
  Monitor, 
  Cpu, 
  HardDrive, 
  Save, 
  Package, 
  DollarSign,
  Laptop,
  Server
} from "lucide-react";
import { motion } from "framer-motion";

// Component types
type ComponentType = string;

// Component interface
interface Component {
  id: string;
  name: string;
  type: ComponentType;
  price: number;
  capacity?: string;
  speed?: string;
  description?: string;
  count?: number;
}

// Product interface
interface Product {
  id: string;
  name: string;
  basePrice: number;
  imageUrl?: string;
}

interface ProductPreviewProps {
  product: Product;
  selectedComponents: {
    [key in ComponentType]?: Component;
  };
  totalPrice: number;
  className?: string;
}

export default function ProductPreview({
  product,
  selectedComponents,
  totalPrice,
  className = ""
}: ProductPreviewProps) {
  // State for 3D rotation
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  
  // Handle mouse move for 3D effect
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Calculate rotation based on mouse position
    const rotateX = ((y / rect.height) - 0.5) * 10;
    const rotateY = ((x / rect.width) - 0.5) * -10;
    
    setRotation({ x: rotateX, y: rotateY });
  };
  
  // Reset rotation when mouse leaves
  const handleMouseLeave = () => {
    setRotation({ x: 0, y: 0 });
  };
  
  // Determine product icon based on components
  const getProductIcon = () => {
    // Check if it has CPU and RAM components
    const hasCPU = !!selectedComponents["CPU"];
    const hasRAM = !!selectedComponents["RAM"];
    const hasStorage = !!selectedComponents["SSD"] || !!selectedComponents["HDD"];
    
    if (hasCPU && hasRAM && hasStorage) {
      return <Laptop className="h-16 w-16 text-blue-500" />;
    } else if (hasCPU && hasRAM) {
      return <Server className="h-16 w-16 text-purple-500" />;
    } else {
      return <Monitor className="h-16 w-16 text-gray-500" />;
    }
  };
  
  // Calculate total RAM capacity
  const calculateTotalRAM = () => {
    const ram = selectedComponents["RAM"];
    if (!ram || !ram.capacity) return null;
    
    // Extract the number from the capacity string (e.g., "8GB" -> 8)
    const capacityMatch = ram.capacity.match(/(\d+)/);
    if (!capacityMatch || !capacityMatch[1]) return ram.capacity;
    
    const capacityValue = parseInt(capacityMatch[1]);
    const unit = ram.capacity.replace(/\d+/g, '');
    const count = ram.count || 1;
    
    return `${capacityValue * count}${unit}`;
  };
  
  // Get total storage capacity
  const getTotalStorage = () => {
    const storage: string[] = [];
    
    if (selectedComponents["SSD"]?.capacity) {
      storage.push(`SSD: ${selectedComponents["SSD"].capacity}`);
    }
    
    if (selectedComponents["HDD"]?.capacity) {
      storage.push(`HDD: ${selectedComponents["HDD"].capacity}`);
    }
    
    if (selectedComponents["NVMe"]?.capacity) {
      storage.push(`NVMe: ${selectedComponents["NVMe"].capacity}`);
    }
    
    return storage.length > 0 ? storage.join(", ") : null;
  };
  
  return (
    <div className={`${className}`}>
      <div 
        className="bg-white rounded-lg shadow-lg overflow-hidden"
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        <div className="p-6">
          <motion.div
            className="flex flex-col items-center"
            style={{
              transformStyle: "preserve-3d",
              transform: `perspective(1000px) rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`
            }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            {product.imageUrl ? (
              <img 
                src={product.imageUrl} 
                alt={product.name} 
                className="w-48 h-48 object-contain mb-4"
              />
            ) : (
              <div className="w-48 h-48 flex items-center justify-center mb-4">
                {getProductIcon()}
              </div>
            )}
            
            <h3 className="text-lg font-bold text-center">{product.name}</h3>
            <p className="text-sm text-gray-500 text-center mt-1">Customized Configuration</p>
            
            <div className="mt-4 w-full space-y-2">
              {selectedComponents["CPU"] && (
                <div className="flex items-center">
                  <Cpu className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm">{selectedComponents["CPU"].name}</span>
                </div>
              )}
              
              {selectedComponents["RAM"] && (
                <div className="flex items-center">
                  <Save className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-sm">
                    {selectedComponents["RAM"].name}
                    {selectedComponents["RAM"].count && selectedComponents["RAM"].count > 1 && (
                      <span className="text-xs text-gray-500 ml-1">
                        ({selectedComponents["RAM"].count} sticks, {calculateTotalRAM()})
                      </span>
                    )}
                  </span>
                </div>
              )}
              
              {getTotalStorage() && (
                <div className="flex items-center">
                  <HardDrive className="h-4 w-4 text-purple-500 mr-2" />
                  <span className="text-sm">{getTotalStorage()}</span>
                </div>
              )}
              
              {selectedComponents["GPU"] && (
                <div className="flex items-center">
                  <svg 
                    className="h-4 w-4 text-red-500 mr-2" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  >
                    <rect x="2" y="6" width="20" height="12" rx="2" />
                    <line x1="6" y1="12" x2="10" y2="12" />
                    <line x1="14" y1="12" x2="18" y2="12" />
                  </svg>
                  <span className="text-sm">{selectedComponents["GPU"].name}</span>
                </div>
              )}
            </div>
            
            <div className="mt-6 w-full pt-4 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Total Price:</span>
                <span className="text-lg font-bold text-blue-600">{totalPrice.toFixed(2)} ج.م</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
