-- Add new columns to JournalEntry table
ALTER TABLE "JournalEntry" ADD COLUMN IF NOT EXISTS "entryNumber" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN IF NOT EXISTS "referenceType" "ReferenceType";
ALTER TABLE "JournalEntry" ADD COLUMN IF NOT EXISTS "isPosted" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "JournalEntry" ADD COLUMN IF NOT EXISTS "branchId" TEXT;

-- Add foreign key constraint for branchId
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_branchId_fkey" 
FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add indexes
CREATE INDEX IF NOT EXISTS "JournalEntry_entryNumber_idx" ON "JournalEntry"("entryNumber");
CREATE INDEX IF NOT EXISTS "JournalEntry_date_idx" ON "JournalEntry"("date");
CREATE INDEX IF NOT EXISTS "JournalEntry_referenceType_idx" ON "JournalEntry"("referenceType");
CREATE INDEX IF NOT EXISTS "JournalEntry_isPosted_idx" ON "JournalEntry"("isPosted");
CREATE INDEX IF NOT EXISTS "JournalEntry_branchId_idx" ON "JournalEntry"("branchId");
