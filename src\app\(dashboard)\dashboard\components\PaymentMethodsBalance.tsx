"use client";

import { motion } from "framer-motion";
import Link from "next/link";

interface PaymentMethodsBalanceProps {
  paymentMethods: {
    total: number;
    cash: number;
    vodafoneCash: number;
    bankTransfer: number;
    creditCard: number;
    customerAccount: number;
  };
}

export default function PaymentMethodsBalance({ paymentMethods }: PaymentMethodsBalanceProps) {
  // Function to get the percentage of each payment method
  const getPercentage = (value: number) => {
    if (paymentMethods.total === 0) return 0;
    return Math.round((value / paymentMethods.total) * 100);
  };

  // Function to get color for payment method
  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case "cash":
        return {
          bg: "bg-green-500",
          text: "text-green-700",
          light: "bg-green-100",
        };
      case "vodafoneCash":
        return {
          bg: "bg-red-500",
          text: "text-red-700",
          light: "bg-red-100",
        };
      case "bankTransfer":
        return {
          bg: "bg-blue-500",
          text: "text-blue-700",
          light: "bg-blue-100",
        };
      case "creditCard":
        return {
          bg: "bg-purple-500",
          text: "text-purple-700",
          light: "bg-purple-100",
        };
      case "customerAccount":
        return {
          bg: "bg-yellow-500",
          text: "text-yellow-700",
          light: "bg-yellow-100",
        };
      default:
        return {
          bg: "bg-gray-500",
          text: "text-gray-700",
          light: "bg-gray-100",
        };
    }
  };

  // Function to get payment method name in English
  const getPaymentMethodName = (method: string) => {
    switch (method) {
      case "cash":
        return "Cash";
      case "vodafoneCash":
        return "Vodafone Cash";
      case "bankTransfer":
        return "Bank Transfer";
      case "creditCard":
        return "Credit Card";
      case "customerAccount":
        return "Customer Account";
      default:
        return method;
    }
  };

  // Create an array of payment methods for easier rendering
  const paymentMethodsArray = [
    { key: "cash", value: paymentMethods.cash },
    { key: "vodafoneCash", value: paymentMethods.vodafoneCash },
    { key: "bankTransfer", value: paymentMethods.bankTransfer },
    { key: "creditCard", value: paymentMethods.creditCard },
    { key: "customerAccount", value: paymentMethods.customerAccount },
  ].sort((a, b) => b.value - a.value); // Sort by value (highest first)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.3 }}
      className="bg-white shadow-lg rounded-lg overflow-hidden"
    >
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-indigo-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
          Payment Methods Balance
        </h3>
        <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2.5 py-0.5 rounded">
          {paymentMethods.total.toFixed(2)} EGP
        </span>
      </div>

      <div className="p-6">
        {paymentMethodsArray.length > 0 ? (
          <div className="space-y-4">
            {paymentMethodsArray.map((method, index) => {
              const colors = getPaymentMethodColor(method.key);
              const percentage = getPercentage(method.value);

              return (
                <motion.div
                  key={method.key}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: 0.1 * index }}
                  className="space-y-2"
                >
                  <Link
                    href={`/dashboard/finance/payment-methods/${method.key}`}
                    className="block hover:bg-gray-50 rounded-lg p-2 -mx-2 transition-colors duration-200"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full ${colors.bg} mr-2`}></div>
                        <span className="text-sm font-medium text-gray-700">
                          {getPaymentMethodName(method.key)}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-sm font-semibold text-gray-900 mr-2">
                          {method.value.toFixed(2)} EGP
                        </span>
                        <span className={`text-xs font-medium ${colors.text}`}>
                          {percentage}%
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${percentage}%` }}
                        transition={{ duration: 0.5, delay: 0.2 * index }}
                        className={`${colors.bg} h-2 rounded-full`}
                      ></motion.div>
                    </div>
                    <div className="mt-1 text-xs text-gray-500 flex items-center justify-end">
                      <span>View transactions</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3 ml-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </Link>
                </motion.div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-sm text-gray-500">لا توجد معاملات مالية</p>
          </div>
        )}
      </div>

      <div className="bg-gray-50 px-6 py-3">
        <div className="text-sm">
          <Link
            href="/dashboard/finance/payment-methods"
            className="font-medium text-indigo-600 hover:text-indigo-500 hover:underline flex items-center justify-between"
          >
            <span>View All Payment Methods</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
