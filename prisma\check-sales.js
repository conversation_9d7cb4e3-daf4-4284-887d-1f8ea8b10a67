const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('Checking sales data...');
  
  const salesCount = await prisma.sale.count();
  console.log(`Number of sales: ${salesCount}`);
  
  const saleItemsCount = await prisma.saleItem.count();
  console.log(`Number of sale items: ${saleItemsCount}`);
  
  const saleItemComponentsCount = await prisma.saleItemComponent.count();
  console.log(`Number of sale item components: ${saleItemComponentsCount}`);
  
  console.log('Check completed');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
