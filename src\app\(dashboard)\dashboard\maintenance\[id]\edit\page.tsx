"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import React from "react";
import { Loader2, ArrowLeft, Save } from "lucide-react";

interface MaintenanceService {
  id: string;
  serviceNumber: string;
  deviceType: string;
  brand: string;
  model?: string;
  serialNumber?: string;
  problemDescription: string;
  receivedDate: string;
  estimatedCompletionDate?: string;
  status: string;
  priority: string;
  initialDiagnosis?: string;
  technicalNotes?: string;
  estimatedCost?: number;
  finalCost?: number;
  isPaid: boolean;
  isWarranty: boolean;
  warrantyDetails?: string;
  estimatedHours?: number;
  actualHours?: number;
  technicianId?: string;
  customerSignature: boolean;
  customerRating?: number;
  customerFeedback?: string;
  notificationMethod?: string;
  paymentMethod?: string;
  paymentStatus?: string;
  invoiceId?: string;
  invoiceNumber?: string;
  contact: {
    id: string;
    name: string;
  };
}

export default function EditMaintenancePage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const id = unwrappedParams.id;

  const router = useRouter();
  const [service, setService] = useState<MaintenanceService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    deviceType: "",
    brand: "",
    model: "",
    serialNumber: "",
    problemDescription: "",
    priority: "",
    initialDiagnosis: "",
    technicalNotes: "",
    estimatedCost: "",
    finalCost: "",
    isPaid: false,
    isWarranty: false,
    warrantyDetails: "",
    estimatedCompletionDate: "",
    estimatedHours: "",
    actualHours: "",
    technicianId: "",
    customerSignature: false,
    customerRating: "",
    customerFeedback: "",
    notificationMethod: "",
    paymentMethod: "",
    paymentStatus: "",
  });

  // Fetch maintenance service details
  useEffect(() => {
    const fetchServiceDetails = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/maintenance/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch maintenance service details");
        }

        const data = await response.json();
        setService(data);

        // Initialize form data
        setFormData({
          deviceType: data.deviceType || "",
          brand: data.brand || "",
          model: data.model || "",
          serialNumber: data.serialNumber || "",
          problemDescription: data.problemDescription || "",
          priority: data.priority || "MEDIUM",
          initialDiagnosis: data.initialDiagnosis || "",
          technicalNotes: data.technicalNotes || "",
          estimatedCost: data.estimatedCost ? data.estimatedCost.toString() : "",
          finalCost: data.finalCost ? data.finalCost.toString() : "",
          isPaid: data.isPaid || false,
          isWarranty: data.isWarranty || false,
          warrantyDetails: data.warrantyDetails || "",
          estimatedCompletionDate: data.estimatedCompletionDate
            ? new Date(data.estimatedCompletionDate).toISOString().split('T')[0]
            : "",
          estimatedHours: data.estimatedHours ? data.estimatedHours.toString() : "",
          actualHours: data.actualHours ? data.actualHours.toString() : "",
          technicianId: data.technicianId || "",
          customerSignature: data.customerSignature || false,
          customerRating: data.customerRating ? data.customerRating.toString() : "",
          customerFeedback: data.customerFeedback || "",
          notificationMethod: data.notificationMethod || "",
          paymentMethod: data.paymentMethod || "",
          paymentStatus: data.paymentStatus || "",
        });
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceDetails();
  }, [id]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox"
        ? (e.target as HTMLInputElement).checked
        : value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.deviceType) {
        throw new Error("Device type is required");
      }

      if (!formData.brand) {
        throw new Error("Brand is required");
      }

      if (!formData.problemDescription) {
        throw new Error("Problem description is required");
      }

      // Prepare data for API
      const maintenanceData = {
        deviceType: formData.deviceType,
        brand: formData.brand,
        model: formData.model || null,
        serialNumber: formData.serialNumber || null,
        problemDescription: formData.problemDescription,
        priority: formData.priority,
        initialDiagnosis: formData.initialDiagnosis || null,
        technicalNotes: formData.technicalNotes || null,
        estimatedCost: formData.estimatedCost ? parseFloat(formData.estimatedCost) : null,
        finalCost: formData.finalCost ? parseFloat(formData.finalCost) : null,
        isPaid: formData.isPaid,
        isWarranty: formData.isWarranty,
        warrantyDetails: formData.isWarranty ? formData.warrantyDetails : null,
        estimatedCompletionDate: formData.estimatedCompletionDate || null,
        estimatedHours: formData.estimatedHours ? parseFloat(formData.estimatedHours) : null,
        actualHours: formData.actualHours ? parseFloat(formData.actualHours) : null,
        technicianId: formData.technicianId || null,
        customerSignature: formData.customerSignature,
        customerRating: formData.customerRating ? parseInt(formData.customerRating) : null,
        customerFeedback: formData.customerFeedback || null,
        notificationMethod: formData.notificationMethod || null,
        paymentMethod: formData.paymentMethod || null,
        paymentStatus: formData.paymentStatus || null,
      };

      // Submit to API
      const response = await fetch(`/api/maintenance/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(maintenanceData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update maintenance service");
      }

      // Redirect back to maintenance service page
      router.push(`/dashboard/maintenance/${id}`);
    } catch (error: any) {
      setError(error.message);
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  if (!service) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 text-yellow-700 p-4 rounded-md">
          Maintenance service not found
        </div>
        <div className="mt-4">
          <Link
            href="/dashboard/maintenance"
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            href={`/dashboard/maintenance/${id}`}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Maintenance Service</h1>
            <p className="text-sm text-gray-500">
              {service.serviceNumber} - {service.contact.name}
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6 space-y-6">
          {/* Device Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Device Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="deviceType" className="block text-sm font-medium text-gray-700 mb-1">
                  Device Type <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="deviceType"
                  name="deviceType"
                  value={formData.deviceType}
                  onChange={handleChange}
                  placeholder="e.g. Laptop, Desktop, Printer"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                />
              </div>
              <div>
                <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-1">
                  Brand <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="brand"
                  name="brand"
                  value={formData.brand}
                  onChange={handleChange}
                  placeholder="e.g. HP, Dell, Lenovo"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                />
              </div>
              <div>
                <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                  Model
                </label>
                <input
                  type="text"
                  id="model"
                  name="model"
                  value={formData.model}
                  onChange={handleChange}
                  placeholder="e.g. ThinkPad X1, Inspiron 15"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
              <div>
                <label htmlFor="serialNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Serial Number
                </label>
                <input
                  type="text"
                  id="serialNumber"
                  name="serialNumber"
                  value={formData.serialNumber}
                  onChange={handleChange}
                  placeholder="Device serial number"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Service Information</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="problemDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Problem Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="problemDescription"
                  name="problemDescription"
                  value={formData.problemDescription}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Describe the problem with the device"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  required
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <select
                    id="priority"
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  >
                    <option value="LOW">Low</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="HIGH">High</option>
                    <option value="URGENT">Urgent</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="estimatedCompletionDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Completion Date
                  </label>
                  <input
                    type="date"
                    id="estimatedCompletionDate"
                    name="estimatedCompletionDate"
                    value={formData.estimatedCompletionDate}
                    onChange={handleChange}
                    className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="initialDiagnosis" className="block text-sm font-medium text-gray-700 mb-1">
                  Initial Diagnosis
                </label>
                <textarea
                  id="initialDiagnosis"
                  name="initialDiagnosis"
                  value={formData.initialDiagnosis}
                  onChange={handleChange}
                  rows={2}
                  placeholder="Initial diagnosis of the problem"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                ></textarea>
              </div>

              <div>
                <label htmlFor="technicalNotes" className="block text-sm font-medium text-gray-700 mb-1">
                  Technical Notes
                </label>
                <textarea
                  id="technicalNotes"
                  name="technicalNotes"
                  value={formData.technicalNotes}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Technical notes and details about the repair"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                ></textarea>
              </div>
            </div>
          </div>

          {/* Cost Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Cost Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="estimatedCost" className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Cost
                </label>
                <input
                  type="number"
                  id="estimatedCost"
                  name="estimatedCost"
                  value={formData.estimatedCost}
                  onChange={handleChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
              <div>
                <label htmlFor="finalCost" className="block text-sm font-medium text-gray-700 mb-1">
                  Final Cost
                </label>
                <input
                  type="number"
                  id="finalCost"
                  name="finalCost"
                  value={formData.finalCost}
                  onChange={handleChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
            </div>

            <div className="mt-4 flex items-center">
              <input
                type="checkbox"
                id="isPaid"
                name="isPaid"
                checked={formData.isPaid}
                onChange={(e) => setFormData({ ...formData, isPaid: e.target.checked })}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="isPaid" className="ml-2 block text-sm font-medium text-gray-700">
                Mark as Paid
              </label>
            </div>

            <div className="mt-4 flex items-center">
              <input
                type="checkbox"
                id="isWarranty"
                name="isWarranty"
                checked={formData.isWarranty}
                onChange={(e) => setFormData({ ...formData, isWarranty: e.target.checked })}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="isWarranty" className="ml-2 block text-sm font-medium text-gray-700">
                Under Warranty
              </label>
            </div>

            {formData.isWarranty && (
              <div className="mt-4">
                <label htmlFor="warrantyDetails" className="block text-sm font-medium text-gray-700 mb-1">
                  Warranty Details
                </label>
                <textarea
                  id="warrantyDetails"
                  name="warrantyDetails"
                  value={formData.warrantyDetails}
                  onChange={handleChange}
                  rows={2}
                  placeholder="Warranty details, expiration date, etc."
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                ></textarea>
              </div>
            )}
          </div>

          {/* Payment Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleChange}
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                >
                  <option value="">Not Specified</option>
                  <option value="CASH">Cash</option>
                  <option value="VODAFONE_CASH">Vodafone Cash</option>
                  <option value="BANK_TRANSFER">Bank Transfer</option>
                  <option value="VISA">Visa</option>
                </select>
              </div>
              <div>
                <label htmlFor="paymentStatus" className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Status
                </label>
                <select
                  id="paymentStatus"
                  name="paymentStatus"
                  value={formData.paymentStatus}
                  onChange={handleChange}
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                >
                  <option value="">Not Specified</option>
                  <option value="UNPAID">Unpaid</option>
                  <option value="PARTIALLY_PAID">Partially Paid</option>
                  <option value="PAID">Paid</option>
                </select>
              </div>
              <div>
                <label htmlFor="notificationMethod" className="block text-sm font-medium text-gray-700 mb-1">
                  Notification Method
                </label>
                <select
                  id="notificationMethod"
                  name="notificationMethod"
                  value={formData.notificationMethod}
                  onChange={handleChange}
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                >
                  <option value="">Not Specified</option>
                  <option value="PHONE">Phone</option>
                  <option value="SMS">SMS</option>
                  <option value="EMAIL">Email</option>
                  <option value="WHATSAPP">WhatsApp</option>
                </select>
              </div>
            </div>
          </div>

          {/* Time Tracking */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Time Tracking</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="estimatedHours" className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Hours
                </label>
                <input
                  type="number"
                  id="estimatedHours"
                  name="estimatedHours"
                  value={formData.estimatedHours}
                  onChange={handleChange}
                  placeholder="0.0"
                  step="0.5"
                  min="0"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
              <div>
                <label htmlFor="actualHours" className="block text-sm font-medium text-gray-700 mb-1">
                  Actual Hours
                </label>
                <input
                  type="number"
                  id="actualHours"
                  name="actualHours"
                  value={formData.actualHours}
                  onChange={handleChange}
                  placeholder="0.0"
                  step="0.5"
                  min="0"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                />
              </div>
            </div>
          </div>

          {/* Customer Feedback */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Feedback</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="customerRating" className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Rating (1-5)
                </label>
                <select
                  id="customerRating"
                  name="customerRating"
                  value={formData.customerRating}
                  onChange={handleChange}
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                >
                  <option value="">Not Rated</option>
                  <option value="1">1 - Poor</option>
                  <option value="2">2 - Fair</option>
                  <option value="3">3 - Good</option>
                  <option value="4">4 - Very Good</option>
                  <option value="5">5 - Excellent</option>
                </select>
              </div>

              <div>
                <label htmlFor="customerFeedback" className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Feedback
                </label>
                <textarea
                  id="customerFeedback"
                  name="customerFeedback"
                  value={formData.customerFeedback}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Customer comments and feedback"
                  className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium"
                ></textarea>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="customerSignature"
                  name="customerSignature"
                  checked={formData.customerSignature}
                  onChange={(e) => setFormData({ ...formData, customerSignature: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="customerSignature" className="ml-2 block text-sm font-medium text-gray-700">
                  Customer Signature Received
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 flex justify-end">
          <Link
            href={`/dashboard/maintenance/${params.id}`}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium mr-2"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSaving}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium flex items-center"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
