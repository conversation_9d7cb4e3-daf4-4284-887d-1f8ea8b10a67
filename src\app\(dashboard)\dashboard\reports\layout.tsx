"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Percent } from "lucide-react";

export default function ReportsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const tabs = [
    {
      id: 'general',
      name: "General Reports",
      href: "/dashboard/reports",
      current: pathname === "/dashboard/reports",
      icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
    },
    {
      id: 'discounts',
      name: "Discount Reports",
      href: "/dashboard/reports/discounts",
      current: pathname === "/dashboard/reports/discounts",
      icon: "M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z"
    },
    {
      id: 'loyalty',
      name: "Loyalty Reports",
      href: "/dashboard/reports/loyalty",
      current: pathname === "/dashboard/reports/loyalty",
      icon: "M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
    },
    {
      id: 'sales',
      name: "Sales Reports",
      href: "/dashboard/reports/sales",
      current: pathname === "/dashboard/reports/sales",
      icon: "M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
    },
    {
      id: 'inventory',
      name: "Inventory Reports",
      href: "/dashboard/reports/inventory",
      current: pathname === "/dashboard/reports/inventory",
      icon: "M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
    },
    {
      id: 'customers',
      name: "Customer Reports",
      href: "/dashboard/reports/customers",
      current: pathname === "/dashboard/reports/customers",
      icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200">
        {/* Desktop Navigation */}
        <div className="hidden md:block">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex flex-wrap px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <Link
                  key={tab.id}
                  href={tab.href}
                  className={`
                    ${tab.current
                      ? 'border-[#3895e7] text-[#3895e7]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                    whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm flex items-center
                  `}
                >
                  {tab.id === 'general' ? (
                    <BarChart className="h-5 w-5 mr-2" />
                  ) : tab.id === 'discounts' ? (
                    <Percent className="h-5 w-5 mr-2" />
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={tab.icon}
                      />
                    </svg>
                  )}
                  {tab.name}
                </Link>
              ))}
            </nav>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden">
          <select
            value={tabs.find(tab => tab.current)?.id || ''}
            onChange={(e) => {
              const selectedTab = tabs.find(tab => tab.id === e.target.value);
              if (selectedTab) {
                window.location.href = selectedTab.href;
              }
            }}
            className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-[#3895e7] focus:border-[#3895e7] sm:text-sm"
          >
            {tabs.map((tab) => (
              <option key={tab.id} value={tab.id}>
                {tab.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="px-4 py-5 sm:px-6">
        {children}
      </div>
    </div>
  );
}
