"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Bell, AlertTriangle, X, Clock, ExternalLink } from "lucide-react";
import { calculateServiceDays } from "@/lib/maintenance";
import { format } from "date-fns";

interface MaintenanceService {
  id: string;
  serviceNumber: string;
  deviceType: string;
  brand: string;
  model?: string;
  receivedDate: string;
  daysOverdue: number;
  contact: {
    name: string;
    phone: string;
  };
}

export default function MaintenanceNotifications() {
  const [isOpen, setIsOpen] = useState(false);
  const [services, setServices] = useState<MaintenanceService[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOverdueServices = async () => {
      try {
        const response = await fetch("/api/maintenance/overdue");
        
        if (response.status === 404) {
          // Maintenance module might not be installed yet
          setIsLoading(false);
          return;
        }
        
        if (!response.ok) {
          throw new Error("Failed to fetch overdue maintenance services");
        }
        
        const data = await response.json();
        setServices(data);
      } catch (error: any) {
        console.error("Error fetching overdue services:", error);
        // Don't show error to user, just show empty state
      } finally {
        setIsLoading(false);
      }
    };

    fetchOverdueServices();

    // Refresh every 30 minutes
    const interval = setInterval(fetchOverdueServices, 30 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  const toggleNotifications = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative">
      <button
        onClick={toggleNotifications}
        className="p-2 rounded-full hover:bg-gray-100 relative"
        aria-label="Notifications"
      >
        <Bell className="h-6 w-6 text-gray-600" />
        {services.length > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">
            {services.length}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20">
          <div className="p-3 bg-indigo-600 text-white flex justify-between items-center">
            <h3 className="text-sm font-semibold flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Overdue Maintenance
            </h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white hover:text-gray-200"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">
                Loading...
              </div>
            ) : services.length > 0 ? (
              <div>
                {services.map((service) => (
                  <div key={service.id} className="p-3 border-b border-gray-100 hover:bg-gray-50">
                    <Link
                      href={`/dashboard/maintenance/${service.id}`}
                      className="block"
                      onClick={() => setIsOpen(false)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-indigo-600">{service.serviceNumber}</p>
                          <p className="text-sm text-gray-600">
                            {service.deviceType} - {service.brand} {service.model}
                          </p>
                          <p className="text-xs text-gray-500">{service.contact.name}</p>
                        </div>
                        <div className="flex items-center bg-red-100 text-red-800 text-xs font-semibold px-2 py-0.5 rounded">
                          <Clock className="h-3 w-3 mr-1" />
                          {service.daysOverdue} days
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Received: {format(new Date(service.receivedDate), "dd/MM/yyyy")}
                      </p>
                    </Link>
                  </div>
                ))}

                <div className="p-2 bg-gray-50 text-center">
                  <Link
                    href="/dashboard/maintenance?overdue=true"
                    className="text-xs text-indigo-600 hover:text-indigo-800 font-medium flex items-center justify-center"
                    onClick={() => setIsOpen(false)}
                  >
                    View All Overdue Services
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Link>
                </div>
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                No overdue maintenance services
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
