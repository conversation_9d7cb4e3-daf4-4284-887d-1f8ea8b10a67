import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { z } from "zod";

// Settings schema for validation
const settingsSchema = z.object({
  isEnabled: z.boolean(),
  autoSuggest: z.boolean(),
  voiceEnabled: z.boolean(),
  notificationsOn: z.boolean(),
});

// GET /api/ai-assistant/settings - Get user's AI assistant settings
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get user's AI assistant settings
    const settings = await db.aIAssistantSettings.findUnique({
      where: {
        userId: session.user.id,
      },
    });
    
    // If no settings exist, return default values
    if (!settings) {
      return NextResponse.json({
        isEnabled: true,
        autoSuggest: true,
        voiceEnabled: false,
        notificationsOn: true,
      });
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error fetching AI assistant settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch AI assistant settings" },
      { status: 500 }
    );
  }
}

// POST /api/ai-assistant/settings - Update user's AI assistant settings
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse and validate request body
    const body = await req.json();
    const validatedData = settingsSchema.parse(body);
    
    // Update or create settings
    const settings = await db.aIAssistantSettings.upsert({
      where: {
        userId: session.user.id,
      },
      update: validatedData,
      create: {
        ...validatedData,
        userId: session.user.id,
      },
    });
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error updating AI assistant settings:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to update AI assistant settings" },
      { status: 500 }
    );
  }
}
