"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

interface Branch {
  id: string;
  name: string;
}

interface Warehouse {
  id: string;
  name: string;
  branchId: string;
}

interface Permission {
  id: string;
  name: string;
  description?: string;
}

interface UserFormProps {
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    branchId?: string;
    isActive: boolean;
    warehouses: { id: string; name: string }[];
    permissions: { id: string; name: string }[];
  };
  onClose: () => void;
  onSuccess: () => void;
}

export default function UserForm({ user, onClose, onSuccess }: UserFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [branches, setBranches] = useState<Branch[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isFetching, setIsFetching] = useState(false);

  const [formData, setFormData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    password: "",
    confirmPassword: "",
    role: user?.role || "EMPLOYEE",
    branchId: user?.branchId || "",
    isActive: user?.isActive ?? true,
    warehouseIds: user?.warehouses.map(w => w.id) || [],
    permissionIds: user?.permissions.map(p => p.id) || [],
  });

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsFetching(true);
      try {
        // Fetch branches
        const branchesResponse = await fetch("/api/branches");
        if (branchesResponse.ok) {
          const branchesData = await branchesResponse.json();
          setBranches(branchesData);
        }

        // Fetch warehouses
        const warehousesResponse = await fetch("/api/warehouses");
        if (warehousesResponse.ok) {
          const warehousesData = await warehousesResponse.json();
          setWarehouses(warehousesData);
        }

        // Fetch permissions
        const permissionsResponse = await fetch("/api/permissions");
        if (permissionsResponse.ok) {
          const permissionsData = await permissionsResponse.json();
          setPermissions(permissionsData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsFetching(false);
      }
    };

    fetchData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleWarehouseChange = (warehouseId: string) => {
    setFormData((prev) => {
      const warehouseIds = [...prev.warehouseIds];
      const index = warehouseIds.indexOf(warehouseId);

      if (index === -1) {
        warehouseIds.push(warehouseId);
      } else {
        warehouseIds.splice(index, 1);
      }

      return { ...prev, warehouseIds };
    });
  };

  const handlePermissionChange = (permissionId: string) => {
    setFormData((prev) => {
      const permissionIds = [...prev.permissionIds];
      const index = permissionIds.indexOf(permissionId);

      if (index === -1) {
        permissionIds.push(permissionId);
      } else {
        permissionIds.splice(index, 1);
      }

      return { ...prev, permissionIds };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validate passwords
    if (!user && formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    try {
      const url = user ? `/api/users/${user.id}` : "/api/users";
      const method = user ? "PATCH" : "POST";

      // Remove confirmPassword from the data sent to the API
      const { confirmPassword, ...dataToSend } = formData;

      // If editing and password is empty, remove it from the request
      if (user && !dataToSend.password) {
        delete dataToSend.password;
      }

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataToSend),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save user");
      }

      onSuccess();
      router.refresh();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!user) return;

    setIsLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete user");
      }

      onSuccess();
      router.refresh();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  // Filter warehouses based on selected branch
  const filteredWarehouses = formData.branchId
    ? warehouses.filter(w => w.branchId === formData.branchId)
    : warehouses;

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg max-h-[80vh] overflow-y-auto">
      <h2 className="text-lg font-medium mb-4">
        {user ? "Edit User" : "Add New User"}
      </h2>

      {error && (
        <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-semibold text-gray-800">
              Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black font-medium"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-semibold text-gray-800">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black font-medium"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-semibold text-gray-800">
              Password {user && "(Leave blank to keep current password)"}
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required={!user}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black font-medium"
            />
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-800">
              Confirm Password
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              required={!user}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black font-medium"
            />
          </div>

          <div>
            <label htmlFor="role" className="block text-sm font-semibold text-gray-800">
              Role
            </label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black font-medium"
            >
              <option value="ADMIN" className="text-black font-medium">Admin</option>
              <option value="MANAGER" className="text-black font-medium">Manager</option>
              <option value="EMPLOYEE" className="text-black font-medium">Employee</option>
            </select>
          </div>

          <div>
            <label htmlFor="branchId" className="block text-sm font-semibold text-gray-800">
              Branch
            </label>
            <select
              id="branchId"
              name="branchId"
              value={formData.branchId}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black font-medium"
              disabled={isFetching}
            >
              <option value="" className="text-black font-medium">Select Branch</option>
              {branches.map((branch) => (
                <option key={branch.id} value={branch.id} className="text-black font-medium">
                  {branch.name}
                </option>
              ))}
            </select>
          </div>

          {formData.branchId && (
            <div>
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                Warehouses
              </label>
              <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                {filteredWarehouses.length > 0 ? (
                  filteredWarehouses.map((warehouse) => (
                    <div key={warehouse.id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`warehouse-${warehouse.id}`}
                        checked={formData.warehouseIds.includes(warehouse.id)}
                        onChange={() => handleWarehouseChange(warehouse.id)}
                        className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
                      />
                      <label htmlFor={`warehouse-${warehouse.id}`} className="ml-2 block text-sm font-medium text-gray-800">
                        {warehouse.name}
                      </label>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No warehouses available for this branch</p>
                )}
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-semibold text-gray-800 mb-2">
              Permissions
            </label>
            <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md p-2">
              {/* Group permissions by category */}
              {[
                { title: "Users", prefix: "user" },
                { title: "Branches", prefix: "branch" },
                { title: "Warehouses", prefix: "warehouse" },
                { title: "Products", prefix: "product" },
                { title: "Inventory", prefix: "inventory" },
                { title: "Sales", prefix: "sale" },
                { title: "Purchases", prefix: "purchase" },
                { title: "Customers", prefix: "customer" },
                { title: "Suppliers", prefix: "supplier" },
                { title: "Accounts", prefix: "account" },
                { title: "Reports", prefix: "report" },
              ].map((category) => {
                const categoryPermissions = permissions.filter(p =>
                  p.name.includes(category.prefix) ||
                  (category.prefix === "user" && p.name.includes("users")) ||
                  (category.prefix === "branch" && p.name.includes("branches")) ||
                  (category.prefix === "warehouse" && p.name.includes("warehouses")) ||
                  (category.prefix === "product" && p.name.includes("products")) ||
                  (category.prefix === "sale" && p.name.includes("sales")) ||
                  (category.prefix === "purchase" && p.name.includes("purchases")) ||
                  (category.prefix === "customer" && p.name.includes("customers")) ||
                  (category.prefix === "supplier" && p.name.includes("suppliers")) ||
                  (category.prefix === "account" && p.name.includes("accounts")) ||
                  (category.prefix === "report" && p.name.includes("reports"))
                );

                if (categoryPermissions.length === 0) return null;

                return (
                  <div key={category.title} className="mb-4">
                    <h3 className="font-semibold text-gray-800 mb-2 border-b pb-1">{category.title}</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {categoryPermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`permission-${permission.id}`}
                            checked={formData.permissionIds.includes(permission.id)}
                            onChange={() => handlePermissionChange(permission.id)}
                            className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
                          />
                          <label htmlFor={`permission-${permission.id}`} className="ml-2 block text-sm font-medium text-gray-800">
                            {permission.name.replace(`${category.prefix}_`, "").replace(`${category.title.toLowerCase()}_`, "")}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {user && (
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm font-medium text-gray-800">
                Active
              </label>
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-between">
          {user && !user.email.toLowerCase().includes("admin") && user.role !== "ADMIN" && (
            <button
              type="button"
              onClick={() => setShowDeleteConfirm(true)}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Delete User
            </button>
          )}

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || isFetching}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isLoading ? "Saving..." : "Save"}
            </button>
          </div>
        </div>

        {/* Delete Confirmation Dialog */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
              <p className="text-sm text-gray-500 mb-4">
                Are you sure you want to delete this user? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDelete}
                  disabled={isLoading}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {isLoading ? "Deleting..." : "Delete"}
                </button>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
