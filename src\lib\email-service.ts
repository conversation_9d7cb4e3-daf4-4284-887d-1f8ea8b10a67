import nodemailer from 'nodemailer';

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content?: Buffer | string;
    path?: string;
    contentType?: string;
  }>;
}

// Create a transporter with environment variables
const createTransporter = () => {
  // Use environment variables for email configuration
  const host = process.env.EMAIL_HOST || 'smtp.gmail.com';
  const port = parseInt(process.env.EMAIL_PORT || '587', 10);
  const secure = process.env.EMAIL_SECURE === 'true';
  const user = process.env.EMAIL_USER || '';
  const pass = process.env.EMAIL_PASSWORD || '';
  
  return nodemailer.createTransport({
    host,
    port,
    secure,
    auth: {
      user,
      pass,
    },
  });
};

// Send email function
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    const transporter = createTransporter();
    
    const defaultFrom = process.env.EMAIL_FROM || '<EMAIL>';
    
    const mailOptions = {
      from: options.from || defaultFrom,
      to: options.to,
      subject: options.subject,
      text: options.text || '',
      html: options.html,
      attachments: options.attachments || [],
    };
    
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

// Send loyalty notification email
export async function sendLoyaltyNotificationEmail(
  to: string,
  customerName: string,
  points: number,
  newTotal: number,
  reason: string,
  loyaltyTier: string
): Promise<boolean> {
  const subject = points > 0 
    ? `${customerName}, you've earned ${points} loyalty points!` 
    : `${customerName}, ${Math.abs(points)} points have been redeemed`;
  
  const tierColor = getTierColor(loyaltyTier);
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #3895e7; margin-bottom: 10px;">VERO ERP Loyalty Program</h1>
        <div style="height: 4px; background: linear-gradient(to right, #3895e7, #307aa8); margin-bottom: 20px;"></div>
      </div>
      
      <div style="margin-bottom: 30px;">
        <h2 style="color: #111827; margin-bottom: 10px;">Hello ${customerName},</h2>
        <p style="color: #4b5563; line-height: 1.5; font-size: 16px;">
          ${points > 0 
            ? `Congratulations! You've earned <strong>${points} loyalty points</strong>` 
            : `You've redeemed <strong>${Math.abs(points)} loyalty points</strong>`}
          ${reason ? `for ${reason}` : ''}.
        </p>
      </div>
      
      <div style="background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
        <h3 style="color: #111827; margin-bottom: 15px;">Your Loyalty Account Summary</h3>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="color: #4b5563;">Current Points Balance:</span>
          <strong style="color: #111827;">${newTotal} points</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="color: #4b5563;">Points Value:</span>
          <strong style="color: #111827;">${(newTotal * 0.01).toFixed(2)} EGP</strong>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #4b5563;">Current Tier:</span>
          <strong style="color: ${tierColor};">${loyaltyTier}</strong>
        </div>
      </div>
      
      <div style="margin-bottom: 30px;">
        <h3 style="color: #111827; margin-bottom: 15px;">What You Can Do With Your Points</h3>
        <ul style="color: #4b5563; padding-left: 20px; line-height: 1.5;">
          <li>Redeem points for discounts on future purchases</li>
          <li>Unlock special offers and promotions</li>
          <li>Earn rewards faster as you move up loyalty tiers</li>
        </ul>
      </div>
      
      <div style="text-align: center; margin-top: 30px;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/login" style="background-color: #3895e7; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Account</a>
      </div>
      
      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 14px;">
        <p>Thank you for your continued business!</p>
        <p>VERO ERP - Your Business Management Solution</p>
      </div>
    </div>
  `;
  
  return sendEmail({
    to,
    subject,
    html,
  });
}

// Send birthday gift notification email
export async function sendBirthdayGiftEmail(
  to: string,
  customerName: string,
  points: number,
  newTotal: number,
  loyaltyTier: string,
  isVIP: boolean
): Promise<boolean> {
  const subject = `Happy Birthday, ${customerName}! We've sent you a special gift`;
  
  const tierColor = getTierColor(loyaltyTier);
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #3895e7; margin-bottom: 10px;">Happy Birthday from VERO ERP!</h1>
        <div style="height: 4px; background: linear-gradient(to right, #3895e7, #307aa8); margin-bottom: 20px;"></div>
      </div>
      
      <div style="margin-bottom: 30px; text-align: center;">
        <img src="https://img.icons8.com/color/96/000000/birthday-cake.png" alt="Birthday Cake" style="width: 80px; height: 80px; margin-bottom: 20px;">
        <h2 style="color: #111827; margin-bottom: 10px;">Happy Birthday, ${customerName}!</h2>
        <p style="color: #4b5563; line-height: 1.5; font-size: 16px;">
          To celebrate your special day, we've added <strong>${points} loyalty points</strong> to your account.
          ${isVIP ? '<strong>As a VIP customer, you received double points!</strong>' : ''}
        </p>
      </div>
      
      <div style="background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
        <h3 style="color: #111827; margin-bottom: 15px;">Your Loyalty Account Summary</h3>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="color: #4b5563;">Current Points Balance:</span>
          <strong style="color: #111827;">${newTotal} points</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="color: #4b5563;">Points Value:</span>
          <strong style="color: #111827;">${(newTotal * 0.01).toFixed(2)} EGP</strong>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #4b5563;">Current Tier:</span>
          <strong style="color: ${tierColor};">${loyaltyTier}</strong>
        </div>
      </div>
      
      <div style="text-align: center; margin-top: 30px;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/login" style="background-color: #3895e7; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Redeem Your Points</a>
      </div>
      
      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 14px;">
        <p>We hope you have a wonderful birthday!</p>
        <p>VERO ERP - Your Business Management Solution</p>
      </div>
    </div>
  `;
  
  return sendEmail({
    to,
    subject,
    html,
  });
}

// Helper function to get tier color
function getTierColor(tier: string): string {
  switch (tier) {
    case 'PLATINUM':
      return '#8884d8';
    case 'GOLD':
      return '#ffc658';
    case 'SILVER':
      return '#82ca9d';
    default: // BRONZE
      return '#cd7f32';
  }
}
