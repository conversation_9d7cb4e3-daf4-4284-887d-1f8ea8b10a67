import { db } from "@/lib/db";

/**
 * Generates a unique service number for a maintenance service
 * Format: [Branch Code]-SRV-[Sequential Number]
 * Example: A-SRV-0001
 */
export async function generateServiceNumber(branchId: string): Promise<string> {
  try {
    // Get the branch code
    const branch = await db.branch.findUnique({
      where: { id: branchId },
      select: { code: true },
    });

    if (!branch) {
      throw new Error("Branch not found");
    }

    // Get the highest service number for this branch
    const highestService = await db.maintenanceService.findFirst({
      where: {
        branchId,
        serviceNumber: {
          startsWith: `${branch.code}-SRV-`,
        },
      },
      orderBy: {
        serviceNumber: "desc",
      },
    });

    let sequentialNumber = 1;

    if (highestService) {
      // Extract the sequential number from the service number
      const parts = highestService.serviceNumber.split("-");
      const lastNumber = parseInt(parts[parts.length - 1]);

      if (!isNaN(lastNumber)) {
        sequentialNumber = lastNumber + 1;
      }
    }

    // Format the sequential number with leading zeros
    const formattedNumber = sequentialNumber.toString().padStart(4, "0");

    // Generate the service number
    const serviceNumber = `${branch.code}-SRV-${formattedNumber}`;

    return serviceNumber;
  } catch (error) {
    console.error("Error generating service number:", error);
    throw error;
  }
}

/**
 * Calculates the number of days a maintenance service has been in the system
 */
export function calculateServiceDays(receivedDate: Date): number {
  const today = new Date();
  const received = new Date(receivedDate);

  const diffTime = Math.abs(today.getTime() - received.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
}

/**
 * Checks if a maintenance service is overdue (more than 15 days)
 */
export function isServiceOverdue(receivedDate: Date): boolean {
  const days = calculateServiceDays(receivedDate);
  return days > 15;
}

/**
 * Gets the status label for a maintenance status
 */
export function getStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    RECEIVED: "Received",
    IN_PROGRESS: "In Progress",
    WAITING_FOR_PARTS: "Waiting for Parts",
    COMPLETED: "Completed",
    DELIVERED: "Delivered",
    CANCELLED: "Cancelled",
    REJECTED_BY_CUSTOMER: "Rejected by Customer",
  };

  return statusMap[status] || status;
}

/**
 * Gets the priority label for a maintenance priority
 */
export function getPriorityLabel(priority: string): string {
  const priorityMap: Record<string, string> = {
    LOW: "Low",
    MEDIUM: "Medium",
    HIGH: "High",
    URGENT: "Urgent",
  };

  return priorityMap[priority] || priority;
}

/**
 * Gets the color for a maintenance status
 */
export function getStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    RECEIVED: "bg-blue-100 text-blue-800",
    IN_PROGRESS: "bg-yellow-100 text-yellow-800",
    WAITING_FOR_PARTS: "bg-purple-100 text-purple-800",
    COMPLETED: "bg-green-100 text-green-800",
    DELIVERED: "bg-gray-100 text-gray-800",
    CANCELLED: "bg-red-100 text-red-800",
    REJECTED_BY_CUSTOMER: "bg-orange-100 text-orange-800",
  };

  return colorMap[status] || "bg-gray-100 text-gray-800";
}

/**
 * Gets the color for a maintenance priority
 */
export function getPriorityColor(priority: string): string {
  const colorMap: Record<string, string> = {
    LOW: "bg-green-100 text-green-800",
    MEDIUM: "bg-blue-100 text-blue-800",
    HIGH: "bg-yellow-100 text-yellow-800",
    URGENT: "bg-red-100 text-red-800",
  };

  return colorMap[priority] || "bg-gray-100 text-gray-800";
}
