import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Clearing all sales data...');
  
  // First, delete records from tables with foreign key dependencies
  await prisma.saleItemComponent.deleteMany();
  console.log('Cleared sale item components');
  
  await prisma.saleItem.deleteMany();
  console.log('Cleared sale items');
  
  await prisma.sale.deleteMany();
  console.log('Cleared sales');
  
  console.log('All sales data cleared successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
