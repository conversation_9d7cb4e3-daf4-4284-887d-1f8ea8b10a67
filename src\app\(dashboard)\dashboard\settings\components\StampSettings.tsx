"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Trash2, Upload, Image } from "lucide-react";

interface StampSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

export default function StampSettings({ settings, onUpdate }: StampSettingsProps) {
  const [showStamp, setShowStamp] = useState(true);
  const [stampImage, setStampImage] = useState<string | null>(null);
  const [stampPosition, setStampPosition] = useState("bottom-right");
  const [stampOpacity, setStampOpacity] = useState(0.7);
  const [stampSize, setStampSize] = useState(100);
  const [stampRotation, setStampRotation] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Load settings
  useEffect(() => {
    if (settings?.stampSettings) {
      const stampSettings = settings.stampSettings;
      setShowStamp(stampSettings.showStamp !== false);
      setStampImage(stampSettings.stampImage || null);
      setStampPosition(stampSettings.stampPosition || "bottom-right");
      setStampOpacity(stampSettings.stampOpacity || 0.7);
      setStampSize(stampSettings.stampSize || 100);
      setStampRotation(stampSettings.stampRotation || 0);
    }
  }, [settings]);
  
  // Handle save
  const handleSave = () => {
    const stampSettings = {
      showStamp,
      stampImage,
      stampPosition,
      stampOpacity,
      stampSize,
      stampRotation
    };
    
    onUpdate({ stampSettings });
  };
  
  // Handle stamp image upload
  const handleStampUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (event) => {
      setStampImage(event.target?.result as string);
    };
    reader.readAsDataURL(file);
  };
  
  // Handle stamp image clear
  const handleStampClear = () => {
    setStampImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };
  
  // Get position style for preview
  const getPositionStyle = () => {
    switch (stampPosition) {
      case "top-left":
        return { top: "10px", left: "10px" };
      case "top-right":
        return { top: "10px", right: "10px" };
      case "bottom-left":
        return { bottom: "10px", left: "10px" };
      case "bottom-right":
        return { bottom: "10px", right: "10px" };
      case "center":
        return { top: "50%", left: "50%", transform: `translate(-50%, -50%) rotate(${stampRotation}deg)` };
      default:
        return { bottom: "10px", right: "10px" };
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Stamp Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Label htmlFor="show-stamp" className="font-medium">
              Show Stamp on Documents
            </Label>
            <Switch
              id="show-stamp"
              checked={showStamp}
              onCheckedChange={setShowStamp}
            />
          </div>
          
          <div className="space-y-4">
            <Label className="font-medium">Stamp Image</Label>
            
            {stampImage ? (
              <div className="border rounded-md p-4">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-medium">Current Stamp</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleStampClear}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                </div>
                
                <div className="relative bg-gray-100 border rounded-md p-4 min-h-[200px] flex items-center justify-center">
                  <div
                    className="absolute"
                    style={{
                      ...getPositionStyle(),
                      opacity: stampOpacity,
                    }}
                  >
                    <img
                      src={stampImage}
                      alt="Stamp"
                      style={{
                        width: `${stampSize}px`,
                        height: "auto",
                        transform: stampPosition !== "center" ? `rotate(${stampRotation}deg)` : undefined,
                      }}
                    />
                  </div>
                  <div className="text-gray-400 text-center">
                    <p>Document Preview Area</p>
                    <p className="text-sm">Stamp will appear as shown in this preview</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="border rounded-md p-6 flex flex-col items-center justify-center">
                <div className="bg-gray-100 rounded-full p-4 mb-4">
                  <Image className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-sm text-gray-500 mb-4">
                  Upload your company stamp image (PNG with transparent background recommended)
                </p>
                <div className="relative">
                  <input
                    type="file"
                    ref={fileInputRef}
                    accept="image/*"
                    className="absolute inset-0 opacity-0 cursor-pointer"
                    onChange={handleStampUpload}
                  />
                  <Button variant="outline">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Stamp Image
                  </Button>
                </div>
              </div>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stamp-position">Position</Label>
              <Select value={stampPosition} onValueChange={setStampPosition}>
                <SelectTrigger id="stamp-position">
                  <SelectValue placeholder="Select position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="top-left">Top Left</SelectItem>
                  <SelectItem value="top-right">Top Right</SelectItem>
                  <SelectItem value="bottom-left">Bottom Left</SelectItem>
                  <SelectItem value="bottom-right">Bottom Right</SelectItem>
                  <SelectItem value="center">Center</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="stamp-opacity">Opacity ({Math.round(stampOpacity * 100)}%)</Label>
              <Input
                id="stamp-opacity"
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                value={stampOpacity}
                onChange={(e) => setStampOpacity(parseFloat(e.target.value))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="stamp-size">Size ({stampSize}px)</Label>
              <Input
                id="stamp-size"
                type="range"
                min="50"
                max="200"
                step="10"
                value={stampSize}
                onChange={(e) => setStampSize(parseInt(e.target.value))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="stamp-rotation">Rotation ({stampRotation}°)</Label>
              <Input
                id="stamp-rotation"
                type="range"
                min="-180"
                max="180"
                step="5"
                value={stampRotation}
                onChange={(e) => setStampRotation(parseInt(e.target.value))}
              />
            </div>
          </div>
          
          <div className="mt-6 flex justify-end">
            <Button onClick={handleSave}>Save Settings</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
