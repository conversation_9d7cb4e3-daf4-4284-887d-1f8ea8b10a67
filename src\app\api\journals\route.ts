import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/journals - Get all journals
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const fromParam = url.searchParams.get("from");
    const fromPaymentMethods = fromParam === "payment-methods";
    const fromSettings = fromParam === "settings";
    const paymentMethod = url.searchParams.get("paymentMethod");
    const branchId = url.searchParams.get("branchId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const isActiveParam = url.searchParams.get("isActive");

    // Skip permission check for payment methods setup or settings
    if (!fromPaymentMethods && !fromSettings) {
      // Check if user has permission to view journals
      const hasViewPermission = await hasPermission("view_accounts");
      if (!hasViewPermission) {
        return NextResponse.json(
          { error: "You don't have permission to view journals" },
          { status: 403 }
        );
      }
    }

    // Build filter
    const filter: any = {
      isActive: isActiveParam !== null ? isActiveParam === 'true' : true,
    };

    if (paymentMethod) {
      filter.paymentMethod = paymentMethod;
    }

    if (branchId) {
      filter.branchId = branchId;
    }

    // Get journals from database
    const journals = await db.journal.findMany({
      where: filter,
      include: {
        branch: true,
        entries: {
          where: startDate || endDate ? {
            date: {
              ...(startDate && { gte: new Date(startDate) }),
              ...(endDate && { lte: new Date(endDate) }),
            }
          } : undefined,
          orderBy: {
            date: "desc",
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json(journals);
  } catch (error) {
    console.error("Error fetching journals:", error);
    return NextResponse.json(
      { error: "Failed to fetch journals" },
      { status: 500 }
    );
  }
}

// POST /api/journals - Create a new journal
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add journals
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add journals" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.paymentMethod) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if journal with this payment method already exists
    const existingJournal = await db.journal.findFirst({
      where: {
        paymentMethod: data.paymentMethod,
      },
    });

    if (existingJournal) {
      return NextResponse.json(
        { error: `A journal for ${data.paymentMethod} payment method already exists` },
        { status: 400 }
      );
    }

    // Create the journal
    const journal = await db.journal.create({
      data: {
        name: data.name,
        description: data.description || null,
        paymentMethod: data.paymentMethod,
        branchId: data.branchId || null,
        isActive: true,
      },
    });

    return NextResponse.json(journal);
  } catch (error) {
    console.error("Error creating journal:", error);
    return NextResponse.json(
      { error: "Failed to create journal" },
      { status: 500 }
    );
  }
}
