"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Loader2, ArrowRight, TrendingUp, TrendingDown, DollarSign,
  CreditCard, FileText, BarChart3, PieChart, LineChart,
  ReceiptText, Wallet, Building, Users, ShoppingCart
} from "lucide-react";
import { format, subMonths, subDays } from "date-fns";
import Link from "next/link";

interface Contact {
  id: string;
  name: string;
  phone: string;
  balance: number;
  isCustomer: boolean;
  isSupplier: boolean;
}

export default function FinancePage() {
  const [isLoading, setIsLoading] = useState(true);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [activeTab, setActiveTab] = useState("customers");
  const [period, setPeriod] = useState("month");
  const [asOfDate, setAsOfDate] = useState(new Date());

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/contacts');
      if (response.ok) {
        const data = await response.json();
        setContacts(data);
      }
    } catch (error) {
      console.error("Error fetching contacts:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getCustomers = () => {
    return contacts.filter(contact => contact.isCustomer);
  };

  const getSuppliers = () => {
    return contacts.filter(contact => contact.isSupplier);
  };

  const getTotalCustomerBalance = () => {
    return getCustomers().reduce((total, customer) => total + customer.balance, 0);
  };

  const getTotalSupplierBalance = () => {
    return getSuppliers().reduce((total, supplier) => total + supplier.balance, 0);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Finance</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setPeriod("week")}>
            Week
          </Button>
          <Button variant={period === "month" ? "default" : "outline"} onClick={() => setPeriod("month")}>
            Month
          </Button>
          <Button variant="outline" onClick={() => setPeriod("year")}>
            Year
          </Button>
          <div className="ml-4">
            <DatePicker date={asOfDate} setDate={setAsOfDate} />
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Customer Receivables</p>
                <h3 className="text-2xl font-bold mt-1">{formatCurrency(getTotalCustomerBalance())}</h3>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <Link href="/dashboard/contacts?filter=customers" className="text-sm text-blue-600 flex items-center">
                View Customers <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Supplier Payables</p>
                <h3 className="text-2xl font-bold mt-1">{formatCurrency(getTotalSupplierBalance())}</h3>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Building className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <Link href="/dashboard/contacts?filter=suppliers" className="text-sm text-blue-600 flex items-center">
                View Suppliers <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Payment Methods</p>
                <h3 className="text-2xl font-bold mt-1">4 Methods</h3>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Wallet className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <Link href="/dashboard/finance/payment-methods" className="text-sm text-blue-600 flex items-center">
                View Payment Methods <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Financial Reports</p>
                <h3 className="text-2xl font-bold mt-1">View Reports</h3>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4">
              <Link href="/dashboard/accounting" className="text-sm text-blue-600 flex items-center">
                Go to Accounting <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Customers and Suppliers */}
      <Tabs defaultValue="customers" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="suppliers">Suppliers</TabsTrigger>
        </TabsList>

        <TabsContent value="customers" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Balances</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading customers...</span>
                </div>
              ) : getCustomers().length > 0 ? (
                <div className="rounded-md border overflow-hidden">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Name</th>
                        <th className="px-4 py-3 text-left">Phone</th>
                        <th className="px-4 py-3 text-right">Balance</th>
                        <th className="px-4 py-3 text-center">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getCustomers().map(customer => (
                        <tr key={customer.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{customer.name}</td>
                          <td className="px-4 py-3">{customer.phone}</td>
                          <td className="px-4 py-3 text-right">{formatCurrency(customer.balance)}</td>
                          <td className="px-4 py-3 text-center">
                            <Link href={`/dashboard/contacts/${customer.id}`} className="text-blue-600 hover:text-blue-800">
                              View
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500">No customers found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="suppliers" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Balances</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading suppliers...</span>
                </div>
              ) : getSuppliers().length > 0 ? (
                <div className="rounded-md border overflow-hidden">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Name</th>
                        <th className="px-4 py-3 text-left">Phone</th>
                        <th className="px-4 py-3 text-right">Balance</th>
                        <th className="px-4 py-3 text-center">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getSuppliers().map(supplier => (
                        <tr key={supplier.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{supplier.name}</td>
                          <td className="px-4 py-3">{supplier.phone}</td>
                          <td className="px-4 py-3 text-right">{formatCurrency(supplier.balance)}</td>
                          <td className="px-4 py-3 text-center">
                            <Link href={`/dashboard/contacts/${supplier.id}`} className="text-blue-600 hover:text-blue-800">
                              View
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500">No suppliers found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
