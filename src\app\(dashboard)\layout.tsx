"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { signOut, useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import { useLanguage } from "@/contexts/LanguageContext";
import { NotificationCenter } from "@/components/ui/notification-center";

import { AppProvider } from "@/contexts/AppContext";
import { LanguageToggle } from "@/components/ui/LanguageToggle";

import { CurrentSelections } from "@/components/ui/CurrentSelections";
import { BarChart, ChevronLeft, ChevronRight, Menu, Percent } from "lucide-react";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [openDropdowns, setOpenDropdowns] = useState<string[]>([]);
  const { language, setLanguage, t } = useLanguage();
  const [userName, setUserName] = useState("");

  useEffect(() => {
    // Check for dark mode preference in localStorage or system preference
    const savedMode = localStorage.getItem('darkMode');

    if (savedMode === 'true') {
      setIsDarkMode(true);
      document.body.classList.add('dark-mode');
    } else if (savedMode === null && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      // If no saved preference but system prefers dark mode
      setIsDarkMode(true);
      document.body.classList.add('dark-mode');
      localStorage.setItem('darkMode', 'true');
    }

    // Listen for changes in system color scheme preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      if (localStorage.getItem('darkMode') === null) {
        // Only auto-switch if user hasn't explicitly set a preference
        setIsDarkMode(e.matches);
        if (e.matches) {
          document.body.classList.add('dark-mode');
        } else {
          document.body.classList.remove('dark-mode');
        }
      }
    };

    // Add event listener for system preference changes
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, []);

  // Get user session
  const { data: session } = useSession();

  // Get user name from session
  useEffect(() => {
    if (session?.user?.name) {
      setUserName(session.user.name);
    }
  }, [session]);

  // Initialize open dropdowns based on current path
  useEffect(() => {
    const initialOpenDropdowns = navigation
      .filter(item => item.subItems && pathname.startsWith(item.href))
      .map(item => item.name);

    setOpenDropdowns(initialOpenDropdowns);
  }, [pathname]);

  // Load sidebar collapsed state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
      setIsSidebarCollapsed(true);
    }
  }, []);

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const toggleSidebarCollapse = () => {
    const newState = !isSidebarCollapsed;
    setIsSidebarCollapsed(newState);
    localStorage.setItem('sidebarCollapsed', newState.toString());
  };

  const toggleDropdown = (name: string) => {
    setOpenDropdowns(prev =>
      prev.includes(name)
        ? prev.filter(item => item !== name)
        : [...prev, name]
    );
  };

  const toggleDarkMode = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);

    if (newMode) {
      document.body.classList.add('dark-mode');
      localStorage.setItem('darkMode', 'true');
    } else {
      document.body.classList.remove('dark-mode');
      localStorage.setItem('darkMode', 'false');
    }
  };

  const navigation = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      subItems: [
        {
          name: "Overview",
          href: "/dashboard"
        },
        {
          name: "Unified Dashboard",
          href: "/dashboard/unified-dashboard"
        }
      ]
    },
    {
      name: "Sales",
      href: "/dashboard/sales",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      subItems: [
        {
          name: "Dashboard",
          href: "/dashboard/sales"
        },
        {
          name: "Orders",
          href: "/dashboard/sales/orders"
        },
        {
          name: "Credit Notes",
          href: "/dashboard/sales/credit-note"
        },
        {
          name: "Discounts",
          href: "/dashboard/sales/discounts"
        },
        {
          name: "Seasonal Promotions",
          href: "/dashboard/sales/discounts/seasonal"
        },
        {
          name: "Reports",
          href: "/dashboard/sales/reports"
        },
        {
          name: "New Sale",
          href: "/dashboard/sales/new"
        }
      ]
    },
    {
      name: "Purchases",
      href: "/dashboard/purchases",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      ),
      subItems: [
        {
          name: "All Purchases",
          href: "/dashboard/purchases"
        },
        {
          name: "Due Invoices",
          href: "/dashboard/purchases/due-invoices"
        },
        {
          name: "New Purchase",
          href: "/dashboard/purchases/new"
        }
      ]
    },
    {
      name: "Inventory",
      href: "/dashboard/inventory",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
        </svg>
      ),
      subItems: [
        {
          name: "All Inventory",
          href: "/dashboard/inventory"
        },
        {
          name: "Low Stock Items",
          href: "/dashboard/inventory/low-stock"
        },
        {
          name: "Transfer Items",
          href: "/dashboard/inventory/transfer"
        }
      ]
    },
    {
      name: "Products",
      href: "/dashboard/products",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      )
    },
    {
      name: "Contacts",
      href: "/dashboard/contacts",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      subItems: [
        {
          name: "All Contacts",
          href: "/dashboard/contacts"
        }
      ]
    },
    {
      name: "Accounting",
      href: "/dashboard/accounting",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      subItems: [
        {
          name: "Simple Dashboard",
          href: "/dashboard/accounting/dashboard"
        },
        {
          name: "Advanced Dashboard",
          href: "/dashboard/accounting"
        },
        {
          name: "Payment Vouchers",
          href: "/dashboard/accounting/payment-vouchers"
        },
        {
          name: "Receipt Vouchers",
          href: "/dashboard/accounting/receipt-vouchers"
        },
        {
          name: "Payment Journals",
          href: "/dashboard/accounting/payment-journals"
        },
        {
          name: "Payment Methods",
          href: "/dashboard/accounting/payment-methods"
        },
        {
          name: "Journals",
          href: "/dashboard/accounting/journals"
        },
        {
          name: "General Ledger",
          href: "/dashboard/accounting/general-ledger"
        },
        {
          name: "Trial Balance",
          href: "/dashboard/accounting/trial-balance"
        },
        {
          name: "Profit & Loss",
          href: "/dashboard/accounting/profit-loss"
        },
        {
          name: "Balance Sheet",
          href: "/dashboard/accounting/balance-sheet"
        },
        {
          name: "Settings",
          href: "/dashboard/settings/accounting"
        },
        {
          name: "Integration",
          href: "/dashboard/settings/accounting/integration"
        }
      ]
    },
    {
      name: "Maintenance",
      href: "/dashboard/maintenance",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      )
    },
    {
      name: "Knowledge Base",
      href: "/dashboard/knowledge",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      )
    },
    {
      name: "System Tester",
      href: "/dashboard/system-tester",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
        </svg>
      )
    },
    {
      name: "Settings",
      href: "/dashboard/settings",
      icon: (
        <svg className="sidebar-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      subItems: [
        {
          name: "General Settings",
          href: "/dashboard/settings"
        },
        {
          name: "Payment Methods",
          href: "/dashboard/settings?tab=payment-methods"
        },
        {
          name: "Reorder Payment Methods",
          href: "/dashboard/settings/payment-methods/reorder"
        }
      ]
    },
  ];

  return (
    <AppProvider>
      <div className="min-h-screen bg-gray-100">
      {/* Mobile sidebar */}
      <div
        className={`fixed inset-0 z-40 flex md:hidden ${
          isMobileSidebarOpen ? "visible" : "invisible"
        }`}
        role="dialog"
        aria-modal="true"
      >
        <div
          className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ${
            isMobileSidebarOpen ? "opacity-100 ease-out duration-300" : "opacity-0 ease-in duration-200"
          }`}
          aria-hidden="true"
          onClick={toggleMobileSidebar}
        ></div>

        <div
          className={`sidebar relative flex-1 flex flex-col max-w-xs w-full pt-5 pb-4 transition ${
            isMobileSidebarOpen
              ? "transform translate-x-0 ease-out duration-300"
              : "transform -translate-x-full ease-in duration-200"
          }`}
        >
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              type="button"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={toggleMobileSidebar}
            >
              <span className="sr-only">Close sidebar</span>
              <svg
                className="h-6 w-6 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="flex-shrink-0 flex items-center px-2 py-3 overflow-hidden">
            <div className="transform scale-95 origin-left">
              {/* Logo container */}
              <div className="flex items-center">
                {/* VERO Logo */}
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-green-500 flex items-center justify-center text-white text-sm font-bold shadow-md">
                  VERO
                </div>

                {/* ERP Text */}
                <span className="ml-2 text-white font-bold text-lg">
                  VERO ERP
                </span>
              </div>
            </div>
          </div>
          <div className="mt-5 flex-1 h-0 overflow-y-auto">
            <nav className="px-2 space-y-1">
              {navigation.map((item) => (
                <div key={item.name}>
                  {item.subItems ? (
                    <div className="flex items-center">
                      <Link
                        href={item.href}
                        className={`sidebar-item ripple flex-grow ${
                          pathname === item.href ? "active" : ""
                        }`}
                      >
                        {item.icon}
                        <span className="ml-3">{item.name}</span>
                      </Link>
                      <button
                        className="p-2 ml-2 rounded-md hover:bg-gray-200"
                        onClick={() => toggleDropdown(item.name)}
                      >
                        <svg
                          className={`h-5 w-5 transform transition-transform duration-200 ${
                            openDropdowns.includes(item.name) ? "rotate-180" : ""
                          }`}
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={`sidebar-item ripple ${
                        pathname === item.href ? "active" : ""
                      }`}
                    >
                      {item.icon}
                      <span className="ml-3">{item.name}</span>
                    </Link>
                  )}

                  {item.subItems && openDropdowns.includes(item.name) && (
                    <div className="mt-1 ml-4 space-y-1">
                      {item.subItems.map((subItem) => (
                        <Link
                          key={subItem.name}
                          href={subItem.href}
                          onClick={() => toggleDropdown(item.name)}
                          className={`sidebar-item ripple ml-4 ${
                            pathname === subItem.href
                              ? "active"
                              : ""
                          }`}
                        >
                          {subItem.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>
          </div>
          <div className="flex-shrink-0 flex border-t border-white border-opacity-20 p-4">
            <div className="w-full text-center text-white text-xs opacity-70">
              VERO ERP v1.0
            </div>
          </div>
        </div>
        <div className="flex-shrink-0 w-14" aria-hidden="true">
          {/* Dummy element to force sidebar to shrink to fit close icon */}
        </div>
      </div>

      {/* Collapsible sidebar for desktop */}
      <div className={`hidden md:flex md:flex-col md:fixed md:inset-y-0 transition-all duration-300 ease-in-out ${
        isSidebarCollapsed ? 'md:w-20' : 'md:w-64'
      }`}>
        <div className="sidebar flex flex-col flex-grow pt-5 overflow-y-auto relative">
          {/* Toggle collapse button */}
          <button
            onClick={toggleSidebarCollapse}
            className="absolute top-5 right-3 p-1 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all duration-200"
            title={isSidebarCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
          >
            {isSidebarCollapsed ? (
              <ChevronRight className="h-4 w-4 text-white" />
            ) : (
              <ChevronLeft className="h-4 w-4 text-white" />
            )}
          </button>

          <div className={`flex items-center flex-shrink-0 px-2 py-3 overflow-hidden ${
            isSidebarCollapsed ? 'justify-center' : ''
          }`}>
            <div className={`transform origin-left transition-all duration-300 ${
              isSidebarCollapsed ? 'scale-90' : 'scale-95'
            }`}>
              {/* Logo container */}
              <div className="flex items-center">
                {/* VERO Logo */}
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-green-500 flex items-center justify-center text-white text-sm font-bold shadow-md">
                  VERO
                </div>

                {/* ERP Text - hidden when sidebar is collapsed */}
                <span className={`ml-2 text-white font-bold text-lg transition-opacity duration-300 ${
                  isSidebarCollapsed ? 'opacity-0 hidden' : 'opacity-100'
                }`}>
                  VERO ERP
                </span>
              </div>
            </div>
          </div>

          <div className="mt-5 flex-1 flex flex-col">
            <nav className="flex-1 px-2 pb-4 space-y-1">
              {navigation.map((item) => (
                <div key={item.name}>
                  {item.subItems && !isSidebarCollapsed ? (
                    <div className="flex items-center">
                      <Link
                        href={item.href}
                        className={`sidebar-item ripple flex-grow ${
                          pathname === item.href ? "active" : ""
                        }`}
                        title={item.name}
                      >
                        {item.icon}
                        <span className={`ml-3 transition-opacity duration-300 ${
                          isSidebarCollapsed ? 'opacity-0 hidden' : 'opacity-100'
                        }`}>{item.name}</span>
                      </Link>
                      <button
                        className="p-2 ml-2 rounded-md hover:bg-gray-200"
                        onClick={() => toggleDropdown(item.name)}
                      >
                        <svg
                          className={`h-5 w-5 transform transition-transform duration-200 ${
                            openDropdowns.includes(item.name) ? "rotate-180" : ""
                          }`}
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={`sidebar-item ripple ${
                        pathname === item.href ? "active" : ""
                      } ${isSidebarCollapsed ? 'justify-center' : ''}`}
                      title={item.name}
                    >
                      {item.icon}
                      <span className={`ml-3 transition-opacity duration-300 ${
                        isSidebarCollapsed ? 'opacity-0 hidden' : 'opacity-100'
                      }`}>{item.name}</span>
                    </Link>
                  )}

                  {item.subItems && openDropdowns.includes(item.name) && !isSidebarCollapsed && (
                    <div className="mt-1 ml-4 space-y-1">
                      {item.subItems.map((subItem) => (
                        <Link
                          key={subItem.name}
                          href={subItem.href}
                          onClick={() => toggleDropdown(item.name)}
                          className={`sidebar-item ripple ml-4 ${
                            pathname === subItem.href
                              ? "active"
                              : ""
                          }`}
                          title={subItem.name}
                        >
                          {subItem.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>
          </div>

          <div className="flex-shrink-0 flex border-t border-white border-opacity-20 p-4">
            <div className={`w-full text-center text-white text-xs opacity-70 ${
              isSidebarCollapsed ? 'opacity-0' : 'opacity-70'
            }`}>
              VERO ERP v1.0
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className={`flex flex-col flex-1 transition-all duration-300 ease-in-out ${
        isSidebarCollapsed ? 'md:pl-20' : 'md:pl-64'
      }`}>
        <div className="sticky top-0 z-10 flex-shrink-0 flex h-16 bg-white shadow-md">
          <button
            type="button"
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden"
            onClick={toggleMobileSidebar}
          >
            <span className="sr-only">Open sidebar</span>
            <Menu className="h-6 w-6" />
          </button>
          <div className="flex-1 px-4 flex justify-between">
            <div className="flex-1 flex items-center">
              {/* Desktop sidebar toggle button */}
              <button
                type="button"
                className="mr-4 p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-color hidden md:block"
                onClick={toggleSidebarCollapse}
              >
                {isSidebarCollapsed ? (
                  <ChevronRight className="h-5 w-5" />
                ) : (
                  <ChevronLeft className="h-5 w-5" />
                )}
              </button>
              <h2 className="text-2xl font-semibold text-gray-900 my-auto">
                {navigation.find((item) => item.href === pathname)?.name || "Dashboard"}
              </h2>
            </div>
            <div className="ml-4 flex items-center md:ml-6 space-x-4">
              {/* Current selections (Branch, Warehouse, POS) */}
              <CurrentSelections />

              {/* Welcome message with user name */}
              <div className="hidden md:flex items-center">
                {userName && (
                  <div className="text-gray-700 font-medium px-4 py-2 rounded-md">
                    <span className="text-primary-color">مرحباً،</span> <span className="font-semibold">{userName}</span>
                  </div>
                )}
              </div>

              <div className="flex-grow"></div>

              {/* Language Toggle */}
              <LanguageToggle />

              <NotificationCenter />

              {/* Logout button in header */}
              <button
                onClick={() => signOut({ callbackUrl: "/login" })}
                className="flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-primary-color to-primary-dark hover:from-primary-dark hover:to-primary-hover rounded-md transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-1"
                title="Sign Out"
              >
                <svg
                  className="h-5 w-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                <span>تسجيل الخروج</span>
              </button>
            </div>
          </div>
        </div>

        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>


      </div>
    </div>
    </AppProvider>
  );
}
