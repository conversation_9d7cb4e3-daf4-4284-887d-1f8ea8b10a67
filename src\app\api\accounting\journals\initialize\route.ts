import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// POST /api/accounting/journals/initialize - Initialize basic journals
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // For now, allow any authenticated user to initialize journals
    // Later, we can add proper permission checks
    // const hasManagePermission = await hasPermission("manage_accounts");
    // if (!hasManagePermission) {
    //   return NextResponse.json(
    //     { error: "You don't have permission to initialize journals" },
    //     { status: 403 }
    //   );
    // }

    // Define the required journals
    const requiredJournals = [
      { code: "CASH-001", name: "Cash Journal", type: "CASH" },
      { code: "VODA-001", name: "Vodafone Cash Journal", type: "VODAFONE_CASH" },
      { code: "BANK-001", name: "Bank Journal", type: "BANK_TRANSFER" },
      { code: "VISA-001", name: "Credit Card Journal", type: "VISA" },
      { code: "CUST-001", name: "Customer Account Journal", type: "CUSTOMER_ACCOUNT" },
      { code: "GEN-001", name: "General Journal", type: "GENERAL" },
    ];

    const journals = [];

    // Check if each journal exists, create if it doesn't
    for (const journal of requiredJournals) {
      let existingJournal = await db.journal.findFirst({
        where: {
          OR: [
            { code: journal.code },
            { name: journal.name, type: journal.type },
          ],
        },
      });

      if (!existingJournal) {
        existingJournal = await db.journal.create({
          data: {
            code: journal.code,
            name: journal.name,
            type: journal.type,
            isActive: true,
          },
        });
      }

      journals.push(existingJournal);
    }

    return NextResponse.json({
      success: true,
      message: "Journals initialized successfully",
      data: journals,
    });
  } catch (error: any) {
    console.error("Error initializing journals:", error);
    return NextResponse.json(
      { error: error.message || "Failed to initialize journals" },
      { status: 500 }
    );
  }
}
