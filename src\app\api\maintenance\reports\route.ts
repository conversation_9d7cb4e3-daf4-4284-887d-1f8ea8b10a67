import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import { format, differenceInDays } from "date-fns";

// GET /api/maintenance/reports - Get maintenance reports and statistics
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view maintenance reports
    const canViewReports = await hasPermission("view_maintenance") || session.user.role === "ADMIN";

    if (!canViewReports) {
      return NextResponse.json(
        { error: "You don't have permission to view maintenance reports" },
        { status: 403 }
      );
    }

    // Get query parameters for date range
    const url = new URL(req.url);
    const startDateStr = url.searchParams.get("startDate");
    const endDateStr = url.searchParams.get("endDate");

    // Parse dates or use defaults
    const startDate = startDateStr ? new Date(startDateStr) : new Date(2000, 0, 1);
    const endDate = endDateStr ? new Date(endDateStr) : new Date();

    // Ensure end date is at the end of the day
    endDate.setHours(23, 59, 59, 999);

    // Get total count of maintenance services in date range
    const totalCount = await db.maintenanceService.count({
      where: {
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // Get count by status
    const completedCount = await db.maintenanceService.count({
      where: {
        status: "COMPLETED",
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const inProgressCount = await db.maintenanceService.count({
      where: {
        status: "IN_PROGRESS",
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const waitingForPartsCount = await db.maintenanceService.count({
      where: {
        status: "WAITING_FOR_PARTS",
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const cancelledCount = await db.maintenanceService.count({
      where: {
        status: "CANCELLED",
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // Get overdue services (more than 15 days and not completed/delivered/cancelled)
    const fifteenDaysAgo = new Date();
    fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);
    
    const overdueCount = await db.maintenanceService.count({
      where: {
        receivedDate: {
          lte: fifteenDaysAgo,
          gte: startDate,
          lte: endDate,
        },
        status: {
          notIn: ["DELIVERED", "CANCELLED", "COMPLETED"],
        },
      },
    });

    // Calculate average completion time
    const completedServices = await db.maintenanceService.findMany({
      where: {
        status: "COMPLETED",
        completionDate: { not: null },
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        receivedDate: true,
        completionDate: true,
      },
    });

    let totalDays = 0;
    completedServices.forEach(service => {
      if (service.completionDate) {
        const days = differenceInDays(new Date(service.completionDate), new Date(service.receivedDate));
        totalDays += days;
      }
    });

    const averageCompletionDays = completedServices.length > 0 
      ? totalDays / completedServices.length 
      : 0;

    // Calculate total revenue
    const maintenanceServices = await db.maintenanceService.findMany({
      where: {
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        finalCost: true,
      },
    });

    const totalRevenue = maintenanceServices.reduce((sum, service) => {
      return sum + (service.finalCost || 0);
    }, 0);

    // Get status breakdown
    const statusBreakdown = await db.maintenanceService.groupBy({
      by: ["status"],
      where: {
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: {
        id: true,
      },
    });

    // Format status breakdown
    const formattedStatusBreakdown = statusBreakdown.map(item => ({
      status: item.status,
      count: item._count.id,
    }));

    // Get monthly statistics
    const monthlyServices = await db.maintenanceService.findMany({
      where: {
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        receivedDate: true,
        finalCost: true,
      },
    });

    // Group by month
    const monthlyStats: Record<string, { count: number; revenue: number }> = {};
    
    monthlyServices.forEach(service => {
      const month = format(new Date(service.receivedDate), "MMM yyyy");
      
      if (!monthlyStats[month]) {
        monthlyStats[month] = { count: 0, revenue: 0 };
      }
      
      monthlyStats[month].count += 1;
      monthlyStats[month].revenue += service.finalCost || 0;
    });

    // Convert to array and sort by month
    const formattedMonthlyStats = Object.entries(monthlyStats).map(([month, stats]) => ({
      month,
      count: stats.count,
      revenue: stats.revenue,
    })).sort((a, b) => {
      const dateA = new Date(a.month);
      const dateB = new Date(b.month);
      return dateA.getTime() - dateB.getTime();
    });

    // Get top device types
    const deviceTypes = await db.maintenanceService.groupBy({
      by: ["deviceType"],
      where: {
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: {
        id: true,
      },
    });

    // Format and sort device types
    const topDeviceTypes = deviceTypes
      .map(item => ({
        deviceType: item.deviceType,
        count: item._count.id,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Get top brands
    const brands = await db.maintenanceService.groupBy({
      by: ["brand"],
      where: {
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: {
        id: true,
      },
    });

    // Format and sort brands
    const topBrands = brands
      .map(item => ({
        brand: item.brand,
        count: item._count.id,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Return all statistics
    return NextResponse.json({
      totalCount,
      completedCount,
      inProgressCount,
      waitingForPartsCount,
      cancelledCount,
      overdueCount,
      averageCompletionDays,
      totalRevenue,
      statusBreakdown: formattedStatusBreakdown,
      monthlyStats: formattedMonthlyStats,
      topDeviceTypes,
      topBrands,
    });
  } catch (error) {
    console.error("Error generating maintenance reports:", error);
    return NextResponse.json(
      { error: "Failed to generate maintenance reports" },
      { status: 500 }
    );
  }
}
