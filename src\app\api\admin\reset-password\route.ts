import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import bcrypt from "bcrypt";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// POST /api/admin/reset-password - Reset admin password
export async function POST(req: NextRequest) {
  try {
    console.log("API: Resetting admin password...");

    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in." },
        { status: 401 }
      );
    }

    // Only allow admins to reset passwords
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden. Only administrators can reset passwords." },
        { status: 403 }
      );
    }

    // Get the request body
    const data = await req.json();
    const { email, newPassword } = data;

    if (!email || !newPassword) {
      return NextResponse.json(
        { error: "Email and new password are required" },
        { status: 400 }
      );
    }

    console.log(`API: Resetting password for ${email}`);

    // Check if the user exists
    const user = await db.user.findUnique({
      where: {
        email,
      },
    });

    if (!user) {
      console.log(`API: User not found: ${email}`);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update the user's password
    await db.user.update({
      where: {
        email,
      },
      data: {
        password: hashedPassword,
      },
    });

    console.log(`API: Password reset successful for ${email}`);

    return NextResponse.json({
      success: true,
      message: "Password reset successful",
    });
  } catch (error) {
    console.error("Error resetting admin password:", error);
    return NextResponse.json(
      {
        error: "Failed to reset admin password",
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
