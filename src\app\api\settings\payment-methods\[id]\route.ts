import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/settings/payment-methods/:id - Get a specific payment method
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view settings
    const hasViewPermission = await hasPermission("view_settings");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view payment methods" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get the payment method
    const paymentMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id,
      },
      include: {
        account: {
          select: {
            id: true,
            name: true,
            accountNumber: true,
            balance: true,
          },
        },
        journal: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    if (!paymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(paymentMethod);
  } catch (error) {
    console.error("Error fetching payment method:", error);
    return NextResponse.json(
      { error: "Failed to fetch payment method" },
      { status: 500 }
    );
  }
}

// PATCH /api/settings/payment-methods/:id - Update a payment method
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if the request is coming from the settings page
    const url = new URL(req.url);
    const fromSettings = url.searchParams.get('from') === 'settings';

    // Skip permission check if the request is from the settings page
    if (!fromSettings) {
      // Check if user has permission to manage settings
      const hasManagePermission = await hasPermission("manage_settings");
      if (!hasManagePermission && session.user.role !== "ADMIN") {
        return NextResponse.json(
          { error: "You don't have permission to manage payment methods" },
          { status: 403 }
        );
      }
    }

    const { id } = params;
    const data = await req.json();

    // Check if payment method exists
    const existingMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id,
      },
    });

    if (!existingMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    // Update the payment method
    const paymentMethod = await db.paymentMethodSettings.update({
      where: {
        id,
      },
      data: {
        name: data.name !== undefined ? data.name : existingMethod.name,
        isActive: data.isActive !== undefined ? data.isActive : existingMethod.isActive,
        accountId: data.accountId !== undefined ? data.accountId : existingMethod.accountId,
        journalId: data.journalId !== undefined ? data.journalId : existingMethod.journalId,
        iconName: data.iconName !== undefined ? data.iconName : existingMethod.iconName,
        color: data.color !== undefined ? data.color : existingMethod.color,
        branchIds: data.branchIds !== undefined ? data.branchIds : existingMethod.branchIds,
        sequence: data.sequence !== undefined ? data.sequence : existingMethod.sequence,
      },
    });

    return NextResponse.json(paymentMethod);
  } catch (error) {
    console.error("Error updating payment method:", error);
    return NextResponse.json(
      { error: "Failed to update payment method" },
      { status: 500 }
    );
  }
}

// DELETE /api/settings/payment-methods/:id - Delete a payment method
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage settings
    const hasManagePermission = await hasPermission("manage_settings");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to manage payment methods" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Check if payment method exists
    const existingMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id,
      },
    });

    if (!existingMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    // Delete the payment method
    await db.paymentMethodSettings.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting payment method:", error);
    return NextResponse.json(
      { error: "Failed to delete payment method" },
      { status: 500 }
    );
  }
}
