"use client";

import React, { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { GripVertical, Save, RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';
import PaymentMethodIcon from '@/components/payment/PaymentMethodIcon';

interface PaymentMethod {
  id: string;
  code: string;
  name: string;
  isActive: boolean;
  sequence: number;
  iconName: string | null;
  iconUrl: string | null;
  color: string | null;
}

// Sortable item component
function SortableItem({ method }: { method: PaymentMethod }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition
  } = useSortable({ id: method.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  };

  return (
    <li
      ref={setNodeRef}
      style={style}
      className={`flex items-center p-3 border rounded-md ${!method.isActive ? 'opacity-50' : ''}`}
    >
      <div {...attributes} {...listeners} className="mr-3 cursor-grab">
        <GripVertical className="h-5 w-5 text-gray-400" />
      </div>
      <div className="flex items-center flex-1">
        <div className="mr-3">
          <PaymentMethodIcon
            methodCode={method.code}
            iconName={method.iconName}
            iconUrl={method.iconUrl}
            color={method.color || undefined}
          />
        </div>
        <div>
          <div className="font-medium">{method.name}</div>
          <div className="text-sm text-gray-500">{method.code}</div>
        </div>
      </div>
      <div className="text-sm text-gray-500">
        {method.isActive ? (
          <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
        ) : (
          <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Inactive</span>
        )}
      </div>
    </li>
  );
}

export default function PaymentMethodsReorder() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('Fetching payment methods...');
      const response = await fetch('/api/settings/payment-methods?from=settings', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
        credentials: 'include',
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Failed to fetch payment methods';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('Fetched payment methods:', data);

      // Sort by sequence
      const sortedMethods = data.sort((a: PaymentMethod, b: PaymentMethod) => a.sequence - b.sequence);
      setPaymentMethods(sortedMethods);
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Save payment methods order
  const saveOrder = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccess(null);

      // Update each payment method with new sequence
      const updatePromises = paymentMethods.map((method, index) =>
        fetch(`/api/settings/payment-methods/${method.id}?from=settings`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sequence: index + 1,
          }),
        })
      );

      const results = await Promise.allSettled(updatePromises);

      // Check for any errors
      const rejected = results.filter(result => result.status === 'rejected');
      if (rejected.length > 0) {
        throw new Error(`Failed to update ${rejected.length} payment methods`);
      }

      setSuccess('Payment methods order saved successfully');

      // Refresh the list
      await fetchPaymentMethods();
    } catch (error) {
      console.error('Error saving payment methods order:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setPaymentMethods((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  // Load payment methods on mount
  React.useEffect(() => {
    fetchPaymentMethods();
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Reorder Payment Methods</CardTitle>
        <CardDescription>
          Drag and drop to change the order of payment methods
        </CardDescription>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Loading payment methods...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : success ? (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle className="text-green-700">Success</AlertTitle>
            <AlertDescription className="text-green-600">{success}</AlertDescription>
          </Alert>
        ) : null}

        {paymentMethods.length === 0 && !isLoading ? (
          <div className="text-center py-4 text-gray-500">
            No payment methods found.
          </div>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={paymentMethods.map(method => method.id)}
              strategy={verticalListSortingStrategy}
            >
              <ul className="space-y-2">
                {paymentMethods.map((method) => (
                  <SortableItem key={method.id} method={method} />
                ))}
              </ul>
            </SortableContext>
          </DndContext>
        )}
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={fetchPaymentMethods}
          disabled={isLoading || isSaving}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>

        <Button
          onClick={saveOrder}
          disabled={isLoading || isSaving || paymentMethods.length === 0}
        >
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save Order'}
        </Button>
      </CardFooter>
    </Card>
  );
}
