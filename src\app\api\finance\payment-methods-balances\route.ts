import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// GET /api/finance/payment-methods-balances - Get payment methods balances
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const period = url.searchParams.get("period") || "all"; // all, today, week, month, year
    const branchId = url.searchParams.get("branchId") || "all";

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date | null = null;

    switch (period) {
      case "today":
        startDate = new Date();
        startDate.setHours(0, 0, 0, 0);
        break;
      case "week":
        startDate = new Date();
        startDate.setDate(now.getDate() - 7);
        break;
      case "month":
        startDate = new Date();
        startDate.setMonth(now.getMonth() - 1);
        break;
      case "year":
        startDate = new Date();
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case "all":
      default:
        startDate = null; // No date filter
    }

    // Build branch filter
    const branchFilter = branchId !== "all" ? { branchId } : {};

    // Build date filter
    const dateFilter = startDate ? {
      date: {
        gte: startDate,
      },
    } : {};

    // Get cash payments
    const cashPayments = await db.sale.aggregate({
      where: {
        ...dateFilter,
        paymentStatus: {
          in: ["PAID", "PARTIALLY_PAID"]
        },
        paymentMethod: "CASH",
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
    });

    // Get Vodafone Cash payments
    const vodafoneCashPayments = await db.sale.aggregate({
      where: {
        ...dateFilter,
        paymentStatus: {
          in: ["PAID", "PARTIALLY_PAID"]
        },
        paymentMethod: "VODAFONE_CASH",
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
    });

    // Get bank transfer payments
    const bankTransferPayments = await db.sale.aggregate({
      where: {
        ...dateFilter,
        paymentStatus: {
          in: ["PAID", "PARTIALLY_PAID"]
        },
        paymentMethod: "BANK_TRANSFER",
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
    });

    // Get credit card payments
    const creditCardPayments = await db.sale.aggregate({
      where: {
        ...dateFilter,
        paymentStatus: {
          in: ["PAID", "PARTIALLY_PAID"]
        },
        paymentMethod: "CREDIT_CARD",
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
    });

    // Get customer account balances (unpaid amounts)
    const customerAccountPayments = await db.sale.aggregate({
      where: {
        ...dateFilter,
        paymentStatus: "UNPAID",
        paymentMethod: "CUSTOMER_ACCOUNT",
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
    });

    // Calculate total for all payment methods
    const paymentMethodsBalances = {
      total: (cashPayments._sum.totalAmount || 0) +
             (vodafoneCashPayments._sum.totalAmount || 0) +
             (bankTransferPayments._sum.totalAmount || 0) +
             (creditCardPayments._sum.totalAmount || 0),
      cash: cashPayments._sum.totalAmount || 0,
      vodafoneCash: vodafoneCashPayments._sum.totalAmount || 0,
      bankTransfer: bankTransferPayments._sum.totalAmount || 0,
      creditCard: creditCardPayments._sum.totalAmount || 0,
      customerAccount: customerAccountPayments._sum.totalAmount || 0
    };

    return NextResponse.json(paymentMethodsBalances);
  } catch (error) {
    console.error("Error fetching payment methods balances:", error);
    return NextResponse.json(
      { 
        error: "Failed to fetch payment methods balances",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
