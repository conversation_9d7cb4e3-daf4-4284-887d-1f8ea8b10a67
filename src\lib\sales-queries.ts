import prisma from "@/lib/prisma";
import cache from "@/lib/cache";
import { Sale, SaleItem, Contact, Branch, User } from "@prisma/client";

// Type definitions for query results
export type SaleSummary = {
  id: string;
  invoiceNumber: string;
  date: Date;
  status: string;
  paymentStatus: string;
  totalAmount: number;
  contactName: string;
  branchName: string;
  itemCount: number;
};

export type SaleDetail = Sale & {
  contact: Contact;
  branch: Branch;
  user: User;
  items: (SaleItem & {
    product: {
      id: string;
      name: string;
    };
  })[];
};

/**
 * Get paginated sales with essential information
 */
export async function getPaginatedSales(
  page: number = 1,
  pageSize: number = 10,
  filters: {
    status?: string;
    paymentStatus?: string;
    contactId?: string;
    branchId?: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
  } = {}
): Promise<{ sales: SaleSummary[]; total: number }> {
  const cacheKey = `sales_paginated_${page}_${pageSize}_${JSON.stringify(filters)}`;

  return cache.getOrSet(cacheKey, async () => {
    // Calculate pagination
    const skip = (page - 1) * pageSize;

    // Build where clause based on filters
    const where: any = {};

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.paymentStatus) {
      where.paymentStatus = filters.paymentStatus;
    }

    if (filters.contactId) {
      where.contactId = filters.contactId;
    }

    if (filters.branchId) {
      where.branchId = filters.branchId;
    }

    if (filters.startDate && filters.endDate) {
      where.date = {
        gte: filters.startDate,
        lte: filters.endDate
      };
    } else if (filters.startDate) {
      where.date = {
        gte: filters.startDate
      };
    } else if (filters.endDate) {
      where.date = {
        lte: filters.endDate
      };
    }

    if (filters.search) {
      where.OR = [
        { invoiceNumber: { contains: filters.search, mode: 'insensitive' } },
        { contact: { name: { contains: filters.search, mode: 'insensitive' } } },
        { contact: { phone: { contains: filters.search } } }
      ];
    }

    // Get total count for pagination
    const total = await prisma.sale.count({ where });

    // Get sales with optimized query
    const sales = await prisma.sale.findMany({
      where,
      select: {
        id: true,
        invoiceNumber: true,
        date: true,
        status: true,
        paymentStatus: true,
        totalAmount: true,
        contact: {
          select: {
            name: true
          }
        },
        branch: {
          select: {
            name: true
          }
        },
        items: {
          select: {
            id: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      },
      skip,
      take: pageSize
    });

    // Transform the results
    const formattedSales: SaleSummary[] = sales.map(sale => ({
      id: sale.id,
      invoiceNumber: sale.invoiceNumber,
      date: sale.date,
      status: sale.status,
      paymentStatus: sale.paymentStatus,
      totalAmount: sale.totalAmount,
      contactName: sale.contact.name,
      branchName: sale.branch.name,
      itemCount: sale.items.length
    }));

    return { sales: formattedSales, total };
  }, 60 * 1000); // Cache for 1 minute
}

/**
 * Get detailed sale information by ID
 */
export async function getSaleById(id: string): Promise<SaleDetail | null> {
  const cacheKey = `sale_detail_${id}`;

  return cache.getOrSet(cacheKey, async () => {
    console.log(`Fetching detailed sale information for ID: ${id}`);

    // First, get the sale without components to avoid the error
    const sale = await prisma.sale.findUnique({
      where: { id },
      include: {
        contact: true,
        branch: true,
        user: true,
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                costPrice: true,
                isCustomizable: true,
                specifications: true
              }
            }
          }
        }
      }
    });

    // If sale exists, get the components separately
    if (sale) {
      // For each item, get its components
      for (const item of sale.items) {
        try {
          const components = await prisma.saleItemComponent.findMany({
            where: {
              saleItemId: item.id
            }
          });

          // Add components to the item
          (item as any).components = components;

          console.log(`Found ${components.length} components for item ${item.id}`);
        } catch (error) {
          console.error(`Error fetching components for item ${item.id}:`, error);
          (item as any).components = [];
        }
      }
    }

    if (sale) {
      console.log(`Found sale with ID: ${id}, has ${sale.items.length} items`);

      // Log component information for debugging
      for (const item of sale.items) {
        if (item.components && item.components.length > 0) {
          console.log(`Item ${item.id} has ${item.components.length} components`);
        }
      }
    } else {
      console.log(`No sale found with ID: ${id}`);
    }

    return sale;
  }, 5 * 60 * 1000); // Cache for 5 minutes
}

/**
 * Get sales summary statistics
 */
export async function getSalesSummary(
  filters: {
    branchId?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}
): Promise<{
  totalSales: number;
  paidSales: number;
  unpaidSales: number;
  partiallyPaidSales: number;
  averageSaleAmount: number;
}> {
  const cacheKey = `sales_summary_${JSON.stringify(filters)}`;

  return cache.getOrSet(cacheKey, async () => {
    // Build where clause based on filters
    const where: any = {};

    if (filters.branchId) {
      where.branchId = filters.branchId;
    }

    if (filters.startDate && filters.endDate) {
      where.date = {
        gte: filters.startDate,
        lte: filters.endDate
      };
    } else if (filters.startDate) {
      where.date = {
        gte: filters.startDate
      };
    } else if (filters.endDate) {
      where.date = {
        lte: filters.endDate
      };
    }

    // Get total sales count
    const totalSales = await prisma.sale.count({ where });

    // Get paid sales count
    const paidSales = await prisma.sale.count({
      where: {
        ...where,
        paymentStatus: 'PAID'
      }
    });

    // Get unpaid sales count
    const unpaidSales = await prisma.sale.count({
      where: {
        ...where,
        paymentStatus: 'UNPAID'
      }
    });

    // Get partially paid sales count
    const partiallyPaidSales = await prisma.sale.count({
      where: {
        ...where,
        paymentStatus: 'PARTIALLY_PAID'
      }
    });

    // Get average sale amount
    const salesAmounts = await prisma.sale.aggregate({
      where,
      _avg: {
        totalAmount: true
      }
    });

    const averageSaleAmount = salesAmounts._avg.totalAmount || 0;

    return {
      totalSales,
      paidSales,
      unpaidSales,
      partiallyPaidSales,
      averageSaleAmount
    };
  }, 5 * 60 * 1000); // Cache for 5 minutes
}

/**
 * Get top selling products
 */
export async function getTopSellingProducts(
  limit: number = 10,
  filters: {
    branchId?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}
): Promise<Array<{
  productId: string;
  productName: string;
  totalQuantity: number;
  totalAmount: number;
}>> {
  const cacheKey = `top_selling_products_${limit}_${JSON.stringify(filters)}`;

  return cache.getOrSet(cacheKey, async () => {
    // Build where clause based on filters
    const where: any = {};

    if (filters.branchId) {
      where.sale = {
        branchId: filters.branchId
      };
    }

    if (filters.startDate || filters.endDate) {
      where.sale = {
        ...where.sale,
        date: {}
      };

      if (filters.startDate) {
        where.sale.date.gte = filters.startDate;
      }

      if (filters.endDate) {
        where.sale.date.lte = filters.endDate;
      }
    }

    // Use raw SQL for better performance with complex aggregations
    const topProducts = await prisma.$queryRaw`
      SELECT
        si."productId",
        p.name as "productName",
        SUM(si.quantity) as "totalQuantity",
        SUM(si."totalPrice") as "totalAmount"
      FROM "SaleItem" si
      JOIN "Product" p ON si."productId" = p.id
      JOIN "Sale" s ON si."saleId" = s.id
      WHERE s.status = 'COMPLETED'
      ${filters.branchId ? prisma.$raw`AND s."branchId" = ${filters.branchId}` : prisma.$raw``}
      ${filters.startDate ? prisma.$raw`AND s.date >= ${filters.startDate}` : prisma.$raw``}
      ${filters.endDate ? prisma.$raw`AND s.date <= ${filters.endDate}` : prisma.$raw``}
      GROUP BY si."productId", p.name
      ORDER BY "totalQuantity" DESC
      LIMIT ${limit}
    `;

    return topProducts as Array<{
      productId: string;
      productName: string;
      totalQuantity: number;
      totalAmount: number;
    }>;
  }, 15 * 60 * 1000); // Cache for 15 minutes
}

/**
 * Invalidate sales cache when data changes
 */
export function invalidateSalesCache(): void {
  // Clear all sales-related cache entries
  cache.deletePattern(/^sales_/);
  cache.deletePattern(/^sale_detail_/);
  cache.deletePattern(/^top_selling_products_/);
}

/**
 * Get sales by date range for charts
 */
export async function getSalesByDateRange(
  groupBy: 'day' | 'week' | 'month' = 'day',
  filters: {
    branchId?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}
): Promise<Array<{
  date: string;
  totalSales: number;
  totalAmount: number;
}>> {
  const cacheKey = `sales_by_date_${groupBy}_${JSON.stringify(filters)}`;

  return cache.getOrSet(cacheKey, async () => {
    // Build date format based on groupBy
    let dateFormat;
    let dateGroupBy;

    switch (groupBy) {
      case 'week':
        dateFormat = 'YYYY-WW';
        dateGroupBy = 'week';
        break;
      case 'month':
        dateFormat = 'YYYY-MM';
        dateGroupBy = 'month';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
        dateGroupBy = 'day';
    }

    // Use raw SQL for better performance with date grouping
    const salesByDate = await prisma.$queryRaw`
      SELECT
        TO_CHAR(s.date, ${dateFormat}) as date,
        COUNT(*) as "totalSales",
        SUM(s."totalAmount") as "totalAmount"
      FROM "Sale" s
      WHERE s.status = 'COMPLETED'
      ${filters.branchId ? prisma.$raw`AND s."branchId" = ${filters.branchId}` : prisma.$raw``}
      ${filters.startDate ? prisma.$raw`AND s.date >= ${filters.startDate}` : prisma.$raw``}
      ${filters.endDate ? prisma.$raw`AND s.date <= ${filters.endDate}` : prisma.$raw``}
      GROUP BY TO_CHAR(s.date, ${dateFormat})
      ORDER BY date
    `;

    return salesByDate as Array<{
      date: string;
      totalSales: number;
      totalAmount: number;
    }>;
  }, 30 * 60 * 1000); // Cache for 30 minutes
}
