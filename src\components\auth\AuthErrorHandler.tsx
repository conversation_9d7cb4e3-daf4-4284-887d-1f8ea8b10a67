"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export function AuthErrorHandler() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [hasShownError, setHasShownError] = useState(false);

  useEffect(() => {
    // Handle authentication errors
    if (status === "unauthenticated" && !hasShownError) {
      setHasShownError(true);
      toast.error("Session expired or authentication failed. Please log in again.");
      router.push("/login");
    }

    // Handle session errors
    if (session?.error && !hasShownError) {
      setHasShownError(true);
      toast.error(`Authentication error: ${session.error}`);
      router.push("/login");
    }
  }, [session, status, router, hasShownError]);

  // This component doesn't render anything
  return null;
}
