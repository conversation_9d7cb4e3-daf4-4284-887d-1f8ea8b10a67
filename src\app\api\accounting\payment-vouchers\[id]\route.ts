import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/payment-vouchers/:id - Get payment voucher by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounting data
    const hasViewPermission = await hasPermission("view_accounting");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view payment voucher details" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Get payment voucher with associated data
    const voucher = await db.paymentVoucher.findUnique({
      where: {
        id,
      },
      include: {
        paymentMethod: {
          select: {
            id: true,
            code: true,
            name: true,
            account: {
              select: {
                id: true,
                code: true,
                name: true,
                balance: true,
              },
            },
          },
        },
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
            isCustomer: true,
            isSupplier: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    if (!voucher) {
      return NextResponse.json(
        { error: "Payment voucher not found" },
        { status: 404 }
      );
    }

    // Get journal entry if exists
    let journalEntry = null;
    if (voucher.journalEntryId) {
      journalEntry = await db.journalEntry.findUnique({
        where: {
          id: voucher.journalEntryId,
        },
        include: {
          journal: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          debitAccount: {
            select: {
              id: true,
              code: true,
              name: true,
              type: true,
            },
          },
          creditAccount: {
            select: {
              id: true,
              code: true,
              name: true,
              type: true,
            },
          },
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        ...voucher,
        journalEntry,
      },
    });
  } catch (error) {
    console.error("Error fetching payment voucher:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch payment voucher" },
      { status: 500 }
    );
  }
}

// PUT /api/accounting/payment-vouchers/:id - Update payment voucher
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to update payment vouchers" },
        { status: 403 }
      );
    }

    const id = params.id;
    const data = await req.json();

    // Check if payment voucher exists
    const existingVoucher = await db.paymentVoucher.findUnique({
      where: {
        id,
      },
      include: {
        paymentMethod: true,
      },
    });

    if (!existingVoucher) {
      return NextResponse.json(
        { error: "Payment voucher not found" },
        { status: 404 }
      );
    }

    // Only allow updating description and status
    const updatedVoucher = await db.paymentVoucher.update({
      where: {
        id,
      },
      data: {
        description: data.description || existingVoucher.description,
        status: data.status || existingVoucher.status,
      },
      include: {
        paymentMethod: {
          select: {
            id: true,
            code: true,
            name: true,
          },
        },
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedVoucher,
    });
  } catch (error) {
    console.error("Error updating payment voucher:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update payment voucher" },
      { status: 500 }
    );
  }
}

// DELETE /api/accounting/payment-vouchers/:id - Delete payment voucher
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to delete payment vouchers" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Check if payment voucher exists
    const existingVoucher = await db.paymentVoucher.findUnique({
      where: {
        id,
      },
    });

    if (!existingVoucher) {
      return NextResponse.json(
        { error: "Payment voucher not found" },
        { status: 404 }
      );
    }

    // Instead of deleting, update status to CANCELLED
    const updatedVoucher = await db.paymentVoucher.update({
      where: {
        id,
      },
      data: {
        status: "CANCELLED",
      },
    });

    return NextResponse.json({
      success: true,
      message: "Payment voucher cancelled successfully",
    });
  } catch (error) {
    console.error("Error cancelling payment voucher:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to cancel payment voucher" },
      { status: 500 }
    );
  }
}
