import React from "react";
import { Badge } from "@/components/ui/badge";
import { Percent, DollarSign, Tag, ShoppingBag, Users } from "lucide-react";

interface DiscountBadgeProps {
  type: "PERCENTAGE" | "FIXED_AMOUNT";
  scope: "INVOICE" | "ITEM" | "CUSTOMER";
  value: number;
  showValue?: boolean;
  size?: "sm" | "md" | "lg";
}

export default function DiscountBadge({
  type,
  scope,
  value,
  showValue = true,
  size = "md"
}: DiscountBadgeProps) {
  // Determine icon based on scope
  const getScopeIcon = () => {
    switch (scope) {
      case "INVOICE":
        return <ShoppingBag className={size === "sm" ? "h-3 w-3 mr-1" : "h-4 w-4 mr-1"} />;
      case "ITEM":
        return <Tag className={size === "sm" ? "h-3 w-3 mr-1" : "h-4 w-4 mr-1"} />;
      case "CUSTOMER":
        return <Users className={size === "sm" ? "h-3 w-3 mr-1" : "h-4 w-4 mr-1"} />;
      default:
        return <Tag className={size === "sm" ? "h-3 w-3 mr-1" : "h-4 w-4 mr-1"} />;
    }
  };

  // Determine icon based on type
  const getTypeIcon = () => {
    return type === "PERCENTAGE" ? (
      <Percent className={size === "sm" ? "h-3 w-3 mr-1" : "h-4 w-4 mr-1"} />
    ) : (
      <DollarSign className={size === "sm" ? "h-3 w-3 mr-1" : "h-4 w-4 mr-1"} />
    );
  };

  // Format the value based on type
  const formattedValue = type === "PERCENTAGE" ? `${value}%` : `${value.toFixed(2)} ج.م`;

  // Determine badge variant and color based on type and scope
  const getBadgeVariant = () => {
    if (type === "PERCENTAGE") {
      switch (scope) {
        case "INVOICE":
          return "bg-blue-100 text-blue-800 hover:bg-blue-200";
        case "ITEM":
          return "bg-green-100 text-green-800 hover:bg-green-200";
        case "CUSTOMER":
          return "bg-purple-100 text-purple-800 hover:bg-purple-200";
        default:
          return "bg-gray-100 text-gray-800 hover:bg-gray-200";
      }
    } else {
      switch (scope) {
        case "INVOICE":
          return "bg-amber-100 text-amber-800 hover:bg-amber-200";
        case "ITEM":
          return "bg-emerald-100 text-emerald-800 hover:bg-emerald-200";
        case "CUSTOMER":
          return "bg-indigo-100 text-indigo-800 hover:bg-indigo-200";
        default:
          return "bg-gray-100 text-gray-800 hover:bg-gray-200";
      }
    }
  };

  // Get scope text
  const getScopeText = () => {
    switch (scope) {
      case "INVOICE":
        return "Invoice";
      case "ITEM":
        return "Item";
      case "CUSTOMER":
        return "Customer";
      default:
        return scope;
    }
  };

  return (
    <Badge 
      className={`${getBadgeVariant()} font-medium ${
        size === "sm" ? "text-xs py-0.5 px-1.5" : 
        size === "lg" ? "text-sm py-1 px-3" : 
        "text-xs py-0.5 px-2"
      } flex items-center`}
    >
      {type === "PERCENTAGE" ? getTypeIcon() : getScopeIcon()}
      {showValue ? formattedValue : getScopeText()}
    </Badge>
  );
}
