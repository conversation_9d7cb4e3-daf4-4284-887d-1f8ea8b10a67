-- Add more detailed account categorization
ALTER TABLE "Account" ADD COLUMN "subtype" TEXT;
ALTER TABLE "Account" ADD COLUMN "accountNumber" TEXT;
ALTER TABLE "Account" ADD COLUMN "description" TEXT;
ALTER TABLE "Account" ADD COLUMN "tags" TEXT[];
ALTER TABLE "Account" ADD COLUMN "isSystemAccount" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "Account" ADD COLUMN "isArchived" BOOLEAN NOT NULL DEFAULT false;

-- Add more detailed journal entry information
ALTER TABLE "JournalEntry" ADD COLUMN "isRecurring" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "JournalEntry" ADD COLUMN "recurringFrequency" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "recurringEndDate" TIMESTAMP(3);
ALTER TABLE "JournalEntry" ADD COLUMN "isPosted" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "JournalEntry" ADD COLUMN "postedDate" TIMESTAMP(3);
ALTER TABLE "JournalEntry" ADD COLUMN "postedBy" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "isReversed" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "JournalEntry" ADD COLUMN "reversalDate" TIMESTAMP(3);
ALTER TABLE "JournalEntry" ADD COLUMN "reversalReason" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "attachments" TEXT[];
ALTER TABLE "JournalEntry" ADD COLUMN "tags" TEXT[];
ALTER TABLE "JournalEntry" ADD COLUMN "approvalStatus" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "approvedBy" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "approvedDate" TIMESTAMP(3);

-- Add more detailed fiscal period information
ALTER TABLE "FiscalPeriod" ADD COLUMN "periodType" TEXT;
ALTER TABLE "FiscalPeriod" ADD COLUMN "closingNotes" TEXT;
ALTER TABLE "FiscalPeriod" ADD COLUMN "closedBy" TEXT;
ALTER TABLE "FiscalPeriod" ADD COLUMN "closedDate" TIMESTAMP(3);
ALTER TABLE "FiscalPeriod" ADD COLUMN "isAdjustmentPeriod" BOOLEAN NOT NULL DEFAULT false;

-- Create AccountingSettings model
CREATE TABLE "AccountingSettings" (
    "id" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "defaultCurrency" TEXT NOT NULL DEFAULT 'USD',
    "fiscalYearStartMonth" INTEGER NOT NULL DEFAULT 1,
    "fiscalYearStartDay" INTEGER NOT NULL DEFAULT 1,
    "accrualAccounting" BOOLEAN NOT NULL DEFAULT true,
    "automaticJournalEntries" BOOLEAN NOT NULL DEFAULT true,
    "requireJournalApproval" BOOLEAN NOT NULL DEFAULT false,
    "allowPostingToPreviousPeriods" BOOLEAN NOT NULL DEFAULT false,
    "allowPostingToFuturePeriods" BOOLEAN NOT NULL DEFAULT true,
    "defaultIncomeAccount" TEXT,
    "defaultExpenseAccount" TEXT,
    "defaultAssetAccount" TEXT,
    "defaultLiabilityAccount" TEXT,
    "defaultEquityAccount" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AccountingSettings_pkey" PRIMARY KEY ("id")
);

-- Create FinancialReport model
CREATE TABLE "FinancialReport" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "format" TEXT NOT NULL DEFAULT 'PDF',
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "fiscalPeriodId" TEXT,
    "fiscalYearId" TEXT,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "parameters" JSONB,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "fileUrl" TEXT,

    CONSTRAINT "FinancialReport_pkey" PRIMARY KEY ("id")
);

-- Create AccountReconciliation model
CREATE TABLE "AccountReconciliation" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "startBalance" DOUBLE PRECISION NOT NULL,
    "endBalance" DOUBLE PRECISION NOT NULL,
    "expectedEndBalance" DOUBLE PRECISION NOT NULL,
    "difference" DOUBLE PRECISION NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "notes" TEXT,
    "reconciledBy" TEXT,
    "reconciledDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AccountReconciliation_pkey" PRIMARY KEY ("id")
);

-- Create ReconciliationItem model
CREATE TABLE "ReconciliationItem" (
    "id" TEXT NOT NULL,
    "reconciliationId" TEXT NOT NULL,
    "ledgerEntryId" TEXT NOT NULL,
    "isReconciled" BOOLEAN NOT NULL DEFAULT false,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReconciliationItem_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints
ALTER TABLE "FinancialReport" ADD CONSTRAINT "FinancialReport_fiscalPeriodId_fkey" FOREIGN KEY ("fiscalPeriodId") REFERENCES "FiscalPeriod"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "FinancialReport" ADD CONSTRAINT "FinancialReport_fiscalYearId_fkey" FOREIGN KEY ("fiscalYearId") REFERENCES "FiscalYear"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "AccountReconciliation" ADD CONSTRAINT "AccountReconciliation_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "ReconciliationItem" ADD CONSTRAINT "ReconciliationItem_reconciliationId_fkey" FOREIGN KEY ("reconciliationId") REFERENCES "AccountReconciliation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ReconciliationItem" ADD CONSTRAINT "ReconciliationItem_ledgerEntryId_fkey" FOREIGN KEY ("ledgerEntryId") REFERENCES "GeneralLedgerEntry"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX "AccountingSettings_companyId_idx" ON "AccountingSettings"("companyId");
CREATE INDEX "FinancialReport_type_idx" ON "FinancialReport"("type");
CREATE INDEX "FinancialReport_fiscalPeriodId_idx" ON "FinancialReport"("fiscalPeriodId");
CREATE INDEX "FinancialReport_fiscalYearId_idx" ON "FinancialReport"("fiscalYearId");
CREATE INDEX "AccountReconciliation_accountId_idx" ON "AccountReconciliation"("accountId");
CREATE INDEX "AccountReconciliation_status_idx" ON "AccountReconciliation"("status");
CREATE INDEX "ReconciliationItem_reconciliationId_idx" ON "ReconciliationItem"("reconciliationId");
CREATE INDEX "ReconciliationItem_ledgerEntryId_idx" ON "ReconciliationItem"("ledgerEntryId");
