const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateAccountsSchema() {
  try {
    console.log('Starting account schema update...');

    // Check if code column exists
    let codeExists = false;
    try {
      await prisma.$queryRaw`SELECT "code" FROM "Account" LIMIT 1`;
      codeExists = true;
      console.log('Code column already exists');
    } catch (error) {
      console.log('Code column does not exist, will create it');
    }

    if (!codeExists) {
      // Add code column
      console.log('Adding code column to Account table...');
      await prisma.$executeRaw`ALTER TABLE "Account" ADD COLUMN "code" TEXT`;
      
      // Generate temporary codes for existing accounts
      console.log('Generating temporary codes for existing accounts...');
      const accounts = await prisma.account.findMany();
      
      for (const account of accounts) {
        const tempCode = `ACC-${account.id.substring(0, 8)}`;
        await prisma.account.update({
          where: { id: account.id },
          data: { code: tempCode }
        });
      }
      
      // Make code column unique
      console.log('Making code column unique...');
      await prisma.$executeRaw`ALTER TABLE "Account" ADD CONSTRAINT "Account_code_key" UNIQUE ("code")`;
    }

    // Check if isDefault column exists
    let isDefaultExists = false;
    try {
      await prisma.$queryRaw`SELECT "isDefault" FROM "Account" LIMIT 1`;
      isDefaultExists = true;
      console.log('isDefault column already exists');
    } catch (error) {
      console.log('isDefault column does not exist, will create it');
    }

    if (!isDefaultExists) {
      // Add isDefault column
      console.log('Adding isDefault column to Account table...');
      await prisma.$executeRaw`ALTER TABLE "Account" ADD COLUMN "isDefault" BOOLEAN NOT NULL DEFAULT false`;
    }

    // Add indexes if they don't exist
    console.log('Adding indexes...');
    try {
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Account_code_idx" ON "Account"("code")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Account_type_idx" ON "Account"("type")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Account_isActive_idx" ON "Account"("isActive")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Account_branchId_idx" ON "Account"("branchId")`;
    } catch (error) {
      console.error('Error creating indexes:', error);
    }

    console.log('Account schema update completed successfully!');
  } catch (error) {
    console.error('Error updating account schema:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAccountsSchema();
