import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/inventory/[id] - Get a specific inventory item
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to view inventory
    const hasViewPermission = await hasPermission("view_products");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view inventory" },
        { status: 403 }
      );
    }
    
    const id = params.id;
    
    // Get inventory item from database
    const inventoryItem = await db.inventory.findUnique({
      where: {
        id,
      },
      include: {
        product: {
          include: {
            category: true,
          },
        },
        warehouse: true,
      },
    });
    
    if (!inventoryItem) {
      return NextResponse.json(
        { error: "Inventory item not found" },
        { status: 404 }
      );
    }
    
    // Format the data for response
    const formattedItem = {
      id: inventoryItem.id,
      productId: inventoryItem.productId,
      productName: inventoryItem.product.name,
      warehouseId: inventoryItem.warehouseId,
      warehouseName: inventoryItem.warehouse.name,
      quantity: inventoryItem.quantity,
      categoryId: inventoryItem.product.categoryId,
      categoryName: inventoryItem.product.category?.name || "Uncategorized",
    };
    
    return NextResponse.json(formattedItem);
  } catch (error) {
    console.error("Error fetching inventory item:", error);
    return NextResponse.json(
      { error: "Failed to fetch inventory item" },
      { status: 500 }
    );
  }
}

// DELETE /api/inventory/[id] - Delete a specific inventory item
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to delete inventory
    const hasDeletePermission = await hasPermission("delete_products");
    if (!hasDeletePermission) {
      return NextResponse.json(
        { error: "You don't have permission to delete inventory" },
        { status: 403 }
      );
    }
    
    const id = params.id;
    
    // Check if inventory item exists
    const inventoryItem = await db.inventory.findUnique({
      where: {
        id,
      },
    });
    
    if (!inventoryItem) {
      return NextResponse.json(
        { error: "Inventory item not found" },
        { status: 404 }
      );
    }
    
    // Delete inventory item
    await db.inventory.delete({
      where: {
        id,
      },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting inventory item:", error);
    return NextResponse.json(
      { error: "Failed to delete inventory item" },
      { status: 500 }
    );
  }
}
