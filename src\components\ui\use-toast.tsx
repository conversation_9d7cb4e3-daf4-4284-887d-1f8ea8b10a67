"use client";

import { toast as sonnerToast, Toast, ToastT } from "sonner";

type ToastProps = Omit<ToastT, "id"> & {
  title?: string;
  description?: string;
  variant?: "default" | "destructive";
};

const toast = ({ title, description, variant, ...props }: ToastProps) => {
  if (variant === "destructive") {
    return sonnerToast.error(title, {
      description,
      ...props,
    });
  }

  return sonnerToast(title, {
    description,
    ...props,
  });
};

// Add convenience methods
toast.success = (title: string, options?: Omit<ToastProps, "title">) => {
  return sonnerToast.success(title, options);
};

toast.error = (title: string, options?: Omit<ToastProps, "title">) => {
  return sonnerToast.error(title, options);
};

toast.info = (title: string, options?: Omit<ToastProps, "title">) => {
  return sonnerToast.info(title, options);
};

toast.warning = (title: string, options?: Omit<ToastProps, "title">) => {
  return sonnerToast.warning(title, options);
};

toast.dismiss = sonnerToast.dismiss;

export function useToast() {
  return {
    toast,
  };
}
