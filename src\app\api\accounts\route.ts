import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounts - Get all accounts
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const fromParam = url.searchParams.get("from");
    const fromPaymentMethods = fromParam === "payment-methods";
    const fromSettings = fromParam === "settings";
    const type = url.searchParams.get("type");
    const includeParent = url.searchParams.get('include') === 'parent';
    const isActiveParam = url.searchParams.get("isActive");

    // Skip permission check for payment methods setup or settings
    if (!fromPaymentMethods && !fromSettings) {
      // Check if user has permission to view accounts
      const hasViewPermission = await hasPermission("view_accounts");
      if (!hasViewPermission) {
        return NextResponse.json(
          { error: "You don't have permission to view accounts" },
          { status: 403 }
        );
      }
    }

    // Build the query
    const query: any = {
      orderBy: [
        {
          code: "asc",
        },
        {
          type: "asc",
        },
        {
          name: "asc",
        },
      ],
      select: {
        id: true,
        name: true,
        code: true,
        type: true,
        balance: true,
        parentId: true,
        branchId: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        branch: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    };

    // Include parent and children if requested
    if (includeParent) {
      query.select.parent = {
        select: {
          id: true,
          name: true,
          code: true,
          type: true,
        }
      };
      query.select.children = {
        select: {
          id: true,
          name: true,
          code: true,
          type: true,
        }
      };
    }

    // Add filters if provided
    if (type || isActiveParam !== null) {
      query.where = {};

      if (type) {
        query.where.type = type;
      }

      if (isActiveParam !== null) {
        query.where.isActive = isActiveParam === 'true';
      }
    }

    const accounts = await db.account.findMany(query);

    // For now, we'll just return the accounts without date filtering
    // since the transactions relationship might not be set up correctly
    return NextResponse.json(accounts);
  } catch (error: any) {
    console.error("Error fetching accounts:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch accounts",
        details: error.message || "Unknown error",
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// POST /api/accounts - Create a new account
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add accounts
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add accounts" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.type) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Create the account
    // Convert empty branchId string to null
    const branchId = data.branchId === "" ? null : data.branchId;
    const parentId = data.parentId === "" ? null : data.parentId;

    const account = await db.account.create({
      data: {
        name: data.name,
        code: data.code,
        type: data.type,
        balance: data.balance || 0,
        branchId: branchId,
        parentId: parentId,
        isActive: true,
      },
    });

    return NextResponse.json(account);
  } catch (error: any) {
    console.error("Error creating account:", error);
    return NextResponse.json(
      {
        error: "Failed to create account",
        details: error.message || "Unknown error",
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
