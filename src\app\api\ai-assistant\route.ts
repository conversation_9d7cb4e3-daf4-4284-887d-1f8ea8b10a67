import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// GET /api/ai-assistant - Get AI assistant status and info
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get user's AI assistant settings
    const settings = await db.aIAssistantSettings.findUnique({
      where: {
        userId: session.user.id,
      },
    });
    
    // If no settings exist, return default values
    if (!settings) {
      return NextResponse.json({
        isEnabled: true,
        autoSuggest: true,
        voiceEnabled: false,
        notificationsOn: true,
      });
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error fetching AI assistant info:", error);
    return NextResponse.json(
      { error: "Failed to fetch AI assistant info" },
      { status: 500 }
    );
  }
}
