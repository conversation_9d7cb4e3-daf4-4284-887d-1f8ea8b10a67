import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// POST /api/journals/entries/create-from-transaction - Create journal entries from a transaction
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to add journal entries
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add journal entries" },
        { status: 403 }
      );
    }
    
    const data = await req.json();
    
    // Validate required fields
    if (!data.transactionType || !data.referenceId || !data.payments || !data.total) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    const { transactionType, referenceId, payments, total, contactId, contactName } = data;
    
    // Create journal entries for each payment method
    const journalEntries = [];
    
    for (const payment of payments) {
      if (payment.method === 'SUPPLIER_ACCOUNT') {
        // Skip supplier account payments as they don't have a journal
        continue;
      }
      
      // Find the journal for this payment method
      const journal = await db.journal.findFirst({
        where: {
          paymentMethod: payment.method,
          isActive: true,
        },
      });
      
      if (!journal) {
        console.warn(`No journal found for payment method ${payment.method}`);
        continue;
      }
      
      // Get the last entry number for this journal
      const lastEntry = await db.journalEntry.findFirst({
        where: {
          journalId: journal.id,
        },
        orderBy: {
          entryNumber: 'desc',
        },
      });
      
      // Generate entry number
      let entryNumber = "1";
      if (lastEntry) {
        const lastEntryNumber = parseInt(lastEntry.entryNumber);
        entryNumber = (lastEntryNumber + 1).toString();
      }
      
      // Create description based on transaction type
      let description = '';
      let type = 'INCOME';
      
      if (transactionType === 'SALE') {
        description = `Payment received for sale #${referenceId}`;
        type = 'INCOME';
      } else if (transactionType === 'PURCHASE') {
        description = `Payment made for purchase #${referenceId}`;
        type = 'EXPENSE';
      }
      
      if (contactName) {
        description += ` from ${contactName}`;
      }
      
      // Create the journal entry
      const entry = await db.journalEntry.create({
        data: {
          journalId: journal.id,
          entryNumber,
          description,
          amount: payment.amount,
          type,
          contactId: contactId || null,
          reference: referenceId,
          referenceType: transactionType,
          date: new Date(),
        },
      });
      
      journalEntries.push(entry);
    }
    
    return NextResponse.json({
      success: true,
      entries: journalEntries,
    });
  } catch (error) {
    console.error("Error creating journal entries from transaction:", error);
    return NextResponse.json(
      { error: "Failed to create journal entries" },
      { status: 500 }
    );
  }
}
