const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking for existing admin user...');
    
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
    });

    if (existingAdmin) {
      console.log('Admin user already exists. Skipping creation.');
      return;
    }

    console.log('Creating admin user...');
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Create the admin user
    const adminUser = await prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        isActive: true,
      },
    });
    
    console.log('Admin user created successfully:', adminUser.email);
    
    // Create default permissions
    console.log('Creating default permissions...');
    
    const defaultPermissions = [
      { name: 'view_dashboard', description: 'View dashboard' },
      { name: 'view_sales', description: 'View sales' },
      { name: 'add_sales', description: 'Add sales' },
      { name: 'edit_sales', description: 'Edit sales' },
      { name: 'delete_sales', description: 'Delete sales' },
      { name: 'view_purchases', description: 'View purchases' },
      { name: 'add_purchases', description: 'Add purchases' },
      { name: 'edit_purchases', description: 'Edit purchases' },
      { name: 'delete_purchases', description: 'Delete purchases' },
      { name: 'view_products', description: 'View products' },
      { name: 'add_products', description: 'Add products' },
      { name: 'edit_products', description: 'Edit products' },
      { name: 'delete_products', description: 'Delete products' },
      { name: 'view_customers', description: 'View customers' },
      { name: 'add_customers', description: 'Add customers' },
      { name: 'edit_customers', description: 'Edit customers' },
      { name: 'delete_customers', description: 'Delete customers' },
      { name: 'view_suppliers', description: 'View suppliers' },
      { name: 'add_suppliers', description: 'Add suppliers' },
      { name: 'edit_suppliers', description: 'Edit suppliers' },
      { name: 'delete_suppliers', description: 'Delete suppliers' },
      { name: 'view_warehouses', description: 'View warehouses' },
      { name: 'add_warehouses', description: 'Add warehouses' },
      { name: 'edit_warehouses', description: 'Edit warehouses' },
      { name: 'delete_warehouses', description: 'Delete warehouses' },
      { name: 'view_branches', description: 'View branches' },
      { name: 'add_branches', description: 'Add branches' },
      { name: 'edit_branches', description: 'Edit branches' },
      { name: 'delete_branches', description: 'Delete branches' },
      { name: 'view_users', description: 'View users' },
      { name: 'add_users', description: 'Add users' },
      { name: 'edit_users', description: 'Edit users' },
      { name: 'delete_users', description: 'Delete users' },
      { name: 'view_settings', description: 'View settings' },
      { name: 'manage_settings', description: 'Manage settings' },
      { name: 'view_reports', description: 'View reports' },
      { name: 'view_accounting', description: 'View accounting' },
      { name: 'manage_accounting', description: 'Manage accounting' },
    ];
    
    // Create permissions if they don't exist
    for (const permission of defaultPermissions) {
      const existingPermission = await prisma.permission.findFirst({
        where: {
          name: permission.name,
        },
      });
      
      if (!existingPermission) {
        await prisma.permission.create({
          data: {
            id: uuidv4(),
            name: permission.name,
            description: permission.description,
          },
        });
      }
    }
    
    console.log('Default permissions created successfully');
    
    // Assign all permissions to admin user
    console.log('Assigning permissions to admin user...');
    
    const allPermissions = await prisma.permission.findMany();
    
    await prisma.user.update({
      where: {
        id: adminUser.id,
      },
      data: {
        permissions: {
          connect: allPermissions.map(permission => ({ id: permission.id })),
        },
      },
    });
    
    console.log('Permissions assigned to admin user successfully');
    
    console.log('Admin user setup completed successfully');
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
