import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/accounts - Get all accounts
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const type = url.searchParams.get("type");
    const isActive = url.searchParams.get("isActive") === "true";
    const search = url.searchParams.get("search");

    // Build query filters
    const filters: any = {};
    if (type) filters.type = type;
    if (url.searchParams.has("isActive")) filters.isActive = isActive;

    if (search) {
      filters.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get accounts
    const accounts = await db.account.findMany({
      where: filters,
      orderBy: [
        { code: "asc" },
      ],
      include: {
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        parent: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      data: accounts,
    });
  } catch (error: any) {
    console.error("Error fetching accounts:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch accounts" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/accounts - Create a new account
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to create accounts
    const hasCreatePermission = await hasPermission("manage_accounts");
    if (!hasCreatePermission) {
      return NextResponse.json(
        { error: "You don't have permission to create accounts" },
        { status: 403 }
      );
    }

    // Parse request body
    const data = await req.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: "Account name is required" }, { status: 400 });
    }

    if (!data.code) {
      return NextResponse.json({ error: "Account code is required" }, { status: 400 });
    }

    if (!data.type) {
      return NextResponse.json({ error: "Account type is required" }, { status: 400 });
    }

    // Check if account with same code already exists
    const existingAccount = await db.account.findFirst({
      where: {
        code: data.code,
      },
    });

    if (existingAccount) {
      return NextResponse.json(
        { error: "An account with this code already exists" },
        { status: 400 }
      );
    }

    // Create account
    // Convert empty branchId string to null
    const branchId = data.branchId === "" ? null : data.branchId || null;
    const parentId = data.parentId === "" ? null : data.parentId || null;

    const account = await db.account.create({
      data: {
        name: data.name,
        code: data.code,
        type: data.type,
        parentId: parentId,
        branchId: branchId,
        isActive: data.isActive !== undefined ? data.isActive : true,
        balance: data.balance || 0,
      },
    });

    return NextResponse.json(account, { status: 201 });
  } catch (error: any) {
    console.error("Error creating account:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create account" },
      { status: 500 }
    );
  }
}
