import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { 
  getCustomerInfo, 
  getProductInfo, 
  getSalesSummary,
  getPaymentMethodBalances
} from "@/lib/ai-assistant-data";

// GET /api/ai-assistant/data - Get data based on query type and parameters
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get query parameters
    const url = new URL(req.url);
    const type = url.searchParams.get("type");
    const query = url.searchParams.get("query") || "";
    const period = url.searchParams.get("period") as "day" | "week" | "month" | "year" || "month";
    
    if (!type) {
      return NextResponse.json(
        { error: "Missing query type" },
        { status: 400 }
      );
    }
    
    let result;
    
    // Process different query types
    switch (type) {
      case "customer":
        result = await getCustomerInfo(query);
        break;
      case "product":
        result = await getProductInfo(query);
        break;
      case "sales":
        result = await getSalesSummary(period);
        break;
      case "payment-methods":
        result = await getPaymentMethodBalances();
        break;
      default:
        return NextResponse.json(
          { error: "Invalid query type" },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error processing data query:", error);
    return NextResponse.json(
      { error: "Failed to process data query" },
      { status: 500 }
    );
  }
}
