import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/payment-methods - Get all payment methods with their accounting links
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounting data
    const hasViewPermission = await hasPermission("view_accounting");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view accounting payment methods" },
        { status: 403 }
      );
    }

    // Get all payment methods with their account and journal links
    const paymentMethods = await db.paymentMethodSettings.findMany({
      include: {
        account: true,
        journal: true,
      },
      orderBy: {
        sequence: "asc",
      },
    });

    return NextResponse.json({
      success: true,
      data: paymentMethods,
    });
  } catch (error) {
    console.error("Error fetching payment methods:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch payment methods" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/payment-methods - Update payment method accounting links
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to update payment method accounting links" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.id || (!data.accountId && !data.journalId)) {
      return NextResponse.json(
        { error: "Payment method ID and at least one of accountId or journalId are required" },
        { status: 400 }
      );
    }

    // Update payment method
    const updatedPaymentMethod = await db.paymentMethodSettings.update({
      where: {
        id: data.id,
      },
      data: {
        accountId: data.accountId || null,
        journalId: data.journalId || null,
      },
      include: {
        account: true,
        journal: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedPaymentMethod,
    });
  } catch (error) {
    console.error("Error updating payment method:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update payment method" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/payment-methods/link-all - Link all payment methods to accounts and journals
export async function PATCH(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to link payment methods" },
        { status: 403 }
      );
    }

    // Get all accounts
    const accounts = await db.account.findMany({
      where: {
        isActive: true,
      },
    });

    // Get all journals
    const journals = await db.journal.findMany({
      where: {
        isActive: true,
      },
    });

    // Get all payment methods
    const paymentMethods = await db.paymentMethodSettings.findMany({
      where: {
        isActive: true,
      },
    });

    // Map accounts and journals by code/name
    const accountsMap = new Map();
    accounts.forEach(account => {
      accountsMap.set(account.code, account);
      accountsMap.set(account.name.toLowerCase(), account);
    });

    const journalsMap = new Map();
    journals.forEach(journal => {
      journalsMap.set(journal.code, journal);
      journalsMap.set(journal.name.toLowerCase(), journal);
      journalsMap.set(journal.type.toLowerCase(), journal);
    });

    // Link payment methods to accounts and journals
    const updatedPaymentMethods = [];

    for (const paymentMethod of paymentMethods) {
      let accountId = paymentMethod.accountId;
      let journalId = paymentMethod.journalId;

      // Try to find matching account if not already linked
      if (!accountId) {
        const matchingAccount = findMatchingAccount(paymentMethod.code, accountsMap);
        if (matchingAccount) {
          accountId = matchingAccount.id;
        }
      }

      // Try to find matching journal if not already linked
      if (!journalId) {
        const matchingJournal = findMatchingJournal(paymentMethod.code, journalsMap);
        if (matchingJournal) {
          journalId = matchingJournal.id;
        }
      }

      // Update payment method if we found new links
      if ((accountId && accountId !== paymentMethod.accountId) || 
          (journalId && journalId !== paymentMethod.journalId)) {
        const updatedPaymentMethod = await db.paymentMethodSettings.update({
          where: {
            id: paymentMethod.id,
          },
          data: {
            accountId: accountId || paymentMethod.accountId,
            journalId: journalId || paymentMethod.journalId,
          },
          include: {
            account: true,
            journal: true,
          },
        });

        updatedPaymentMethods.push(updatedPaymentMethod);
      }
    }

    return NextResponse.json({
      success: true,
      message: `${updatedPaymentMethods.length} payment methods linked to accounts and journals`,
      data: updatedPaymentMethods,
    });
  } catch (error) {
    console.error("Error linking payment methods:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to link payment methods" },
      { status: 500 }
    );
  }
}

// Helper function to find matching account for a payment method
function findMatchingAccount(paymentMethodCode: string, accountsMap: Map<string, any>) {
  const code = paymentMethodCode.toLowerCase();
  
  // Direct mappings
  if (code === "cash") return accountsMap.get("1000") || accountsMap.get("cash");
  if (code === "vodafone_cash") return accountsMap.get("1010") || accountsMap.get("vodafone cash");
  if (code === "bank_transfer") return accountsMap.get("1020") || accountsMap.get("bank account");
  if (code === "visa" || code === "credit_card") return accountsMap.get("1030") || accountsMap.get("credit card");
  if (code === "customer_account") return accountsMap.get("1100") || accountsMap.get("accounts receivable");
  
  return null;
}

// Helper function to find matching journal for a payment method
function findMatchingJournal(paymentMethodCode: string, journalsMap: Map<string, any>) {
  const code = paymentMethodCode.toLowerCase();
  
  // Direct mappings
  if (code === "cash") return journalsMap.get("CASH") || journalsMap.get("cash");
  if (code === "vodafone_cash") return journalsMap.get("VFCASH") || journalsMap.get("vodafone cash");
  if (code === "bank_transfer") return journalsMap.get("BANK") || journalsMap.get("bank");
  if (code === "visa" || code === "credit_card") return journalsMap.get("VISA") || journalsMap.get("credit card");
  if (code === "customer_account") return journalsMap.get("CUST") || journalsMap.get("customer account");
  
  return null;
}
