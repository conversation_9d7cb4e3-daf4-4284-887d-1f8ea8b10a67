"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Printer, RefreshCw, Check, X } from "lucide-react";
import { ThermalPrinter } from "@/lib/thermal-printer";

interface ThermalPrinterSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

export default function ThermalPrinterSettings({ settings, onUpdate }: ThermalPrinterSettingsProps) {
  const [isEnabled, setIsEnabled] = useState(false);
  const [printerN<PERSON>, setPrinterName] = useState("");
  const [paperWidth, setPaperWidth] = useState(80);
  const [paperHeight, setPaperHeight] = useState(297);
  const [printDensity, setPrintDensity] = useState("medium");
  const [printSpeed, setPrintSpeed] = useState("normal");
  const [characterSet, setCharacterSet] = useState("UTF-8");
  const [encoding, setEncoding] = useState("windows-1256");
  const [copies, setCopies] = useState(1);
  const [availablePrinters, setAvailablePrinters] = useState<string[]>([]);
  const [isTestingPrinter, setIsTestingPrinter] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [activeTab, setActiveTab] = useState("general");
  
  // Initialize thermal printer
  const thermalPrinter = new ThermalPrinter();
  
  // Load settings
  useEffect(() => {
    if (settings?.thermalPrinterSettings) {
      const thermalSettings = settings.thermalPrinterSettings;
      setIsEnabled(thermalSettings.enabled || false);
      setPrinterName(thermalSettings.printerName || "");
      setPaperWidth(thermalSettings.paperWidth || 80);
      setPaperHeight(thermalSettings.paperHeight || 297);
      setPrintDensity(thermalSettings.printDensity || "medium");
      setPrintSpeed(thermalSettings.printSpeed || "normal");
      setCharacterSet(thermalSettings.characterSet || "UTF-8");
      setEncoding(thermalSettings.encoding || "windows-1256");
      setCopies(thermalSettings.copies || 1);
    }
    
    // Get available printers
    const fetchPrinters = async () => {
      try {
        const printers = await thermalPrinter.getAvailablePrinters();
        setAvailablePrinters(printers);
      } catch (error) {
        console.error("Failed to get available printers:", error);
        setAvailablePrinters(["Default Printer"]);
      }
    };
    
    fetchPrinters();
  }, [settings]);
  
  // Handle save
  const handleSave = () => {
    const thermalPrinterSettings = {
      enabled: isEnabled,
      printerName,
      paperWidth,
      paperHeight,
      printDensity,
      printSpeed,
      characterSet,
      encoding,
      copies
    };
    
    onUpdate({ thermalPrinterSettings });
  };
  
  // Handle test printer
  const handleTestPrinter = async () => {
    setIsTestingPrinter(true);
    setTestResult(null);
    
    try {
      // Test printer connection
      const isConnected = await thermalPrinter.testPrinter(printerName);
      
      if (isConnected) {
        // Print test receipt
        await thermalPrinter.printReceipt({
          company_name: "VERO Company",
          receipt_number: "TEST-" + new Date().getTime(),
          date: new Date().toLocaleDateString(),
          cashier: "Test User",
          items: [
            { name: "Test Item 1", quantity: 1, price: "10.00", total: "10.00" },
            { name: "Test Item 2", quantity: 2, price: "15.00", total: "30.00" }
          ],
          subtotal: "40.00",
          tax: "4.00",
          total: "44.00",
          payment_method: "Cash",
          footer_text: "This is a test receipt. Thank you!"
        }, {
          copies: 1
        });
        
        setTestResult({
          success: true,
          message: "Test receipt printed successfully!"
        });
      } else {
        setTestResult({
          success: false,
          message: "Failed to connect to printer. Please check printer name and connection."
        });
      }
    } catch (error: any) {
      setTestResult({
        success: false,
        message: `Failed to print test receipt: ${error.message}`
      });
    } finally {
      setIsTestingPrinter(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Thermal Printer Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
            <TabsTrigger value="test">Test Printer</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-thermal-printer" className="font-medium">
                  Enable Thermal Printer
                </Label>
                <Switch
                  id="enable-thermal-printer"
                  checked={isEnabled}
                  onCheckedChange={setIsEnabled}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="printer-name">Printer Name</Label>
                <Select value={printerName} onValueChange={setPrinterName}>
                  <SelectTrigger id="printer-name">
                    <SelectValue placeholder="Select printer" />
                  </SelectTrigger>
                  <SelectContent>
                    {availablePrinters.map((printer) => (
                      <SelectItem key={printer} value={printer}>
                        {printer}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="paper-width">Paper Width (mm)</Label>
                  <Input
                    id="paper-width"
                    type="number"
                    value={paperWidth}
                    onChange={(e) => setPaperWidth(parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="paper-height">Paper Height (mm)</Label>
                  <Input
                    id="paper-height"
                    type="number"
                    value={paperHeight}
                    onChange={(e) => setPaperHeight(parseInt(e.target.value))}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="copies">Default Copies</Label>
                <Input
                  id="copies"
                  type="number"
                  min={1}
                  max={10}
                  value={copies}
                  onChange={(e) => setCopies(parseInt(e.target.value))}
                />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="advanced">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="print-density">Print Density</Label>
                <Select value={printDensity} onValueChange={setPrintDensity}>
                  <SelectTrigger id="print-density">
                    <SelectValue placeholder="Select print density" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="print-speed">Print Speed</Label>
                <Select value={printSpeed} onValueChange={setPrintSpeed}>
                  <SelectTrigger id="print-speed">
                    <SelectValue placeholder="Select print speed" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="slow">Slow</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="fast">Fast</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="character-set">Character Set</Label>
                <Select value={characterSet} onValueChange={setCharacterSet}>
                  <SelectTrigger id="character-set">
                    <SelectValue placeholder="Select character set" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UTF-8">UTF-8</SelectItem>
                    <SelectItem value="ASCII">ASCII</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="encoding">Encoding</Label>
                <Select value={encoding} onValueChange={setEncoding}>
                  <SelectTrigger id="encoding">
                    <SelectValue placeholder="Select encoding" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="windows-1256">Windows-1256 (Arabic)</SelectItem>
                    <SelectItem value="windows-1252">Windows-1252 (Latin)</SelectItem>
                    <SelectItem value="utf-8">UTF-8</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="test">
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Test your thermal printer configuration by printing a test receipt.
                Make sure your printer is connected and turned on.
              </p>
              
              {testResult && (
                <div className={`p-4 rounded-md ${testResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                  <div className="flex items-center">
                    {testResult.success ? (
                      <Check className="h-5 w-5 mr-2" />
                    ) : (
                      <X className="h-5 w-5 mr-2" />
                    )}
                    <p className="font-medium">{testResult.message}</p>
                  </div>
                </div>
              )}
              
              <Button
                onClick={handleTestPrinter}
                disabled={isTestingPrinter || !isEnabled || !printerName}
                className="w-full"
              >
                {isTestingPrinter ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Testing Printer...
                  </>
                ) : (
                  <>
                    <Printer className="h-4 w-4 mr-2" />
                    Print Test Receipt
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
        
        <div className="mt-6 flex justify-end">
          <Button onClick={handleSave}>Save Settings</Button>
        </div>
      </CardContent>
    </Card>
  );
}
