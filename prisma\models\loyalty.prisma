// Loyalty Program models
model LoyaltyProgram {
  id                String   @id @default(uuid())
  name              String
  description       String?
  pointsPerEGP      Float    @default(1)  // How many points earned per EGP spent
  pointValueInEGP   Float    @default(0.01) // Value of 1 point in EGP
  minPointsToRedeem Int      @default(100) // Minimum points required to redeem
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  tiers             LoyaltyTier[]
}

model LoyaltyTier {
  id                String   @id @default(uuid())
  loyaltyProgramId  String
  name              String   // BRONZE, SILVER, GOLD, PLATINUM
  description       String?
  minPoints         Int      // Minimum points required to reach this tier
  pointsMultiplier  Float    @default(1) // Multiplier for points earned at this tier
  discountPercentage Float?   // Automatic discount percentage for this tier
  isActive          Boolean  @default(true)
  color             String?  // Color code for UI display
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  loyaltyProgram    LoyaltyProgram @relation(fields: [loyaltyProgramId], references: [id])
  accounts          LoyaltyAccount[]
}

model LoyaltyAccount {
  id                String   @id @default(uuid())
  contactId         String   @unique
  currentPoints     Int      @default(0)
  totalPointsEarned Int      @default(0)
  totalPointsRedeemed Int     @default(0)
  loyaltyTierId     String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  contact           Contact  @relation(fields: [contactId], references: [id])
  loyaltyTier       LoyaltyTier? @relation(fields: [loyaltyTierId], references: [id])
  transactions      LoyaltyTransaction[]
  birthdayGifts     LoyaltyBirthdayGift[]
}

model LoyaltyTransaction {
  id                String   @id @default(uuid())
  loyaltyAccountId  String
  type              String   // EARN, REDEEM, ADJUST, EXPIRE, BIRTHDAY_GIFT
  points            Int
  saleId            String?  @unique
  creditNoteId      String?  @unique
  description       String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  loyaltyAccount    LoyaltyAccount @relation(fields: [loyaltyAccountId], references: [id])
  sale              Sale?    @relation(fields: [saleId], references: [id])
  creditNote        CreditNote? @relation(fields: [creditNoteId], references: [id])
  birthdayGift      LoyaltyBirthdayGift?
}

model LoyaltyBirthdayGift {
  id                  String   @id @default(uuid())
  contactId           String
  year                Int      // Year the gift was sent
  points              Int
  description         String?
  loyaltyTransactionId String?  @unique
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  contact             Contact  @relation(fields: [contactId], references: [id])
  loyaltyTransaction  LoyaltyTransaction? @relation(fields: [loyaltyTransactionId], references: [id])
}

// Notification model
model Notification {
  id          String   @id @default(uuid())
  userId      String
  type        String   // SYSTEM, LOYALTY, BIRTHDAY, SALE, PURCHASE, etc.
  title       String
  message     String
  link        String?
  read        Boolean  @default(false)
  metadata    String?  // JSON string for additional data
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  user        User     @relation(fields: [userId], references: [id])
}
