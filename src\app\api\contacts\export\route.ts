import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import * as XLSX from 'xlsx';

// GET /api/contacts/export - Export contacts to Excel
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view contacts
    const canViewCustomers = await hasPermission("view_customers");
    const canViewSuppliers = await hasPermission("view_suppliers");
    const canViewContacts = canViewCustomers || canViewSuppliers || session.user.role === "ADMIN";

    if (!canViewContacts) {
      return NextResponse.json(
        { error: "You don't have permission to export contacts" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const type = url.searchParams.get("type"); // customer, supplier, or both
    const search = url.searchParams.get("search");
    const balance = url.searchParams.get("balance");

    // Build the query
    const query: any = {
      where: {},
      orderBy: {
        name: "asc",
      },
    };

    // Apply type filter
    if (type === "customer") {
      query.where.isCustomer = true;
    } else if (type === "supplier") {
      query.where.isSupplier = true;
    }

    // Apply search filter
    if (search) {
      query.where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Apply balance filter
    if (balance === "non-zero") {
      query.where.balance = { not: 0 };
    }

    // Get contacts
    const contacts = await db.contact.findMany(query);

    // Transform the data for Excel export
    const contactsForExport = contacts.map(contact => {
      return {
        ID: contact.id,
        Name: contact.name,
        Phone: contact.phone,
        Address: contact.address || '',
        Email: contact.email || '',
        'Is Customer': contact.isCustomer ? 'Yes' : 'No',
        'Is Supplier': contact.isSupplier ? 'Yes' : 'No',
        'Balance': contact.balance,
        'Credit Limit': contact.creditLimit || 0,
        'Credit Period (Days)': contact.creditPeriod || 0,
        'Opening Balance': contact.openingBalance || 0,
        'Opening Balance Date': contact.openingBalanceDate ? new Date(contact.openingBalanceDate).toISOString().split('T')[0] : '',
        'Status': contact.isActive ? 'Active' : 'Inactive',
        'Created At': new Date(contact.createdAt).toISOString().split('T')[0],
        'Updated At': new Date(contact.updatedAt).toISOString().split('T')[0]
      };
    });

    // Create a worksheet
    const worksheet = XLSX.utils.json_to_sheet(contactsForExport);
    
    // Create a workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Contacts');
    
    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
    
    // Set headers for file download
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="contacts.xlsx"'
      }
    });
  } catch (error) {
    console.error("Error exporting contacts:", error);
    return NextResponse.json(
      { error: "Failed to export contacts" },
      { status: 500 }
    );
  }
}
