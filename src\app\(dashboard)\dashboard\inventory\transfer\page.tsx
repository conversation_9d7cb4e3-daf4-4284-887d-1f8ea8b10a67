"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface Product {
  id: string;
  name: string;
  categoryId: string;
  categoryName: string;
}

interface Warehouse {
  id: string;
  name: string;
}

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  warehouseId: string;
  warehouseName: string;
  quantity: number;
}

interface TransferItem {
  productId: string;
  productName: string;
  quantity: number;
  availableQuantity: number;
  sourceWarehouseId: string;
  sourceWarehouseName: string;
  destinationWarehouseId: string;
  destinationWarehouseName: string;
}

export default function TransferInventoryPage() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [selectedProduct, setSelectedProduct] = useState<string>("");
  const [sourceWarehouse, setSourceWarehouse] = useState<string>("");
  const [destinationWarehouse, setDestinationWarehouse] = useState<string>("");
  const [quantity, setQuantity] = useState<number>(1);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [availableQuantity, setAvailableQuantity] = useState<number>(0);
  const [transferItems, setTransferItems] = useState<TransferItem[]>([]);
  const [notes, setNotes] = useState<string>("");

  // Fetch products and warehouses
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          setProducts(productsData);
          setFilteredProducts(productsData);
        }

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        if (warehousesResponse.ok) {
          const warehousesData = await warehousesResponse.json();
          setWarehouses(warehousesData);
        }

        // Fetch inventory
        const inventoryResponse = await fetch('/api/inventory');
        if (inventoryResponse.ok) {
          const inventoryData = await inventoryResponse.json();
          setInventory(inventoryData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load data. Please try again.");
      }
    };

    fetchData();
  }, []);

  // Filter products based on search term and source warehouse
  useEffect(() => {
    let filtered = products;

    // Filter by search term
    if (searchTerm.trim() !== "") {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by source warehouse - only show products that have inventory in the selected warehouse
    if (sourceWarehouse) {
      filtered = filtered.filter(product => {
        const inventoryItem = inventory.find(
          item => item.productId === product.id && item.warehouseId === sourceWarehouse && item.quantity > 0
        );
        return !!inventoryItem;
      });
    }

    setFilteredProducts(filtered);
  }, [searchTerm, products, sourceWarehouse, inventory]);

  // Update available quantity when product or source warehouse changes
  useEffect(() => {
    if (selectedProduct && sourceWarehouse) {
      const inventoryItem = inventory.find(
        item => item.productId === selectedProduct && item.warehouseId === sourceWarehouse
      );

      setAvailableQuantity(inventoryItem ? inventoryItem.quantity : 0);
    } else {
      setAvailableQuantity(0);
    }
  }, [selectedProduct, sourceWarehouse, inventory]);

  // Handle adding a product to the transfer list
  const handleAddProduct = () => {
    if (!selectedProduct) {
      setError("Please select a product");
      return;
    }

    if (!sourceWarehouse) {
      setError("Please select a source warehouse");
      return;
    }

    if (!destinationWarehouse) {
      setError("Please select a destination warehouse");
      return;
    }

    if (sourceWarehouse === destinationWarehouse) {
      setError("Source and destination warehouses cannot be the same");
      return;
    }

    if (quantity <= 0) {
      setError("Quantity must be greater than zero");
      return;
    }

    if (quantity > availableQuantity) {
      setError(`Cannot transfer more than available quantity (${availableQuantity})`);
      return;
    }

    // Check if product is already in the list
    const existingItemIndex = transferItems.findIndex(item => item.productId === selectedProduct);

    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...transferItems];
      const newQuantity = updatedItems[existingItemIndex].quantity + quantity;

      if (newQuantity > updatedItems[existingItemIndex].availableQuantity) {
        setError(`Cannot transfer more than available quantity (${updatedItems[existingItemIndex].availableQuantity})`);
        return;
      }

      updatedItems[existingItemIndex].quantity = newQuantity;
      setTransferItems(updatedItems);
    } else {
      // Add new item
      const productName = products.find(p => p.id === selectedProduct)?.name || '';
      const sourceWarehouseName = warehouses.find(w => w.id === sourceWarehouse)?.name || '';
      const destinationWarehouseName = warehouses.find(w => w.id === destinationWarehouse)?.name || '';

      setTransferItems([
        ...transferItems,
        {
          productId: selectedProduct,
          productName,
          quantity,
          availableQuantity,
          sourceWarehouseId: sourceWarehouse,
          sourceWarehouseName,
          destinationWarehouseId: destinationWarehouse,
          destinationWarehouseName
        }
      ]);
    }

    // Reset form
    setSelectedProduct("");
    setQuantity(1);
    setError(null);
  };

  // Handle removing a product from the transfer list
  const handleRemoveProduct = (productId: string) => {
    setTransferItems(transferItems.filter(item => item.productId !== productId));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // The ONLY validation we do is to check if there are items in the transfer list
    if (transferItems.length === 0) {
      setError("Please add at least one product to transfer");
      return;
    }

    // We don't need to check sourceWarehouse or destinationWarehouse here
    // because they are already stored with each item in the transferItems array

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the source and destination warehouses from the first item in the list
      // This ensures we use the warehouses that were selected when the items were added
      const firstItem = transferItems[0];
      const sourceWarehouseId = firstItem.sourceWarehouseId;
      const destinationWarehouseId = firstItem.destinationWarehouseId;

      const response = await fetch('/api/inventory/transfers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceWarehouseId,
          destinationWarehouseId,
          notes,
          items: transferItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity
          }))
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create inventory transfer');
      }

      const data = await response.json();

      const sourceWarehouseName = warehouses.find(w => w.id === sourceWarehouse)?.name || '';
      const destWarehouseName = warehouses.find(w => w.id === destinationWarehouse)?.name || '';

      setSuccess(`Successfully transferred ${transferItems.length} products from ${sourceWarehouseName} to ${destWarehouseName}`);

      // Reset form
      setSourceWarehouse("");
      setDestinationWarehouse("");
      setTransferItems([]);
      setNotes("");

      // Refresh inventory data
      const inventoryResponse = await fetch('/api/inventory');
      if (inventoryResponse.ok) {
        const inventoryData = await inventoryResponse.json();
        setInventory(inventoryData);
      }

    } catch (error) {
      console.error("Error transferring inventory:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-black sm:text-3xl sm:truncate">
            Transfer Stock
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/dashboard/inventory"
            className="inline-flex items-center px-4 py-2 border-2 border-gray-300 rounded-md shadow-sm text-sm font-medium text-black bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Inventory
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          {success && (
            <div className="mb-4 bg-green-50 border-l-4 border-green-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-700">
                    {success}
                  </p>
                </div>
              </div>
            </div>
          )}

          {error && (
            <div className="mb-4 bg-red-50 border-l-4 border-red-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">
                    {error}
                  </p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-6 bg-blue-50 p-4 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">How to transfer products</h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ol className="list-decimal list-inside space-y-1">
                      <li>Select the source warehouse first</li>
                      <li>Select the destination warehouse</li>
                      <li>Choose a product from the dropdown (only products available in the source warehouse will be shown)</li>
                      <li>Enter the quantity to transfer</li>
                      <li>Click "Add" to add the product to the transfer list</li>
                      <li>Repeat steps 3-5 for additional products</li>
                      <li>Click "Transfer Stock" when ready to complete the transfer</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <label htmlFor="source-warehouse" className="block text-sm font-medium text-black">
                  Source Warehouse
                </label>
                <div className="mt-1">
                  <select
                    id="source-warehouse"
                    name="source-warehouse"
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    value={sourceWarehouse}
                    onChange={(e) => setSourceWarehouse(e.target.value)}
                  >
                    <option value="" className="text-black font-medium">Select source warehouse</option>
                    {warehouses.map((warehouse) => (
                      <option key={warehouse.id} value={warehouse.id} className="text-black font-medium">
                        {warehouse.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="destination-warehouse" className="block text-sm font-medium text-black">
                  Destination Warehouse
                </label>
                <div className="mt-1">
                  <select
                    id="destination-warehouse"
                    name="destination-warehouse"
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    value={destinationWarehouse}
                    onChange={(e) => setDestinationWarehouse(e.target.value)}
                  >
                    <option value="" className="text-black font-medium">Select destination warehouse</option>
                    {warehouses.map((warehouse) => (
                      <option
                        key={warehouse.id}
                        value={warehouse.id}
                        disabled={warehouse.id === sourceWarehouse}
                        className="text-black font-medium"
                      >
                        {warehouse.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="sm:col-span-6">
                <label htmlFor="product-search" className="block text-sm font-medium text-black">
                  Search Products
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                    type="text"
                    id="product-search"
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    placeholder="Search by product name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      type="button"
                      onClick={() => setSearchTerm("")}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      ✕
                    </button>
                  )}
                </div>
              </div>

              <div className="sm:col-span-6">
                <label htmlFor="product" className="block text-sm font-medium text-black">
                  Product
                </label>
                <div className="mt-1">
                  <select
                    id="product"
                    name="product"
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    value={selectedProduct}
                    onChange={(e) => setSelectedProduct(e.target.value)}
                    disabled={!sourceWarehouse}
                  >
                    <option value="" className="text-black font-medium">
                      {!sourceWarehouse ? "Please select source warehouse first" : "Select a product"}
                    </option>
                    {filteredProducts.map((product) => (
                      <option key={product.id} value={product.id} className="text-black font-medium">
                        {product.name} ({product.categoryName})
                      </option>
                    ))}
                  </select>
                </div>
                {!sourceWarehouse && (
                  <p className="mt-2 text-sm text-red-500">
                    Please select a source warehouse first to see available products
                  </p>
                )}
                {selectedProduct && sourceWarehouse && (
                  <p className="mt-2 text-sm text-black">
                    Available: <span className="font-medium">{availableQuantity}</span> units
                  </p>
                )}
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="quantity" className="block text-sm font-medium text-black">
                  Quantity to Transfer
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="quantity"
                    id="quantity"
                    min="1"
                    max={availableQuantity}
                    className="block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md disabled:bg-gray-100 disabled:text-gray-500"
                    value={quantity}
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    disabled={!selectedProduct || !sourceWarehouse}
                  />
                </div>
                {selectedProduct && sourceWarehouse && (
                  <p className="mt-1 text-sm text-gray-500">
                    Available: {availableQuantity}
                  </p>
                )}
              </div>

              <div className="sm:col-span-1 flex items-end">
                <button
                  type="button"
                  onClick={handleAddProduct}
                  disabled={!selectedProduct || !sourceWarehouse || quantity <= 0 || quantity > availableQuantity}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Add
                </button>
              </div>
            </div>

            {/* Products list */}
            <div className="mt-8 border-t border-gray-200 pt-6">
              <h3 className="text-xl font-bold text-gray-900 mb-3">Products to Transfer</h3>
              <p className="text-sm text-gray-500 mb-4">The following products will be transferred between warehouses. Each product will be transferred from its source warehouse to its destination warehouse as shown in the table.</p>

              {transferItems.length === 0 ? (
                <div className="bg-gray-50 p-4 text-center rounded-md">
                  <p className="text-gray-500">No products added yet. Use the form above to add products to transfer.</p>
                </div>
              ) : (
                <div className="flex flex-col">
                  <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                      <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Product
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Quantity
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                From
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                To
                              </th>
                              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {transferItems.map((item) => (
                                <tr key={item.productId}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {item.productName}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {item.quantity}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {item.sourceWarehouseName}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {item.destinationWarehouseName}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button
                                      type="button"
                                      onClick={() => handleRemoveProduct(item.productId)}
                                      className="text-red-600 hover:text-red-900"
                                    >
                                      Remove
                                    </button>
                                  </td>
                                </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="mt-6">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                Notes
              </label>
              <div className="mt-1">
                <textarea
                  id="notes"
                  name="notes"
                  rows={3}
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => router.push('/dashboard/inventory')}
                className="mr-3 inline-flex items-center px-4 py-2 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-black bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || transferItems.length === 0}
                className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(isLoading || transferItems.length === 0) ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isLoading ? 'Transferring...' : 'Transfer Stock'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
