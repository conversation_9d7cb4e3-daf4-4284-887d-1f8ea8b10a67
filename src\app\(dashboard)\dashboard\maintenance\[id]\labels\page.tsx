"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Printer } from "lucide-react";
import { format } from "date-fns";

interface MaintenanceService {
  id: string;
  serviceNumber: string;
  deviceType: string;
  brand: string;
  model?: string;
  serialNumber?: string;
  problemDescription: string;
  receivedDate: string;
  status: string;
  contact: {
    id: string;
    name: string;
    phone: string;
  };
}

export default function MaintenanceLabelsPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const id = unwrappedParams.id;

  const router = useRouter();
  const [service, setService] = useState<MaintenanceService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch maintenance service details
  useEffect(() => {
    const fetchServiceDetails = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/maintenance/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch maintenance service details");
        }

        const data = await response.json();
        setService(data);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceDetails();
  }, [id]);

  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          {error}
        </div>
        <div className="mt-4">
          <Link
            href={`/dashboard/maintenance/${id}`}
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance Details
          </Link>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 text-yellow-700 p-4 rounded-md">
          Maintenance service not found
        </div>
        <div className="mt-4">
          <Link
            href="/dashboard/maintenance"
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header with back button and print button - hidden when printing */}
      <div className="flex justify-between items-center mb-6 print:hidden">
        <div className="flex items-center">
          <Link
            href={`/dashboard/maintenance/${id}`}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Print Labels</h1>
        </div>
        <button
          onClick={handlePrint}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center"
        >
          <Printer className="h-4 w-4 mr-2" />
          Print Labels
        </button>
      </div>

      {/* Instructions - hidden when printing */}
      <div className="mb-8 bg-blue-50 p-4 rounded-md print:hidden">
        <h2 className="text-lg font-semibold text-blue-800 mb-2">Printing Instructions</h2>
        <p className="text-blue-700">
          These labels are designed to be printed on standard label paper. One label should be attached to the device, and the other to the receipt given to the customer.
        </p>
        <p className="text-blue-700 mt-2">
          Click the &quot;Print Labels&quot; button above to print. Make sure your printer settings are configured for the correct label size.
        </p>
      </div>

      {/* Labels container */}
      <div className="flex flex-col space-y-8">
        {/* First Label - For the device */}
        <div className="border border-gray-300 rounded-md p-4 max-w-[300px] mx-auto">
          <div className="text-center mb-2 font-bold text-lg border-b border-gray-300 pb-1">
            ملصق الجهاز
          </div>
          <div className="text-center font-bold text-xl mb-2 border-2 border-black p-1 rounded-md bg-gray-100">
            {service.serviceNumber}
          </div>
          <div className="grid grid-cols-1 gap-1 text-sm">
            <div className="flex justify-between">
              <span className="font-semibold">العميل:</span>
              <span className="text-right">{service.contact.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">الهاتف:</span>
              <span className="text-right">{service.contact.phone}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">الجهاز:</span>
              <span className="text-right">{service.deviceType}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">الماركة/الموديل:</span>
              <span className="text-right">{service.brand} {service.model}</span>
            </div>
            {service.serialNumber && (
              <div className="flex justify-between">
                <span className="font-semibold">الرقم التسلسلي:</span>
                <span className="text-right">{service.serialNumber}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="font-semibold">التاريخ:</span>
              <span className="text-right">{format(new Date(service.receivedDate), "dd/MM/yyyy")}</span>
            </div>
          </div>
        </div>

        {/* Second Label - For the receipt */}
        <div className="border border-gray-300 rounded-md p-4 max-w-[300px] mx-auto">
          <div className="text-center mb-2 font-bold text-lg border-b border-gray-300 pb-1">
            ملصق الإيصال
          </div>
          <div className="text-center font-bold text-xl mb-2 border-2 border-black p-1 rounded-md bg-gray-100">
            {service.serviceNumber}
          </div>
          <div className="grid grid-cols-1 gap-1 text-sm">
            <div className="flex justify-between">
              <span className="font-semibold">العميل:</span>
              <span className="text-right">{service.contact.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">الهاتف:</span>
              <span className="text-right">{service.contact.phone}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">الجهاز:</span>
              <span className="text-right">{service.deviceType}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">المشكلة:</span>
              <span className="text-right">{service.problemDescription.length > 40
                ? `${service.problemDescription.substring(0, 40)}...`
                : service.problemDescription}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">التاريخ:</span>
              <span className="text-right">{format(new Date(service.receivedDate), "dd/MM/yyyy")}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Print-specific styles */}
      <style jsx global>{`
        @media print {
          @page {
            size: 100mm 50mm;
            margin: 0;
          }
          body {
            margin: 0;
            padding: 0;
          }
          .border {
            border: 1px solid #000 !important;
          }
          .print:hidden {
            display: none !important;
          }
          .max-w-\\[300px\\] {
            max-width: 100% !important;
            width: 100% !important;
          }
          .p-4 {
            padding: 5mm !important;
          }
          .space-y-8 > * + * {
            margin-top: 0 !important;
          }
          .space-y-8 {
            display: flex;
            flex-direction: column;
          }
          .space-y-8 > div:first-child {
            page-break-after: always;
          }
          .text-sm {
            font-size: 10pt !important;
          }
          .text-lg {
            font-size: 12pt !important;
          }
          .text-xl {
            font-size: 14pt !important;
          }
        }
      `}</style>
    </div>
  );
}
