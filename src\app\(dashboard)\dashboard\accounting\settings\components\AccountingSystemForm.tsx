"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Save } from "lucide-react";
import { toast } from "sonner";

export default function AccountingSystemForm() {
  const [formData, setFormData] = useState({
    accountingSystem: "egyptian", // egyptian, american, international
    accountCodeFormat: "numeric", // numeric, alphanumeric
    accountCodeLength: 6,
    accountCodeSeparator: "-",
    useSubAccounts: true,
    autoGenerateJournalEntries: true,
    requireBalancedJournalEntries: true,
    allowPostingToPreviousPeriods: false,
    defaultTaxRate: 14, // Egyptian VAT rate
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form input changes
  const handleChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // In a real implementation, this would save to the server
      // await fetch("/api/accounting/settings", {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify(formData),
      // });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Accounting system settings saved successfully");
    } catch (error) {
      console.error("Error saving accounting system settings:", error);
      toast.error("Failed to save accounting system settings");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Accounting System Type</h3>
        <RadioGroup
          value={formData.accountingSystem}
          onValueChange={(value) => handleChange("accountingSystem", value)}
          className="space-y-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="egyptian" id="egyptian" />
            <Label htmlFor="egyptian">Egyptian Accounting System</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="american" id="american" />
            <Label htmlFor="american">American Accounting System (GAAP)</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="international" id="international" />
            <Label htmlFor="international">International Accounting Standards (IFRS)</Label>
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Account Code Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="accountCodeFormat">Account Code Format</Label>
            <Select
              value={formData.accountCodeFormat}
              onValueChange={(value) => handleChange("accountCodeFormat", value)}
            >
              <SelectTrigger id="accountCodeFormat">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="numeric">Numeric (e.g., 101001)</SelectItem>
                <SelectItem value="alphanumeric">Alphanumeric (e.g., A1001)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="accountCodeLength">Account Code Length</Label>
            <Input
              id="accountCodeLength"
              type="number"
              min={4}
              max={12}
              value={formData.accountCodeLength}
              onChange={(e) => handleChange("accountCodeLength", parseInt(e.target.value))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="accountCodeSeparator">Account Code Separator</Label>
            <Select
              value={formData.accountCodeSeparator}
              onValueChange={(value) => handleChange("accountCodeSeparator", value)}
            >
              <SelectTrigger id="accountCodeSeparator">
                <SelectValue placeholder="Select separator" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="-">Hyphen (-)</SelectItem>
                <SelectItem value=".">Dot (.)</SelectItem>
                <SelectItem value="/">Slash (/)</SelectItem>
                <SelectItem value="">None</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="defaultTaxRate">Default Tax Rate (%)</Label>
            <Input
              id="defaultTaxRate"
              type="number"
              min={0}
              max={100}
              step={0.01}
              value={formData.defaultTaxRate}
              onChange={(e) => handleChange("defaultTaxRate", parseFloat(e.target.value))}
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Accounting Behavior</h3>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="useSubAccounts"
              checked={formData.useSubAccounts}
              onCheckedChange={(checked) => handleChange("useSubAccounts", !!checked)}
            />
            <Label htmlFor="useSubAccounts">Use Sub-Accounts</Label>
          </div>
          <p className="text-sm text-gray-500 ml-6">
            Allow accounts to have parent-child relationships for better organization
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="autoGenerateJournalEntries"
              checked={formData.autoGenerateJournalEntries}
              onCheckedChange={(checked) => handleChange("autoGenerateJournalEntries", !!checked)}
            />
            <Label htmlFor="autoGenerateJournalEntries">Auto-Generate Journal Entries</Label>
          </div>
          <p className="text-sm text-gray-500 ml-6">
            Automatically create journal entries for sales, purchases, and other transactions
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="requireBalancedJournalEntries"
              checked={formData.requireBalancedJournalEntries}
              onCheckedChange={(checked) => handleChange("requireBalancedJournalEntries", !!checked)}
            />
            <Label htmlFor="requireBalancedJournalEntries">Require Balanced Journal Entries</Label>
          </div>
          <p className="text-sm text-gray-500 ml-6">
            Enforce that all journal entries must have equal debits and credits
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="allowPostingToPreviousPeriods"
              checked={formData.allowPostingToPreviousPeriods}
              onCheckedChange={(checked) => handleChange("allowPostingToPreviousPeriods", !!checked)}
            />
            <Label htmlFor="allowPostingToPreviousPeriods">Allow Posting to Previous Periods</Label>
          </div>
          <p className="text-sm text-gray-500 ml-6">
            Allow journal entries to be posted to closed accounting periods (not recommended)
          </p>
        </div>
      </div>

      <div className="flex justify-end pt-4">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Save Settings
        </Button>
      </div>
    </form>
  );
}
