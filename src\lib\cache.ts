/**
 * Simple in-memory cache implementation
 * 
 * This provides a basic caching mechanism for frequently accessed data.
 * In a production environment, consider using Redis or a similar distributed cache.
 */

interface CacheItem<T> {
  value: T;
  expiry: number;
}

class Cache {
  private cache: Map<string, CacheItem<any>>;
  private defaultTTL: number; // Time to live in milliseconds

  constructor(defaultTTL = 5 * 60 * 1000) { // Default 5 minutes
    this.cache = new Map();
    this.defaultTTL = defaultTTL;
  }

  /**
   * Set a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param ttl Time to live in milliseconds (optional, defaults to constructor value)
   */
  set<T>(key: string, value: T, ttl?: number): void {
    const expiry = Date.now() + (ttl || this.defaultTTL);
    this.cache.set(key, { value, expiry });
  }

  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or undefined if not found or expired
   */
  get<T>(key: string): T | undefined {
    const item = this.cache.get(key);
    
    // Return undefined if item doesn't exist or has expired
    if (!item || item.expiry < Date.now()) {
      if (item) {
        // Clean up expired item
        this.cache.delete(key);
      }
      return undefined;
    }
    
    return item.value as T;
  }

  /**
   * Check if a key exists in the cache and is not expired
   * @param key The cache key
   * @returns True if the key exists and is not expired
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    const exists = !!item && item.expiry >= Date.now();
    
    if (!exists && item) {
      // Clean up expired item
      this.cache.delete(key);
    }
    
    return exists;
  }

  /**
   * Delete a key from the cache
   * @param key The cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get a value from the cache or compute it if not found
   * @param key The cache key
   * @param fn Function to compute the value if not in cache
   * @param ttl Time to live in milliseconds (optional)
   * @returns The cached or computed value
   */
  async getOrSet<T>(key: string, fn: () => Promise<T>, ttl?: number): Promise<T> {
    const cachedValue = this.get<T>(key);
    
    if (cachedValue !== undefined) {
      return cachedValue;
    }
    
    const value = await fn();
    this.set(key, value, ttl);
    return value;
  }

  /**
   * Delete all keys matching a pattern
   * @param pattern Regex pattern to match keys
   */
  deletePattern(pattern: RegExp): void {
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache stats
   * @returns Object with cache statistics
   */
  getStats() {
    const now = Date.now();
    let activeItems = 0;
    let expiredItems = 0;
    
    this.cache.forEach(item => {
      if (item.expiry >= now) {
        activeItems++;
      } else {
        expiredItems++;
      }
    });
    
    return {
      totalItems: this.cache.size,
      activeItems,
      expiredItems
    };
  }
}

// Create a singleton instance
const cache = new Cache();

export default cache;
