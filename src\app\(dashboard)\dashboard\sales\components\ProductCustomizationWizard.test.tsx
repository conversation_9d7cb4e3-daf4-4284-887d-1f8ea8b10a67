import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ProductCustomizationWizard, { CustomizedProduct } from './ProductCustomizationWizard';
import { act } from 'react-dom/test-utils';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => {
  const actual = jest.requireActual('framer-motion');
  return {
    ...actual,
    motion: {
      div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    },
    AnimatePresence: ({ children }: any) => <>{children}</>,
  };
});

// Mock product data
const mockProduct = {
  id: 'product-1',
  name: 'Test Computer',
  basePrice: 1000,
  costPrice: 800,
  isCustomizable: true,
  availableComponents: {
    CPU: [
      {
        id: 'cpu-1',
        name: 'Intel i5',
        type: 'CPU',
        price: 200,
        stock: 10,
        speed: '3.2 GHz'
      },
      {
        id: 'cpu-2',
        name: 'Intel i7',
        type: 'CPU',
        price: 300,
        stock: 5,
        speed: '4.0 GHz'
      }
    ],
    RAM: [
      {
        id: 'ram-1',
        name: 'Kingston 8GB',
        type: 'RAM',
        price: 100,
        stock: 20,
        capacity: '8GB',
        speed: '3200 MHz'
      },
      {
        id: 'ram-2',
        name: 'Corsair 16GB',
        type: 'RAM',
        price: 150,
        stock: 15,
        capacity: '16GB',
        speed: '3600 MHz'
      }
    ],
    SSD: [
      {
        id: 'ssd-1',
        name: 'Samsung 500GB',
        type: 'SSD',
        price: 120,
        stock: 8,
        capacity: '500GB'
      }
    ]
  }
};

// Mock functions
const mockOnClose = jest.fn();
const mockOnConfirm = jest.fn();

describe('ProductCustomizationWizard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the wizard with product information', () => {
    render(
      <ProductCustomizationWizard
        isOpen={true}
        onClose={mockOnClose}
        product={mockProduct}
        onConfirm={mockOnConfirm}
      />
    );

    // Check if product name is displayed
    expect(screen.getByText(`Customize ${mockProduct.name}`)).toBeInTheDocument();
    
    // Check if base price is displayed
    expect(screen.getByText(`${mockProduct.basePrice.toFixed(2)} ج.م`)).toBeInTheDocument();
  });

  it('allows navigation between steps', async () => {
    render(
      <ProductCustomizationWizard
        isOpen={true}
        onClose={mockOnClose}
        product={mockProduct}
        onConfirm={mockOnConfirm}
      />
    );

    // Initial step should be "Base Product"
    expect(screen.getByText('Base configuration')).toBeInTheDocument();

    // Click next button
    const nextButton = screen.getByText('Next');
    await act(async () => {
      fireEvent.click(nextButton);
    });

    // Should now be on "Processor" step
    expect(screen.getByText('CPU')).toBeInTheDocument();
    expect(screen.getByText('Intel i5')).toBeInTheDocument();
    expect(screen.getByText('Intel i7')).toBeInTheDocument();

    // Click next button again
    await act(async () => {
      fireEvent.click(nextButton);
    });

    // Should now be on "Memory" step
    expect(screen.getByText('RAM')).toBeInTheDocument();
    expect(screen.getByText('Kingston 8GB')).toBeInTheDocument();

    // Click previous button
    const prevButton = screen.getByText('Previous');
    await act(async () => {
      fireEvent.click(prevButton);
    });

    // Should be back on "Processor" step
    expect(screen.getByText('CPU')).toBeInTheDocument();
  });

  it('allows selecting components', async () => {
    render(
      <ProductCustomizationWizard
        isOpen={true}
        onClose={mockOnClose}
        product={mockProduct}
        onConfirm={mockOnConfirm}
      />
    );

    // Navigate to Processor step
    const nextButton = screen.getByText('Next');
    await act(async () => {
      fireEvent.click(nextButton);
    });

    // Select a CPU
    const cpuOption = screen.getByText('Intel i7').closest('div[role="button"]');
    if (cpuOption) {
      await act(async () => {
        fireEvent.click(cpuOption);
      });
    }

    // Navigate to Memory step
    await act(async () => {
      fireEvent.click(nextButton);
    });

    // Select RAM
    const ramOption = screen.getByText('Kingston 8GB').closest('div[role="button"]');
    if (ramOption) {
      await act(async () => {
        fireEvent.click(ramOption);
      });
    }

    // Navigate to final step
    await act(async () => {
      fireEvent.click(nextButton);
    });
    await act(async () => {
      fireEvent.click(nextButton);
    });

    // Check if price summary shows selected components
    expect(screen.getByText('CPU:')).toBeInTheDocument();
    expect(screen.getByText('RAM:')).toBeInTheDocument();
    
    // Check if total price is calculated correctly (base + CPU + RAM)
    const expectedTotal = mockProduct.basePrice + 300 + 100;
    expect(screen.getByText(`${expectedTotal.toFixed(2)} ج.م`)).toBeInTheDocument();

    // Confirm the customization
    const confirmButton = screen.getByText('Confirm');
    await act(async () => {
      fireEvent.click(confirmButton);
    });

    // Check if onConfirm was called with the correct data
    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
    const customizedProduct = mockOnConfirm.mock.calls[0][0] as CustomizedProduct;
    expect(customizedProduct.productId).toBe(mockProduct.id);
    expect(customizedProduct.totalPrice).toBe(expectedTotal);
    expect(customizedProduct.selectedComponents.CPU).toBeDefined();
    expect(customizedProduct.selectedComponents.RAM).toBeDefined();
    expect(customizedProduct.selectedComponents.CPU.id).toBe('cpu-2');
    expect(customizedProduct.selectedComponents.RAM.id).toBe('ram-1');
  });

  it('allows setting a custom price', async () => {
    render(
      <ProductCustomizationWizard
        isOpen={true}
        onClose={mockOnClose}
        product={mockProduct}
        onConfirm={mockOnConfirm}
      />
    );

    // Navigate to pricing step
    const nextButton = screen.getByText('Next');
    await act(async () => {
      fireEvent.click(nextButton);
    });
    await act(async () => {
      fireEvent.click(nextButton);
    });
    await act(async () => {
      fireEvent.click(nextButton);
    });
    await act(async () => {
      fireEvent.click(nextButton);
    });

    // Enable custom price
    const customPriceToggle = screen.getByLabelText('Set custom price');
    await act(async () => {
      fireEvent.click(customPriceToggle);
    });

    // Set a custom price
    const customPriceInput = screen.getByLabelText(/Custom Price/);
    await act(async () => {
      fireEvent.change(customPriceInput, { target: { value: '1500' } });
    });

    // Confirm the customization
    const confirmButton = screen.getByText('Confirm');
    await act(async () => {
      fireEvent.click(confirmButton);
    });

    // Check if onConfirm was called with the custom price
    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
    const customizedProduct = mockOnConfirm.mock.calls[0][0] as CustomizedProduct;
    expect(customizedProduct.totalPrice).toBe(1500);
  });

  it('closes when the close button is clicked', () => {
    render(
      <ProductCustomizationWizard
        isOpen={true}
        onClose={mockOnClose}
        product={mockProduct}
        onConfirm={mockOnConfirm}
      />
    );

    // Click the close button (X)
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    // Check if onClose was called
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
});
