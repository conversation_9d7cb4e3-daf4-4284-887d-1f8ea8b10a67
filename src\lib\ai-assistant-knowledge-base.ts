/**
 * VERO ERP Knowledge Base
 * 
 * This file contains comprehensive knowledge about the VERO ERP system.
 * It serves as the central knowledge repository for the AI Assistant.
 */

export interface ModuleKnowledge {
  name: string;
  description: string;
  features: string[];
  tables: string[];
  routes: string[];
  components: string[];
  workflows: WorkflowKnowledge[];
  commonIssues: IssueKnowledge[];
  relatedModules: string[];
}

export interface WorkflowKnowledge {
  name: string;
  description: string;
  steps: string[];
  requiredPermissions: string[];
  relatedTables: string[];
}

export interface IssueKnowledge {
  issue: string;
  description: string;
  possibleSolutions: string[];
  affectedComponents: string[];
}

export interface SystemKnowledge {
  version: string;
  lastUpdated: string;
  modules: ModuleKnowledge[];
  globalFeatures: string[];
  databaseSchema: DatabaseSchemaKnowledge;
  permissions: PermissionKnowledge[];
  settings: SettingKnowledge[];
  integrations: IntegrationKnowledge[];
}

export interface DatabaseSchemaKnowledge {
  tables: TableKnowledge[];
  relationships: RelationshipKnowledge[];
}

export interface TableKnowledge {
  name: string;
  arabicName: string;
  description: string;
  fields: FieldKnowledge[];
  relatedTables: string[];
  module: string;
}

export interface FieldKnowledge {
  name: string;
  arabicName: string;
  type: string;
  description: string;
  isRequired: boolean;
  isUnique: boolean;
  defaultValue?: string;
}

export interface RelationshipKnowledge {
  sourceTable: string;
  targetTable: string;
  type: "one-to-one" | "one-to-many" | "many-to-many";
  description: string;
}

export interface PermissionKnowledge {
  name: string;
  description: string;
  defaultRoles: string[];
}

export interface SettingKnowledge {
  name: string;
  description: string;
  defaultValue: string;
  category: string;
}

export interface IntegrationKnowledge {
  name: string;
  description: string;
  status: "active" | "planned" | "deprecated";
}

/**
 * The complete knowledge base for VERO ERP
 */
export const veroERPKnowledge: SystemKnowledge = {
  version: "1.0.0",
  lastUpdated: new Date().toISOString(),
  globalFeatures: [
    "متعدد اللغات (العربية والإنجليزية)",
    "متعدد الفروع والمستودعات",
    "نظام صلاحيات متكامل",
    "نظام إشعارات متقدم",
    "مساعد ذكي متكامل",
    "واجهة مستخدم سهلة الاستخدام",
    "تقارير متقدمة وقابلة للتخصيص",
    "نظام نسخ احتياطي واستعادة",
    "تتبع التغييرات والسجلات",
    "دعم الطباعة والتصدير بصيغ متعددة"
  ],
  modules: [
    {
      name: "المبيعات",
      description: "إدارة عمليات البيع وفواتير المبيعات ومرتجعات المبيعات",
      features: [
        "إنشاء فواتير مبيعات جديدة",
        "إدارة مرتجعات المبيعات (إشعارات الخصم)",
        "تتبع حالة الدفع للفواتير",
        "دعم الخصومات والضرائب",
        "دعم طرق دفع متعددة",
        "تقسيم المدفوعات على عدة طرق دفع",
        "طباعة الفواتير وإشعارات الخصم",
        "تقارير المبيعات المتقدمة",
        "إدارة الخصومات الموسمية والعروض"
      ],
      tables: ["sale", "saleItem", "creditNote", "creditNoteItem", "discount", "seasonalPromotion"],
      routes: [
        "/dashboard/sales",
        "/dashboard/sales/orders",
        "/dashboard/sales/new",
        "/dashboard/sales/credit-note",
        "/dashboard/sales/discounts",
        "/dashboard/sales/discounts/seasonal",
        "/dashboard/sales/reports"
      ],
      components: [
        "SalesForm",
        "SalesTable",
        "CreditNoteForm",
        "DiscountForm",
        "SeasonalPromotionForm",
        "SalesReportGenerator"
      ],
      workflows: [
        {
          name: "إنشاء فاتورة مبيعات",
          description: "عملية إنشاء فاتورة مبيعات جديدة",
          steps: [
            "الانتقال إلى صفحة المبيعات",
            "النقر على 'فاتورة جديدة'",
            "اختيار العميل من القائمة أو إضافة عميل جديد",
            "إضافة المنتجات المطلوبة وتحديد الكميات والأسعار",
            "تطبيق الخصومات والضرائب إذا لزم الأمر",
            "تحديد طريقة الدفع",
            "حفظ الفاتورة وطباعتها"
          ],
          requiredPermissions: ["SALES_CREATE"],
          relatedTables: ["sale", "saleItem", "contact", "product", "paymentMethod"]
        },
        {
          name: "إنشاء مرتجع مبيعات",
          description: "عملية إنشاء مرتجع مبيعات (إشعار خصم)",
          steps: [
            "الانتقال إلى صفحة إشعارات الخصم",
            "النقر على 'إشعار خصم جديد'",
            "اختيار العميل",
            "اختيار الفاتورة المرتبطة",
            "تحديد المنتجات المرتجعة والكميات",
            "تحديد طريقة استرداد المبلغ",
            "حفظ إشعار الخصم وطباعته"
          ],
          requiredPermissions: ["SALES_CREDIT_NOTE_CREATE"],
          relatedTables: ["creditNote", "creditNoteItem", "sale", "contact", "product"]
        }
      ],
      commonIssues: [
        {
          issue: "خطأ في حساب الإجمالي عند إضافة كميات كبيرة",
          description: "عند إضافة منتج بكمية كبيرة تظهر مشكلة في حساب الإجمالي",
          possibleSolutions: [
            "التأكد من أن الكمية ضمن الحدود المسموح بها",
            "التحقق من عدم وجود أخطاء في حساب الضرائب والخصومات",
            "إعادة تحميل الصفحة وتجربة العملية مرة أخرى"
          ],
          affectedComponents: ["SalesForm", "SalesItemTable"]
        },
        {
          issue: "عدم ظهور بعض المنتجات في قائمة المنتجات",
          description: "بعض المنتجات لا تظهر في قائمة المنتجات عند إنشاء فاتورة جديدة",
          possibleSolutions: [
            "التأكد من أن المنتج نشط وليس محذوفًا",
            "التأكد من وجود مخزون كافٍ للمنتج",
            "التحقق من صلاحيات المستخدم للوصول إلى المنتج"
          ],
          affectedComponents: ["ProductSelector", "SalesForm"]
        }
      ],
      relatedModules: ["المخزون", "العملاء", "المحاسبة"]
    },
    {
      name: "المشتريات",
      description: "إدارة عمليات الشراء وفواتير المشتريات ومرتجعات المشتريات",
      features: [
        "إنشاء فواتير مشتريات جديدة",
        "إدارة مرتجعات المشتريات",
        "تتبع حالة الدفع للفواتير",
        "دعم الضرائب",
        "دعم طرق دفع متعددة",
        "طباعة الفواتير وإشعارات المرتجعات",
        "تقارير المشتريات المتقدمة"
      ],
      tables: ["purchase", "purchaseItem", "purchaseReturn", "purchaseReturnItem"],
      routes: [
        "/dashboard/purchases",
        "/dashboard/purchases/new",
        "/dashboard/purchases/due-invoices"
      ],
      components: [
        "PurchaseForm",
        "PurchaseTable",
        "PurchaseReturnForm",
        "PurchaseReportGenerator"
      ],
      workflows: [
        {
          name: "إنشاء فاتورة مشتريات",
          description: "عملية إنشاء فاتورة مشتريات جديدة",
          steps: [
            "الانتقال إلى صفحة المشتريات",
            "النقر على 'فاتورة جديدة'",
            "اختيار المورد من القائمة أو إضافة مورد جديد",
            "إضافة المنتجات المطلوبة وتحديد الكميات والأسعار",
            "تطبيق الضرائب إذا لزم الأمر",
            "تحديد طريقة الدفع",
            "حفظ الفاتورة وطباعتها"
          ],
          requiredPermissions: ["PURCHASES_CREATE"],
          relatedTables: ["purchase", "purchaseItem", "contact", "product", "paymentMethod"]
        }
      ],
      commonIssues: [
        {
          issue: "عدم تحديث المخزون بعد إنشاء فاتورة مشتريات",
          description: "في بعض الحالات لا يتم تحديث المخزون بعد إنشاء فاتورة مشتريات",
          possibleSolutions: [
            "التأكد من أن حالة الفاتورة 'مكتملة'",
            "التحقق من إعدادات تحديث المخزون",
            "إعادة تحميل صفحة المخزون"
          ],
          affectedComponents: ["PurchaseForm", "InventoryManager"]
        }
      ],
      relatedModules: ["المخزون", "الموردين", "المحاسبة"]
    }
  ],
  databaseSchema: {
    tables: [
      {
        name: "user",
        arabicName: "المستخدمين",
        description: "جدول المستخدمين في النظام",
        fields: [
          {
            name: "id",
            arabicName: "المعرف",
            type: "string",
            description: "المعرف الفريد للمستخدم",
            isRequired: true,
            isUnique: true
          },
          {
            name: "name",
            arabicName: "الاسم",
            type: "string",
            description: "اسم المستخدم",
            isRequired: true,
            isUnique: false
          },
          {
            name: "email",
            arabicName: "البريد الإلكتروني",
            type: "string",
            description: "البريد الإلكتروني للمستخدم",
            isRequired: true,
            isUnique: true
          },
          {
            name: "password",
            arabicName: "كلمة المرور",
            type: "string",
            description: "كلمة مرور المستخدم (مشفرة)",
            isRequired: true,
            isUnique: false
          },
          {
            name: "role",
            arabicName: "الدور",
            type: "enum",
            description: "دور المستخدم في النظام",
            isRequired: true,
            isUnique: false,
            defaultValue: "USER"
          }
        ],
        relatedTables: ["userPermission", "branch"],
        module: "الإعدادات"
      }
    ],
    relationships: [
      {
        sourceTable: "sale",
        targetTable: "contact",
        type: "one-to-many",
        description: "كل فاتورة مبيعات مرتبطة بعميل واحد"
      },
      {
        sourceTable: "sale",
        targetTable: "saleItem",
        type: "one-to-many",
        description: "كل فاتورة مبيعات تحتوي على عدة عناصر"
      }
    ]
  },
  permissions: [
    {
      name: "SALES_VIEW",
      description: "عرض المبيعات",
      defaultRoles: ["ADMIN", "MANAGER", "SALES"]
    },
    {
      name: "SALES_CREATE",
      description: "إنشاء فواتير مبيعات جديدة",
      defaultRoles: ["ADMIN", "MANAGER", "SALES"]
    }
  ],
  settings: [
    {
      name: "companyName",
      description: "اسم الشركة",
      defaultValue: "VERO",
      category: "عام"
    },
    {
      name: "taxRate",
      description: "نسبة الضريبة الافتراضية",
      defaultValue: "14",
      category: "المبيعات"
    }
  ],
  integrations: [
    {
      name: "الضرائب المصرية",
      description: "تكامل مع نظام الفاتورة الإلكترونية المصري",
      status: "planned"
    }
  ]
};

/**
 * Get knowledge about a specific module
 */
export function getModuleKnowledge(moduleName: string): ModuleKnowledge | undefined {
  return veroERPKnowledge.modules.find(module => 
    module.name === moduleName || 
    module.name.toLowerCase() === moduleName.toLowerCase()
  );
}

/**
 * Get knowledge about a specific table
 */
export function getTableKnowledge(tableName: string): TableKnowledge | undefined {
  return veroERPKnowledge.databaseSchema.tables.find(table => 
    table.name === tableName || 
    table.arabicName === tableName
  );
}

/**
 * Get knowledge about a specific workflow
 */
export function getWorkflowKnowledge(workflowName: string): WorkflowKnowledge | undefined {
  for (const module of veroERPKnowledge.modules) {
    const workflow = module.workflows.find(wf => 
      wf.name === workflowName || 
      wf.name.toLowerCase() === workflowName.toLowerCase()
    );
    if (workflow) return workflow;
  }
  return undefined;
}

/**
 * Search the knowledge base for a specific term
 */
export function searchKnowledgeBase(term: string): any[] {
  const results = [];
  const lowerTerm = term.toLowerCase();
  
  // Search in modules
  for (const module of veroERPKnowledge.modules) {
    if (
      module.name.toLowerCase().includes(lowerTerm) ||
      module.description.toLowerCase().includes(lowerTerm)
    ) {
      results.push({
        type: "module",
        data: module
      });
    }
    
    // Search in workflows
    for (const workflow of module.workflows) {
      if (
        workflow.name.toLowerCase().includes(lowerTerm) ||
        workflow.description.toLowerCase().includes(lowerTerm)
      ) {
        results.push({
          type: "workflow",
          data: workflow,
          module: module.name
        });
      }
    }
    
    // Search in issues
    for (const issue of module.commonIssues) {
      if (
        issue.issue.toLowerCase().includes(lowerTerm) ||
        issue.description.toLowerCase().includes(lowerTerm)
      ) {
        results.push({
          type: "issue",
          data: issue,
          module: module.name
        });
      }
    }
  }
  
  // Search in tables
  for (const table of veroERPKnowledge.databaseSchema.tables) {
    if (
      table.name.toLowerCase().includes(lowerTerm) ||
      table.arabicName.toLowerCase().includes(lowerTerm) ||
      table.description.toLowerCase().includes(lowerTerm)
    ) {
      results.push({
        type: "table",
        data: table
      });
    }
  }
  
  return results;
}
