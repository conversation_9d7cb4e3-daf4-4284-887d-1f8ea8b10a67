import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { format, subDays, parseISO, isAfter, isBefore, differenceInDays } from "date-fns";

// GET /api/dashboard/unified - Get unified dashboard data
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get("startDate")
      ? new Date(url.searchParams.get("startDate")!)
      : subDays(new Date(), 30);
    const endDate = url.searchParams.get("endDate")
      ? new Date(url.searchParams.get("endDate")!)
      : new Date();
    const branchId = url.searchParams.get("branchId") || "all";
    const timeFrame = url.searchParams.get("timeFrame") || "month";

    // Prepare branch filter
    const branchFilter = branchId !== "all" ? { branchId } : {};

    // Get sales data
    const salesData = await prisma.sale.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
        ...branchFilter,
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        contact: true,
      },
      orderBy: {
        date: "asc",
      },
    });

    // Get purchases data
    const purchasesData = await prisma.purchase.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
        ...branchFilter,
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        contact: true,
        // Removed payments as it's not yet in the schema
      },
      orderBy: {
        date: "asc",
      },
    });

    // Get inventory data
    const inventoryData = await prisma.inventory.findMany({
      include: {
        product: {
          include: {
            category: true,
          },
        },
        warehouse: true,
      },
      where: {
        warehouse: branchId !== "all" ? { branchId } : {},
      },
    });

    // Get payment methods data
    const paymentMethodsData = await prisma.paymentMethodSettings.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        sequence: "asc",
      },
    });

    // Process sales data
    const salesByDate = salesData.reduce((acc: any[], sale) => {
      const dateStr = format(new Date(sale.date), "yyyy-MM-dd");
      const existingDate = acc.find(item => item.date === dateStr);

      if (existingDate) {
        existingDate.amount += sale.totalAmount;
        existingDate.count += 1;
      } else {
        acc.push({
          date: dateStr,
          amount: sale.totalAmount,
          count: 1,
        });
      }

      return acc;
    }, []);

    // Process purchases data
    const purchasesByDate = purchasesData.reduce((acc: any[], purchase) => {
      const dateStr = format(new Date(purchase.date), "yyyy-MM-dd");
      const existingDate = acc.find(item => item.date === dateStr);

      if (existingDate) {
        existingDate.amount += purchase.totalAmount;
        existingDate.count += 1;
      } else {
        acc.push({
          date: dateStr,
          amount: purchase.totalAmount,
          count: 1,
        });
      }

      return acc;
    }, []);

    // Process inventory data by category
    const inventoryByCategory = inventoryData.reduce((acc: any[], inv) => {
      const categoryName = inv.product.category.name;
      const existingCategory = acc.find(item => item.name === categoryName);
      const value = inv.quantity * inv.costPrice;

      if (existingCategory) {
        existingCategory.value += value;
        existingCategory.count += 1;
      } else {
        acc.push({
          name: categoryName,
          value,
          count: 1,
        });
      }

      return acc;
    }, []);

    // Get low stock products
    const lowStockProducts = inventoryData
      .filter(inv => {
        // Assuming a product is low stock if quantity is less than 5
        // This should be replaced with a proper minimum stock level from the product settings
        const minStock = 5;
        return inv.quantity < minStock;
      })
      .map(inv => ({
        id: inv.product.id,
        name: inv.product.name,
        currentStock: inv.quantity,
        minStock: 5, // This should be replaced with a proper minimum stock level
        warehouseName: inv.warehouse.name,
      }));

    // Get due invoices
    const today = new Date();
    const dueInvoices = purchasesData
      .filter(purchase =>
        purchase.dueDate &&
        purchase.paymentStatus !== "PAID" &&
        differenceInDays(new Date(purchase.dueDate), today) <= 7
      )
      .map(purchase => {
        const dueDate = new Date(purchase.dueDate!);
        let status = "due-soon";

        if (isBefore(dueDate, today)) {
          status = "overdue";
        } else if (
          dueDate.getDate() === today.getDate() &&
          dueDate.getMonth() === today.getMonth() &&
          dueDate.getFullYear() === today.getFullYear()
        ) {
          status = "due-today";
        }

        // Since payments are not yet available, use the total amount
        // In the future, this should calculate: totalAmount - sum of payments
        const remainingAmount = purchase.totalAmount;

        return {
          id: purchase.id,
          invoiceNumber: purchase.invoiceNumber,
          supplierName: purchase.contact.name,
          dueDate: purchase.dueDate,
          amount: remainingAmount,
          status,
        };
      })
      .sort((a, b) => {
        // Sort by status (overdue first, then due today, then due soon)
        if (a.status !== b.status) {
          if (a.status === "overdue") return -1;
          if (b.status === "overdue") return 1;
          if (a.status === "due-today") return -1;
          if (b.status === "due-today") return 1;
        }

        // Then sort by due date
        return new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime();
      })
      .slice(0, 5); // Limit to 5 invoices

    // Calculate total sales
    const totalSales = salesData.reduce((sum, sale) => sum + sale.totalAmount, 0);
    const salesCount = salesData.length;

    // Calculate total purchases
    const totalPurchases = purchasesData.reduce((sum, purchase) => sum + purchase.totalAmount, 0);
    const purchasesCount = purchasesData.length;

    // Calculate total inventory value
    const totalInventoryValue = inventoryData.reduce(
      (sum, inv) => sum + inv.quantity * inv.costPrice,
      0
    );

    // Calculate cash balance (simplified)
    const cashBalance = totalSales - totalPurchases;

    // Prepare payment methods data
    const paymentMethodsBalance = paymentMethodsData.map(method => ({
      name: method.name,
      amount: Math.random() * 10000, // This should be replaced with actual data
      code: method.code,
    }));

    // Return unified dashboard data
    return NextResponse.json({
      sales: {
        total: totalSales,
        count: salesCount,
        paid: salesData.filter(sale => sale.paymentStatus === "PAID").reduce((sum, sale) => sum + sale.totalAmount, 0),
        byDate: salesByDate,
        topProducts: [] // This should be implemented
      },
      purchases: {
        total: totalPurchases,
        count: purchasesCount,
        unpaid: purchasesData.filter(purchase => purchase.paymentStatus !== "PAID").reduce((sum, purchase) => sum + purchase.totalAmount, 0),
        byDate: purchasesByDate,
        dueInvoices
      },
      inventory: {
        totalProducts: inventoryData.length,
        lowStock: lowStockProducts,
        totalValue: totalInventoryValue,
        byCategory: inventoryByCategory
      },
      finance: {
        cashBalance,
        accountsReceivable: salesData.filter(sale => sale.paymentStatus !== "PAID").reduce((sum, sale) => sum + sale.totalAmount, 0),
        accountsPayable: purchasesData.filter(purchase => purchase.paymentStatus !== "PAID").reduce((sum, purchase) => sum + purchase.totalAmount, 0),
        paymentMethods: paymentMethodsBalance
      }
    });
  } catch (error) {
    console.error("Error fetching unified dashboard data:", error);
    return NextResponse.json(
      { error: "Failed to fetch unified dashboard data" },
      { status: 500 }
    );
  }
}
