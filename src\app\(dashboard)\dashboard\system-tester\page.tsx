"use client";

import { useState } from "react";
import { AISystemTester } from "@/components/ai-assistant/AISystemTester";
import { AIAssistant } from "@/components/ai-assistant/AIAssistant";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bot, Code, Database, Layers } from "lucide-react";

export default function SystemTesterPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">اختبار النظام</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bot className="h-5 w-5 mr-2 text-blue-600" />
              المساعد الذكي
            </CardTitle>
            <CardDescription>
              استخدم المساعد الذكي للتفاعل مع النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              يمكنك استخدام المساعد الذكي للاستعلام عن حالة النظام، واختبار الوظائف، وفحص قاعدة البيانات.
            </p>
            <p className="text-sm text-gray-500">
              جرب الأسئلة التالية:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
              <li>ما هو هيكل قاعدة البيانات؟</li>
              <li>عرض بيانات جدول المبيعات</li>
              <li>ما هي مشاكل واجهة المستخدم؟</li>
              <li>اختبار عملية المبيعات</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-blue-600" />
              قاعدة البيانات
            </CardTitle>
            <CardDescription>
              معلومات عن قاعدة البيانات
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              يمكن للمساعد الذكي استكشاف قاعدة البيانات وعرض معلومات عن الجداول والعلاقات والبيانات.
            </p>
            <p className="text-sm text-gray-500">
              الاستعلامات المتاحة:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
              <li>عرض هيكل قاعدة البيانات</li>
              <li>عرض بيانات جدول محدد</li>
              <li>عرض العلاقات بين الجداول</li>
              <li>عرض إحصائيات قاعدة البيانات</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Layers className="h-5 w-5 mr-2 text-blue-600" />
              اختبار العمليات
            </CardTitle>
            <CardDescription>
              اختبار عمليات النظام المختلفة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              يمكن للمساعد الذكي اختبار عمليات النظام المختلفة واكتشاف المشاكل.
            </p>
            <p className="text-sm text-gray-500">
              العمليات المتاحة للاختبار:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
              <li>عملية إنشاء فاتورة مبيعات</li>
              <li>عملية إدارة المخزون</li>
              <li>عملية إدارة العملاء</li>
              <li>عملية المحاسبة والتقارير المالية</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <AISystemTester />

      {/* The AIAssistant component is already included in the layout */}
    </div>
  );
}
