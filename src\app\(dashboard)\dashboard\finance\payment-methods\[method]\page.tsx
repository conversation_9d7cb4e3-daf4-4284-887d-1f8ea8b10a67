"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  Calendar,
  Download,
  Filter,
  Printer,
  Search
} from "lucide-react";
import { format } from "date-fns";

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  type: string;
  reference: string;
  referenceType: string;
}

export default function PaymentMethodDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const methodKey = params.method as string;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<any>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [period, setPeriod] = useState("month");
  const [searchQuery, setSearchQuery] = useState("");
  const [totalBalance, setTotalBalance] = useState(0);

  // Function to get payment method name
  const getPaymentMethodName = (key: string) => {
    switch (key) {
      case "cash":
        return "Cash";
      case "vodafoneCash":
        return "Vodafone Cash";
      case "bankTransfer":
        return "Bank Transfer";
      case "creditCard":
        return "Credit Card";
      case "customerAccount":
        return "Customer Account";
      default:
        return key;
    }
  };

  // Function to get payment method color
  const getPaymentMethodColor = (key: string) => {
    switch (key) {
      case "cash":
        return "bg-green-100 text-green-800 border-green-200";
      case "vodafoneCash":
        return "bg-red-100 text-red-800 border-red-200";
      case "bankTransfer":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "creditCard":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "customerAccount":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Function to get payment method code
  const getPaymentMethodCode = (key: string) => {
    switch (key) {
      case "cash":
        return "CASH";
      case "vodafoneCash":
        return "VODAFONE_CASH";
      case "bankTransfer":
        return "BANK_TRANSFER";
      case "creditCard":
        return "VISA";
      case "customerAccount":
        return "CUSTOMER_ACCOUNT";
      default:
        return key.toUpperCase();
    }
  };

  // Fetch payment method details and transactions
  useEffect(() => {
    const fetchPaymentMethodDetails = async () => {
      setIsLoading(true);
      try {
        // Fetch payment method balance
        const balanceResponse = await fetch(`/api/finance/payment-methods-balances?period=${period}`);
        if (!balanceResponse.ok) {
          throw new Error("Failed to fetch payment method balance");
        }
        const balanceData = await balanceResponse.json();

        // Set payment method data
        const methodData = {
          name: getPaymentMethodName(methodKey),
          code: getPaymentMethodCode(methodKey),
          balance: balanceData[methodKey] || 0,
          color: getPaymentMethodColor(methodKey),
        };

        setPaymentMethod(methodData);
        setTotalBalance(balanceData[methodKey] || 0);

        // Fetch transactions for this payment method
        const transactionsResponse = await fetch(`/api/finance/transactions?method=${methodData.code}&period=${period}`);
        if (transactionsResponse.ok) {
          const transactionsData = await transactionsResponse.json();
          setTransactions(transactionsData.transactions || []);
        }

        setError(null);
      } catch (error) {
        console.error("Error fetching payment method details:", error);
        setError(error instanceof Error ? error.message : "Failed to load payment method details");
      } finally {
        setIsLoading(false);
      }
    };

    if (methodKey) {
      fetchPaymentMethodDetails();
    }
  }, [methodKey, period]);

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading payment method details</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
        <Link href="/dashboard/finance/payment-methods" className="inline-flex items-center text-indigo-600 hover:text-indigo-900">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Payment Methods
        </Link>
      </div>
    );
  }

  if (!paymentMethod) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Payment method not found</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>The requested payment method could not be found.</p>
              </div>
            </div>
          </div>
        </div>
        <Link href="/dashboard/finance/payment-methods" className="inline-flex items-center text-indigo-600 hover:text-indigo-900">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Payment Methods
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href="/dashboard/finance/payment-methods" className="inline-flex items-center text-indigo-600 hover:text-indigo-900">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Payment Methods
        </Link>
      </div>

      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">{paymentMethod.name}</h1>
          <p className="text-gray-500">Payment Method Details</p>
        </div>
        <div className="flex space-x-2 mt-4 md:mt-0">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="bg-white border border-gray-300 text-gray-700 py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
          <button className="bg-white border border-gray-300 p-2 rounded-md shadow-sm hover:bg-gray-50">
            <Printer className="h-5 w-5 text-gray-500" />
          </button>
          <button className="bg-white border border-gray-300 p-2 rounded-md shadow-sm hover:bg-gray-50">
            <Download className="h-5 w-5 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Balance Card */}
      <div className={`rounded-lg p-6 mb-8 ${paymentMethod.color}`}>
        <h2 className="text-lg font-semibold mb-2">Current Balance</h2>
        <div className="text-3xl font-bold">{totalBalance.toFixed(2)} EGP</div>
      </div>

      {/* Transactions */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 md:mb-0">Transactions</h3>
          <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-full"
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </button>
          </div>
        </div>

        {transactions.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-gray-500">No transactions found for this payment method.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reference
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(transaction.date), 'dd/MM/yyyy')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {transaction.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.reference}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                      <span className={transaction.type === 'DEBIT' ? 'text-green-600' : 'text-red-600'}>
                        {transaction.type === 'DEBIT' ? '+' : '-'}{transaction.amount.toFixed(2)} EGP
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
