// Accounting Module Schema

// Account Types
enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

// Journal Types
enum JournalType {
  CASH
  VODAFONE_CASH
  BANK_TRANSFER
  VISA
  CUSTOMER_ACCOUNT
  GENERAL
}

// Account Model
model Account {
  id                String          @id @default(uuid())
  code              String          @unique // Account code (e.g., 1000, 2000)
  name              String
  type              AccountType
  parentId          String?         // Parent account ID for hierarchical structure
  parent            Account?        @relation("AccountHierarchy", fields: [parentId], references: [id])
  children          Account[]       @relation("AccountHierarchy")
  balance           Float           @default(0)
  isActive          Boolean         @default(true)
  branchId          String?         // Optional branch association
  branch            Branch?         @relation(fields: [branchId], references: [id])
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  debitEntries      JournalEntry[]  @relation("DebitAccount")
  creditEntries     JournalEntry[]  @relation("CreditAccount")

  @@index([code])
  @@index([type])
  @@index([parentId])
  @@index([isActive])
}

// Journal Model
model Journal {
  id                String          @id @default(uuid())
  code              String          @unique // Journal code (e.g., CASH-001)
  name              String
  type              JournalType
  branchId          String?         // Optional branch association
  branch            Branch?         @relation(fields: [branchId], references: [id])
  isActive          Boolean         @default(true)
  entries           JournalEntry[]
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@index([code])
  @@index([type])
  @@index([isActive])
}

// Journal Entry Model
model JournalEntry {
  id                String          @id @default(uuid())
  journal           Journal         @relation(fields: [journalId], references: [id])
  journalId         String
  entryNumber       String          // Sequential number for each journal
  date              DateTime        @default(now())
  description       String
  debitAccountId    String          // Account to debit
  debitAccount      Account         @relation("DebitAccount", fields: [debitAccountId], references: [id])
  creditAccountId   String          // Account to credit
  creditAccount     Account         @relation("CreditAccount", fields: [creditAccountId], references: [id])
  amount            Float
  reference         String?         // Reference to sale or purchase invoice
  referenceType     String?         // Type of reference: SALE, PURCHASE, PAYMENT, RECEIPT
  isPosted          Boolean         @default(false)
  fiscalPeriodId    String?
  fiscalPeriod      FiscalPeriod?   @relation(fields: [fiscalPeriodId], references: [id])
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@unique([entryNumber, journalId])
  @@index([journalId])
  @@index([date])
  @@index([debitAccountId])
  @@index([creditAccountId])
  @@index([isPosted])
}

// Fiscal Year Model
model FiscalYear {
  id                String          @id @default(uuid())
  name              String
  startDate         DateTime
  endDate           DateTime
  isClosed          Boolean         @default(false)
  periods           FiscalPeriod[]
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@index([isClosed])
}

// Fiscal Period Model
model FiscalPeriod {
  id                String          @id @default(uuid())
  fiscalYear        FiscalYear      @relation(fields: [fiscalYearId], references: [id])
  fiscalYearId      String
  name              String
  startDate         DateTime
  endDate           DateTime
  isClosed          Boolean         @default(false)
  journalEntries    JournalEntry[]
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@index([fiscalYearId])
  @@index([isClosed])
  @@index([startDate, endDate])
}
