import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/inventory/transfers/[id] - Get a specific inventory transfer
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view inventory
    const hasViewPermission = await hasPermission("view_products") ||
                             await hasPermission("view_inventory") ||
                             session.user.role === "ADMIN";
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view inventory transfers" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Get transfer from database
    const transfer = await db.inventoryTransfer.findUnique({
      where: {
        id,
      },
      include: {
        sourceWarehouse: true,
        destinationWarehouse: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true
              }
            }
          }
        }
      },
    });

    if (!transfer) {
      return NextResponse.json(
        { error: "Inventory transfer not found" },
        { status: 404 }
      );
    }

    // Format the response
    const formattedTransfer = {
      id: transfer.id,
      referenceNumber: transfer.referenceNumber,
      date: transfer.date,
      sourceWarehouse: {
        id: transfer.sourceWarehouse.id,
        name: transfer.sourceWarehouse.name
      },
      destinationWarehouse: {
        id: transfer.destinationWarehouse.id,
        name: transfer.destinationWarehouse.name
      },
      user: transfer.user,
      notes: transfer.notes,
      items: transfer.items.map(item => ({
        id: item.id,
        product: {
          id: item.product.id,
          name: item.product.name,
          category: item.product.category.name
        },
        quantity: item.quantity
      })),
      createdAt: transfer.createdAt,
      updatedAt: transfer.updatedAt
    };

    return NextResponse.json(formattedTransfer);
  } catch (error) {
    console.error("Error fetching inventory transfer:", error);
    return NextResponse.json(
      { error: "Failed to fetch inventory transfer" },
      { status: 500 }
    );
  }
}

// PUT /api/inventory/transfers/[id] - Update a specific inventory transfer
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit inventory
    const hasEditPermission = await hasPermission("edit_products") ||
                             session.user.role === "ADMIN";
    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to edit inventory transfers" },
        { status: 403 }
      );
    }

    const id = params.id;
    const data = await req.json();

    // Check if transfer exists
    const transfer = await db.inventoryTransfer.findUnique({
      where: {
        id,
      },
      include: {
        items: true
      }
    });

    if (!transfer) {
      return NextResponse.json(
        { error: "Inventory transfer not found" },
        { status: 404 }
      );
    }

    // Update each item and adjust inventory accordingly
    for (const updatedItem of data.items) {
      // Find the original item
      const originalItem = transfer.items.find(item => item.id === updatedItem.id);

      if (!originalItem) {
        return NextResponse.json(
          { error: `Item with ID ${updatedItem.id} not found in this transfer` },
          { status: 400 }
        );
      }

      // If quantity has changed, adjust inventory
      if (originalItem.quantity !== updatedItem.quantity) {
        const quantityDifference = updatedItem.quantity - originalItem.quantity;

        if (quantityDifference !== 0) {
          // If quantity increased, check if there's enough inventory in source warehouse
          if (quantityDifference > 0) {
            const sourceInventory = await db.inventory.findFirst({
              where: {
                productId: originalItem.productId,
                warehouseId: transfer.sourceWarehouseId
              }
            });

            if (!sourceInventory || sourceInventory.quantity < quantityDifference) {
              return NextResponse.json(
                { error: `Not enough inventory in source warehouse for product ID ${originalItem.productId}` },
                { status: 400 }
              );
            }

            // Reduce additional quantity from source warehouse
            await db.inventory.update({
              where: {
                id: sourceInventory.id
              },
              data: {
                quantity: sourceInventory.quantity - quantityDifference
              }
            });

            // Add additional quantity to destination warehouse
            const destInventory = await db.inventory.findFirst({
              where: {
                productId: originalItem.productId,
                warehouseId: transfer.destinationWarehouseId
              }
            });

            if (destInventory) {
              await db.inventory.update({
                where: {
                  id: destInventory.id
                },
                data: {
                  quantity: destInventory.quantity + quantityDifference
                }
              });
            } else {
              // This shouldn't happen normally, but just in case
              await db.inventory.create({
                data: {
                  productId: originalItem.productId,
                  warehouseId: transfer.destinationWarehouseId,
                  quantity: quantityDifference,
                  costPrice: 0
                }
              });
            }
          } else { // quantityDifference < 0, meaning quantity decreased
            // Add the reduced quantity back to source warehouse
            const sourceInventory = await db.inventory.findFirst({
              where: {
                productId: originalItem.productId,
                warehouseId: transfer.sourceWarehouseId
              }
            });

            if (sourceInventory) {
              await db.inventory.update({
                where: {
                  id: sourceInventory.id
                },
                data: {
                  quantity: sourceInventory.quantity - quantityDifference // Note: - negative = add
                }
              });
            } else {
              // This shouldn't happen normally, but just in case
              await db.inventory.create({
                data: {
                  productId: originalItem.productId,
                  warehouseId: transfer.sourceWarehouseId,
                  quantity: -quantityDifference,
                  costPrice: 0
                }
              });
            }

            // Reduce the quantity from destination warehouse
            const destInventory = await db.inventory.findFirst({
              where: {
                productId: originalItem.productId,
                warehouseId: transfer.destinationWarehouseId
              }
            });

            if (destInventory) {
              const newQuantity = destInventory.quantity + quantityDifference; // Note: + negative = subtract

              if (newQuantity <= 0) {
                // If new quantity would be zero or negative, check if this is allowed
                // For now, we'll allow it but set to zero
                await db.inventory.update({
                  where: {
                    id: destInventory.id
                  },
                  data: {
                    quantity: 0
                  }
                });
              } else {
                await db.inventory.update({
                  where: {
                    id: destInventory.id
                  },
                  data: {
                    quantity: newQuantity
                  }
                });
              }
            }
          }

          // Update the transfer item quantity
          await db.inventoryTransferItem.update({
            where: {
              id: originalItem.id
            },
            data: {
              quantity: updatedItem.quantity
            }
          });
        }
      }
    }

    // Update transfer notes if provided
    if (data.notes !== undefined) {
      await db.inventoryTransfer.update({
        where: {
          id
        },
        data: {
          notes: data.notes,
          updatedAt: new Date()
        }
      });
    }

    // Get updated transfer
    const updatedTransfer = await db.inventoryTransfer.findUnique({
      where: {
        id,
      },
      include: {
        sourceWarehouse: true,
        destinationWarehouse: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true
              }
            }
          }
        }
      },
    });

    // Format the response
    const formattedTransfer = {
      id: updatedTransfer!.id,
      referenceNumber: updatedTransfer!.referenceNumber,
      date: updatedTransfer!.date,
      sourceWarehouse: {
        id: updatedTransfer!.sourceWarehouse.id,
        name: updatedTransfer!.sourceWarehouse.name
      },
      destinationWarehouse: {
        id: updatedTransfer!.destinationWarehouse.id,
        name: updatedTransfer!.destinationWarehouse.name
      },
      user: updatedTransfer!.user,
      notes: updatedTransfer!.notes,
      items: updatedTransfer!.items.map(item => ({
        id: item.id,
        product: {
          id: item.product.id,
          name: item.product.name,
          category: item.product.category.name
        },
        quantity: item.quantity
      })),
      createdAt: updatedTransfer!.createdAt,
      updatedAt: updatedTransfer!.updatedAt
    };

    return NextResponse.json(formattedTransfer);
  } catch (error) {
    console.error("Error updating inventory transfer:", error);
    return NextResponse.json(
      { error: "Failed to update inventory transfer" },
      { status: 500 }
    );
  }
}

// DELETE /api/inventory/transfers/[id] - Delete a specific inventory transfer
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to delete inventory
    const hasDeletePermission = await hasPermission("delete_products") ||
                               session.user.role === "ADMIN";
    if (!hasDeletePermission) {
      return NextResponse.json(
        { error: "You don't have permission to delete inventory transfers" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Check if transfer exists
    const transfer = await db.inventoryTransfer.findUnique({
      where: {
        id,
      },
      include: {
        items: true
      }
    });

    if (!transfer) {
      return NextResponse.json(
        { error: "Inventory transfer not found" },
        { status: 404 }
      );
    }

    // Reverse the inventory changes
    for (const item of transfer.items) {
      // Add quantity back to source warehouse
      const sourceInventory = await db.inventory.findFirst({
        where: {
          productId: item.productId,
          warehouseId: transfer.sourceWarehouseId
        }
      });

      if (sourceInventory) {
        await db.inventory.update({
          where: {
            id: sourceInventory.id
          },
          data: {
            quantity: sourceInventory.quantity + item.quantity
          }
        });
      } else {
        // Create new inventory entry if it doesn't exist
        await db.inventory.create({
          data: {
            productId: item.productId,
            warehouseId: transfer.sourceWarehouseId,
            quantity: item.quantity,
            costPrice: 0 // We don't know the original cost price
          }
        });
      }

      // Reduce quantity from destination warehouse
      const destInventory = await db.inventory.findFirst({
        where: {
          productId: item.productId,
          warehouseId: transfer.destinationWarehouseId
        }
      });

      if (destInventory) {
        const newQuantity = destInventory.quantity - item.quantity;

        if (newQuantity <= 0) {
          // Delete inventory entry if quantity is zero or negative
          await db.inventory.delete({
            where: {
              id: destInventory.id
            }
          });
        } else {
          // Update quantity
          await db.inventory.update({
            where: {
              id: destInventory.id
            },
            data: {
              quantity: newQuantity
            }
          });
        }
      }
    }

    // Delete the transfer
    await db.inventoryTransfer.delete({
      where: {
        id
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting inventory transfer:", error);
    return NextResponse.json(
      { error: "Failed to delete inventory transfer" },
      { status: 500 }
    );
  }
}
