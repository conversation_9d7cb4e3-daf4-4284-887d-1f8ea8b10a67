generator client {
  provider = "prisma-client-js"
}

generator ts_node {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String          @id @default(uuid())
  email       String          @unique
  name        String
  password    String
  role        Role            @default(EMPLOYEE)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  branchId    String?
  isActive    Boolean         @default(true)
  purchases   Purchase[]
  sales       Sale[]
  branch      Branch?         @relation(fields: [branchId], references: [id])
  warehouses  UserWarehouse[]
  permissions Permission[]    @relation("PermissionToUser")
}

model UserWarehouse {
  id          String    @id @default(uuid())
  userId      String
  warehouseId String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)

  @@unique([userId, warehouseId])
}

model Permission {
  id          String   @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]   @relation("PermissionToUser")
}

model Branch {
  id         String      @id @default(uuid())
  name       String
  address    String
  phone      String
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  code       String      @unique
  isActive   Boolean     @default(true)
  purchases  Purchase[]
  sales      Sale[]
  users      User[]
  warehouses Warehouse[]
}

model Warehouse {
  id        String          @id @default(uuid())
  name      String
  branchId  String
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  isActive  Boolean         @default(true)
  inventory Inventory[]
  users     UserWarehouse[]
  branch    Branch          @relation(fields: [branchId], references: [id])
}

model Product {
  id             String          @id @default(uuid())
  name           String
  description    String?
  basePrice      Float
  categoryId     String
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  componentType  String?
  isComponent    Boolean         @default(false)
  isCustomizable Boolean         @default(false)
  costPrice      Float           @default(0)
  inventory      Inventory[]
  category       Category        @relation(fields: [categoryId], references: [id])
  purchaseItems  PurchaseItem[]
  saleItems      SaleItem[]
  specifications Specification[]
}

model Category {
  id          String    @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Specification {
  id        String   @id @default(uuid())
  name      String
  value     String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id])
}

model Inventory {
  id          String    @id @default(uuid())
  productId   String
  warehouseId String
  quantity    Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  costPrice   Float     @default(0)
  product     Product   @relation(fields: [productId], references: [id])
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])
}

model Sale {
  id             String        @id @default(uuid())
  userId         String
  branchId       String
  date           DateTime      @default(now())
  status         SaleStatus    @default(PENDING)
  totalAmount    Float
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  applyTax       Boolean       @default(false)
  currency       String        @default("EGP")
  discountAmount Float         @default(0)
  notes          String?
  subtotalAmount Float
  taxAmount      Float         @default(0)
  taxRate        Float         @default(0)
  invoiceNumber  String        @unique
  paymentMethod  PaymentMethod @default(CASH)
  paymentStatus  PaymentStatus @default(UNPAID)
  contactId      String
  branch         Branch        @relation(fields: [branchId], references: [id])
  contact        Contact       @relation(fields: [contactId], references: [id])
  user           User          @relation(fields: [userId], references: [id])
  items          SaleItem[]
}

model SaleItem {
  id             String              @id @default(uuid())
  saleId         String
  productId      String
  quantity       Int
  unitPrice      Float
  totalPrice     Float
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  isCustomized   Boolean             @default(false)
  specifications String?
  product        Product             @relation(fields: [productId], references: [id])
  sale           Sale                @relation(fields: [saleId], references: [id])
  components     SaleItemComponent[]
}

model SaleItemComponent {
  id            String   @id @default(uuid())
  saleItemId    String
  componentId   String
  componentName String
  componentType String
  quantity      Int
  totalQuantity Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  saleItem      SaleItem @relation(fields: [saleItemId], references: [id])
}

model Purchase {
  id             String         @id @default(uuid())
  userId         String
  branchId       String
  date           DateTime       @default(now())
  status         PurchaseStatus @default(PENDING)
  totalAmount    Float
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  contactId      String
  currency       String         @default("EGP")
  discountAmount Float          @default(0)
  invoiceNumber  String         @unique
  notes          String?
  paymentMethod  PaymentMethod  @default(CASH)
  paymentStatus  PaymentStatus  @default(UNPAID)
  subtotalAmount Float
  taxAmount      Float          @default(0)
  branch         Branch         @relation(fields: [branchId], references: [id])
  contact        Contact        @relation(fields: [contactId], references: [id])
  user           User           @relation(fields: [userId], references: [id])
  items          PurchaseItem[]
}

model PurchaseItem {
  id         String   @id @default(uuid())
  purchaseId String
  productId  String
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  product    Product  @relation(fields: [productId], references: [id])
  purchase   Purchase @relation(fields: [purchaseId], references: [id])
}

model Contact {
  id                 String     @id @default(uuid())
  name               String
  phone              String     @unique
  address            String?
  isCustomer         Boolean    @default(false)
  isSupplier         Boolean    @default(false)
  balance            Float      @default(0)
  isActive           Boolean    @default(true)
  createdAt          DateTime   @default(now())
  updatedAt          DateTime   @updatedAt
  openingBalance     Float      @default(0)
  openingBalanceDate DateTime   @default(now())
  purchases          Purchase[]
  sales              Sale[]
}

enum Role {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum PaymentMethod {
  CASH
  VODAFONE_CASH
  BANK_TRANSFER
  CREDIT_CARD
  CUSTOMER_ACCOUNT
}

enum PaymentStatus {
  PAID
  UNPAID
  PARTIALLY_PAID
}

enum SaleStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum PurchaseStatus {
  PENDING
  COMPLETED
  CANCELLED
}
