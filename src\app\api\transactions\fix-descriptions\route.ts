import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

// POST /api/transactions/fix-descriptions - Fix transaction descriptions
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find all purchase transactions with old description format
    const purchaseTransactions = await prisma.transaction.findMany({
      where: {
        referenceType: 'PURCHASE',
        description: {
          contains: '(Contact:',
        },
      },
    });

    console.log(`Found ${purchaseTransactions.length} purchase transactions to update`);

    // Update purchase transactions
    const updatedTransactions = [];
    for (const transaction of purchaseTransactions) {
      // Extract invoice number from reference
      const invoiceNumber = transaction.reference;
      
      if (!invoiceNumber) {
        console.log(`Skipping transaction ${transaction.id} - No invoice number`);
        continue;
      }

      // Update description
      const updatedTransaction = await prisma.transaction.update({
        where: {
          id: transaction.id,
        },
        data: {
          description: `Purchase Invoice #${invoiceNumber}`,
        },
      });

      updatedTransactions.push(updatedTransaction);
    }

    // Find all inventory transactions for purchases
    const inventoryTransactions = await prisma.transaction.findMany({
      where: {
        type: 'DEBIT',
        description: {
          contains: 'Purchase ',
          not: {
            contains: 'Invoice #',
          },
        },
      },
    });

    console.log(`Found ${inventoryTransactions.length} inventory transactions to update`);

    // Update inventory transactions
    for (const transaction of inventoryTransactions) {
      // Extract invoice number from description
      const match = transaction.description.match(/Purchase ([A-Z]-[A-Z]-\d+)/);
      if (!match) {
        console.log(`Skipping transaction ${transaction.id} - Cannot extract invoice number`);
        continue;
      }

      const invoiceNumber = match[1];

      // Update description
      const updatedTransaction = await prisma.transaction.update({
        where: {
          id: transaction.id,
        },
        data: {
          description: `Purchase Invoice #${invoiceNumber}`,
        },
      });

      updatedTransactions.push(updatedTransaction);
    }

    // Find all payment transactions for purchases
    const paymentTransactions = await prisma.transaction.findMany({
      where: {
        type: 'CREDIT',
        description: {
          contains: 'Purchase ',
          not: {
            contains: 'Invoice #',
          },
        },
      },
    });

    console.log(`Found ${paymentTransactions.length} payment transactions to update`);

    // Update payment transactions
    for (const transaction of paymentTransactions) {
      // Extract invoice number and payment method from description
      const match = transaction.description.match(/Purchase ([A-Z]-[A-Z]-\d+) \((.+)\)/);
      if (!match) {
        console.log(`Skipping transaction ${transaction.id} - Cannot extract invoice number`);
        continue;
      }

      const invoiceNumber = match[1];
      const paymentMethod = match[2];

      // Update description
      const updatedTransaction = await prisma.transaction.update({
        where: {
          id: transaction.id,
        },
        data: {
          description: `Payment for Invoice #${invoiceNumber} (${paymentMethod})`,
        },
      });

      updatedTransactions.push(updatedTransaction);
    }

    return NextResponse.json({
      success: true,
      updatedCount: updatedTransactions.length,
    });
  } catch (error) {
    console.error('Error fixing transaction descriptions:', error);
    return NextResponse.json(
      { error: 'Failed to fix transaction descriptions' },
      { status: 500 }
    );
  }
}
