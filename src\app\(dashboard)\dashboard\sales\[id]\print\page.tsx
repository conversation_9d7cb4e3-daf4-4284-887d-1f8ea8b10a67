"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Printer } from "lucide-react";
import { PrintableDocument } from "@/components/shared/PrintableDocument";
import { PrintButton } from "@/components/shared/PrintButton";
import { formatCurrency, formatDate } from "@/lib/utils";

export default function SalePrintPage() {
  const params = useParams();
  const router = useRouter();
  const saleId = params.id as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sale, setSale] = useState<any>(null);
  const [companyInfo, setCompanyInfo] = useState<any>({
    name: "VERO Company",
    address: "",
    phone: "",
    email: "",
    website: "",
    taxNumber: ""
  });

  // Fetch sale details
  useEffect(() => {
    const fetchSaleDetails = async () => {
      setIsLoading(true);
      try {
        // Fetch sale data
        const response = await fetch(`/api/sales/${saleId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch sale details");
        }
        const data = await response.json();
        setSale(data);
        
        // Fetch company info
        try {
          const settingsResponse = await fetch("/api/settings/company");
          if (settingsResponse.ok) {
            const settingsData = await settingsResponse.json();
            if (settingsData) {
              setCompanyInfo(settingsData);
            }
          }
        } catch (error) {
          // Use default company info if settings can't be fetched
          console.error("Error fetching company info:", error);
        }
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSaleDetails();
  }, [saleId]);

  // Prepare print data
  const preparePrintData = () => {
    if (!sale) return null;
    
    // Generate items HTML
    const itemsHtml = sale.items.map((item: any) => `
      <tr>
        <td>${item.product?.name || 'Unknown Product'}</td>
        <td style="text-align: right;">${item.quantity}</td>
        <td style="text-align: right;">${formatCurrency(item.unitPrice, sale.currency)}</td>
        <td style="text-align: right;">${formatCurrency(item.totalPrice, sale.currency)}</td>
      </tr>
    `).join('');
    
    // Generate items table
    const itemsTable = `
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Item</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Qty</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Price</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${itemsHtml}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Subtotal:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(sale.subtotalAmount, sale.currency)}</td>
          </tr>
          ${sale.taxAmount > 0 ? `
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Tax:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(sale.taxAmount, sale.currency)}</td>
          </tr>
          ` : ''}
          ${sale.discountAmount > 0 ? `
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Discount:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(sale.discountAmount, sale.currency)}</td>
          </tr>
          ` : ''}
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Total:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(sale.totalAmount, sale.currency)}</td>
          </tr>
        </tfoot>
      </table>
    `;
    
    // Prepare customer details
    const customerDetails = sale.contact ? `
      ${sale.contact.name || 'Unknown Customer'}<br>
      ${sale.contact.phone || ''}<br>
      ${sale.contact.address || ''}
    ` : 'Unknown Customer';
    
    // Prepare payment method
    const paymentMethod = sale.paymentMethod || 'Not specified';
    
    // Prepare print data
    return {
      id: sale.id,
      company_name: companyInfo.name,
      invoice_number: sale.invoiceNumber,
      date: formatDate(sale.date),
      customer_details: customerDetails,
      invoice_items: itemsTable,
      subtotal: formatCurrency(sale.subtotalAmount, sale.currency),
      tax: formatCurrency(sale.taxAmount, sale.currency),
      discount: formatCurrency(sale.discountAmount, sale.currency),
      total: formatCurrency(sale.totalAmount, sale.currency),
      payment_method: paymentMethod,
      payment_status: sale.paymentStatus,
      barcode: sale.invoiceNumber,
      branch: sale.branch?.name || 'Main Branch',
      user: sale.user?.name || 'Unknown User',
      notes: sale.notes || ''
    };
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          {error}
        </div>
        <div className="mt-4">
          <Link
            href={`/dashboard/sales/${saleId}`}
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Sale Details
          </Link>
        </div>
      </div>
    );
  }

  const printData = preparePrintData();

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6 print:hidden">
        <Link
          href={`/dashboard/sales/${saleId}`}
          className="flex items-center text-indigo-600 hover:text-indigo-900"
        >
          <ArrowLeft className="h-5 w-5 mr-1" />
          Back to Sale Details
        </Link>
        
        {printData && (
          <PrintButton
            documentType="invoice"
            documentData={printData}
          />
        )}
      </div>

      {printData && (
        <PrintableDocument
          documentType="invoice"
          documentData={printData}
          showControls={true}
        />
      )}
    </div>
  );
}
