import React from "react";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import DiscountBadge from "../../discounts/components/DiscountBadge";

interface Discount {
  id: string;
  name: string;
  type: "PERCENTAGE" | "FIXED_AMOUNT";
  scope: "INVOICE" | "ITEM" | "CUSTOMER";
  value: number;
  amount: number;
}

interface AppliedDiscountsListProps {
  discounts: Discount[];
  onRemoveDiscount?: (id: string) => void;
  showRemoveButton?: boolean;
  maxHeight?: string;
  emptyMessage?: string;
  title?: string;
  subtotal?: number;
}

export default function AppliedDiscountsList({
  discounts,
  onRemoveDiscount,
  showRemoveButton = true,
  maxHeight = "200px",
  emptyMessage = "No discounts applied",
  title = "Applied Discounts",
  subtotal = 0
}: AppliedDiscountsListProps) {
  // Calculate total discount amount
  const totalDiscountAmount = discounts.reduce((sum, discount) => sum + discount.amount, 0);
  
  // Calculate discount percentage of subtotal
  const discountPercentage = subtotal > 0 
    ? (totalDiscountAmount / subtotal) * 100 
    : 0;
  
  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        {subtotal > 0 && (
          <div className="text-xs text-gray-500">
            Total: {totalDiscountAmount.toFixed(2)} ج.م 
            {discountPercentage > 0 && (
              <span className="ml-1 text-green-600">
                ({discountPercentage.toFixed(1)}% off)
              </span>
            )}
          </div>
        )}
      </div>
      
      <ScrollArea className={`w-full rounded-md border p-2`} style={{ maxHeight }}>
        {discounts.length === 0 ? (
          <div className="py-4 text-center text-sm text-gray-500">
            {emptyMessage}
          </div>
        ) : (
          <div className="space-y-2">
            {discounts.map((discount) => (
              <div key={discount.id} className="flex items-center justify-between py-1">
                <div className="flex items-center space-x-2">
                  <DiscountBadge 
                    type={discount.type} 
                    scope={discount.scope} 
                    value={discount.value}
                    size="sm"
                    showValue={true}
                  />
                  <span className="text-sm font-medium">{discount.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">
                    {discount.amount.toFixed(2)} ج.م
                  </span>
                  {showRemoveButton && onRemoveDiscount && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 rounded-full"
                      onClick={() => onRemoveDiscount(discount.id)}
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remove</span>
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
