import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import {
  createPurchaseJournalEntries,
  TransactionType,
  PaymentInfo,
  ensureAccountsExist
} from "@/lib/accounting-new";

// GET /api/purchases - Get all purchases
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view purchases
    const hasViewPermission = await hasPermission("view_purchases");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view purchases" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const branchId = url.searchParams.get("branchId");
    const status = url.searchParams.get("status");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const contactId = url.searchParams.get("contactId");

    // Build filter object
    const filter: any = {};

    if (branchId && branchId !== "all") {
      filter.branchId = branchId;
    }

    if (status && status !== "all") {
      filter.status = status;
    }

    if (contactId) {
      filter.contactId = contactId;
    }

    // Date range filter
    if (startDate && endDate) {
      filter.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      filter.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      filter.date = {
        lte: new Date(endDate),
      };
    }

    // Get purchases from database
    const purchases = await db.purchase.findMany({
      where: filter,
      include: {
        contact: true,
        branch: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    });

    return NextResponse.json(purchases);
  } catch (error) {
    console.error("Error fetching purchases:", error);
    return NextResponse.json(
      { error: "Failed to fetch purchases" },
      { status: 500 }
    );
  }
}

// POST /api/purchases - Create a new purchase
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add purchases
    const hasAddPermission = await hasPermission("add_purchases");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add purchases" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.contactId || !data.branchId || !data.items || data.items.length === 0) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Start a transaction
    const purchase = await db.$transaction(async (tx) => {
      // Get the contact to update balance
      const contact = await tx.contact.findUnique({
        where: { id: data.contactId }
      });

      // Create the purchase
      const newPurchase = await tx.purchase.create({
        data: {
          invoiceNumber: data.invoiceNumber,
          contactId: data.contactId,
          branchId: data.branchId,
          userId: session.user.id,
          date: data.date ? new Date(data.date) : new Date(),
          dueDate: data.dueDate ? new Date(data.dueDate) : null,
          status: data.status || "PENDING",
          paymentMethod: data.paymentMethod || "CASH",
          paymentStatus: data.paymentStatus || "PAID",
          subtotalAmount: data.subtotal,
          discountAmount: data.discount || 0,
          taxAmount: data.taxAmount || 0,
          totalAmount: data.total,
          currency: data.currency || "EGP",
          notes: data.notes,
        },
      });

      // Update contact balance if payment status is not PAID
      // For PAID purchases, the balance is handled by journal entries
      if (contact && data.paymentStatus !== "PAID") {
        // If payment status is UNPAID or PARTIALLY_PAID, increase supplier balance
        await tx.contact.update({
          where: { id: data.contactId },
          data: {
            balance: {
              increment: data.total - (data.payments?.reduce((sum, p) => sum + p.amount, 0) || 0)
            }
          }
        });
      }

      // Create purchase items and update inventory
      for (const item of data.items) {
        // Create purchase item
        const purchaseItem = await tx.purchaseItem.create({
          data: {
            purchaseId: newPurchase.id,
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.total,
          },
        });

        // Update inventory
        const inventory = await tx.inventory.findFirst({
          where: {
            productId: item.productId,
            warehouseId: item.warehouseId,
          },
        });

        if (inventory) {
          // Calculate weighted average cost
          const oldTotalValue = inventory.quantity * inventory.costPrice;
          const newItemValue = item.quantity * item.unitPrice;
          const newTotalQuantity = inventory.quantity + item.quantity;
          const weightedAverageCost = newTotalQuantity > 0
            ? (oldTotalValue + newItemValue) / newTotalQuantity
            : item.unitPrice;

          console.log(`Calculating weighted average cost for product ${item.productId}:`);
          console.log(`- Old inventory: ${inventory.quantity} units at ${inventory.costPrice} = ${oldTotalValue}`);
          console.log(`- New purchase: ${item.quantity} units at ${item.unitPrice} = ${newItemValue}`);
          console.log(`- New total: ${newTotalQuantity} units at ${weightedAverageCost}`);

          // Update existing inventory with weighted average cost
          await tx.inventory.update({
            where: {
              id: inventory.id,
            },
            data: {
              quantity: newTotalQuantity,
              costPrice: weightedAverageCost,
            },
          });

          // Also update the product's cost price
          await tx.product.update({
            where: {
              id: item.productId,
            },
            data: {
              costPrice: weightedAverageCost,
            },
          });
        } else {
          // Create new inventory entry
          await tx.inventory.create({
            data: {
              productId: item.productId,
              warehouseId: item.warehouseId,
              quantity: item.quantity,
              costPrice: item.unitPrice,
            },
          });

          // Update the product's cost price
          await tx.product.update({
            where: {
              id: item.productId,
            },
            data: {
              costPrice: item.unitPrice,
            },
          });
        }
      }

      // No accounting transactions are created here anymore
      // They will be created after the transaction is complete using the accounting service

      return newPurchase;
    });

    // Create journal entries for the purchase using the accounting service
    try {
      // Get contact name
      const contact = await db.contact.findUnique({
        where: {
          id: data.contactId,
        },
        select: {
          name: true,
        },
      });

      // Prepare payment data for journal entries
      const payments: PaymentInfo[] = [];

      // If we have payment data in the request
      if (data.payments && Array.isArray(data.payments) && data.payments.length > 0) {
        // Use the payment data from the request
        payments.push(...data.payments);
      } else if (data.paymentMethod && data.paymentStatus === "PAID") {
        // Use the single payment method
        payments.push({
          method: data.paymentMethod,
          amount: data.total,
        });
      } else if (data.paymentMethod === "SUPPLIER_ACCOUNT" || data.paymentStatus === "UNPAID") {
        // Use supplier account
        payments.push({
          method: "SUPPLIER_ACCOUNT",
          amount: data.total,
        });
      }

      // Ensure accounts and journals exist
      await ensureAccountsExist();

      // Only create journal entries if we have valid payment methods
      if (payments.length > 0) {
        await createPurchaseJournalEntries(
          payments,
          data.total,
          data.subtotal,
          {
            transactionType: TransactionType.PURCHASE,
            referenceId: purchase.id,
            referenceNumber: purchase.invoiceNumber,
            description: `Purchase ${purchase.invoiceNumber}`,
            contactId: data.contactId,
            contactName: contact?.name,
            branchId: data.branchId,
            date: new Date(),
          }
        );
      }
    } catch (journalError) {
      console.error("Error creating journal entries:", journalError);
      // Don't fail the purchase creation if journal entries fail
    }

    return NextResponse.json(purchase, { status: 201 });
  } catch (error) {
    console.error("Error creating purchase:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create purchase" },
      { status: 500 }
    );
  }
}
