// Script to set up accounts and payment methods for vouchers
const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');
const prisma = new PrismaClient();

// Default accounts to create
const defaultAccounts = [
  // Asset accounts
  { code: '1000', name: 'Assets', type: 'ASSET', isParent: true },
  { code: '1100', name: 'Cash', type: 'ASSET', parentCode: '1000' },
  { code: '1110', name: 'Cash on Hand', type: 'ASSET', parentCode: '1100' },
  { code: '1120', name: 'Vodafone Cash', type: 'ASSET', parentCode: '1100' },
  { code: '1130', name: 'Bank Account', type: 'ASSET', parentCode: '1100' },
  { code: '1140', name: 'Credit Card', type: 'ASSET', parentCode: '1100' },
  
  { code: '1200', name: 'Accounts Receivable', type: 'ASSET', parentCode: '1000' },
  { code: '1210', name: 'Customer Receivables', type: 'ASSET', parentCode: '1200' },
  
  { code: '1300', name: 'Inventory', type: 'ASSET', parentCode: '1000' },
  { code: '1310', name: 'Merchandise Inventory', type: 'ASSET', parentCode: '1300' },
  
  // Liability accounts
  { code: '2000', name: 'Liabilities', type: 'LIABILITY', isParent: true },
  { code: '2100', name: 'Accounts Payable', type: 'LIABILITY', parentCode: '2000' },
  { code: '2110', name: 'Supplier Payables', type: 'LIABILITY', parentCode: '2100' },
  
  // Equity accounts
  { code: '3000', name: 'Equity', type: 'EQUITY', isParent: true },
  { code: '3100', name: 'Owner\'s Equity', type: 'EQUITY', parentCode: '3000' },
  
  // Revenue accounts
  { code: '4000', name: 'Revenue', type: 'REVENUE', isParent: true },
  { code: '4100', name: 'Sales Revenue', type: 'REVENUE', parentCode: '4000' },
  { code: '4200', name: 'Other Revenue', type: 'REVENUE', parentCode: '4000' },
  
  // Expense accounts
  { code: '5000', name: 'Expenses', type: 'EXPENSE', isParent: true },
  { code: '5100', name: 'Cost of Goods Sold', type: 'EXPENSE', parentCode: '5000' },
  { code: '5200', name: 'Operating Expenses', type: 'EXPENSE', parentCode: '5000' },
  { code: '5210', name: 'Rent Expense', type: 'EXPENSE', parentCode: '5200' },
  { code: '5220', name: 'Utilities Expense', type: 'EXPENSE', parentCode: '5200' },
  { code: '5230', name: 'Salaries Expense', type: 'EXPENSE', parentCode: '5200' },
  { code: '5240', name: 'Office Supplies', type: 'EXPENSE', parentCode: '5200' },
  { code: '5250', name: 'Miscellaneous Expense', type: 'EXPENSE', parentCode: '5200' },
];

// Default journals to create
const defaultJournals = [
  { code: 'CASH', name: 'Cash Journal', type: 'CASH' },
  { code: 'VFCASH', name: 'Vodafone Cash Journal', type: 'VODAFONE_CASH' },
  { code: 'BANK', name: 'Bank Journal', type: 'BANK_TRANSFER' },
  { code: 'VISA', name: 'Credit Card Journal', type: 'VISA' },
  { code: 'CUST', name: 'Customer Accounts Journal', type: 'CUSTOMER_ACCOUNT' },
  { code: 'GEN', name: 'General Journal', type: 'GENERAL' },
  { code: 'PAYMENT', name: 'Payment Vouchers Journal', type: 'PAYMENT_VOUCHER' },
  { code: 'RECEIPT', name: 'Receipt Vouchers Journal', type: 'RECEIPT_VOUCHER' },
];

// Payment methods configuration
const paymentMethodsMap = {
  'CASH': {
    name: 'Cash',
    iconName: 'banknote',
    color: '#22c55e', // green
    accountCode: '1110',
    journalCode: 'CASH',
  },
  'VODAFONE_CASH': {
    name: 'Vodafone Cash',
    iconName: 'smartphone',
    color: '#ef4444', // red
    accountCode: '1120',
    journalCode: 'VFCASH',
  },
  'BANK_TRANSFER': {
    name: 'Bank Transfer',
    iconName: 'building-bank',
    color: '#3b82f6', // blue
    accountCode: '1130',
    journalCode: 'BANK',
  },
  'VISA': {
    name: 'Visa',
    iconName: 'credit-card',
    color: '#8b5cf6', // purple
    accountCode: '1140',
    journalCode: 'VISA',
  },
};

async function main() {
  try {
    console.log('Setting up accounts and payment methods for vouchers...');
    
    // Create accounts
    const accountMap = new Map();
    
    // First pass: Create parent accounts
    for (const account of defaultAccounts.filter(a => a.isParent)) {
      let existingAccount = await prisma.account.findFirst({
        where: { code: account.code },
      });
      
      if (!existingAccount) {
        existingAccount = await prisma.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            balance: 0,
            isActive: true,
          },
        });
        console.log(`Created parent account: ${account.name} (${account.code})`);
      } else {
        console.log(`Parent account already exists: ${account.name} (${account.code})`);
      }
      
      accountMap.set(account.code, existingAccount);
    }
    
    // Second pass: Create child accounts
    for (const account of defaultAccounts.filter(a => !a.isParent)) {
      let existingAccount = await prisma.account.findFirst({
        where: { code: account.code },
      });
      
      if (!existingAccount) {
        const parentAccount = accountMap.get(account.parentCode);
        
        existingAccount = await prisma.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            balance: 0,
            isActive: true,
            parentId: parentAccount ? parentAccount.id : null,
          },
        });
        console.log(`Created child account: ${account.name} (${account.code})`);
      } else {
        console.log(`Child account already exists: ${account.name} (${account.code})`);
      }
      
      accountMap.set(account.code, existingAccount);
    }
    
    // Create journals
    const journalMap = new Map();
    
    for (const journal of defaultJournals) {
      let existingJournal = await prisma.journal.findFirst({
        where: { code: journal.code },
      });
      
      if (!existingJournal) {
        existingJournal = await prisma.journal.create({
          data: {
            code: journal.code,
            name: journal.name,
            type: journal.type,
            isActive: true,
          },
        });
        console.log(`Created journal: ${journal.name} (${journal.code})`);
      } else {
        console.log(`Journal already exists: ${journal.name} (${journal.code})`);
      }
      
      journalMap.set(journal.code, existingJournal);
    }
    
    // Set up payment methods
    for (const [code, config] of Object.entries(paymentMethodsMap)) {
      const account = accountMap.get(config.accountCode);
      const journal = journalMap.get(config.journalCode);
      
      if (!account) {
        console.error(`Account not found for payment method ${code}: ${config.accountCode}`);
        continue;
      }
      
      if (!journal) {
        console.error(`Journal not found for payment method ${code}: ${config.journalCode}`);
        continue;
      }
      
      let paymentMethod = await prisma.paymentMethodSettings.findFirst({
        where: { code },
      });
      
      if (paymentMethod) {
        console.log(`Updating payment method: ${code}`);
        await prisma.paymentMethodSettings.update({
          where: { id: paymentMethod.id },
          data: {
            accountId: account.id,
            journalId: journal.id,
            name: config.name,
            iconName: config.iconName,
            color: config.color,
            isActive: true,
          },
        });
      } else {
        console.log(`Creating payment method: ${code}`);
        await prisma.paymentMethodSettings.create({
          data: {
            code,
            name: config.name,
            accountId: account.id,
            journalId: journal.id,
            iconName: config.iconName,
            color: config.color,
            isActive: true,
            sequence: Object.keys(paymentMethodsMap).indexOf(code) + 1,
          },
        });
      }
    }
    
    // Set up payment voucher and receipt voucher journals
    const paymentVoucherJournal = journalMap.get('PAYMENT');
    const receiptVoucherJournal = journalMap.get('RECEIPT');
    
    if (paymentVoucherJournal) {
      console.log('Setting up payment voucher journal...');
      // No need to link to a payment method
    }
    
    if (receiptVoucherJournal) {
      console.log('Setting up receipt voucher journal...');
      // No need to link to a payment method
    }
    
    console.log('Setup completed successfully!');
  } catch (error) {
    console.error('Error setting up accounts and payment methods:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
