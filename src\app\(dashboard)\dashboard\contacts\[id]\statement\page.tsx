"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { use } from "react";
import Head from "next/head";

interface Contact {
  id: string;
  name: string;
  phone: string;
  address?: string;
  isCustomer: boolean;
  isSupplier: boolean;
  isActive: boolean;
  balance: number;
  openingBalance: number;
  openingBalanceDate: string;
}

interface StatementItem {
  date: string;
  description: string;
  reference: string;
  debit: number;
  credit: number;
  balance: number;
  type: string;
  paymentMethod?: string;
}

interface StatementTotals {
  debit: number;
  credit: number;
  balance: number;
}

interface StatementData {
  contact: Contact;
  statement: StatementItem[];
  totals: StatementTotals;
}

export default function ContactStatementPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id } = use(params);

  const [statementData, setStatementData] = useState<StatementData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");

  // Fetch statement data
  const fetchStatementData = async () => {
    setIsLoading(true);
    try {
      // Build URL with query parameters
      let url = `/api/contacts/${id}/statement`;
      const params = new URLSearchParams();

      if (startDate) {
        params.append("startDate", startDate);
      }

      if (endDate) {
        params.append("endDate", endDate);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch statement");
      }

      const data = await response.json();
      setStatementData(data);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStatementData();
  }, [id]);

  const handleDateFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === "startDate") {
      setStartDate(value);
    } else if (name === "endDate") {
      setEndDate(value);
    }
  };

  const handleFilterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchStatementData();
  };

  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-500">Loading statement...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 p-4 rounded-md text-red-700 mb-4">
          {error}
        </div>
        <button
          onClick={() => router.push(`/dashboard/contacts/${id}`)}
          className="text-indigo-600 hover:text-indigo-900"
        >
          Back to Contact Details
        </button>
      </div>
    );
  }

  if (!statementData) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-500">No statement data available</p>
          <button
            onClick={() => router.push(`/dashboard/contacts/${id}`)}
            className="mt-4 text-indigo-600 hover:text-indigo-900"
          >
            Back to Contact Details
          </button>
        </div>
      </div>
    );
  }

  const { contact, statement, totals } = statementData;

  return (
    <>
      <Head>
        <style>
          {`
            @import url('/src/app/(dashboard)/dashboard/contacts/[id]/statement/print.css');
          `}
        </style>
      </Head>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6 print-hidden">
          <h1 className="text-2xl font-bold text-gray-900">Account Statement</h1>
          <div className="space-x-2">
            <button
              onClick={() => router.push(`/dashboard/contacts/${id}`)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium"
            >
              Back
            </button>
            <button
              onClick={handlePrint}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium"
            >
              Print
            </button>
          </div>
        </div>

        {/* Print Header - Only visible when printing */}
        <div className="hidden print:block print-header">
          <h1>ACCOUNT STATEMENT</h1>
          <div className="company-name">Vero ERP System</div>
          <div className="company-info">
            <p>Statement Date: {new Date().toLocaleDateString()}</p>
          </div>
        </div>

      {/* Screen version */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6 print-hidden">
        <div className="p-6 border-b">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-xl font-bold mb-4 text-gray-900">Contact Information</h2>
              <div className="space-y-2">
                <p><span className="font-semibold text-gray-700">Name:</span> <span className="font-medium text-gray-900">{contact.name}</span></p>
                <p><span className="font-semibold text-gray-700">Phone:</span> <span className="font-medium text-gray-900">{contact.phone}</span></p>
                {contact.address && (
                  <p><span className="font-semibold text-gray-700">Address:</span> <span className="font-medium text-gray-900">{contact.address}</span></p>
                )}
                <p>
                  <span className="font-semibold text-gray-700">Type:</span>{" "}
                  <span className="font-medium text-gray-900">
                    {contact.isCustomer && contact.isSupplier
                      ? "Customer & Supplier"
                      : contact.isCustomer
                      ? "Customer"
                      : "Supplier"}
                  </span>
                </p>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-bold mb-4 text-gray-900">Statement Summary</h2>
              <div className="space-y-2">
                <p><span className="font-semibold text-gray-700">Current Balance:</span> <span className={`font-semibold ${totals.balance < 0 ? "text-red-700" : "text-green-700"}`}>{totals.balance.toFixed(2)} EGP</span></p>
                <p><span className="font-semibold text-gray-700">Total Debits:</span> <span className="font-medium text-gray-900">{totals.debit.toFixed(2)} EGP</span></p>
                <p><span className="font-semibold text-gray-700">Total Credits:</span> <span className="font-medium text-gray-900">{totals.credit.toFixed(2)} EGP</span></p>
                <p>
                  <span className="font-semibold text-gray-700">Statement Period:</span>{" "}
                  <span className="font-medium text-gray-900">
                    {startDate ? format(new Date(startDate), "dd/MM/yyyy") : "All"} to{" "}
                    {endDate ? format(new Date(endDate), "dd/MM/yyyy") : "Present"}
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Print version */}
      <div className="hidden print:block print-info">
        <div className="print-info-section">
          <h2>Contact Information</h2>
          <p><strong>Name:</strong> {contact.name}</p>
          <p><strong>Phone:</strong> {contact.phone}</p>
          {contact.address && (
            <p><strong>Address:</strong> {contact.address}</p>
          )}
          <p>
            <strong>Type:</strong>{" "}
            {contact.isCustomer && contact.isSupplier
              ? "Customer & Supplier"
              : contact.isCustomer
              ? "Customer"
              : "Supplier"}
          </p>
        </div>

        <div className="print-info-section">
          <h2>Statement Summary</h2>
          <p><strong>Current Balance:</strong> {totals.balance.toFixed(2)} EGP</p>
          <p><strong>Total Debits:</strong> {totals.debit.toFixed(2)} EGP</p>
          <p><strong>Total Credits:</strong> {totals.credit.toFixed(2)} EGP</p>
          <p>
            <strong>Statement Period:</strong>{" "}
            {startDate ? format(new Date(startDate), "dd/MM/yyyy") : "All"} to{" "}
            {endDate ? format(new Date(endDate), "dd/MM/yyyy") : "Present"}
          </p>
        </div>
      </div>

        <div className="p-4 border-b print-hidden">
          <form onSubmit={handleFilterSubmit} className="flex flex-wrap gap-4 items-end">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                Start Date
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={startDate}
                onChange={handleDateFilterChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                End Date
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={endDate}
                onChange={handleDateFilterChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <button
                type="submit"
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                Apply Filter
              </button>
            </div>
          </form>
        </div>

        {/* Screen version of the table */}
        <div className="overflow-x-auto print-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Reference
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Debit
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Credit
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Balance
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {statement.map((item, index) => (
                <tr key={index} className={item.type === "OPENING_BALANCE" ? "bg-gray-50" : ""}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">
                    {format(new Date(item.date), "dd/MM/yyyy")}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.description}
                    {item.paymentMethod && (
                      <span className="ml-2 text-xs font-medium text-gray-700">
                        ({item.paymentMethod.replace("_", " ")})
                      </span>
                    )}
                    {item.type === "SALE" && item.paymentStatus && (
                      <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        item.paymentStatus === "PAID"
                          ? "bg-green-100 text-green-800"
                          : item.paymentStatus === "PARTIALLY_PAID"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}>
                        {item.paymentStatus.replace("_", " ")}
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">
                    {item.reference || "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900">
                    {item.debit > 0 ? item.debit.toFixed(2) : "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900">
                    {item.credit > 0 ? item.credit.toFixed(2) : "-"}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-bold ${
                    item.balance < 0 ? "text-red-700" : "text-green-700"
                  }`}>
                    {item.balance.toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot className="bg-gray-50">
              <tr>
                <td colSpan={3} className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                  Totals
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-bold text-gray-900">
                  {totals.debit.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-bold text-gray-900">
                  {totals.credit.toFixed(2)}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-bold ${
                  totals.balance < 0 ? "text-red-700" : "text-green-700"
                }`}>
                  {totals.balance.toFixed(2)}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>

        {/* Print version of the table */}
        <div className="hidden print:block no-break">
          <table className="print-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Description</th>
                <th>Reference</th>
                <th className="amount">Debit</th>
                <th className="amount">Credit</th>
                <th className="amount">Balance</th>
              </tr>
            </thead>
            <tbody>
              {statement.map((item, index) => (
                <tr key={index} className={item.type === "OPENING_BALANCE" ? "bg-gray-50" : ""}>
                  <td>{format(new Date(item.date), "dd/MM/yyyy")}</td>
                  <td>
                    {item.description}
                    {item.paymentMethod && ` (${item.paymentMethod.replace("_", " ")})`}
                    {item.type === "SALE" && item.paymentStatus && ` [${item.paymentStatus.replace("_", " ")}]`}
                  </td>
                  <td>{item.reference || "-"}</td>
                  <td className="amount">{item.debit > 0 ? item.debit.toFixed(2) : "-"}</td>
                  <td className="amount">{item.credit > 0 ? item.credit.toFixed(2) : "-"}</td>
                  <td className="amount">{item.balance.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="total-row">
                <td colSpan={3}>Totals</td>
                <td className="amount">{totals.debit.toFixed(2)}</td>
                <td className="amount">{totals.credit.toFixed(2)}</td>
                <td className="amount">{totals.balance.toFixed(2)}</td>
              </tr>
            </tfoot>
          </table>
        </div>

        {/* Print footer */}
        <div className="hidden print:block print-footer">
          <p>This statement was generated on {new Date().toLocaleDateString()} by Vero ERP System.</p>
          <p>For any inquiries, please contact support.</p>
        </div>
      </div>
    </>
  );
}
