"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { ArrowLeft, Save, Wallet, Building2, CreditCard, Smartphone, Loader2 } from "lucide-react";
import Link from "next/link";

interface PaymentMethod {
  id: string;
  name: string;
  code: string;
}

interface Contact {
  id: string;
  name: string;
  phone?: string;
}

interface Account {
  id: string;
  name: string;
  code: string;
  type: string;
}

export default function NewPaymentVoucherPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [expenseAccounts, setExpenseAccounts] = useState<Account[]>([]);
  
  const [formData, setFormData] = useState({
    amount: "",
    description: "",
    paymentMethodId: "",
    contactId: "",
    expenseAccountId: "",
    date: new Date(),
  });

  // Fetch payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        const response = await fetch("/api/settings/payment-methods");
        if (response.ok) {
          const data = await response.json();
          setPaymentMethods(data.filter((method: PaymentMethod) => 
            ["CASH", "VODAFONE_CASH", "BANK_TRANSFER", "VISA"].includes(method.code)
          ));
        }
      } catch (error) {
        console.error("Error fetching payment methods:", error);
      }
    };

    const fetchContacts = async () => {
      try {
        const response = await fetch("/api/contacts");
        if (response.ok) {
          const data = await response.json();
          setContacts(data);
        }
      } catch (error) {
        console.error("Error fetching contacts:", error);
      }
    };

    const fetchExpenseAccounts = async () => {
      try {
        const response = await fetch("/api/accounting/accounts?type=EXPENSE");
        if (response.ok) {
          const data = await response.json();
          setExpenseAccounts(data);
        }
      } catch (error) {
        console.error("Error fetching expense accounts:", error);
      }
    };

    setIsLoading(true);
    Promise.all([
      fetchPaymentMethods(),
      fetchContacts(),
      fetchExpenseAccounts()
    ]).finally(() => {
      setIsLoading(false);
    });
  }, []);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({ ...prev, date }));
    }
  };

  // Get payment method icon
  const getPaymentMethodIcon = (code: string) => {
    switch (code) {
      case "CASH":
        return <Wallet className="h-5 w-5" />;
      case "VODAFONE_CASH":
        return <Smartphone className="h-5 w-5" />;
      case "BANK_TRANSFER":
        return <Building2 className="h-5 w-5" />;
      case "VISA":
        return <CreditCard className="h-5 w-5" />;
      default:
        return <Wallet className="h-5 w-5" />;
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.amount || !formData.description || !formData.paymentMethodId || !formData.expenseAccountId) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }
    
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/accounting/payment-vouchers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          amount: Number(formData.amount),
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        toast.success("Payment voucher created successfully");
        
        // Reset form
        setFormData({
          amount: "",
          description: "",
          paymentMethodId: "",
          contactId: "",
          expenseAccountId: "",
          date: new Date(),
        });
        
        // Navigate to the voucher details page
        router.push(`/dashboard/accounting/payment-vouchers/${data.data.id}`);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to create payment voucher");
      }
    } catch (error) {
      console.error("Error creating payment voucher:", error);
      toast.error("Failed to create payment voucher");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">إنشاء إذن صرف جديد / New Payment Voucher</h1>
          <p className="text-gray-500">إنشاء إذن صرف جديد لتسجيل المصروفات</p>
        </div>
        <Link href="/dashboard/accounting/payment-vouchers">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
        </Link>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">جاري تحميل البيانات...</span>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>بيانات إذن الصرف / Payment Voucher Details</CardTitle>
              <CardDescription>أدخل بيانات إذن الصرف الجديد</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="amount">المبلغ / Amount <span className="text-red-500">*</span></Label>
                  <Input
                    id="amount"
                    name="amount"
                    type="number"
                    step="0.01"
                    placeholder="أدخل المبلغ"
                    value={formData.amount}
                    onChange={handleChange}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="date">التاريخ / Date</Label>
                  <DatePicker date={formData.date} setDate={handleDateChange} />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="paymentMethod">طريقة الدفع / Payment Method <span className="text-red-500">*</span></Label>
                  <Select
                    value={formData.paymentMethodId}
                    onValueChange={(value) => handleSelectChange("paymentMethodId", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر طريقة الدفع" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.id}>
                          <div className="flex items-center">
                            {getPaymentMethodIcon(method.code)}
                            <span className="ml-2">{method.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="expenseAccount">حساب المصروفات / Expense Account <span className="text-red-500">*</span></Label>
                  <Select
                    value={formData.expenseAccountId}
                    onValueChange={(value) => handleSelectChange("expenseAccountId", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حساب المصروفات" />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseAccounts.map((account) => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="contact">جهة الاتصال / Contact</Label>
                  <Select
                    value={formData.contactId}
                    onValueChange={(value) => handleSelectChange("contactId", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر جهة الاتصال (اختياري)" />
                    </SelectTrigger>
                    <SelectContent>
                      {contacts.map((contact) => (
                        <SelectItem key={contact.id} value={contact.id}>
                          {contact.name} {contact.phone && `(${contact.phone})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Label htmlFor="description">الوصف / Description <span className="text-red-500">*</span></Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="أدخل وصف إذن الصرف"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  required
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Link href="/dashboard/accounting/payment-vouchers">
                <Button variant="outline" type="button">
                  إلغاء
                </Button>
              </Link>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    حفظ إذن الصرف
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </form>
      )}
    </div>
  );
}
