/*
  Warnings:

  - A unique constraint covering the columns `[code]` on the table `Branch` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[invoiceNumber]` on the table `Sale` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `code` to the `Branch` table without a default value. This is not possible if the table is not empty.
  - Added the required column `invoiceNumber` to the `Sale` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "PaymentMethod" AS ENUM ('CASH', 'VODAFONE_CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'CUSTOMER_ACCOUNT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "PaymentStatus" AS ENUM ('PAID', 'UNPAID', 'PARTIALLY_PAID');

-- AlterTable
-- First, add the column as nullable
ALTER TABLE "Branch" ADD COLUMN     "code" TEXT;

-- Update existing records with a default value
UPDATE "Branch" SET "code" = 'A' WHERE "code" IS NULL;

-- Then make the column NOT NULL
ALTER TABLE "Branch" ALTER COLUMN "code" SET NOT NULL;

-- AlterTable
-- First, add the invoiceNumber column as nullable
ALTER TABLE "Sale" ADD COLUMN     "invoiceNumber" TEXT,
ADD COLUMN     "paymentMethod" "PaymentMethod" NOT NULL DEFAULT 'CASH',
ADD COLUMN     "paymentStatus" "PaymentStatus" NOT NULL DEFAULT 'UNPAID';

-- Update existing records with a default value for invoiceNumber
UPDATE "Sale" SET "invoiceNumber" = CONCAT('INV-', id) WHERE "invoiceNumber" IS NULL;

-- Then make the column NOT NULL
ALTER TABLE "Sale" ALTER COLUMN "invoiceNumber" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Branch_code_key" ON "Branch"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Sale_invoiceNumber_key" ON "Sale"("invoiceNumber");
