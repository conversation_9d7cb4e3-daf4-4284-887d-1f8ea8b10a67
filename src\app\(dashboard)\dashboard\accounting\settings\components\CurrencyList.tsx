"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, <PERSON><PERSON><PERSON>, <PERSON>, StarOff } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface Currency {
  id: string;
  code: string;
  name: string;
  symbol: string;
  exchangeRate: number;
  isBaseCurrency: boolean;
  isActive: boolean;
}

interface CurrencyListProps {
  currencies: Currency[];
  onEdit: (currency: Currency) => void;
  isLoading: boolean;
}

export default function CurrencyList({ currencies, onEdit, isLoading }: CurrencyListProps) {
  return (
    <Card>
      <CardContent className="p-0">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Loading currencies...</span>
          </div>
        ) : currencies.length === 0 ? (
          <div className="text-center p-8 text-gray-500">
            No currencies found. Add your first currency to get started.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left font-medium text-gray-500">Code</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-500">Name</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-500">Symbol</th>
                  <th className="px-4 py-3 text-right font-medium text-gray-500">Exchange Rate</th>
                  <th className="px-4 py-3 text-center font-medium text-gray-500">Base</th>
                  <th className="px-4 py-3 text-center font-medium text-gray-500">Status</th>
                  <th className="px-4 py-3 text-right font-medium text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currencies.map((currency) => (
                  <tr key={currency.id} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-3 font-medium">{currency.code}</td>
                    <td className="px-4 py-3">{currency.name}</td>
                    <td className="px-4 py-3">{currency.symbol}</td>
                    <td className="px-4 py-3 text-right">{currency.exchangeRate.toFixed(4)}</td>
                    <td className="px-4 py-3 text-center">
                      {currency.isBaseCurrency ? (
                        <Star className="h-5 w-5 text-yellow-500 inline" />
                      ) : (
                        <StarOff className="h-5 w-5 text-gray-300 inline" />
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      <Badge variant={currency.isActive ? "success" : "destructive"}>
                        {currency.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </td>
                    <td className="px-4 py-3 text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(currency)}
                      >
                        <Pencil className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
