import * as React from "react";

interface TabsProps extends React.HTMLAttributes<HTMLDivElement> {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
}

const Tabs = React.forwardRef<HTMLDivElement, TabsProps>(
  ({ className, defaultValue, value, onValueChange, ...props }, ref) => {
    const [selectedTab, setSelectedTab] = React.useState<string>(defaultValue || "");

    React.useEffect(() => {
      if (value !== undefined) {
        setSelectedTab(value);
      }
    }, [value]);

    const handleTabChange = (tabValue: string) => {
      setSelectedTab(tabValue);
      if (onValueChange) {
        onValueChange(tabValue);
      }
    };

    // Create a ref to store the div element
    const divRef = React.useRef<HTMLDivElement>(null);

    // Combine the forwarded ref with our local ref
    React.useImperativeHandle(ref, () => divRef.current!);

    // Set up the onValueChange function on the div element
    React.useEffect(() => {
      if (divRef.current) {
        (divRef.current as any).__onValueChange = handleTabChange;
      }
    }, [handleTabChange]);

    return (
      <div
        ref={divRef}
        className={`tabs ${className || ""}`}
        data-selected-tab={selectedTab}
        {...props}
      />
    );
  }
);
Tabs.displayName = "Tabs";

interface TabsListProps extends React.HTMLAttributes<HTMLDivElement> {}

const TabsList = React.forwardRef<HTMLDivElement, TabsListProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={`inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500 ${className || ""}`}
      {...props}
    />
  )
);
TabsList.displayName = "TabsList";

interface TabsTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  value: string;
}

const TabsTrigger = React.forwardRef<HTMLButtonElement, TabsTriggerProps>(
  ({ className, value, ...props }, ref) => {
    const [isActive, setIsActive] = React.useState(false);

    // Create a ref to store the button element
    const buttonRef = React.useRef<HTMLButtonElement>(null);

    // Combine the forwarded ref with our local ref
    React.useImperativeHandle(ref, () => buttonRef.current!);

    React.useEffect(() => {
      if (buttonRef.current) {
        const tabs = buttonRef.current.closest(".tabs");
        if (tabs) {
          const selectedTab = tabs.getAttribute("data-selected-tab");
          setIsActive(selectedTab === value);

          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (
                mutation.type === "attributes" &&
                mutation.attributeName === "data-selected-tab"
              ) {
                const newSelectedTab = (mutation.target as Element).getAttribute(
                  "data-selected-tab"
                );
                setIsActive(newSelectedTab === value);
              }
            });
          });

          observer.observe(tabs, { attributes: true });

          return () => {
            observer.disconnect();
          };
        }
      }
    }, [value]);

    const onClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      const tabs = e.currentTarget.closest(".tabs");
      if (tabs) {
        const onValueChange = (tabs as any).__onValueChange;
        if (typeof onValueChange === "function") {
          onValueChange(value);
        }
        tabs.setAttribute("data-selected-tab", value);
      }
      if (props.onClick) {
        props.onClick(e);
      }
    };

    return (
      <button
        ref={buttonRef}
        className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-indigo-600 data-[state=active]:shadow-sm ${className || ""}`}
        data-state={isActive ? "active" : "inactive"}
        onClick={onClick}
        {...props}
      />
    );
  }
);
TabsTrigger.displayName = "TabsTrigger";

interface TabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
}

const TabsContent = React.forwardRef<HTMLDivElement, TabsContentProps>(
  ({ className, value, ...props }, ref) => {
    const [isVisible, setIsVisible] = React.useState(false);

    // Create a ref to store the div element
    const divRef = React.useRef<HTMLDivElement>(null);

    // Combine the forwarded ref with our local ref
    React.useImperativeHandle(ref, () => divRef.current!);

    React.useEffect(() => {
      if (divRef.current) {
        const tabs = divRef.current.closest(".tabs");
        if (tabs) {
          const selectedTab = tabs.getAttribute("data-selected-tab");
          setIsVisible(selectedTab === value);

          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (
                mutation.type === "attributes" &&
                mutation.attributeName === "data-selected-tab"
              ) {
                const newSelectedTab = (mutation.target as Element).getAttribute(
                  "data-selected-tab"
                );
                setIsVisible(newSelectedTab === value);
              }
            });
          });

          observer.observe(tabs, { attributes: true });

          return () => {
            observer.disconnect();
          };
        }
      }
    }, [value]);

    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={divRef}
        className={`mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${className || ""}`}
        data-state={isVisible ? "active" : "inactive"}
        {...props}
      />
    );
  }
);
TabsContent.displayName = "TabsContent";

export { Tabs, TabsList, TabsTrigger, TabsContent };
