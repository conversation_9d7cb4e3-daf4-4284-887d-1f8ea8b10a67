import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// POST /api/maintenance/notify - Send notification to customer
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit maintenance services
    const canEditMaintenance = await hasPermission("edit_maintenance") || session.user.role === "ADMIN";

    if (!canEditMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to send maintenance notifications" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.serviceId || !data.contactId || !data.message || !data.notificationType) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if service exists
    const service = await db.maintenanceService.findUnique({
      where: { id: data.serviceId },
      include: {
        contact: true,
      },
    });

    if (!service) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Check if contact matches service
    if (service.contactId !== data.contactId) {
      return NextResponse.json(
        { error: "Contact does not match service" },
        { status: 400 }
      );
    }

    // Validate notification type
    if (!["sms", "email", "both"].includes(data.notificationType)) {
      return NextResponse.json(
        { error: "Invalid notification type" },
        { status: 400 }
      );
    }

    // Check if email is available for email notifications
    if ((data.notificationType === "email" || data.notificationType === "both") && !service.contact.email) {
      return NextResponse.json(
        { error: "Customer email is not available for email notification" },
        { status: 400 }
      );
    }

    // Create notification record
    const notification = await db.notification.create({
      data: {
        type: data.notificationType,
        recipient: service.contact.phone,
        recipientEmail: service.contact.email || null,
        subject: data.subject || `Maintenance Service #${service.serviceNumber} Update`,
        message: data.message,
        status: "PENDING",
        userId: session.user.id,
        relatedEntityType: "MAINTENANCE",
        relatedEntityId: service.id,
      },
    });

    // In a real implementation, you would integrate with SMS and email services here
    // For now, we'll simulate sending by updating the status after a delay
    
    // Update notification status to SENT (simulating successful delivery)
    setTimeout(async () => {
      try {
        await db.notification.update({
          where: { id: notification.id },
          data: { status: "SENT", sentAt: new Date() },
        });
      } catch (error) {
        console.error("Error updating notification status:", error);
      }
    }, 2000);

    // Create a status history entry for the notification
    await db.maintenanceStatusHistory.create({
      data: {
        maintenanceServiceId: service.id,
        status: service.status, // Keep the same status
        notes: `Customer notified via ${data.notificationType}: ${data.message.substring(0, 50)}...`,
        userId: session.user.id,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Notification sent successfully",
      notificationId: notification.id,
    });
  } catch (error) {
    console.error("Error sending notification:", error);
    return NextResponse.json(
      { error: "Failed to send notification" },
      { status: 500 }
    );
  }
}
