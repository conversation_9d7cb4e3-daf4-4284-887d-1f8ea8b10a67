"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  Search,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  ArrowUpRight,
  Mail,
  Phone,
  DollarSign,
  Bell
} from "lucide-react";
import { format, addDays, isAfter, isBefore, differenceInDays } from "date-fns";
import Link from "next/link";

// Define types
interface Purchase {
  id: string;
  invoiceNumber: string;
  date: string;
  dueDate: string | null;
  totalAmount: number;
  paymentStatus: "PAID" | "UNPAID" | "PARTIALLY_PAID";
  contact: {
    id: string;
    name: string;
    phone: string;
  };
  reminderSent: boolean;
  lastReminderDate: string | null;
  payments: PurchasePayment[];
}

interface PurchasePayment {
  id: string;
  amount: number;
  paymentMethod: string;
  paymentDate: string;
}

export default function DueInvoicesPage() {
  // State for purchases
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [filteredPurchases, setFilteredPurchases] = useState<Purchase[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // State for filters
  const [searchTerm, setSearchTerm] = useState("");
  const [dueFilter, setDueFilter] = useState("all");
  const [reminderFilter, setReminderFilter] = useState("all");
  const [supplierFilter, setSupplierFilter] = useState("all");

  // State for suppliers
  const [suppliers, setSuppliers] = useState<{id: string, name: string}[]>([]);

  // Fetch purchases
  useEffect(() => {
    const fetchPurchases = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/purchases?paymentStatus=UNPAID,PARTIALLY_PAID");
        if (response.ok) {
          const data = await response.json();
          setPurchases(data);
        } else {
          console.error("Failed to fetch purchases");
        }
      } catch (error) {
        console.error("Error fetching purchases:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPurchases();
  }, []);

  // Extract unique suppliers
  useEffect(() => {
    const uniqueSuppliers = Array.from(
      new Set(purchases.map(purchase => purchase.contact.id))
    ).map(id => {
      const purchase = purchases.find(p => p.contact.id === id);
      return {
        id,
        name: purchase?.contact.name || ""
      };
    });

    setSuppliers(uniqueSuppliers);
  }, [purchases]);

  // Filter purchases
  useEffect(() => {
    let result = [...purchases];

    // Apply search filter
    if (searchTerm) {
      result = result.filter(purchase =>
        purchase.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        purchase.contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        purchase.contact.phone.includes(searchTerm)
      );
    }

    // Apply due date filter
    const today = new Date();
    if (dueFilter === "overdue") {
      result = result.filter(purchase =>
        purchase.dueDate && isBefore(new Date(purchase.dueDate), today)
      );
    } else if (dueFilter === "today") {
      result = result.filter(purchase => {
        if (!purchase.dueDate) return false;
        const dueDate = new Date(purchase.dueDate);
        return (
          dueDate.getDate() === today.getDate() &&
          dueDate.getMonth() === today.getMonth() &&
          dueDate.getFullYear() === today.getFullYear()
        );
      });
    } else if (dueFilter === "thisWeek") {
      const endOfWeek = addDays(today, 7);
      result = result.filter(purchase =>
        purchase.dueDate &&
        isAfter(new Date(purchase.dueDate), today) &&
        isBefore(new Date(purchase.dueDate), endOfWeek)
      );
    } else if (dueFilter === "nextWeek") {
      const startOfNextWeek = addDays(today, 7);
      const endOfNextWeek = addDays(today, 14);
      result = result.filter(purchase =>
        purchase.dueDate &&
        isAfter(new Date(purchase.dueDate), startOfNextWeek) &&
        isBefore(new Date(purchase.dueDate), endOfNextWeek)
      );
    }

    // Apply reminder filter
    if (reminderFilter === "sent") {
      result = result.filter(purchase => purchase.reminderSent);
    } else if (reminderFilter === "notSent") {
      result = result.filter(purchase => !purchase.reminderSent);
    }

    // Apply supplier filter
    if (supplierFilter !== "all") {
      result = result.filter(purchase => purchase.contact.id === supplierFilter);
    }

    // Sort by due date (closest first)
    result.sort((a, b) => {
      if (!a.dueDate) return 1;
      if (!b.dueDate) return -1;
      return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
    });

    setFilteredPurchases(result);
  }, [purchases, searchTerm, dueFilter, reminderFilter, supplierFilter]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Calculate remaining amount
  const getRemainingAmount = (purchase: Purchase) => {
    const totalPaid = purchase.payments.reduce((sum, payment) => sum + payment.amount, 0);
    return purchase.totalAmount - totalPaid;
  };

  // Get due status
  const getDueStatus = (dueDate: string | null) => {
    if (!dueDate) return "no-due-date";

    const today = new Date();
    const due = new Date(dueDate);
    const daysUntilDue = differenceInDays(due, today);

    if (daysUntilDue < 0) return "overdue";
    if (daysUntilDue === 0) return "due-today";
    if (daysUntilDue <= 7) return "due-soon";
    return "upcoming";
  };

  // Send reminder
  const sendReminder = async (purchaseId: string) => {
    try {
      const response = await fetch(`/api/purchases/${purchaseId}/reminder`, {
        method: "POST",
      });

      if (response.ok) {
        // Update the purchase in the state
        setPurchases(prevPurchases =>
          prevPurchases.map(purchase =>
            purchase.id === purchaseId
              ? {
                  ...purchase,
                  reminderSent: true,
                  lastReminderDate: new Date().toISOString()
                }
              : purchase
          )
        );
      } else {
        const error = await response.json();
        alert(`Failed to send reminder: ${error.error}`);
      }
    } catch (error) {
      console.error("Error sending reminder:", error);
      alert("An error occurred while sending the reminder");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Due Invoices</h2>
          <p className="text-muted-foreground">
            Track and manage upcoming and overdue purchase invoices
          </p>
        </div>
        <div className="flex space-x-2">
          <Link href="/dashboard/purchases/new">
            <Button variant="outline">
              <DollarSign className="mr-2 h-4 w-4" />
              Record Payment
            </Button>
          </Link>
          <Button onClick={() => sendReminder("all")}>
            <Bell className="mr-2 h-4 w-4" />
            Send All Reminders
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Overdue</p>
                <h3 className="text-2xl font-bold mt-1">
                  {filteredPurchases.filter(p => getDueStatus(p.dueDate) === "overdue").length}
                </h3>
                <p className="text-sm text-gray-500 mt-1">Invoices</p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Due Today</p>
                <h3 className="text-2xl font-bold mt-1">
                  {filteredPurchases.filter(p => getDueStatus(p.dueDate) === "due-today").length}
                </h3>
                <p className="text-sm text-gray-500 mt-1">Invoices</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Due This Week</p>
                <h3 className="text-2xl font-bold mt-1">
                  {filteredPurchases.filter(p => getDueStatus(p.dueDate) === "due-soon").length}
                </h3>
                <p className="text-sm text-gray-500 mt-1">Invoices</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Calendar className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Due</p>
                <h3 className="text-2xl font-bold mt-1">
                  {formatCurrency(
                    filteredPurchases.reduce((sum, p) => sum + getRemainingAmount(p), 0)
                  )}
                </h3>
                <p className="text-sm text-gray-500 mt-1">Amount</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="text"
                placeholder="Search invoices or suppliers"
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select value={dueFilter} onValueChange={setDueFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by due date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Due Dates</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
                <SelectItem value="today">Due Today</SelectItem>
                <SelectItem value="thisWeek">Due This Week</SelectItem>
                <SelectItem value="nextWeek">Due Next Week</SelectItem>
              </SelectContent>
            </Select>

            <Select value={reminderFilter} onValueChange={setReminderFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by reminder" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Reminders</SelectItem>
                <SelectItem value="sent">Reminder Sent</SelectItem>
                <SelectItem value="notSent">No Reminder Sent</SelectItem>
              </SelectContent>
            </Select>

            <Select value={supplierFilter} onValueChange={setSupplierFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by supplier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Suppliers</SelectItem>
                {suppliers.map((supplier) => (
                  <SelectItem key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Due Invoices List */}
      <Card>
        <CardHeader>
          <CardTitle>Due Invoices</CardTitle>
          <CardDescription>
            Manage upcoming and overdue purchase invoices
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              <span className="ml-3 text-lg text-gray-700">Loading invoices...</span>
            </div>
          ) : filteredPurchases.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">No due invoices</h3>
              <p className="mt-1 text-sm text-gray-500">All invoices have been paid or no invoices match your filters.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                  <tr>
                    <th className="px-6 py-3">Invoice #</th>
                    <th className="px-6 py-3">Supplier</th>
                    <th className="px-6 py-3">Due Date</th>
                    <th className="px-6 py-3">Amount Due</th>
                    <th className="px-6 py-3">Status</th>
                    <th className="px-6 py-3">Reminder</th>
                    <th className="px-6 py-3">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPurchases.map((purchase) => {
                    const dueStatus = getDueStatus(purchase.dueDate);
                    const remainingAmount = getRemainingAmount(purchase);

                    return (
                      <tr key={purchase.id} className="bg-white border-b hover:bg-gray-50">
                        <td className="px-6 py-4 font-medium">
                          <Link href={`/dashboard/purchases/${purchase.id}`} className="text-blue-600 hover:underline">
                            {purchase.invoiceNumber}
                          </Link>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex flex-col">
                            <span>{purchase.contact.name}</span>
                            <span className="text-xs text-gray-500">{purchase.contact.phone}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          {purchase.dueDate ? (
                            <div className="flex items-center">
                              <Badge className={
                                dueStatus === "overdue" ? "bg-red-100 text-red-800" :
                                dueStatus === "due-today" ? "bg-orange-100 text-orange-800" :
                                dueStatus === "due-soon" ? "bg-yellow-100 text-yellow-800" :
                                "bg-blue-100 text-blue-800"
                              }>
                                {format(new Date(purchase.dueDate), "MMM d, yyyy")}
                              </Badge>
                              {dueStatus === "overdue" && (
                                <span className="ml-2 text-xs text-red-600">
                                  {Math.abs(differenceInDays(new Date(purchase.dueDate), new Date()))} days overdue
                                </span>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-500">No due date</span>
                          )}
                        </td>
                        <td className="px-6 py-4 font-medium">
                          {formatCurrency(remainingAmount)}
                        </td>
                        <td className="px-6 py-4">
                          <Badge className={
                            purchase.paymentStatus === "UNPAID" ? "bg-red-100 text-red-800" :
                            "bg-yellow-100 text-yellow-800"
                          }>
                            {purchase.paymentStatus === "UNPAID" ? "Unpaid" : "Partially Paid"}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">
                          {purchase.reminderSent ? (
                            <div className="flex flex-col">
                              <Badge variant="outline" className="bg-green-50">
                                <CheckCircle className="mr-1 h-3 w-3 text-green-600" />
                                Sent
                              </Badge>
                              {purchase.lastReminderDate && (
                                <span className="text-xs text-gray-500 mt-1">
                                  {format(new Date(purchase.lastReminderDate), "MMM d, yyyy")}
                                </span>
                              )}
                            </div>
                          ) : (
                            <Badge variant="outline" className="bg-gray-50">
                              <XCircle className="mr-1 h-3 w-3 text-gray-600" />
                              Not sent
                            </Badge>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => sendReminder(purchase.id)}
                              disabled={purchase.reminderSent}
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                            <Link href={`/dashboard/purchases/${purchase.id}`}>
                              <Button variant="outline" size="sm">
                                <ArrowUpRight className="h-4 w-4" />
                              </Button>
                            </Link>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
