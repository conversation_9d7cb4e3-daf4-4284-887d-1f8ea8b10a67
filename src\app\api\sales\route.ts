import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  createSaleJournalEntries,
  TransactionType,
  PaymentInfo,
  ensureAccountsExist,
  ensureJournalsExist
} from "@/lib/accounting-new";
import cache from "@/lib/cache";

// GET /api/sales - Get all sales
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const branchId = url.searchParams.get("branchId");
    const status = url.searchParams.get("status");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const contactId = url.searchParams.get("contactId");
    const search = url.searchParams.get("search");
    const dateRange = url.searchParams.get("dateRange");
    const includeItems = url.searchParams.get("includeItems") === "true";

    // Pagination parameters
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const skip = (page - 1) * limit;

    // Generate cache key based on query parameters
    const cacheKey = `sales_${branchId || 'all'}_${status || 'all'}_${startDate || ''}_${endDate || ''}_${contactId || ''}_${search || ''}_${dateRange || ''}_${page}_${limit}_${includeItems ? 'with_items' : 'no_items'}`;

    // Check if we have cached data
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached sales data for key: ${cacheKey}`);
      return NextResponse.json(cachedData);
    }

    console.log(`Cache miss for key: ${cacheKey}, fetching from database`);

    // Build filter object
    const filter: any = {};

    if (branchId && branchId !== "all") {
      filter.branchId = branchId;
    }

    if (status && status !== "all") {
      filter.status = status;
    }

    if (contactId) {
      filter.contactId = contactId;
    }

    // Search filter - optimize by using indexes
    if (search) {
      // Check if search looks like an invoice number (alphanumeric)
      if (/^[A-Za-z0-9-]+$/.test(search)) {
        filter.OR = [
          { invoiceNumber: { contains: search, mode: "insensitive" } },
        ];
      } else {
        filter.OR = [
          { invoiceNumber: { contains: search, mode: "insensitive" } },
          { contact: { name: { contains: search, mode: "insensitive" } } },
        ];
      }
    }

    // Date range filter
    if (startDate && endDate) {
      filter.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      filter.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      filter.date = {
        lte: new Date(endDate),
      };
    } else if (dateRange) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (dateRange === "today") {
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        filter.date = {
          gte: today,
          lt: tomorrow,
        };
      } else if (dateRange === "week") {
        const weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 7);
        filter.date = {
          gte: weekAgo,
        };
      } else if (dateRange === "month") {
        const monthAgo = new Date(today);
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        filter.date = {
          gte: monthAgo,
        };
      }
    }

    // Get total count for pagination - use a more efficient count query
    const totalCount = await db.sale.count({
      where: filter,
    });

    // Define select fields to optimize data retrieval
    const selectFields = {
      id: true,
      invoiceNumber: true,
      date: true,
      status: true,
      paymentStatus: true,
      paymentMethod: true,
      totalAmount: true,
      subtotalAmount: true,
      discountAmount: true,
      taxAmount: true,
      createdAt: true,
      contactId: true,
      branchId: true,
      userId: true,
    };

    // Get sales from database with pagination and optimized includes
    const sales = await db.sale.findMany({
      where: filter,
      select: {
        ...selectFields,
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        // Only include items if specifically requested
        ...(includeItems ? {
          items: {
            select: {
              id: true,
              productId: true,
              quantity: true,
              unitPrice: true,
              totalPrice: true,
              product: {
                select: {
                  id: true,
                  name: true,
                }
              }
            }
          }
        } : {}),
      },
      orderBy: {
        date: "desc",
      },
      skip,
      take: limit,
    });

    // Calculate summary statistics - use a more efficient approach with fewer queries
    const [totalAmountStats, statusStats, paymentStatusStats] = await Promise.all([
      // Total sales amount
      db.sale.aggregate({
        where: filter,
        _sum: {
          totalAmount: true,
        },
      }),
      // Count by status
      db.sale.groupBy({
        by: ['status'],
        where: filter,
        _count: {
          id: true,
        },
      }),
      // Count by payment status
      db.sale.groupBy({
        by: ['paymentStatus'],
        where: filter,
        _count: {
          id: true,
        },
        _sum: {
          totalAmount: true,
        },
      }),
    ]);

    // Format the summary stats
    const summary = {
      totalAmount: totalAmountStats._sum.totalAmount || 0,
      statusCounts: Object.fromEntries(
        statusStats.map(item => [item.status, item._count.id])
      ),
      paymentStatusCounts: Object.fromEntries(
        paymentStatusStats.map(item => [item.paymentStatus, {
          count: item._count.id,
          amount: item._sum.totalAmount || 0
        }])
      ),
    };

    // Prepare the response data
    const responseData = {
      sales,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
      summary,
    };

    // Cache the result for 5 minutes (300000 ms)
    cache.set(cacheKey, responseData, 5 * 60 * 1000);
    console.log(`Cached sales data for key: ${cacheKey}`);

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error fetching sales:", error);
    return NextResponse.json(
      { error: "Failed to fetch sales" },
      { status: 500 }
    );
  }
}

// POST /api/sales - Create a new sale
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await req.json();

    // Validate required fields - only items are required
    if (!data.items || data.items.length === 0) {
      return NextResponse.json(
        { error: "No items provided for the sale" },
        { status: 400 }
      );
    }

    // If contactId or branchId are missing, we'll handle it in the transaction

    // Start a transaction
    const sale = await db.$transaction(async (tx) => {
      // Get default branch if not provided
      let branchId = data.branchId;
      if (!branchId) {
        const defaultBranch = await tx.branch.findFirst({
          where: { isActive: true }
        });
        if (defaultBranch) {
          branchId = defaultBranch.id;
        } else {
          return NextResponse.json(
            { error: "No active branch found and no branch specified" },
            { status: 400 }
          );
        }
      }

      // Get default contact (customer) if not provided
      let contactId = data.contactId;
      if (!contactId) {
        // Try to find a default customer (you might want to create one for walk-in customers)
        const defaultContact = await tx.contact.findFirst({
          where: { isCustomer: true }
        });
        if (defaultContact) {
          contactId = defaultContact.id;
        } else {
          // Create a walk-in customer if none exists
          const walkInCustomer = await tx.contact.create({
            data: {
              name: "Walk-in Customer",
              isCustomer: true,
              isSupplier: false,
              phone: "N/A"
            }
          });
          contactId = walkInCustomer.id;
        }
      }

      // Get the contact to update balance
      const contact = contactId ? await tx.contact.findUnique({
        where: { id: contactId }
      }) : null;

      // Create the sale
      const newSale = await tx.sale.create({
        data: {
          invoiceNumber: data.invoiceNumber,
          contactId: contactId,
          branchId: branchId,
          userId: session.user.id,
          date: data.date || new Date(),
          status: data.status || "PENDING",
          paymentMethod: data.paymentMethod || "CASH",
          paymentStatus: data.paymentStatus || "PAID",
          subtotalAmount: data.subtotal,
          discountAmount: data.discount || 0,
          applyTax: data.applyTax || false,
          taxRate: data.taxRate || 0,
          taxAmount: data.taxAmount || 0,
          totalAmount: data.total,
          currency: data.currency || "EGP",
          notes: data.notes,
        },
      });

      // Add sale discounts if any
      if (data.discounts && data.discounts.length > 0) {
        for (const discount of data.discounts) {
          await tx.saleDiscount.create({
            data: {
              saleId: newSale.id,
              discountId: discount.id,
              type: discount.type,
              value: discount.value,
              amount: discount.amount,
            },
          });
        }
      }

      // Add item discounts if any
      if (data.itemDiscounts && Object.keys(data.itemDiscounts).length > 0) {
        for (const [saleItemId, discounts] of Object.entries(data.itemDiscounts)) {
          if (Array.isArray(discounts) && discounts.length > 0) {
            for (const discount of discounts) {
              await tx.saleItemDiscount.create({
                data: {
                  saleItemId,
                  discountId: discount.id,
                  type: discount.type,
                  value: discount.value,
                  amount: discount.amount,
                },
              });
            }
          }
        }
      }

      // Update contact balance if payment status is not PAID
      // For PAID sales, the balance is handled by journal entries
      if (contact && contact.isCustomer) {
        // Calculate the unpaid amount
        const totalAmount = data.total;
        const paidAmount = data.payments?.reduce((sum, p) => sum + p.amount, 0) || 0;
        const unpaidAmount = totalAmount - paidAmount;

        console.log(`Updating contact balance: Total: ${totalAmount}, Paid: ${paidAmount}, Unpaid: ${unpaidAmount}`);

        // Only increment the balance by the unpaid amount
        if (unpaidAmount > 0) {
          await tx.contact.update({
            where: { id: contactId },
            data: {
              balance: {
                increment: unpaidAmount
              }
            }
          });
        }
      }

      // Create sale items and update inventory
      for (const item of data.items) {
        // Create sale item with specifications if available
        const saleItemData: any = {
          saleId: newSale.id,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.total,
          // Note: warehouseId is not stored in SaleItem, but we need it for inventory updates
        };

        // If item has specifications, store them as JSON
        if (item.specifications) {
          saleItemData.specifications = JSON.stringify(item.specifications);
        }

        // If item is customized, store customization info
        if (item.isCustomized) {
          saleItemData.isCustomized = true;
        }

        const saleItem = await tx.saleItem.create({
          data: saleItemData,
        });

        // Update inventory for the main product
        const inventory = await tx.inventory.findFirst({
          where: {
            productId: item.productId,
            warehouseId: item.warehouseId,
          },
        });

        if (!inventory) {
          throw new Error(`Inventory not found for product ${item.productId} in warehouse ${item.warehouseId}`);
        }

        if (inventory.quantity < item.quantity) {
          throw new Error(`Not enough stock for product ${item.productId} in warehouse ${item.warehouseId}`);
        }

        await tx.inventory.update({
          where: {
            id: inventory.id,
          },
          data: {
            quantity: inventory.quantity - item.quantity,
          },
        });

        // If item has customized components, update their inventory too
        if (item.components && Array.isArray(item.components)) {
          for (const component of item.components) {
            // Find component inventory
            const componentInventory = await tx.inventory.findFirst({
              where: {
                productId: component.id,
                warehouseId: item.warehouseId,
              },
            });

            if (componentInventory) {
              // Calculate the total quantity to deduct
              // For components with count (like RAM), we need to deduct count * quantity
              const deductQuantity = component.totalCount || component.count || item.quantity;

              // Check if there's enough stock
              if (componentInventory.quantity < deductQuantity) {
                throw new Error(`Not enough stock for component ${component.name} (${component.type}). Need ${deductQuantity}, but only ${componentInventory.quantity} available.`);
              }

              // Deduct component from inventory
              await tx.inventory.update({
                where: {
                  id: componentInventory.id,
                },
                data: {
                  quantity: componentInventory.quantity - deductQuantity,
                },
              });

              // Get component details from database if price is missing
              let componentPrice = component.price;
              if (componentPrice === undefined || isNaN(componentPrice)) {
                // Fetch the component from the database to get its price
                const componentDetails = await tx.product.findUnique({
                  where: { id: component.id }
                });
                componentPrice = componentDetails?.basePrice || 0;
              }

              // Calculate total price safely
              const componentCount = component.count || 1;
              const componentTotalPrice = componentPrice * componentCount * item.quantity;

              // Create a record of component usage
              await tx.saleItemComponent.create({
                data: {
                  saleItemId: saleItem.id,
                  componentId: component.id,
                  componentName: component.name || "Unknown Component",
                  componentType: component.type,
                  quantity: componentCount, // Number of components per unit
                  totalQuantity: deductQuantity, // Total number of components deducted
                  unitPrice: componentPrice,
                  totalPrice: componentTotalPrice,
                },
              });
            }
          }
        }
      }

      // No accounting transactions are created here anymore
      // They will be created after the transaction is complete using the accounting service

      return newSale;
    });

    // Create journal entries for the sale using the accounting service
    try {
      console.log("Sale created successfully, now creating journal entries");

      // Get contact name
      const contact = await db.contact.findUnique({
        where: {
          id: data.contactId,
        },
        select: {
          name: true,
        },
      });

      // Prepare payment data for journal entries
      const payments: PaymentInfo[] = [];

      // If we have payment data in the request
      if (data.payments && Array.isArray(data.payments) && data.payments.length > 0) {
        console.log("Using payment data from request:", data.payments);
        // Use the payment data from the request
        payments.push(...data.payments);
      } else if (data.paymentMethod && data.paymentStatus === "PAID") {
        console.log("Using single payment method:", data.paymentMethod);
        // Use the single payment method
        payments.push({
          method: data.paymentMethod,
          amount: data.total,
        });
      } else if (data.paymentMethod === "CUSTOMER_ACCOUNT" || data.paymentStatus === "UNPAID") {
        console.log("Using customer account payment method");
        // Use customer account
        payments.push({
          method: "CUSTOMER_ACCOUNT",
          amount: data.total,
        });
      }

      console.log("Ensuring journals and accounts exist");
      // Check if required journals and accounts exist and create them if they don't
      await ensureAccountsExist(); // This also calls ensureJournalsExist()

      // Only create journal entries if we have valid payment methods
      if (payments.length > 0) {
        console.log("Creating journal entries for payments:", payments);
        try {
          await createSaleJournalEntries(
            payments,
            data.total,
            data.subtotal,
            {
              transactionType: TransactionType.SALE,
              referenceId: sale.id,
              referenceNumber: sale.invoiceNumber,
              description: `Sale ${sale.invoiceNumber}`,
              contactId: data.contactId,
              contactName: contact?.name,
              branchId: data.branchId,
              date: new Date(),
            }
          );
          console.log("Journal entries created successfully");
        } catch (journalCreationError) {
          console.error("Error in createSaleJournalEntries:", journalCreationError);
          // Don't fail the sale creation if journal entries fail
        }
      } else {
        console.log("No valid payment methods found, skipping journal entries");
      }
    } catch (journalError) {
      console.error("Error creating journal entries:", journalError);
      // Don't fail the sale creation if journal entries fail
    }

    // Note: We're now using the ensureJournalsExist function from accounting-new.ts

    // Invalidate sales cache
    cache.deletePattern(/^sales_/);
    console.log("Invalidated sales cache after creating new sale");

    return NextResponse.json(sale, { status: 201 });
  } catch (error) {
    console.error("Error creating sale:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create sale" },
      { status: 500 }
    );
  }
}
