/**
 * <PERSON><PERSON><PERSON> to set up the accounting module using Prisma
 *
 * This script will:
 * 1. Create necessary accounting tables using Prisma
 * 2. Initialize the accounting module with default accounts and journals
 */

const { PrismaClient } = require('@prisma/client');

// Initialize Prisma client
const prisma = new PrismaClient();

// Default accounts to create
const defaultAccounts = [
  // Asset accounts (1000-1999)
  { code: '1000', name: 'الأصول', type: 'ASSET', accountNumber: '1000', parentId: null },
  { code: '1100', name: 'النقدية', type: 'ASSET', accountNumber: '1100', parentCode: '1000' },
  { code: '1101', name: 'الصندوق', type: 'ASSET', accountNumber: '1101', parentCode: '1100' },
  { code: '1102', name: 'البنك', type: 'ASSET', accountNumber: '1102', parentCode: '1100' },
  { code: '1200', name: 'الحسابات المدينة', type: 'ASSET', accountNumber: '1200', parentCode: '1000' },
  { code: '1201', name: 'العملاء', type: 'ASSET', accountNumber: '1201', parentCode: '1200' },
  { code: '1300', name: 'المخزون', type: 'ASSET', accountNumber: '1300', parentCode: '1000' },
  { code: '1301', name: 'مخزون البضائع', type: 'ASSET', accountNumber: '1301', parentCode: '1300' },
  { code: '1400', name: 'الأصول الثابتة', type: 'ASSET', accountNumber: '1400', parentCode: '1000' },
  { code: '1401', name: 'الأثاث والتجهيزات', type: 'ASSET', accountNumber: '1401', parentCode: '1400' },
  { code: '1402', name: 'أجهزة الكمبيوتر والمعدات', type: 'ASSET', accountNumber: '1402', parentCode: '1400' },

  // Liability accounts (2000-2999)
  { code: '2000', name: 'الخصوم', type: 'LIABILITY', accountNumber: '2000', parentId: null },
  { code: '2100', name: 'الحسابات الدائنة', type: 'LIABILITY', accountNumber: '2100', parentCode: '2000' },
  { code: '2101', name: 'الموردين', type: 'LIABILITY', accountNumber: '2101', parentCode: '2100' },
  { code: '2200', name: 'الضرائب المستحقة', type: 'LIABILITY', accountNumber: '2200', parentCode: '2000' },
  { code: '2201', name: 'ضريبة القيمة المضافة', type: 'LIABILITY', accountNumber: '2201', parentCode: '2200' },
  { code: '2300', name: 'القروض', type: 'LIABILITY', accountNumber: '2300', parentCode: '2000' },

  // Equity accounts (3000-3999)
  { code: '3000', name: 'حقوق الملكية', type: 'EQUITY', accountNumber: '3000', parentId: null },
  { code: '3100', name: 'رأس المال', type: 'EQUITY', accountNumber: '3100', parentCode: '3000' },
  { code: '3200', name: 'الأرباح المحتجزة', type: 'EQUITY', accountNumber: '3200', parentCode: '3000' },

  // Revenue accounts (4000-4999)
  { code: '4000', name: 'الإيرادات', type: 'REVENUE', accountNumber: '4000', parentId: null },
  { code: '4100', name: 'إيرادات المبيعات', type: 'REVENUE', accountNumber: '4100', parentCode: '4000' },
  { code: '4200', name: 'إيرادات الخدمات', type: 'REVENUE', accountNumber: '4200', parentCode: '4000' },

  // Expense accounts (5000-5999)
  { code: '5000', name: 'المصروفات', type: 'EXPENSE', accountNumber: '5000', parentId: null },
  { code: '5100', name: 'تكلفة البضاعة المباعة', type: 'EXPENSE', accountNumber: '5100', parentCode: '5000' },
  { code: '5200', name: 'الرواتب والأجور', type: 'EXPENSE', accountNumber: '5200', parentCode: '5000' },
  { code: '5300', name: 'الإيجار', type: 'EXPENSE', accountNumber: '5300', parentCode: '5000' },
  { code: '5400', name: 'المرافق', type: 'EXPENSE', accountNumber: '5400', parentCode: '5000' },
  { code: '5500', name: 'مصروفات المكتب', type: 'EXPENSE', accountNumber: '5500', parentCode: '5000' },
];

// Default journals to create
const defaultJournals = [
  { code: 'CASH', name: 'دفتر النقدية', type: 'CASH', paymentMethod: 'CASH' },
  { code: 'VFCASH', name: 'دفتر فودافون كاش', type: 'VODAFONE_CASH', paymentMethod: 'VODAFONE_CASH' },
  { code: 'BANK', name: 'دفتر البنك', type: 'BANK_TRANSFER', paymentMethod: 'BANK_TRANSFER' },
  { code: 'VISA', name: 'دفتر بطاقات الائتمان', type: 'VISA', paymentMethod: 'CREDIT_CARD' },
  { code: 'CUST', name: 'دفتر حسابات العملاء', type: 'CUSTOMER_ACCOUNT', paymentMethod: 'CUSTOMER_ACCOUNT' },
  { code: 'GEN', name: 'دفتر اليومية العامة', type: 'GENERAL', paymentMethod: null },
  { code: 'SALES', name: 'دفتر المبيعات', type: 'SALES', paymentMethod: null },
  { code: 'PURCH', name: 'دفتر المشتريات', type: 'PURCHASES', paymentMethod: null },
];

// Function to initialize accounting module
async function initializeAccountingModule() {
  try {
    console.log('Initializing accounting module...');

    // Create default accounts
    // First, create parent accounts
    const accountMap = new Map();

    // Process accounts in two passes: first parent accounts, then child accounts
    const parentAccounts = defaultAccounts.filter(account => !account.parentCode);
    const childAccounts = defaultAccounts.filter(account => account.parentCode);

    console.log('Creating parent accounts...');
    for (const account of parentAccounts) {
      try {
        // Check if account already exists
        const existingAccount = await prisma.account.findFirst({
          where: {
            OR: [
              { code: account.code },
              { name: account.name, type: account.type },
            ],
          },
        });

        if (!existingAccount) {
          // Create the account
          const newAccount = await prisma.account.create({
            data: {
              code: account.code,
              name: account.name,
              type: account.type,
              isActive: true,
              accountNumber: account.accountNumber,
            },
          });
          accountMap.set(account.code, newAccount.id);
          console.log(`Created parent account: ${account.name} (${account.code})`);
        } else {
          accountMap.set(account.code, existingAccount.id);
          console.log(`Parent account already exists: ${account.name} (${account.code})`);

          // Update the account if needed
          if (existingAccount.code !== account.code || existingAccount.accountNumber !== account.accountNumber) {
            await prisma.account.update({
              where: { id: existingAccount.id },
              data: {
                code: account.code,
                accountNumber: account.accountNumber
              },
            });
            console.log(`Updated parent account: ${account.name}`);
          }
        }
      } catch (error) {
        console.error(`Error processing parent account ${account.name}:`, error);
      }
    }

    console.log('Creating child accounts...');
    for (const account of childAccounts) {
      try {
        // Get parent ID
        const parentId = accountMap.get(account.parentCode);

        if (!parentId) {
          console.error(`Parent account with code ${account.parentCode} not found for ${account.code}`);
          continue;
        }

        // Check if account already exists
        const existingAccount = await prisma.account.findFirst({
          where: {
            OR: [
              { code: account.code },
              { name: account.name, type: account.type },
            ],
          },
        });

        if (!existingAccount) {
          // Create the account
          const newAccount = await prisma.account.create({
            data: {
              code: account.code,
              name: account.name,
              type: account.type,
              isActive: true,
              accountNumber: account.accountNumber,
              parentId: parentId,
            },
          });
          accountMap.set(account.code, newAccount.id);
          console.log(`Created child account: ${account.name} (${account.code}) under parent ${account.parentCode}`);
        } else {
          accountMap.set(account.code, existingAccount.id);
          console.log(`Child account already exists: ${account.name} (${account.code})`);

          // Update the account if needed
          if (existingAccount.code !== account.code ||
              existingAccount.accountNumber !== account.accountNumber ||
              existingAccount.parentId !== parentId) {
            await prisma.account.update({
              where: { id: existingAccount.id },
              data: {
                code: account.code,
                accountNumber: account.accountNumber,
                parentId: parentId
              },
            });
            console.log(`Updated child account: ${account.name}`);
          }
        }
      } catch (error) {
        console.error(`Error processing child account ${account.name}:`, error);
      }
    }

    // Check if Journal model exists in the schema
    let journalModelExists = true;
    try {
      await prisma.journal.findFirst();
    } catch (error) {
      journalModelExists = false;
      console.log('Journal model does not exist in the schema. Skipping journal creation.');
    }

    // Create default journals if the model exists
    if (journalModelExists) {
      for (const journal of defaultJournals) {
        try {
          // Check if journal already exists
          const existingJournal = await prisma.journal.findFirst({
            where: {
              OR: [
                { code: journal.code },
                { name: journal.name, type: journal.type },
              ],
            },
          });

          if (!existingJournal) {
            // Create the journal
            await prisma.journal.create({
              data: {
                code: journal.code,
                name: journal.name,
                type: journal.type,
                paymentMethod: journal.paymentMethod,
                isActive: true,
              },
            });
            console.log(`Created journal: ${journal.name} (${journal.code})`);
          } else {
            console.log(`Journal already exists: ${journal.name} (${journal.code})`);
          }
        } catch (error) {
          console.error(`Error processing journal ${journal.name}:`, error);
        }
      }
    }

    // Check if FiscalYear model exists in the schema
    let fiscalYearModelExists = true;
    try {
      await prisma.fiscalYear.findFirst();
    } catch (error) {
      fiscalYearModelExists = false;
      console.log('FiscalYear model does not exist in the schema. Skipping fiscal year creation.');
    }

    // Create current fiscal year and period if the model exists
    if (fiscalYearModelExists) {
      try {
        const currentYear = new Date().getFullYear();
        const startDate = new Date(currentYear, 0, 1); // January 1st of current year
        const endDate = new Date(currentYear, 11, 31); // December 31st of current year

        const existingFiscalYear = await prisma.fiscalYear.findFirst({
          where: {
            name: `السنة المالية ${currentYear}`,
          },
        });

        let fiscalYearId;

        if (!existingFiscalYear) {
          const fiscalYear = await prisma.fiscalYear.create({
            data: {
              name: `السنة المالية ${currentYear}`,
              startDate,
              endDate,
              isClosed: false,
            },
          });
          fiscalYearId = fiscalYear.id;
          console.log(`Created fiscal year: السنة المالية ${currentYear}`);
        } else {
          fiscalYearId = existingFiscalYear.id;
          console.log(`Fiscal year already exists: السنة المالية ${currentYear}`);
        }

        // Create 12 monthly periods for the fiscal year
        const monthNames = [
          'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        for (let month = 0; month < 12; month++) {
          const periodStartDate = new Date(currentYear, month, 1);
          const periodEndDate = new Date(currentYear, month + 1, 0); // Last day of the month
          const periodName = `${monthNames[month]} ${currentYear}`;

          const existingPeriod = await prisma.fiscalPeriod.findFirst({
            where: {
              fiscalYearId,
              name: periodName,
            },
          });

          if (!existingPeriod) {
            await prisma.fiscalPeriod.create({
              data: {
                fiscalYearId,
                name: periodName,
                startDate: periodStartDate,
                endDate: periodEndDate,
                isClosed: month < new Date().getMonth(), // Close past months
              },
            });
            console.log(`Created fiscal period: ${periodName}`);
          } else {
            console.log(`Fiscal period already exists: ${periodName}`);
          }
        }
      } catch (error) {
        console.error('Error creating fiscal year and periods:', error);
      }
    }

    console.log('Accounting module initialized successfully');
  } catch (error) {
    console.error('Error initializing accounting module:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Starting accounting module setup...');

    // Initialize accounting module with default data
    await initializeAccountingModule();

    console.log('Accounting module setup completed successfully');
  } catch (error) {
    console.error('Error during setup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
