import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { exec } from "child_process";
import { promisify } from "util";
import fs from "fs";
import path from "path";
import bcrypt from "bcryptjs";

const execPromise = promisify(exec);

// Function to create admin user if it doesn't exist
async function ensureAdminUser() {
  try {
    // Check if admin user exists
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    });

    if (adminUser) {
      console.log("Admin user already exists");
      return;
    }

    console.log("Admin user not found. Creating a new admin user...");

    // Find a branch or create one
    let branch = await db.branch.findFirst();

    if (!branch) {
      console.log("No branch found. Creating a new branch...");
      branch = await db.branch.create({
        data: {
          name: "Main Branch",
          code: "A",
          address: "Cairo, Egypt",
          phone: "01000000000",
        },
      });
      console.log(`Created new branch: ${branch.name}`);
    }

    // Create default permissions
    const defaultPermissions = [
      { name: "view_users", description: "View users" },
      { name: "add_users", description: "Add new users" },
      { name: "edit_users", description: "Edit existing users" },
      { name: "delete_users", description: "Delete users" },
      { name: "view_branches", description: "View branches" },
      { name: "add_branches", description: "Add new branches" },
      { name: "edit_branches", description: "Edit existing branches" },
      { name: "delete_branches", description: "Delete branches" },
      { name: "view_warehouses", description: "View warehouses" },
      { name: "add_warehouses", description: "Add new warehouses" },
      { name: "edit_warehouses", description: "Edit existing warehouses" },
      { name: "delete_warehouses", description: "Delete warehouses" },
      { name: "view_products", description: "View products" },
      { name: "add_products", description: "Add new products" },
      { name: "edit_products", description: "Edit existing products" },
      { name: "delete_products", description: "Delete products" },
      { name: "view_inventory", description: "View inventory" },
      { name: "add_inventory", description: "Add inventory" },
      { name: "edit_inventory", description: "Edit inventory" },
      { name: "delete_inventory", description: "Delete inventory" },
      { name: "view_sales", description: "View sales" },
      { name: "add_sales", description: "Add new sales" },
      { name: "edit_sales", description: "Edit existing sales" },
      { name: "delete_sales", description: "Delete sales" },
      { name: "view_purchases", description: "View purchases" },
      { name: "add_purchases", description: "Add new purchases" },
      { name: "edit_purchases", description: "Edit existing purchases" },
      { name: "delete_purchases", description: "Delete purchases" },
      { name: "view_contacts", description: "View contacts" },
      { name: "add_contacts", description: "Add new contacts" },
      { name: "edit_contacts", description: "Edit existing contacts" },
      { name: "delete_contacts", description: "Delete contacts" },
      { name: "view_reports", description: "View reports" },
      { name: "view_settings", description: "View settings" },
      { name: "edit_settings", description: "Edit settings" },
    ];

    // Create permissions
    const createdPermissions = [];
    for (const permission of defaultPermissions) {
      // Check if permission already exists
      const existingPermission = await db.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        const newPermission = await db.permission.create({
          data: permission,
        });
        createdPermissions.push(newPermission);
        console.log(`Created permission: ${permission.name}`);
      } else {
        createdPermissions.push(existingPermission);
        console.log(`Permission already exists: ${permission.name}`);
      }
    }

    // Create a new admin user
    const hashedPassword = await bcrypt.hash("admin123", 10);
    const newAdmin = await db.user.create({
      data: {
        name: "Admin User",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "ADMIN",
        branchId: branch.id,
        isActive: true,
        permissions: {
          connect: createdPermissions.map(p => ({ id: p.id })),
        },
      },
    });

    // Create a warehouse if none exists
    const warehouse = await db.warehouse.findFirst({
      where: {
        branchId: branch.id,
      },
    });

    if (!warehouse) {
      const newWarehouse = await db.warehouse.create({
        data: {
          name: "Main Warehouse",
          branchId: branch.id,
          isActive: true,
        },
      });

      // Connect admin to warehouse
      await db.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: newWarehouse.id,
        },
      });
    } else {
      // Connect admin to existing warehouse
      await db.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: warehouse.id,
        },
      });
    }

    console.log("Admin user created successfully");
    console.log("Email: <EMAIL>");
    console.log("Password: admin123");
  } catch (error) {
    console.error("Error creating admin user:", error);
    throw error;
  }
}

// Function to check if user is admin
async function isAdmin(req: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return false;
  }

  // Check if user has admin role
  return session.user.role === "ADMIN";

  // For development, you can uncomment this to bypass admin check
  // return true;
}

// GET /api/system/database/backups - Get list of database backups
export async function GET(
  req: NextRequest,
  { params }: { params: { action: string } }
) {
  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }

    const { action } = params;

    if (action === "backups") {
      // Get list of backup files
      const backupDir = path.join(process.cwd(), "backups");

      // Create backup directory if it doesn't exist
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      const files = fs.readdirSync(backupDir);

      // Get file details
      const backups = files
        .filter(file => file.endsWith(".sql") || file.endsWith(".gz") || file.endsWith(".zip"))
        .map(file => {
          const filePath = path.join(backupDir, file);
          const stats = fs.statSync(filePath);

          return {
            name: file,
            size: formatFileSize(stats.size),
            date: new Date(stats.mtime).toLocaleString(),
          };
        })
        .sort((a, b) => {
          // Sort by date (newest first)
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        });

      return NextResponse.json({ backups });
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error(`Error in database ${params.action} GET:`, error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}

// POST /api/system/database/[action] - Perform database operations
export async function POST(
  req: NextRequest,
  { params }: { params: { action: string } }
) {
  // Store action in outer scope for error handling
  let action: string;

  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }

    action = params.action;

    switch (action) {
      case "reset":
        // Reset database (drop and recreate all tables)
        await db.$executeRawUnsafe(`DROP SCHEMA public CASCADE; CREATE SCHEMA public;`);

        // Run prisma migrate reset
        await execPromise("npx prisma migrate deploy");

        // Ensure admin user exists
        await ensureAdminUser();

        return NextResponse.json({ message: "Database reset successfully. Admin user has been created/verified." });

      case "seed":
        // Seed database with sample data
        await execPromise("npx prisma db seed");

        // Ensure admin user exists
        await ensureAdminUser();

        return NextResponse.json({ message: "Database seeded successfully. Admin user has been created/verified." });

      case "clean":
        // First, backup the admin user
        const adminUser = await db.user.findFirst({
          where: {
            email: "<EMAIL>",
          },
          include: {
            permissions: true,
            branch: true,
            warehouses: true,
          },
        });

        // Clean database (delete all data but keep tables)
        const tables = await db.$queryRaw`
          SELECT tablename FROM pg_tables
          WHERE schemaname = 'public'
          AND tablename != '_prisma_migrations'
        `;

        // Disable foreign key checks
        await db.$executeRawUnsafe(`SET session_replication_role = 'replica';`);

        // Delete data from all tables
        for (const { tablename } of tables as { tablename: string }[]) {
          await db.$executeRawUnsafe(`TRUNCATE TABLE "${tablename}" CASCADE;`);
        }

        // Re-enable foreign key checks
        await db.$executeRawUnsafe(`SET session_replication_role = 'origin';`);

        // Ensure admin user exists
        await ensureAdminUser();

        return NextResponse.json({ message: "Database cleaned successfully. Admin user has been preserved/recreated." });

      case "backup":
        // Create backup directory if it doesn't exist
        const backupDir = path.join(process.cwd(), "backups");
        if (!fs.existsSync(backupDir)) {
          fs.mkdirSync(backupDir, { recursive: true });
        }

        // Generate backup filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
        const backupFile = path.join(backupDir, `backup-${timestamp}.sql`);

        // Get database connection details from environment variables
        const dbUrl = process.env.DATABASE_URL;
        if (!dbUrl) {
          return NextResponse.json(
            { error: "Database URL not configured" },
            { status: 500 }
          );
        }

        // Parse database URL to get connection details
        const dbUrlMatch = dbUrl.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
        if (!dbUrlMatch) {
          return NextResponse.json(
            { error: "Invalid database URL format" },
            { status: 500 }
          );
        }

        const [, user, password, host, port, dbName] = dbUrlMatch;

        // Create pg_dump command based on OS
        let pgDumpCmd;

        // Extract database name without query parameters
        const cleanDbName = dbName.split('?')[0];

        if (process.platform === 'win32') {
          // Windows command
          pgDumpCmd = `set "PGPASSWORD=${password}" && pg_dump -h ${host} -p ${port} -U ${user} -d ${cleanDbName} -f "${backupFile}"`;
        } else {
          // Unix/Linux/Mac command
          pgDumpCmd = `PGPASSWORD="${password}" pg_dump -h ${host} -p ${port} -U ${user} -d ${cleanDbName} -f "${backupFile}"`;
        }

        try {
          // Execute pg_dump
          await execPromise(pgDumpCmd);
        } catch (error: any) {
          console.error("pg_dump error:", error);

          // Create backup file with basic information if pg_dump fails
          const timestamp = new Date().toISOString();
          const errorMessage = error.message || 'Unknown error';
          const backupContent = `-- Database backup created on ${timestamp}\n` +
                               `-- This is a placeholder backup because pg_dump failed.\n` +
                               `-- Error: ${errorMessage}\n` +
                               `-- Please ensure that PostgreSQL client tools are installed and in your PATH.\n`;

          fs.writeFileSync(backupFile, backupContent);

          return NextResponse.json({
            message: "Created placeholder backup file. pg_dump failed - please install PostgreSQL client tools.",
            filename: path.basename(backupFile),
            warning: "This is a placeholder backup because pg_dump failed."
          });
        }

        return NextResponse.json({
          message: "Database backup created successfully",
          filename: path.basename(backupFile)
        });

      case "restore":
        // Get filename from request body
        const body = await req.json();
        const { filename } = body;

        if (!filename) {
          return NextResponse.json(
            { error: "Filename is required" },
            { status: 400 }
          );
        }

        // Validate filename to prevent directory traversal
        if (filename.includes("..") || filename.includes("/") || filename.includes("\\")) {
          return NextResponse.json(
            { error: "Invalid filename" },
            { status: 400 }
          );
        }

        const restoreFile = path.join(process.cwd(), "backups", filename);

        // Check if file exists
        if (!fs.existsSync(restoreFile)) {
          return NextResponse.json(
            { error: "Backup file not found" },
            { status: 404 }
          );
        }

        // Get database connection details
        const restoreDbUrl = process.env.DATABASE_URL;
        if (!restoreDbUrl) {
          return NextResponse.json(
            { error: "Database URL not configured" },
            { status: 500 }
          );
        }

        // Parse database URL
        const restoreDbUrlMatch = restoreDbUrl.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
        if (!restoreDbUrlMatch) {
          return NextResponse.json(
            { error: "Invalid database URL format" },
            { status: 500 }
          );
        }

        const [, restoreUser, restorePassword, restoreHost, restorePort, restoreDbName] = restoreDbUrlMatch;

        // Extract database name without query parameters
        const cleanRestoreDbName = restoreDbName.split('?')[0];

        // Drop and recreate database
        await db.$executeRawUnsafe(`DROP SCHEMA public CASCADE; CREATE SCHEMA public;`);

        // Create psql command to restore based on OS
        let psqlCmd;
        if (process.platform === 'win32') {
          // Windows command
          psqlCmd = `set "PGPASSWORD=${restorePassword}" && psql -h ${restoreHost} -p ${restorePort} -U ${restoreUser} -d ${cleanRestoreDbName} -f "${restoreFile}"`;
        } else {
          // Unix/Linux/Mac command
          psqlCmd = `PGPASSWORD="${restorePassword}" psql -h ${restoreHost} -p ${restorePort} -U ${restoreUser} -d ${cleanRestoreDbName} -f "${restoreFile}"`;
        }

        try {
          // Execute psql
          await execPromise(psqlCmd);

          // Ensure admin user exists after restore
          await ensureAdminUser();
        } catch (error: any) {
          console.error("psql restore error:", error);
          return NextResponse.json({
            error: "Failed to restore database. Make sure PostgreSQL client tools are installed.",
            details: error.message || "Unknown error"
          }, { status: 500 });
        }

        return NextResponse.json({ message: "Database restored successfully. Admin user has been verified/created." });

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error(`Error in database POST:`, error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}

// DELETE /api/system/database/backup - Delete a backup file
export async function DELETE(
  req: NextRequest,
  { params }: { params: { action: string } }
) {
  // Store action in outer scope for error handling
  let action: string;

  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }

    action = params.action;

    if (action === "backup") {
      // Get filename from request body
      const body = await req.json();
      const { filename } = body;

      if (!filename) {
        return NextResponse.json(
          { error: "Filename is required" },
          { status: 400 }
        );
      }

      // Validate filename to prevent directory traversal
      if (filename.includes("..") || filename.includes("/") || filename.includes("\\")) {
        return NextResponse.json(
          { error: "Invalid filename" },
          { status: 400 }
        );
      }

      const backupFile = path.join(process.cwd(), "backups", filename);

      // Check if file exists
      if (!fs.existsSync(backupFile)) {
        return NextResponse.json(
          { error: "Backup file not found" },
          { status: 404 }
        );
      }

      // Delete file
      fs.unlinkSync(backupFile);

      return NextResponse.json({ message: "Backup file deleted successfully" });
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error(`Error in database DELETE:`, error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) {
    return bytes + " B";
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + " KB";
  } else if (bytes < 1024 * 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(2) + " MB";
  } else {
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + " GB";
  }
}
