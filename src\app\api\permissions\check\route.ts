import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// POST /api/permissions/check - Check if user has specific permissions
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the permissions to check from the request body
    const data = await req.json();
    const permissionsToCheck = data.permissions || [];

    if (!Array.isArray(permissionsToCheck) || permissionsToCheck.length === 0) {
      return NextResponse.json(
        { error: "Invalid permissions array" },
        { status: 400 }
      );
    }

    // Check if user is admin (admins have all permissions)
    if (session.user.role === "ADMIN") {
      console.log("Admin user detected in permissions check. Granting all permissions.");
      return NextResponse.json({
        permissions: permissionsToCheck,
        isAdmin: true,
        message: "Admin user has all permissions"
      });
    }

    // Get user permissions from database
    const user = await db.user.findUnique({
      where: {
        email: session.user.email,
      },
      select: {
        permissions: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Get the names of the user's permissions
    const userPermissionNames = user.permissions.map(p => p.name);

    // Check which of the requested permissions the user has
    const grantedPermissions = permissionsToCheck.filter(permission =>
      userPermissionNames.includes(permission)
    );

    return NextResponse.json({
      permissions: grantedPermissions,
      isAdmin: false
    });
  } catch (error) {
    console.error("Error checking permissions:", error);
    return NextResponse.json(
      { error: "Failed to check permissions" },
      { status: 500 }
    );
  }
}
