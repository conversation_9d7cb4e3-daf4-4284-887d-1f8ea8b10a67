import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/receipt-vouchers/:id - Get receipt voucher by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounting data
    const hasViewPermission = await hasPermission("view_accounting");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view receipt voucher details" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Get receipt voucher with associated data
    const voucher = await db.receiptVoucher.findUnique({
      where: {
        id,
      },
      include: {
        paymentMethod: {
          select: {
            id: true,
            code: true,
            name: true,
            account: {
              select: {
                id: true,
                code: true,
                name: true,
                balance: true,
              },
            },
          },
        },
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
            isCustomer: true,
            isSupplier: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    if (!voucher) {
      return NextResponse.json(
        { error: "Receipt voucher not found" },
        { status: 404 }
      );
    }

    // Get journal entry if exists
    let journalEntry = null;
    if (voucher.journalEntryId) {
      journalEntry = await db.journalEntry.findUnique({
        where: {
          id: voucher.journalEntryId,
        },
        include: {
          journal: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          debitAccount: {
            select: {
              id: true,
              code: true,
              name: true,
              type: true,
            },
          },
          creditAccount: {
            select: {
              id: true,
              code: true,
              name: true,
              type: true,
            },
          },
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        ...voucher,
        journalEntry,
      },
    });
  } catch (error) {
    console.error("Error fetching receipt voucher:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch receipt voucher" },
      { status: 500 }
    );
  }
}

// PUT /api/accounting/receipt-vouchers/:id - Update receipt voucher
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to update receipt vouchers" },
        { status: 403 }
      );
    }

    const id = params.id;
    const data = await req.json();

    // Check if receipt voucher exists
    const existingVoucher = await db.receiptVoucher.findUnique({
      where: {
        id,
      },
      include: {
        paymentMethod: true,
      },
    });

    if (!existingVoucher) {
      return NextResponse.json(
        { error: "Receipt voucher not found" },
        { status: 404 }
      );
    }

    // Only allow updating description and status
    const updatedVoucher = await db.receiptVoucher.update({
      where: {
        id,
      },
      data: {
        description: data.description || existingVoucher.description,
        status: data.status || existingVoucher.status,
      },
      include: {
        paymentMethod: {
          select: {
            id: true,
            code: true,
            name: true,
          },
        },
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedVoucher,
    });
  } catch (error) {
    console.error("Error updating receipt voucher:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update receipt voucher" },
      { status: 500 }
    );
  }
}

// DELETE /api/accounting/receipt-vouchers/:id - Delete receipt voucher
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to delete receipt vouchers" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Check if receipt voucher exists
    const existingVoucher = await db.receiptVoucher.findUnique({
      where: {
        id,
      },
    });

    if (!existingVoucher) {
      return NextResponse.json(
        { error: "Receipt voucher not found" },
        { status: 404 }
      );
    }

    // Instead of deleting, update status to CANCELLED
    const updatedVoucher = await db.receiptVoucher.update({
      where: {
        id,
      },
      data: {
        status: "CANCELLED",
      },
    });

    return NextResponse.json({
      success: true,
      message: "Receipt voucher cancelled successfully",
    });
  } catch (error) {
    console.error("Error cancelling receipt voucher:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to cancel receipt voucher" },
      { status: 500 }
    );
  }
}
