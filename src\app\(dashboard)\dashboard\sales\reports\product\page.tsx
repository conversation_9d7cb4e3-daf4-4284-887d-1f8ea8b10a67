"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts";
import {
  Download,
  Filter,
  Search,
  Package,
  ArrowUpRight,
  ArrowDownRight,
  Printer
} from "lucide-react";
import { format, subDays, subMonths } from "date-fns";
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";

// Define chart colors
const COLORS = ['#3895e7', '#307aa8', '#4bc0c0', '#ffcd56', '#ff9f40', '#ff6384'];

export default function ProductSalesReportPage() {
  // State for date range and filters
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // State for report data
  const [reportData, setReportData] = useState<any>({
    topProducts: [],
    productCategories: [],
    salesTrend: [],
    productPerformance: []
  });

  // State for categories
  const [categories, setCategories] = useState<any[]>([]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/products/categories');
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch report data
  useEffect(() => {
    const fetchReportData = async () => {
      setIsLoading(true);

      try {
        const formattedStartDate = format(startDate, 'yyyy-MM-dd');
        const formattedEndDate = format(endDate, 'yyyy-MM-dd');

        const response = await fetch(
          `/api/reports/sales/products?startDate=${formattedStartDate}&endDate=${formattedEndDate}&categoryId=${selectedCategory}&search=${searchTerm}`
        );

        if (response.ok) {
          const data = await response.json();
          setReportData(data);
        } else {
          console.error('Error fetching report data:', await response.text());
        }
      } catch (error) {
        console.error('Error fetching report data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReportData();
  }, [startDate, endDate, selectedCategory, searchTerm]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Export report to Excel
  const exportToExcel = () => {
    try {
      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Create top products worksheet
      const topProductsData = [
        ['Top Selling Products'],
        [`Period: ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`],
        [''],
        ['Product', 'Category', 'Quantity Sold', 'Total Revenue', 'Average Price']
      ];

      reportData.topProducts.forEach((product: any) => {
        topProductsData.push([
          product.name,
          product.category,
          product.quantitySold,
          product.totalRevenue,
          product.averagePrice
        ]);
      });

      const topProductsWs = XLSX.utils.aoa_to_sheet(topProductsData);
      XLSX.utils.book_append_sheet(workbook, topProductsWs, 'Top Products');

      // Create product categories worksheet
      const categoriesData = [
        ['Sales by Product Category'],
        ['Category', 'Products Count', 'Total Quantity', 'Total Revenue', 'Percentage']
      ];

      reportData.productCategories.forEach((category: any) => {
        categoriesData.push([
          category.name,
          category.productsCount,
          category.totalQuantity,
          category.totalRevenue,
          category.percentage
        ]);
      });

      const categoriesWs = XLSX.utils.aoa_to_sheet(categoriesData);
      XLSX.utils.book_append_sheet(workbook, categoriesWs, 'Categories');

      // Create sales trend worksheet
      const trendData = [
        ['Product Sales Trend'],
        ['Date', 'Quantity Sold', 'Revenue']
      ];

      reportData.salesTrend.forEach((item: any) => {
        trendData.push([
          item.date,
          item.quantity,
          item.revenue
        ]);
      });

      const trendWs = XLSX.utils.aoa_to_sheet(trendData);
      XLSX.utils.book_append_sheet(workbook, trendWs, 'Sales Trend');

      // Create product performance worksheet
      const performanceData = [
        ['Product Performance Metrics'],
        ['Product', 'Category', 'Quantity Sold', 'Revenue', 'Profit Margin', 'Stock Level', 'Turnover Rate']
      ];

      reportData.productPerformance.forEach((product: any) => {
        performanceData.push([
          product.name,
          product.category,
          product.quantitySold,
          product.revenue,
          product.profitMargin,
          product.stockLevel,
          product.turnoverRate
        ]);
      });

      const performanceWs = XLSX.utils.aoa_to_sheet(performanceData);
      XLSX.utils.book_append_sheet(workbook, performanceWs, 'Performance');

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      // Save file
      saveAs(blob, `product_sales_report_${format(new Date(), 'yyyy-MM-dd')}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
    }
  };

  // Print report
  const printReport = () => {
    window.print();
  };

  return (
    <div className="space-y-6 print:space-y-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 print:hidden">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Product Sales Report</h2>
          <p className="text-muted-foreground">
            Analyze sales performance by product
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={exportToExcel}>
            <Download className="mr-2 h-4 w-4" />
            Export to Excel
          </Button>
          <Button variant="outline" onClick={printReport}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="print:hidden">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <DatePicker date={startDate} setDate={setStartDate} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <DatePicker date={endDate} setDate={setEndDate} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Category</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Search Product</label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search by product name"
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-3 text-lg text-gray-700">Loading report data...</span>
        </div>
      ) : (
        <>
          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Sales by Category</CardTitle>
                <CardDescription>
                  Distribution of sales across product categories
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={reportData.productCategories}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="totalRevenue"
                      nameKey="name"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {reportData.productCategories.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [formatCurrency(value as number), 'Revenue']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Sales Trend</CardTitle>
                <CardDescription>
                  Sales trend over the selected period
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={reportData.salesTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" orientation="left" stroke="#3895e7" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip formatter={(value, name) => [
                      name === 'revenue' ? formatCurrency(value as number) : value,
                      name === 'revenue' ? 'Revenue' : 'Quantity'
                    ]} />
                    <Legend />
                    <Bar
                      yAxisId="right"
                      dataKey="quantity"
                      name="Quantity Sold"
                      fill="#3895e7"
                    />
                    <Bar
                      yAxisId="left"
                      dataKey="revenue"
                      name="Revenue"
                      fill="#82ca9d"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Products Table */}
          <Card>
            <CardHeader>
              <CardTitle>Top Selling Products</CardTitle>
              <CardDescription>
                Products with the highest sales volume
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                      <th className="px-6 py-3">Product</th>
                      <th className="px-6 py-3">Category</th>
                      <th className="px-6 py-3">Quantity Sold</th>
                      <th className="px-6 py-3">Total Revenue</th>
                      <th className="px-6 py-3">Average Price</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.topProducts.map((product: any) => (
                      <tr key={product.id} className="bg-white border-b hover:bg-gray-50">
                        <td className="px-6 py-4 font-medium">{product.name}</td>
                        <td className="px-6 py-4">{product.category}</td>
                        <td className="px-6 py-4">{product.quantitySold}</td>
                        <td className="px-6 py-4">{formatCurrency(product.totalRevenue)}</td>
                        <td className="px-6 py-4">{formatCurrency(product.averagePrice)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Product Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Product Performance Metrics</CardTitle>
              <CardDescription>
                Detailed performance analysis of products
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                      <th className="px-6 py-3">Product</th>
                      <th className="px-6 py-3">Profit Margin</th>
                      <th className="px-6 py-3">Stock Level</th>
                      <th className="px-6 py-3">Turnover Rate</th>
                      <th className="px-6 py-3">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.productPerformance.map((product: any) => (
                      <tr key={product.id} className="bg-white border-b hover:bg-gray-50">
                        <td className="px-6 py-4 font-medium">{product.name}</td>
                        <td className="px-6 py-4">{product.profitMargin}%</td>
                        <td className="px-6 py-4">{product.stockLevel}</td>
                        <td className="px-6 py-4">{product.turnoverRate}</td>
                        <td className="px-6 py-4">
                          <Badge className={
                            product.status === 'High Performing' ? 'bg-green-100 text-green-800' :
                            product.status === 'Average' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }>
                            {product.status}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
