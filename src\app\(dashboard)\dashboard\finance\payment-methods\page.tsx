"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

export default function PaymentMethodsPage() {
  const [paymentMethods, setPaymentMethods] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState("all");

  useEffect(() => {
    const fetchPaymentMethodsBalances = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/finance/payment-methods-balances?period=${period}`);
        if (!response.ok) {
          throw new Error("Failed to fetch payment methods balances");
        }
        const data = await response.json();
        setPaymentMethods(data);
        setError(null);
      } catch (error) {
        console.error("Error fetching payment methods balances:", error);
        setError(error instanceof Error ? error.message : "Failed to load payment methods data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPaymentMethodsBalances();
  }, [period]);

  // Function to get payment method name in English
  const getPaymentMethodName = (method: string) => {
    switch (method) {
      case "cash":
        return "Cash";
      case "vodafoneCash":
        return "Vodafone Cash";
      case "bankTransfer":
        return "Bank Transfer";
      case "creditCard":
        return "Credit Card";
      case "customerAccount":
        return "Customer Account";
      default:
        return method;
    }
  };

  // Function to get color for payment method
  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case "cash":
        return "bg-green-100 text-green-800 border-green-200";
      case "vodafoneCash":
        return "bg-red-100 text-red-800 border-red-200";
      case "bankTransfer":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "creditCard":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "customerAccount":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <p className="mt-4 text-gray-600">Loading payment methods data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <div className="flex items-center justify-center text-red-500 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-center text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-center text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Create an array of payment methods for easier rendering
  const paymentMethodsArray = [
    { key: "cash", value: paymentMethods.cash },
    { key: "vodafoneCash", value: paymentMethods.vodafoneCash },
    { key: "bankTransfer", value: paymentMethods.bankTransfer },
    { key: "creditCard", value: paymentMethods.creditCard },
    { key: "customerAccount", value: paymentMethods.customerAccount },
  ].sort((a, b) => b.value - a.value); // Sort by value (highest first)

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-4 md:mb-0">Payment Methods</h1>
        <div className="flex space-x-2">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="bg-white border border-gray-300 text-gray-700 py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>

      {/* Total Balance Card */}
      <div className="bg-white shadow-lg rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Total Balance</h2>
        <div className="text-3xl font-bold text-indigo-600">{paymentMethods.total.toFixed(2)} EGP</div>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {paymentMethodsArray.map((method) => (
            <div
              key={method.key}
              className={`p-4 rounded-lg border ${getPaymentMethodColor(method.key)}`}
            >
              <div className="text-sm font-medium">{getPaymentMethodName(method.key)}</div>
              <div className="text-lg font-semibold mt-1">{method.value.toFixed(2)} EGP</div>
            </div>
          ))}
        </div>
      </div>

      {/* Payment Methods Details */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Payment Methods Details</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Method
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Balance
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Percentage
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paymentMethodsArray.map((method) => (
                <tr key={method.key} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full ${getPaymentMethodColor(method.key).split(" ")[0]} mr-2`}></div>
                      <div className="text-sm font-medium text-gray-900">{getPaymentMethodName(method.key)}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{method.value.toFixed(2)} EGP</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {paymentMethods.total === 0 ? 0 : Math.round((method.value / paymentMethods.total) * 100)}%
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/dashboard/finance/payment-methods/${method.key}`}
                      className="text-indigo-600 hover:text-indigo-900 hover:underline"
                    >
                      View Details
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
