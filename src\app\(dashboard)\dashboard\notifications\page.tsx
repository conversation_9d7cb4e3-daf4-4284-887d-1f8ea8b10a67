"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import { 
  Bell, 
  Check, 
  CheckCheck, 
  Star, 
  Gift, 
  ShoppingCart, 
  Package, 
  Info, 
  Calendar,
  Filter,
  Search,
  Trash2,
  RefreshCw
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";

interface Notification {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  link?: string;
  read: boolean;
  metadata?: string;
  createdAt: string;
  updatedAt: string;
}

export default function NotificationsPage() {
  const { data: session } = useSession();
  const router = useRouter();
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState<[Date | undefined, Date | undefined]>([undefined, undefined]);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  
  // Fetch notifications
  const fetchNotifications = async () => {
    if (!session?.user) return;
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/notifications?userId=${session.user.id}`);
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications);
        setFilteredNotifications(data.notifications);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Mark notification as read
  const markAsRead = async (id: string) => {
    try {
      const response = await fetch("/api/notifications", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id,
          read: true,
        }),
      });
      
      if (response.ok) {
        setNotifications(
          notifications.map((notification) =>
            notification.id === id
              ? { ...notification, read: true }
              : notification
          )
        );
        
        setFilteredNotifications(
          filteredNotifications.map((notification) =>
            notification.id === id
              ? { ...notification, read: true }
              : notification
          )
        );
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };
  
  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter((n) => !n.read);
      
      for (const notification of unreadNotifications) {
        await fetch("/api/notifications", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id: notification.id,
            read: true,
          }),
        });
      }
      
      setNotifications(
        notifications.map((notification) => ({
          ...notification,
          read: true,
        }))
      );
      
      setFilteredNotifications(
        filteredNotifications.map((notification) => ({
          ...notification,
          read: true,
        }))
      );
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };
  
  // Mark selected notifications as read
  const markSelectedAsRead = async () => {
    try {
      for (const id of selectedNotifications) {
        await fetch("/api/notifications", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id,
            read: true,
          }),
        });
      }
      
      setNotifications(
        notifications.map((notification) =>
          selectedNotifications.includes(notification.id)
            ? { ...notification, read: true }
            : notification
        )
      );
      
      setFilteredNotifications(
        filteredNotifications.map((notification) =>
          selectedNotifications.includes(notification.id)
            ? { ...notification, read: true }
            : notification
        )
      );
      
      setSelectedNotifications([]);
    } catch (error) {
      console.error("Error marking selected notifications as read:", error);
    }
  };
  
  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    
    if (notification.link) {
      router.push(notification.link);
    }
  };
  
  // Get icon for notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "LOYALTY":
        return <Star className="h-5 w-5 text-yellow-500" />;
      case "BIRTHDAY":
        return <Gift className="h-5 w-5 text-pink-500" />;
      case "SALE":
        return <ShoppingCart className="h-5 w-5 text-green-500" />;
      case "CREDIT_NOTE":
        return <Package className="h-5 w-5 text-red-500" />;
      case "SYSTEM":
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };
  
  // Get notification type label
  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case "LOYALTY":
        return "Loyalty";
      case "BIRTHDAY":
        return "Birthday";
      case "SALE":
        return "Sale";
      case "CREDIT_NOTE":
        return "Credit Note";
      case "SYSTEM":
        return "System";
      default:
        return type;
    }
  };
  
  // Get notification type color
  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case "LOYALTY":
        return "bg-yellow-100 text-yellow-800";
      case "BIRTHDAY":
        return "bg-pink-100 text-pink-800";
      case "SALE":
        return "bg-green-100 text-green-800";
      case "CREDIT_NOTE":
        return "bg-red-100 text-red-800";
      case "SYSTEM":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };
  
  // Toggle notification selection
  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications((prev) =>
      prev.includes(id)
        ? prev.filter((notificationId) => notificationId !== id)
        : [...prev, id]
    );
  };
  
  // Toggle select all notifications
  const toggleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map((n) => n.id));
    }
  };
  
  // Apply filters
  useEffect(() => {
    let filtered = [...notifications];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (notification) =>
          notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          notification.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply date range filter
    if (dateRange[0] && dateRange[1]) {
      filtered = filtered.filter((notification) => {
        const notificationDate = new Date(notification.createdAt);
        return (
          notificationDate >= dateRange[0]! &&
          notificationDate <= dateRange[1]!
        );
      });
    }
    
    // Apply type filter
    if (selectedTypes.length > 0) {
      filtered = filtered.filter((notification) =>
        selectedTypes.includes(notification.type)
      );
    }
    
    // Apply read/unread filter
    if (showUnreadOnly) {
      filtered = filtered.filter((notification) => !notification.read);
    }
    
    setFilteredNotifications(filtered);
  }, [notifications, searchTerm, dateRange, selectedTypes, showUnreadOnly]);
  
  // Fetch notifications on mount
  useEffect(() => {
    if (session?.user) {
      fetchNotifications();
    }
  }, [session]);
  
  // Get unique notification types
  const notificationTypes = Array.from(
    new Set(notifications.map((notification) => notification.type))
  );
  
  // Get unread count
  const unreadCount = notifications.filter((n) => !n.read).length;
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchNotifications}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button 
            variant="default" 
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark All as Read
          </Button>
        </div>
      </div>
      
      {/* Filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Filters</CardTitle>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => {
                setSearchTerm("");
                setDateRange([undefined, undefined]);
                setSelectedTypes([]);
                setShowUnreadOnly(false);
              }}
            >
              Clear Filters
            </Button>
          </div>
          <CardDescription>
            Filter notifications by various criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="Search notifications..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <div className="flex items-center space-x-2">
                <DatePicker
                  value={dateRange[0]}
                  onChange={(date) => setDateRange([date, dateRange[1]])}
                  placeholder="From"
                />
                <span>to</span>
                <DatePicker
                  value={dateRange[1]}
                  onChange={(date) => setDateRange([dateRange[0], date])}
                  placeholder="To"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Notification Type</label>
              <Select
                value={selectedTypes.length === 1 ? selectedTypes[0] : ""}
                onValueChange={(value) => {
                  if (value === "") {
                    setSelectedTypes([]);
                  } else {
                    setSelectedTypes([value]);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Types</SelectItem>
                  {notificationTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {getNotificationTypeLabel(type)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <div className="flex items-center space-x-2 pt-2">
                <Checkbox
                  id="unread-only"
                  checked={showUnreadOnly}
                  onCheckedChange={(checked) => setShowUnreadOnly(checked as boolean)}
                />
                <label htmlFor="unread-only" className="text-sm">
                  Show unread only
                </label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>All Notifications</CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {filteredNotifications.length} notifications
              </Badge>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {unreadCount} unread
                </Badge>
              )}
            </div>
          </div>
          <CardDescription>
            Your recent notifications and alerts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-gray-500">
              <Bell className="h-12 w-12 text-gray-300 mb-2" />
              <p>No notifications found</p>
            </div>
          ) : (
            <div>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={
                      selectedNotifications.length > 0 &&
                      selectedNotifications.length === filteredNotifications.length
                    }
                    onCheckedChange={toggleSelectAll}
                  />
                  <label htmlFor="select-all" className="text-sm">
                    Select All
                  </label>
                </div>
                
                {selectedNotifications.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={markSelectedAsRead}
                      disabled={selectedNotifications.every(
                        (id) => notifications.find((n) => n.id === id)?.read
                      )}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Mark Selected as Read
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="divide-y">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`py-4 ${
                      !notification.read ? "bg-blue-50" : ""
                    }`}
                  >
                    <div className="flex items-start">
                      <div className="mr-3 pt-1">
                        <Checkbox
                          checked={selectedNotifications.includes(notification.id)}
                          onCheckedChange={() => toggleNotificationSelection(notification.id)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                      
                      <div className="flex-shrink-0 mr-3">
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      <div 
                        className="flex-1 cursor-pointer"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex items-start justify-between">
                          <div>
                            <p className={`font-medium ${!notification.read ? "text-blue-600" : "text-gray-900"}`}>
                              {notification.title}
                            </p>
                            <Badge 
                              className={`mt-1 text-xs ${getNotificationTypeColor(notification.type)}`}
                            >
                              {getNotificationTypeLabel(notification.type)}
                            </Badge>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-gray-500">
                              {format(new Date(notification.createdAt), "MMM d, yyyy")}
                            </p>
                            <p className="text-xs text-gray-500">
                              {format(new Date(notification.createdAt), "h:mm a")}
                            </p>
                          </div>
                        </div>
                        <p className="text-gray-600 mt-1">{notification.message}</p>
                        {notification.link && (
                          <p className="text-xs text-blue-600 mt-1">Click to view details</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
