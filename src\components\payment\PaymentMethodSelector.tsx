"use client";

import React, { useState, useEffect } from 'react';
import PaymentMethodIcon from './PaymentMethodIcon';

interface PaymentMethod {
  id: string;
  code: string;
  name: string;
  isActive: boolean;
  iconName: string | null;
  iconUrl: string | null;
  color: string | null;
  branchIds: string[];
}

interface PaymentMethodSelectorProps {
  selectedMethod: string;
  onSelect: (methodCode: string) => void;
  branchId?: string;
  includeCustomerAccount?: boolean;
  includeSupplierAccount?: boolean;
  disabled?: boolean;
  className?: string;
}

export default function PaymentMethodSelector({
  selectedMethod,
  onSelect,
  branchId,
  includeCustomerAccount = false,
  includeSupplierAccount = false,
  disabled = false,
  className = ''
}: PaymentMethodSelectorProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/settings/payment-methods');

        if (!response.ok) {
          throw new Error('Failed to fetch payment methods');
        }

        const data = await response.json();

        // Filter active payment methods
        let filteredMethods = data.filter((method: PaymentMethod) => method.isActive);

        // Filter by branch if branchId is provided
        if (branchId) {
          filteredMethods = filteredMethods.filter((method: PaymentMethod) =>
            !method.branchIds ||
            method.branchIds.length === 0 ||
            method.branchIds.includes(branchId)
          );
        }

        setPaymentMethods(filteredMethods);
      } catch (error) {
        console.error('Error fetching payment methods:', error);
        setError(error instanceof Error ? error.message : 'Failed to load payment methods');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPaymentMethods();
  }, [branchId]);

  // Add special account types if needed
  const allMethods = [...paymentMethods];

  if (includeCustomerAccount) {
    allMethods.push({
      id: 'customer-account',
      code: 'CUSTOMER_ACCOUNT',
      name: 'Customer Account',
      isActive: true,
      iconName: null,
      color: null,
      branchIds: []
    });
  }

  if (includeSupplierAccount) {
    allMethods.push({
      id: 'supplier-account',
      code: 'SUPPLIER_ACCOUNT',
      name: 'Supplier Account',
      isActive: true,
      iconName: null,
      color: null,
      branchIds: []
    });
  }

  if (isLoading) {
    return (
      <div className={`grid grid-cols-2 gap-2 ${className}`}>
        <div className="p-2 border rounded-md flex items-center animate-pulse bg-gray-100 h-10"></div>
        <div className="p-2 border rounded-md flex items-center animate-pulse bg-gray-100 h-10"></div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 text-sm">Error: {error}</div>;
  }

  if (allMethods.length === 0) {
    return <div className="text-gray-500 text-sm">No payment methods available</div>;
  }

  return (
    <div className={`grid grid-cols-2 gap-2 ${className}`}>
      {allMethods.map((method) => (
        <div
          key={method.id}
          className={`p-2 border rounded-md cursor-pointer flex items-center ${
            selectedMethod === method.code
              ? 'bg-indigo-50 border-indigo-500'
              : 'border-gray-300 hover:bg-gray-50'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => !disabled && onSelect(method.code)}
        >
          <PaymentMethodIcon
            methodCode={method.code}
            iconName={method.iconName}
            iconUrl={method.iconUrl}
            color={method.color}
          />
          <span className="font-medium text-black ml-2">{method.name}</span>
        </div>
      ))}
    </div>
  );
}
