"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Loader2, Download, Search, RefreshCw } from "lucide-react";
import { format } from "date-fns";

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
  balance: number;
  debitBalance: number;
  creditBalance: number;
}

export default function TrialBalancePage() {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [asOfDate, setAsOfDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState(false);

  // Fetch trial balance
  const fetchTrialBalance = async () => {
    setIsLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      
      if (asOfDate) {
        params.append("asOfDate", asOfDate.toISOString());
      }

      const response = await fetch(`/api/accounting/trial-balance?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setAccounts(data.accounts || []);
      }
    } catch (error) {
      console.error("Error fetching trial balance:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    if (accounts.length === 0) return;

    // Create CSV content
    let csvContent = "Account Code,Account Name,Account Type,Debit,Credit\n";
    
    accounts.forEach(account => {
      csvContent += [
        account.code,
        account.name,
        account.type,
        account.debitBalance.toFixed(2),
        account.creditBalance.toFixed(2)
      ].join(",") + "\n";
    });
    
    // Add totals
    const totalDebit = accounts.reduce((sum, account) => sum + account.debitBalance, 0);
    const totalCredit = accounts.reduce((sum, account) => sum + account.creditBalance, 0);
    
    csvContent += [
      "",
      "TOTAL",
      "",
      totalDebit.toFixed(2),
      totalCredit.toFixed(2)
    ].join(",") + "\n";
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `trial_balance_${format(asOfDate || new Date(), "yyyyMMdd")}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Group accounts by type
  const groupedAccounts = accounts.reduce((groups, account) => {
    const type = account.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(account);
    return groups;
  }, {} as Record<string, Account[]>);

  // Calculate totals
  const totalDebit = accounts.reduce((sum, account) => sum + account.debitBalance, 0);
  const totalCredit = accounts.reduce((sum, account) => sum + account.creditBalance, 0);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Trial Balance</h1>
        <Button variant="outline" onClick={exportToCSV} disabled={accounts.length === 0 || isLoading}>
          <Download className="h-4 w-4 mr-2" />
          Export to CSV
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">As of Date</label>
              <DatePicker date={asOfDate} setDate={setAsOfDate} />
            </div>

            <div className="flex items-end">
              <Button onClick={fetchTrialBalance} disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {accounts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>
              Trial Balance as of {format(asOfDate || new Date(), "MMMM d, yyyy")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-gray-100 border-b">
                      <th className="px-4 py-3 text-left">Account Code</th>
                      <th className="px-4 py-3 text-left">Account Name</th>
                      <th className="px-4 py-3 text-left">Account Type</th>
                      <th className="px-4 py-3 text-right">Debit</th>
                      <th className="px-4 py-3 text-right">Credit</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(groupedAccounts).map(([type, accounts]) => (
                      <>
                        <tr key={type} className="bg-gray-50">
                          <td colSpan={5} className="px-4 py-2 font-medium">{type}</td>
                        </tr>
                        {accounts.map(account => (
                          <tr key={account.id} className="border-b hover:bg-gray-50">
                            <td className="px-4 py-3">{account.code}</td>
                            <td className="px-4 py-3">{account.name}</td>
                            <td className="px-4 py-3">{account.type}</td>
                            <td className="px-4 py-3 text-right">
                              {account.debitBalance > 0 ? account.debitBalance.toFixed(2) : ""}
                            </td>
                            <td className="px-4 py-3 text-right">
                              {account.creditBalance > 0 ? account.creditBalance.toFixed(2) : ""}
                            </td>
                          </tr>
                        ))}
                        <tr className="border-b bg-gray-50">
                          <td colSpan={3} className="px-4 py-2 text-right font-medium">Subtotal for {type}</td>
                          <td className="px-4 py-2 text-right font-medium">
                            {accounts.reduce((sum, account) => sum + account.debitBalance, 0).toFixed(2)}
                          </td>
                          <td className="px-4 py-2 text-right font-medium">
                            {accounts.reduce((sum, account) => sum + account.creditBalance, 0).toFixed(2)}
                          </td>
                        </tr>
                      </>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-100 font-medium">
                      <td colSpan={3} className="px-4 py-3 text-right">TOTAL</td>
                      <td className="px-4 py-3 text-right">{totalDebit.toFixed(2)}</td>
                      <td className="px-4 py-3 text-right">{totalCredit.toFixed(2)}</td>
                    </tr>
                    {Math.abs(totalDebit - totalCredit) > 0.01 && (
                      <tr className="bg-red-50 text-red-700">
                        <td colSpan={3} className="px-4 py-3 text-right font-medium">OUT OF BALANCE</td>
                        <td colSpan={2} className="px-4 py-3 text-center font-medium">
                          {Math.abs(totalDebit - totalCredit).toFixed(2)}
                        </td>
                      </tr>
                    )}
                  </tfoot>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
