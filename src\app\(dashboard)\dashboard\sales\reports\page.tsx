"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { format, subDays } from "date-fns";
import { ArrowDown, ArrowUp, Banknote, CreditCard } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

// Mock report data
const mockReportData = {
  paidSales: 125000.50,
  unpaidSales: 45750.25,
  paidReturns: 15250.75,
  unpaidReturns: 5500.00,
};

export default function SalesReportsPage() {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [selectedBranch, setSelectedBranch] = useState("all");
  const [reportData, setReportData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [branches, setBranches] = useState<any[]>([]);
  const [selectedCardType, setSelectedCardType] = useState<string | null>(null);

  // Set default dates (last 30 days)
  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    setEndDate(today.toISOString().split("T")[0]);
    setStartDate(thirtyDaysAgo.toISOString().split("T")[0]);

    // Fetch branches
    fetchBranches();
  }, []);

  // Fetch branches
  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches');
      if (response.ok) {
        const data = await response.json();
        setBranches([
          { id: "all", name: "All Branches" },
          ...data.map((branch: any) => ({ id: branch.id, name: branch.name }))
        ]);
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
      // Fallback to default branches if API fails
      setBranches([
        { id: "all", name: "All Branches" },
        { id: "branch1", name: "Main Branch" },
        { id: "branch2", name: "Downtown Branch" }
      ]);
    }
  };

  // Generate report
  const generateReport = async () => {
    setIsLoading(true);

    try {
      // Fetch report data from API
      const response = await fetch(`/api/reports/sales?type=financial&startDate=${startDate}&endDate=${endDate}&branchId=${selectedBranch}`);

      if (response.ok) {
        const data = await response.json();
        setReportData(data.data || data);
      } else {
        console.error("Error fetching report:", await response.text());
        // Fallback to mock data if API fails
        setReportData(mockReportData);
      }
    } catch (error) {
      console.error("Error generating report:", error);
      // Fallback to mock data if API fails
      setReportData(mockReportData);
    } finally {
      setIsLoading(false);
    }
  };

  // State for detailed data
  const [paidSalesDetails, setPaidSalesDetails] = useState<any[]>([]);
  const [unpaidSalesDetails, setUnpaidSalesDetails] = useState<any[]>([]);
  const [paidCreditNotesDetails, setPaidCreditNotesDetails] = useState<any[]>([]);
  const [unpaidCreditNotesDetails, setUnpaidCreditNotesDetails] = useState<any[]>([]);
  const [creditLimitWarnings, setCreditLimitWarnings] = useState<any[]>([]);

  // Handle card click to show details
  const handleCardClick = async (cardType: string) => {
    if (selectedCardType === cardType) {
      // If clicking the same card, close the details
      setSelectedCardType(null);
    } else {
      // Otherwise, show details for the clicked card and fetch detailed data
      setSelectedCardType(cardType);

      try {
        // Fetch detailed data based on card type
        if (cardType === 'paidSales' && paidSalesDetails.length === 0) {
          const response = await fetch(`/api/reports/sales/details?type=paidSales&startDate=${startDate}&endDate=${endDate}&branchId=${selectedBranch}`);
          if (response.ok) {
            const data = await response.json();
            setPaidSalesDetails(data.details || []);
          }
        } else if (cardType === 'unpaidSales' && unpaidSalesDetails.length === 0) {
          // Fetch unpaid sales
          const salesResponse = await fetch(`/api/reports/sales/details?type=unpaidSales&startDate=${startDate}&endDate=${endDate}&branchId=${selectedBranch}`);
          if (salesResponse.ok) {
            const data = await salesResponse.json();
            setUnpaidSalesDetails(data.details || []);
          }

          // Fetch credit limit warnings
          const warningsResponse = await fetch(`/api/reports/sales/credit-warnings`);
          if (warningsResponse.ok) {
            const data = await warningsResponse.json();
            setCreditLimitWarnings(data.warnings || []);
          }
        } else if (cardType === 'paidReturns' && paidCreditNotesDetails.length === 0) {
          const response = await fetch(`/api/reports/sales/details?type=paidCreditNotes&startDate=${startDate}&endDate=${endDate}&branchId=${selectedBranch}`);
          if (response.ok) {
            const data = await response.json();
            setPaidCreditNotesDetails(data.details || []);
          }
        } else if (cardType === 'unpaidReturns' && unpaidCreditNotesDetails.length === 0) {
          const response = await fetch(`/api/reports/sales/details?type=unpaidCreditNotes&startDate=${startDate}&endDate=${endDate}&branchId=${selectedBranch}`);
          if (response.ok) {
            const data = await response.json();
            setUnpaidCreditNotesDetails(data.details || []);
          }
        }
      } catch (error) {
        console.error("Error fetching detailed data:", error);
      }
    }
  };

  // Get details for the selected card type
  const getCardDetails = () => {
    if (!selectedCardType || !reportData) return null;

    switch (selectedCardType) {
      case 'paidSales':
        return {
          title: 'Paid Sales Details',
          description: 'Sales that have been fully paid using cash, bank transfer, Vodafone Cash, or Visa.',
          value: reportData.paidSales.toFixed(2),
          color: 'green',
          icon: <CreditCard className="h-6 w-6 text-green-600" />,
          details: paidSalesDetails
        };
      case 'unpaidSales':
        return {
          title: 'Credit Sales Details',
          description: 'Sales that have been made on credit to customer accounts.',
          value: reportData.unpaidSales.toFixed(2),
          color: 'yellow',
          icon: <Banknote className="h-6 w-6 text-yellow-600" />,
          details: unpaidSalesDetails,
          warnings: creditLimitWarnings
        };
      case 'paidReturns':
        return {
          title: 'Paid Credit Notes Details',
          description: 'Credit notes that have been fully refunded using cash, bank transfer, Vodafone Cash, or Visa.',
          value: reportData.paidReturns.toFixed(2),
          color: 'blue',
          icon: <ArrowUp className="h-6 w-6 text-blue-600" />,
          details: paidCreditNotesDetails
        };
      case 'unpaidReturns':
        return {
          title: 'Credit Notes on Account Details',
          description: 'Credit notes that have been applied to customer accounts.',
          value: reportData.unpaidReturns.toFixed(2),
          color: 'red',
          icon: <ArrowDown className="h-6 w-6 text-red-600" />,
          details: unpaidCreditNotesDetails
        };
      case 'netSales':
        return {
          title: 'Net Sales Details',
          description: 'Total sales minus total credit notes, representing the actual revenue.',
          value: (reportData.paidSales + reportData.unpaidSales - reportData.paidReturns - reportData.unpaidReturns).toFixed(2),
          color: 'indigo',
          icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
        };
      default:
        return null;
    }
  };

  // Call generateReport when component mounts or when filters change
  useEffect(() => {
    if (startDate && endDate) {
      generateReport();
    }
  }, [startDate, endDate, selectedBranch]);

  return (
    <div className="space-y-6">
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Sales Reports
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            type="button"
            onClick={() => window.print()}
            className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Print Report
          </button>
          <Link
            href="/dashboard/sales/orders"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Sales Orders
          </Link>
        </div>
      </div>

      {/* Fixed Report Summary at the top */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md mb-6 sticky top-0 z-10">
        <div className="px-4 py-3 sm:px-6">
          <div className="grid grid-cols-5 gap-4">
            {/* Paid Sales */}
            <div
              className={`bg-white overflow-hidden rounded-lg border ${selectedCardType === 'paidSales' ? 'border-green-500 shadow-lg' : 'border-green-100'} p-3 cursor-pointer transition-all duration-200 hover:shadow-md`}
              onClick={() => handleCardClick('paidSales')}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-green-100 rounded-md p-2 mr-2">
                  <CreditCard className="h-4 w-4 text-green-600" />
                </div>
                <div className="w-full">
                  <div className="text-xs font-medium text-gray-500 truncate">
                    Total Paid Sales
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    {reportData ? formatCurrency(reportData.paidSales) : formatCurrency(0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Unpaid Sales */}
            <div
              className={`bg-white overflow-hidden rounded-lg border ${selectedCardType === 'unpaidSales' ? 'border-yellow-500 shadow-lg' : 'border-yellow-100'} p-3 cursor-pointer transition-all duration-200 hover:shadow-md`}
              onClick={() => handleCardClick('unpaidSales')}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-yellow-100 rounded-md p-2 mr-2">
                  <Banknote className="h-4 w-4 text-yellow-600" />
                </div>
                <div className="w-full">
                  <div className="text-xs font-medium text-gray-500 truncate">
                    Total Credit Sales
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    {reportData ? formatCurrency(reportData.unpaidSales) : formatCurrency(0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Paid Returns */}
            <div
              className={`bg-white overflow-hidden rounded-lg border ${selectedCardType === 'paidReturns' ? 'border-blue-500 shadow-lg' : 'border-blue-100'} p-3 cursor-pointer transition-all duration-200 hover:shadow-md`}
              onClick={() => handleCardClick('paidReturns')}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-blue-100 rounded-md p-2 mr-2">
                  <ArrowUp className="h-4 w-4 text-blue-600" />
                </div>
                <div className="w-full">
                  <div className="text-xs font-medium text-gray-500 truncate">
                    Total Paid Credit Notes
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    {reportData ? formatCurrency(reportData.paidReturns) : formatCurrency(0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Unpaid Returns */}
            <div
              className={`bg-white overflow-hidden rounded-lg border ${selectedCardType === 'unpaidReturns' ? 'border-red-500 shadow-lg' : 'border-red-100'} p-3 cursor-pointer transition-all duration-200 hover:shadow-md`}
              onClick={() => handleCardClick('unpaidReturns')}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-red-100 rounded-md p-2 mr-2">
                  <ArrowDown className="h-4 w-4 text-red-600" />
                </div>
                <div className="w-full">
                  <div className="text-xs font-medium text-gray-500 truncate">
                    Total Credit Notes on Account
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    {reportData ? formatCurrency(reportData.unpaidReturns) : formatCurrency(0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Net Sales */}
            <div
              className={`bg-white overflow-hidden rounded-lg border ${selectedCardType === 'netSales' ? 'border-indigo-500 shadow-lg' : 'border-indigo-100'} p-3 cursor-pointer transition-all duration-200 hover:shadow-md`}
              onClick={() => handleCardClick('netSales')}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-indigo-100 rounded-md p-2 mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="w-full">
                  <div className="text-xs font-medium text-gray-500 truncate">
                    Net Sales
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    {reportData ? formatCurrency(reportData.paidSales + reportData.unpaidSales - reportData.paidReturns - reportData.unpaidReturns) : formatCurrency(0)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Card Details Section */}
      {selectedCardType && reportData && (
        <div className="bg-white shadow overflow-hidden sm:rounded-md mb-6">
          <div className="px-4 py-5 sm:p-6">
            {(() => {
              const details = getCardDetails();
              if (!details) return null;

              return (
                <>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {details.title}
                    </h3>
                    <button
                      onClick={() => setSelectedCardType(null)}
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>

                  <div className="flex items-center mb-4">
                    <div className={`flex-shrink-0 bg-${details.color}-100 rounded-md p-3 mr-4`}>
                      {details.icon}
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">{details.description}</p>
                      <p className="text-2xl font-bold text-gray-900">{formatCurrency(parseFloat(details.value))}</p>
                    </div>
                  </div>

                  {selectedCardType === 'paidSales' && (
                    <div className="mt-4 border-t pt-4">
                      <h4 className="text-md font-medium text-gray-900 mb-2">Paid Invoices</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {details.details && details.details.length > 0 ? (
                              details.details.map((sale: any) => (
                                <tr key={sale.id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{sale.invoiceNumber}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(sale.date).toLocaleDateString()}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{sale.contactName}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(sale.totalAmount)}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {sale.payments && sale.payments.length > 0
                                      ? sale.payments.map((payment: any) => (
                                          <div key={payment.id} className="flex items-center">
                                            <span className="mr-1">{payment.method.replace('_', ' ')}</span>
                                            <span className="text-gray-400">({formatCurrency(payment.amount)})</span>
                                          </div>
                                        ))
                                      : sale.paymentMethod
                                    }
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                                  {paidSalesDetails.length === 0 ? "Loading..." : "No paid sales found for the selected period"}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {selectedCardType === 'paidReturns' && (
                    <div className="mt-4 border-t pt-4">
                      <h4 className="text-md font-medium text-gray-900 mb-2">Paid Credit Notes</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Note #</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {details.details && details.details.length > 0 ? (
                              details.details.map((creditNote: any) => (
                                <tr key={creditNote.id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{creditNote.creditNoteNumber}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(creditNote.date).toLocaleDateString()}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{creditNote.contactName}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(creditNote.totalAmount)}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {creditNote.payments && creditNote.payments.length > 0
                                      ? creditNote.payments.map((payment: any) => (
                                          <div key={payment.id} className="flex items-center">
                                            <span className="mr-1">{payment.method.replace('_', ' ')}</span>
                                            <span className="text-gray-400">({formatCurrency(payment.amount)})</span>
                                          </div>
                                        ))
                                      : creditNote.paymentMethod
                                    }
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                                  {paidCreditNotesDetails.length === 0 ? "Loading..." : "No paid credit notes found for the selected period"}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {selectedCardType === 'unpaidReturns' && (
                    <div className="mt-4 border-t pt-4">
                      <h4 className="text-md font-medium text-gray-900 mb-2">Credit Notes on Account</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Note #</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {details.details && details.details.length > 0 ? (
                              details.details.map((creditNote: any) => (
                                <tr key={creditNote.id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{creditNote.creditNoteNumber}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(creditNote.date).toLocaleDateString()}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{creditNote.contactName}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(creditNote.totalAmount)}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                      creditNote.paymentStatus === 'UNPAID'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : 'bg-orange-100 text-orange-800'
                                    }`}>
                                      {creditNote.paymentStatus === 'UNPAID' ? 'UNPAID' : 'PARTIALLY PAID'}
                                    </span>
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                                  {unpaidCreditNotesDetails.length === 0 ? "Loading..." : "No unpaid credit notes found for the selected period"}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {selectedCardType === 'unpaidSales' && (
                    <div className="mt-4 border-t pt-4">
                      <h4 className="text-md font-medium text-gray-900 mb-2">Unpaid Invoices</h4>

                      {/* Credit Limit Warning Section */}
                      {details.warnings && details.warnings.length > 0 && (
                        <div className="mb-6 bg-yellow-50 p-4 rounded-md border border-yellow-200">
                          <h5 className="text-sm font-medium text-yellow-800 mb-2">Credit Limit Warnings</h5>
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-yellow-100">
                                <tr>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-yellow-800 uppercase tracking-wider">Customer</th>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-yellow-800 uppercase tracking-wider">Current Balance</th>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-yellow-800 uppercase tracking-wider">Credit Limit</th>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-yellow-800 uppercase tracking-wider">Usage</th>
                                  <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-yellow-800 uppercase tracking-wider">Credit Period</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {details.warnings.map((warning: any) => {
                                  const usagePercent = warning.creditLimit > 0
                                    ? Math.min(100, Math.round((warning.balance / warning.creditLimit) * 100))
                                    : 100;

                                  return (
                                    <tr key={warning.contactId}>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{warning.contactName}</td>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{warning.balance.toFixed(2)} EGP</td>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{warning.creditLimit.toFixed(2)} EGP</td>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                        <div className="flex items-center">
                                          <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                            <div
                                              className={`h-2.5 rounded-full ${
                                                usagePercent >= 100 ? 'bg-red-500' :
                                                usagePercent >= 80 ? 'bg-yellow-500' : 'bg-green-500'
                                              }`}
                                              style={{ width: `${usagePercent}%` }}
                                            ></div>
                                          </div>
                                          <span>{usagePercent}%</span>
                                        </div>
                                      </td>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{warning.creditPeriod} days</td>
                                    </tr>
                                  );
                                })}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}

                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {details.details && details.details.length > 0 ? (
                              details.details.map((sale: any) => {
                                // Calculate due date based on sale date and customer credit period
                                const saleDate = new Date(sale.date);
                                const creditPeriod = sale.creditPeriod || 30; // Default to 30 days if not specified
                                const dueDate = new Date(saleDate);
                                dueDate.setDate(dueDate.getDate() + creditPeriod);

                                // Check if overdue
                                const isOverdue = dueDate < new Date();

                                return (
                                  <tr key={sale.id}>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{sale.invoiceNumber}</td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(sale.date).toLocaleDateString()}</td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{sale.contactName}</td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{sale.totalAmount.toFixed(2)} EGP</td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        sale.paymentStatus === 'UNPAID'
                                          ? 'bg-yellow-100 text-yellow-800'
                                          : 'bg-orange-100 text-orange-800'
                                      }`}>
                                        {sale.paymentStatus}
                                      </span>
                                    </td>
                                    <td className={`px-6 py-4 whitespace-nowrap text-sm ${isOverdue ? 'font-medium text-red-600' : 'text-gray-500'}`}>
                                      {dueDate.toLocaleDateString()}
                                      {isOverdue && ' (Overdue)'}
                                    </td>
                                  </tr>
                                );
                              })
                            ) : (
                              <tr>
                                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                                  {unpaidSalesDetails.length === 0 ? "Loading..." : "No unpaid sales found for the selected period"}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {selectedCardType === 'netSales' && (
                    <div className="mt-4 border-t pt-4">
                      <h4 className="text-md font-medium text-gray-900 mb-2">Net Sales Summary</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-md">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">Sales</h5>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500">Paid Sales:</span>
                              <span className="text-sm font-medium">{reportData.paidSales.toFixed(2)} EGP</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500">Credit Sales:</span>
                              <span className="text-sm font-medium">{reportData.unpaidSales.toFixed(2)} EGP</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 mt-1">
                              <span className="text-sm font-medium">Total Sales:</span>
                              <span className="text-sm font-medium">{(reportData.paidSales + reportData.unpaidSales).toFixed(2)} EGP</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-md">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">Credit Notes</h5>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500">Paid Credit Notes:</span>
                              <span className="text-sm font-medium">{reportData.paidReturns.toFixed(2)} EGP</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500">Credit Notes on Account:</span>
                              <span className="text-sm font-medium">{reportData.unpaidReturns.toFixed(2)} EGP</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 mt-1">
                              <span className="text-sm font-medium">Total Credit Notes:</span>
                              <span className="text-sm font-medium">{(reportData.paidReturns + reportData.unpaidReturns).toFixed(2)} EGP</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-indigo-50 p-4 rounded-md md:col-span-2">
                          <h5 className="text-sm font-medium text-indigo-700 mb-2">Net Sales Calculation</h5>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500">Total Sales:</span>
                              <span className="text-sm font-medium">{(reportData.paidSales + reportData.unpaidSales).toFixed(2)} EGP</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500">Total Credit Notes:</span>
                              <span className="text-sm font-medium">- {(reportData.paidReturns + reportData.unpaidReturns).toFixed(2)} EGP</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 mt-1">
                              <span className="text-sm font-medium text-indigo-700">Net Sales:</span>
                              <span className="text-sm font-bold text-indigo-700">{(reportData.paidSales + reportData.unpaidSales - reportData.paidReturns - reportData.unpaidReturns).toFixed(2)} EGP</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              );
            })()}
          </div>
        </div>
      )}

      {/* Report Filters */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Filter Reports
          </h3>
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            {/* Branch */}
            <div className="sm:col-span-2">
              <label htmlFor="branch" className="block text-sm font-medium text-gray-700">
                Branch
              </label>
              <select
                id="branch"
                name="branch"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                value={selectedBranch}
                onChange={(e) => setSelectedBranch(e.target.value)}
              >
                {branches.map((branch) => (
                  <option key={branch.id} value={branch.id}>
                    {branch.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range */}
            <div className="sm:col-span-2">
              <label htmlFor="start-date" className="block text-sm font-medium text-gray-700">
                Start Date
              </label>
              <input
                type="date"
                name="start-date"
                id="start-date"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="end-date" className="block text-sm font-medium text-gray-700">
                End Date
              </label>
              <input
                type="date"
                name="end-date"
                id="end-date"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>

          <div className="mt-4">
            <button
              type="button"
              onClick={generateReport}
              disabled={isLoading || !startDate || !endDate}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {isLoading ? "Loading..." : "Update Report"}
            </button>
          </div>
        </div>
      </div>

      {/* Report Results - Detailed View */}
      {reportData && (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Report Details
            </h3>

            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-2">
              {/* Paid Sales */}
              <div className="bg-white overflow-hidden shadow rounded-lg border border-green-100">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-green-100 rounded-md p-3">
                      <CreditCard className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total Paid Sales
                        </dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {reportData.paidSales.toFixed(2)} ج.م
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              {/* Unpaid Sales */}
              <div className="bg-white overflow-hidden shadow rounded-lg border border-yellow-100">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                      <Banknote className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total Credit Sales
                        </dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {reportData.unpaidSales.toFixed(2)} EGP
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              {/* Paid Returns */}
              <div className="bg-white overflow-hidden shadow rounded-lg border border-blue-100">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                      <ArrowUp className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total Paid Credit Notes
                        </dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {reportData.paidReturns.toFixed(2)} EGP
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              {/* Unpaid Returns */}
              <div className="bg-white overflow-hidden shadow rounded-lg border border-red-100">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-red-100 rounded-md p-3">
                      <ArrowDown className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total Credit Notes on Account
                        </dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {reportData.unpaidReturns.toFixed(2)} EGP
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              {/* Net Sales */}
              <div className="bg-white overflow-hidden shadow rounded-lg border border-indigo-100 lg:col-span-2">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-indigo-100 rounded-md p-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Net Sales (Sales - Credit Notes)
                        </dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {(reportData.paidSales + reportData.unpaidSales - reportData.paidReturns - reportData.unpaidReturns).toFixed(2)} EGP
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Date Range Info */}
            <div className="mt-4 text-sm text-gray-500 text-center">
              <p>
                Report for period: {startDate} to {endDate}
                {selectedBranch !== "all" && ` - ${branches.find(b => b.id === selectedBranch)?.name || ""}`}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
