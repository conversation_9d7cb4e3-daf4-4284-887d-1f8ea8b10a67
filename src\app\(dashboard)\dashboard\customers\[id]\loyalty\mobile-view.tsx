"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  Award, 
  CreditCard, 
  DollarSign, 
  Gift, 
  Plus, 
  Minus, 
  ShoppingBag, 
  RefreshCw,
  TrendingUp,
  Star,
  Calendar,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { format } from "date-fns";

interface LoyaltyData {
  contact: {
    id: string;
    name: string;
    phone: string;
    isVIP: boolean;
    loyaltyPoints: number;
    loyaltyTier: string;
  };
  loyaltyInfo: {
    currentPoints: number;
    availableValue: number;
    tier: string;
    isVIP: boolean;
  };
  recentTransactions: Array<{
    type: string;
    reference: string;
    date: string;
    points: number;
    description: string;
  }>;
}

interface MobileLoyaltyViewProps {
  loyaltyData: LoyaltyData;
  isUpdating: boolean;
  pointsToAdd: number;
  setPointsToAdd: (points: number) => void;
  pointsToRedeem: number;
  setPointsToRedeem: (points: number) => void;
  handleAddPoints: () => Promise<void>;
  handleRedeemPoints: () => Promise<void>;
  handleToggleVIP: () => Promise<void>;
}

export default function MobileLoyaltyView({
  loyaltyData,
  isUpdating,
  pointsToAdd,
  setPointsToAdd,
  pointsToRedeem,
  setPointsToRedeem,
  handleAddPoints,
  handleRedeemPoints,
  handleToggleVIP
}: MobileLoyaltyViewProps) {
  const router = useRouter();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    summary: true,
    manage: false,
    transactions: false
  });
  
  const { contact, loyaltyInfo, recentTransactions } = loyaltyData;
  
  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };
  
  // Get tier color
  const getTierColor = (tier: string) => {
    switch (tier) {
      case "PLATINUM":
        return "#8884d8";
      case "GOLD":
        return "#ffc658";
      case "SILVER":
        return "#82ca9d";
      default:
        return "#8884d8";
    }
  };
  
  // Get next tier info
  const getNextTierInfo = (currentTier: string, currentPoints: number) => {
    switch (currentTier) {
      case "PLATINUM":
        return {
          tier: "PLATINUM",
          pointsNeeded: 0,
          progress: 100
        };
      case "GOLD":
        const platinumPoints = 10000;
        const pointsToplatinum = platinumPoints - currentPoints;
        const platinumProgress = (currentPoints / platinumPoints) * 100;
        return {
          tier: "PLATINUM",
          pointsNeeded: pointsToplatinum,
          progress: platinumProgress
        };
      case "SILVER":
        const goldPoints = 5000;
        const pointsToGold = goldPoints - currentPoints;
        const goldProgress = (currentPoints / goldPoints) * 100;
        return {
          tier: "GOLD",
          pointsNeeded: pointsToGold,
          progress: goldProgress
        };
      default:
        const silverPoints = 1000;
        const pointsToSilver = silverPoints - currentPoints;
        const silverProgress = (currentPoints / silverPoints) * 100;
        return {
          tier: "SILVER",
          pointsNeeded: pointsToSilver,
          progress: silverProgress
        };
    }
  };
  
  const nextTierInfo = getNextTierInfo(loyaltyInfo.tier, loyaltyInfo.currentPoints);
  
  return (
    <div className="space-y-4">
      {/* Customer Name */}
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold text-gray-900">
          {contact.name}
        </h1>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">VIP</span>
          <Switch 
            checked={loyaltyInfo.isVIP} 
            onCheckedChange={handleToggleVIP}
            disabled={isUpdating}
          />
        </div>
      </div>
      
      {/* Summary Section */}
      <Card>
        <CardHeader className="pb-2 flex flex-row items-center justify-between cursor-pointer"
          onClick={() => toggleSection('summary')}>
          <CardTitle className="text-lg">Loyalty Summary</CardTitle>
          {expandedSections.summary ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </CardHeader>
        {expandedSections.summary && (
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center mb-1">
                  <Gift className="h-4 w-4 text-blue-600 mr-1" />
                  <p className="text-xs text-blue-600 font-medium">Points</p>
                </div>
                <p className="text-xl font-bold">{loyaltyInfo.currentPoints}</p>
              </div>
              
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="flex items-center mb-1">
                  <DollarSign className="h-4 w-4 text-green-600 mr-1" />
                  <p className="text-xs text-green-600 font-medium">Value</p>
                </div>
                <p className="text-xl font-bold">{loyaltyInfo.availableValue.toFixed(2)} ج.م</p>
              </div>
              
              <div className="bg-purple-50 p-3 rounded-lg">
                <div className="flex items-center mb-1">
                  <Award className="h-4 w-4 text-purple-600 mr-1" />
                  <p className="text-xs text-purple-600 font-medium">Tier</p>
                </div>
                <p className="text-xl font-bold">{loyaltyInfo.tier}</p>
              </div>
              
              <div className="bg-amber-50 p-3 rounded-lg">
                <div className="flex items-center mb-1">
                  <TrendingUp className="h-4 w-4 text-amber-600 mr-1" />
                  <p className="text-xs text-amber-600 font-medium">Next Tier</p>
                </div>
                <p className="text-xl font-bold">{nextTierInfo.pointsNeeded}</p>
              </div>
            </div>
            
            {nextTierInfo.pointsNeeded > 0 && (
              <div className="mt-3 w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${nextTierInfo.progress}%` }}
                ></div>
              </div>
            )}
            
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full mt-4"
              onClick={() => router.push(`/dashboard/customers/${contact.id}/loyalty/birthday-gift`)}
            >
              <Gift className="h-4 w-4 mr-1" />
              Send Birthday Gift
            </Button>
          </CardContent>
        )}
      </Card>
      
      {/* Manage Points Section */}
      <Card>
        <CardHeader className="pb-2 flex flex-row items-center justify-between cursor-pointer"
          onClick={() => toggleSection('manage')}>
          <CardTitle className="text-lg">Manage Points</CardTitle>
          {expandedSections.manage ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </CardHeader>
        {expandedSections.manage && (
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="add-points-mobile">Add Points</Label>
                <div className="flex mt-1">
                  <Input
                    id="add-points-mobile"
                    type="number"
                    min="0"
                    value={pointsToAdd || ''}
                    onChange={(e) => setPointsToAdd(parseInt(e.target.value) || 0)}
                    className="rounded-r-none"
                  />
                  <Button 
                    onClick={handleAddPoints}
                    disabled={isUpdating || pointsToAdd <= 0}
                    className="rounded-l-none"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Value: {(pointsToAdd * 0.01).toFixed(2)} ج.م
                </p>
              </div>
              
              <div>
                <Label htmlFor="redeem-points-mobile">Redeem Points</Label>
                <div className="flex mt-1">
                  <Input
                    id="redeem-points-mobile"
                    type="number"
                    min="0"
                    max={loyaltyInfo.currentPoints}
                    value={pointsToRedeem || ''}
                    onChange={(e) => setPointsToRedeem(parseInt(e.target.value) || 0)}
                    className="rounded-r-none"
                  />
                  <Button 
                    onClick={handleRedeemPoints}
                    disabled={isUpdating || pointsToRedeem <= 0 || pointsToRedeem > loyaltyInfo.currentPoints}
                    className="rounded-l-none"
                    variant="destructive"
                  >
                    <Minus className="h-4 w-4 mr-1" />
                    Redeem
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Value: {(pointsToRedeem * 0.01).toFixed(2)} ج.م
                </p>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
      
      {/* Transactions Section */}
      <Card>
        <CardHeader className="pb-2 flex flex-row items-center justify-between cursor-pointer"
          onClick={() => toggleSection('transactions')}>
          <CardTitle className="text-lg">Recent Transactions</CardTitle>
          {expandedSections.transactions ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </CardHeader>
        {expandedSections.transactions && (
          <CardContent>
            {recentTransactions.length === 0 ? (
              <div className="text-center py-6">
                <ShoppingBag className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                <p className="text-gray-500">No transactions found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentTransactions.slice(0, 5).map((transaction, index) => (
                  <div key={index} className="flex items-center justify-between border-b pb-4">
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-full ${
                        transaction.type === 'SALE' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        {transaction.type === 'SALE' ? (
                          <Plus className={`h-4 w-4 ${
                            transaction.type === 'SALE' ? 'text-green-600' : 'text-red-600'
                          }`} />
                        ) : (
                          <Minus className={`h-4 w-4 ${
                            transaction.type === 'SALE' ? 'text-green-600' : 'text-red-600'
                          }`} />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-sm">
                          {transaction.type === 'SALE' ? 'Purchase' : 'Credit Note'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {transaction.reference}
                        </p>
                        <p className="text-xs text-gray-400">
                          {format(new Date(transaction.date), 'MMM d, yyyy')}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium text-sm ${
                        transaction.type === 'SALE' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'SALE' ? '+' : '-'}{Math.round(transaction.points)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {(transaction.points * 0.01).toFixed(2)} ج.م
                      </p>
                    </div>
                  </div>
                ))}
                
                {recentTransactions.length > 5 && (
                  <Button variant="ghost" size="sm" className="w-full">
                    View All Transactions
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  );
}
