"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from "recharts";
import {
  Download,
  Filter,
  Search,
  Users,
  ArrowUpRight,
  ArrowDownRight,
  Printer
} from "lucide-react";
import { format, subDays, subMonths } from "date-fns";
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";

// Define chart colors
const COLORS = ['#3895e7', '#307aa8', '#4bc0c0', '#ffcd56', '#ff9f40', '#ff6384'];

export default function CustomerSalesReportPage() {
  // State for date range and filters
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedLoyaltyTier, setSelectedLoyaltyTier] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // State for report data
  const [reportData, setReportData] = useState<any>({
    topCustomers: [],
    customersByLoyaltyTier: [],
    customerRetention: {},
    purchaseFrequency: []
  });

  // Loyalty tiers
  const loyaltyTiers = [
    { id: "BRONZE", name: "Bronze" },
    { id: "SILVER", name: "Silver" },
    { id: "GOLD", name: "Gold" },
    { id: "PLATINUM", name: "Platinum" }
  ];

  // Fetch report data
  useEffect(() => {
    const fetchReportData = async () => {
      setIsLoading(true);

      try {
        const formattedStartDate = format(startDate, 'yyyy-MM-dd');
        const formattedEndDate = format(endDate, 'yyyy-MM-dd');

        const response = await fetch(
          `/api/reports/sales/customers?startDate=${formattedStartDate}&endDate=${formattedEndDate}&loyaltyTier=${selectedLoyaltyTier}&search=${searchTerm}`
        );

        if (response.ok) {
          const data = await response.json();
          setReportData(data);
        } else {
          console.error('Error fetching report data:', await response.text());
        }
      } catch (error) {
        console.error('Error fetching report data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReportData();
  }, [startDate, endDate, selectedLoyaltyTier, searchTerm]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Export report to Excel
  const exportToExcel = () => {
    try {
      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Create top customers worksheet
      const topCustomersData = [
        ['Top Customers by Sales'],
        [`Period: ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`],
        [''],
        ['Customer', 'Phone', 'Loyalty Tier', 'Orders', 'Total Spent', 'Average Order Value', 'Loyalty Points']
      ];

      reportData.topCustomers.forEach((customer: any) => {
        topCustomersData.push([
          customer.name,
          customer.phone,
          customer.loyaltyTier,
          customer.orderCount,
          customer.totalSpent,
          customer.averageOrderValue,
          customer.loyaltyPoints
        ]);
      });

      const topCustomersWs = XLSX.utils.aoa_to_sheet(topCustomersData);
      XLSX.utils.book_append_sheet(workbook, topCustomersWs, 'Top Customers');

      // Create loyalty tiers worksheet
      const tiersData = [
        ['Customers by Loyalty Tier'],
        ['Tier', 'Customer Count', 'Total Orders', 'Total Revenue', 'Average Spend']
      ];

      reportData.customersByLoyaltyTier.forEach((tier: any) => {
        tiersData.push([
          tier.tier,
          tier.customerCount,
          tier.orderCount,
          tier.totalSpent,
          tier.averageSpend
        ]);
      });

      const tiersWs = XLSX.utils.aoa_to_sheet(tiersData);
      XLSX.utils.book_append_sheet(workbook, tiersWs, 'Loyalty Tiers');

      // Create purchase frequency worksheet
      const frequencyData = [
        ['Purchase Frequency'],
        ['Frequency', 'Customer Count', 'Total Revenue', 'Percentage']
      ];

      reportData.purchaseFrequency.forEach((item: any) => {
        frequencyData.push([
          item.frequency,
          item.customerCount,
          item.totalRevenue,
          item.percentage
        ]);
      });

      const frequencyWs = XLSX.utils.aoa_to_sheet(frequencyData);
      XLSX.utils.book_append_sheet(workbook, frequencyWs, 'Purchase Frequency');

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      // Save file
      saveAs(blob, `customer_sales_report_${format(new Date(), 'yyyy-MM-dd')}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
    }
  };

  // Print report
  const printReport = () => {
    window.print();
  };

  return (
    <div className="space-y-6 print:space-y-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 print:hidden">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Customer Sales Report</h2>
          <p className="text-muted-foreground">
            Analyze sales performance by customer
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={exportToExcel}>
            <Download className="mr-2 h-4 w-4" />
            Export to Excel
          </Button>
          <Button variant="outline" onClick={printReport}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="print:hidden">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <DatePicker date={startDate} setDate={setStartDate} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <DatePicker date={endDate} setDate={setEndDate} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Loyalty Tier</label>
              <Select value={selectedLoyaltyTier} onValueChange={setSelectedLoyaltyTier}>
                <SelectTrigger>
                  <SelectValue placeholder="Select loyalty tier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  {loyaltyTiers.map((tier) => (
                    <SelectItem key={tier.id} value={tier.id}>
                      {tier.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Search Customer</label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search by name or phone"
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-3 text-lg text-gray-700">Loading report data...</span>
        </div>
      ) : (
        <>
          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Customers by Loyalty Tier</CardTitle>
                <CardDescription>
                  Distribution of sales across loyalty tiers
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={reportData.customersByLoyaltyTier}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="totalSpent"
                      nameKey="tier"
                      label={({ tier, percent }) => `${tier}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {reportData.customersByLoyaltyTier.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [formatCurrency(value as number), 'Revenue']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Purchase Frequency</CardTitle>
                <CardDescription>
                  Distribution of customers by purchase frequency
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={reportData.purchaseFrequency}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="frequency" />
                    <YAxis yAxisId="left" orientation="left" stroke="#3895e7" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip formatter={(value, name) => [
                      name === 'totalRevenue' ? formatCurrency(value as number) : value,
                      name === 'totalRevenue' ? 'Revenue' : 'Customers'
                    ]} />
                    <Legend />
                    <Bar
                      yAxisId="right"
                      dataKey="customerCount"
                      name="Customer Count"
                      fill="#3895e7"
                    />
                    <Bar
                      yAxisId="left"
                      dataKey="totalRevenue"
                      name="Revenue"
                      fill="#82ca9d"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Customer Retention */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Retention</CardTitle>
              <CardDescription>
                New vs returning customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900">New Customers</h3>
                  <div className="mt-2 flex items-baseline">
                    <p className="text-3xl font-semibold text-gray-900">
                      {reportData.customerRetention.newCustomers}
                    </p>
                    <p className="ml-2 text-sm font-medium text-gray-500">
                      ({reportData.customerRetention.newPercentage}%)
                    </p>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    First purchase during this period
                  </p>
                </div>

                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900">Returning Customers</h3>
                  <div className="mt-2 flex items-baseline">
                    <p className="text-3xl font-semibold text-gray-900">
                      {reportData.customerRetention.returningCustomers}
                    </p>
                    <p className="ml-2 text-sm font-medium text-gray-500">
                      ({reportData.customerRetention.returningPercentage}%)
                    </p>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    Made purchases before this period
                  </p>
                </div>

                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900">Retention Rate</h3>
                  <div className="mt-2 flex items-baseline">
                    <p className="text-3xl font-semibold text-gray-900">
                      {reportData.customerRetention.retentionRate}%
                    </p>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    Customers who returned from previous period
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Customers Table */}
          <Card>
            <CardHeader>
              <CardTitle>Top Customers</CardTitle>
              <CardDescription>
                Customers with the highest purchase value
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                      <th className="px-6 py-3">Customer</th>
                      <th className="px-6 py-3">Phone</th>
                      <th className="px-6 py-3">Loyalty Tier</th>
                      <th className="px-6 py-3">Orders</th>
                      <th className="px-6 py-3">Total Spent</th>
                      <th className="px-6 py-3">Avg. Order Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.topCustomers.map((customer: any) => (
                      <tr key={customer.id} className="bg-white border-b hover:bg-gray-50">
                        <td className="px-6 py-4 font-medium">{customer.name}</td>
                        <td className="px-6 py-4">{customer.phone}</td>
                        <td className="px-6 py-4">
                          <Badge className={
                            customer.loyaltyTier === 'PLATINUM' ? 'bg-purple-100 text-purple-800' :
                            customer.loyaltyTier === 'GOLD' ? 'bg-yellow-100 text-yellow-800' :
                            customer.loyaltyTier === 'SILVER' ? 'bg-gray-100 text-gray-800' :
                            'bg-amber-100 text-amber-800'
                          }>
                            {customer.loyaltyTier}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">{customer.orderCount}</td>
                        <td className="px-6 py-4">{formatCurrency(customer.totalSpent)}</td>
                        <td className="px-6 py-4">{formatCurrency(customer.averageOrderValue)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
