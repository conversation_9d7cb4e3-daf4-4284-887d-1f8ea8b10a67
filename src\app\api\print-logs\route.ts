import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

/**
 * GET /api/print-logs
 * Retrieve print logs
 */
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const documentType = searchParams.get('documentType');
    const documentId = searchParams.get('documentId');
    const userId = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    
    // Build where clause
    let whereClause: any = {};
    
    if (documentType) {
      whereClause.documentType = documentType;
    }
    
    if (documentId) {
      whereClause.documentId = documentId;
    }
    
    if (userId) {
      whereClause.userId = userId;
    }
    
    if (startDate || endDate) {
      whereClause.printedAt = {};
      
      if (startDate) {
        whereClause.printedAt.gte = new Date(startDate);
      }
      
      if (endDate) {
        whereClause.printedAt.lte = new Date(endDate);
      }
    }
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Get logs with pagination
    const logs = await db.printLog.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        template: {
          select: {
            id: true,
            name: true,
            type: true
          }
        }
      },
      orderBy: {
        printedAt: 'desc'
      },
      skip,
      take: limit
    });
    
    // Get total count for pagination
    const totalCount = await db.printLog.count({
      where: whereClause
    });
    
    return NextResponse.json({
      logs,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching print logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch print logs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/print-logs
 * Create a new print log
 */
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const data = await request.json();
    
    // Validate the data
    if (!data.documentType || !data.documentId || !data.templateId) {
      return NextResponse.json(
        { error: 'Document type, document ID, and template ID are required' },
        { status: 400 }
      );
    }
    
    // Create the log
    const log = await db.printLog.create({
      data: {
        documentType: data.documentType,
        documentId: data.documentId,
        userId: session.user.id,
        templateId: data.templateId,
        copies: data.copies || 1,
        printerName: data.printerName,
        status: data.status || 'success',
        errorMessage: data.errorMessage
      }
    });
    
    return NextResponse.json(log);
  } catch (error) {
    console.error('Error creating print log:', error);
    return NextResponse.json(
      { error: 'Failed to create print log' },
      { status: 500 }
    );
  }
}
