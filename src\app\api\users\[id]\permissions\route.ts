import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// GET /api/users/[id]/permissions - Get user permissions
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Only admins or the user themselves can view their permissions
    if (session.user.role !== "ADMIN" && session.user.id !== id) {
      return NextResponse.json(
        { error: "You don't have permission to view this user's permissions" },
        { status: 403 }
      );
    }

    // Check if user exists
    const user = await db.user.findUnique({
      where: {
        id,
      },
      include: {
        permissions: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(user.permissions);
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch user permissions" },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id]/permissions - Update user permissions
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admins can update permissions
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only administrators can update permissions" },
        { status: 403 }
      );
    }

    const { id } = params;
    const data = await req.json();

    // Validate required fields
    if (!data.permissionIds || !Array.isArray(data.permissionIds)) {
      return NextResponse.json(
        { error: "Permission IDs array is required" },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await db.user.findUnique({
      where: {
        id,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if this is the admin user
    if (user.email.toLowerCase().includes("admin") && user.role === "ADMIN") {
      // For admin users, ensure they always have all permissions
      const allPermissions = await db.permission.findMany();
      const allPermissionIds = allPermissions.map(p => p.id);
      
      // Update user permissions
      await db.user.update({
        where: {
          id,
        },
        data: {
          permissions: {
            set: allPermissionIds.map(permId => ({ id: permId })),
          },
        },
      });

      return NextResponse.json({ 
        message: "Admin user permissions updated. Note: Admin users always have all permissions.",
        permissionIds: allPermissionIds
      });
    }

    // For non-admin users, update with the provided permissions
    await db.user.update({
      where: {
        id,
      },
      data: {
        permissions: {
          set: data.permissionIds.map((permId: string) => ({ id: permId })),
        },
      },
    });

    // Get updated user with permissions
    const updatedUser = await db.user.findUnique({
      where: {
        id,
      },
      include: {
        permissions: true,
      },
    });

    return NextResponse.json({
      message: "User permissions updated successfully",
      permissions: updatedUser?.permissions,
    });
  } catch (error) {
    console.error("Error updating user permissions:", error);
    return NextResponse.json(
      { error: "Failed to update user permissions" },
      { status: 500 }
    );
  }
}
