import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import * as XLSX from 'xlsx';

// GET /api/contacts/template - Get Excel template for contact import
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Create template data
    const templateData = [
      {
        Name: 'Example Customer',
        Phone: '01234567890',
        Address: 'Example Address',
        Email: '<EMAIL>',
        IsCustomer: 'Yes',
        IsSupplier: 'No',
        CreditLimit: 5000,
        CreditPeriod: 30,
        OpeningBalance: 0,
        OpeningBalanceDate: new Date().toISOString().split('T')[0],
        IsActive: 'Yes'
      },
      {
        Name: 'Example Supplier',
        Phone: '01987654321',
        Address: 'Supplier Address',
        Email: '<EMAIL>',
        IsCustomer: 'No',
        IsSupplier: 'Yes',
        CreditLimit: 0,
        CreditPeriod: 0,
        OpeningBalance: 0,
        OpeningBalanceDate: new Date().toISOString().split('T')[0],
        IsActive: 'Yes'
      }
    ];

    // Create a worksheet with the template data
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // Add validation and instructions
    const validationSheet = XLSX.utils.aoa_to_sheet([
      ['Contact Import Template Instructions'],
      [''],
      ['Required Fields:'],
      ['- Name: Contact name (required)'],
      ['- Phone: Contact phone number (required and must be unique)'],
      ['- IsCustomer or IsSupplier: At least one must be "Yes"'],
      [''],
      ['Optional Fields:'],
      ['- Address: Contact address'],
      ['- Email: Contact email address'],
      ['- CreditLimit: Credit limit for customers (default: 0)'],
      ['- CreditPeriod: Credit period in days for customers (default: 30)'],
      ['- OpeningBalance: Opening balance (default: 0)'],
      ['- OpeningBalanceDate: Date of opening balance (format: YYYY-MM-DD)'],
      ['- IsActive: "Yes" or "No" (default: "Yes")'],
      [''],
      ['Notes:'],
      ['- Phone numbers must be unique'],
      ['- A contact can be both a customer and a supplier'],
      ['- Credit limit and period are only applicable for customers'],
      ['- Opening balance represents the initial balance of the contact'],
      ['- Positive opening balance means the contact owes you money'],
      ['- Negative opening balance means you owe the contact money'],
    ]);

    // Create a workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Contacts Template');
    XLSX.utils.book_append_sheet(workbook, validationSheet, 'Instructions');
    
    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
    
    // Set headers for file download
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="contacts_import_template.xlsx"'
      }
    });
  } catch (error) {
    console.error("Error generating template:", error);
    return NextResponse.json(
      { error: "Failed to generate template" },
      { status: 500 }
    );
  }
}
