"use client";

import React, { useEffect, useState } from 'react';
import { PrintService } from '@/lib/print-service';
import { PrintButton } from './PrintButton';
import { BarcodeComponent } from './Barcodes';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

interface PrintableDocumentProps {
  documentType: string;
  documentData: any;
  children?: React.ReactNode;
  showControls?: boolean;
  language?: string;
  className?: string;
}

export function PrintableDocument({
  documentType,
  documentData,
  children,
  showControls = true,
  language = 'ar',
  className = ''
}: PrintableDocumentProps) {
  const [settings, setSettings] = useState<any>(null);
  const [template, setTemplate] = useState<any>(null);
  const [renderedContent, setRenderedContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("preview");

  const printService = new PrintService();

  useEffect(() => {
    async function loadPrintData() {
      try {
        setIsLoading(true);

        // Initialize print service
        await printService.initialize();

        // Get settings
        const printSettings = await printService.getSettings();
        setSettings(printSettings);

        // Get template
        const printTemplate = await printService.getTemplate(documentType, language);
        setTemplate(printTemplate);

        if (printTemplate && documentData) {
          // Generate HTML content
          const html = await printService.generatePrintHTML(documentType, documentData, language);
          setRenderedContent(html);
        }

        setIsLoading(false);
      } catch (err: any) {
        console.error('Failed to load print data:', err);
        setError(err.message);
        setIsLoading(false);
      }
    }

    loadPrintData();
  }, [documentType, documentData, language]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Generate HTML content
      const html = await printService.generatePrintHTML(documentType, documentData, language);
      setRenderedContent(html);

      setIsLoading(false);
    } catch (err: any) {
      console.error('Failed to refresh print data:', err);
      setError(err.message);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div className="p-4 text-center">Loading print template...</div>;
  }

  if (error) {
    return <div className="p-4 text-center text-red-500">Error: {error}</div>;
  }

  if (!template) {
    return (
      <div className="p-4 text-center">
        <p className="text-amber-500 mb-4">No print template found for {documentType}</p>
        {children}
      </div>
    );
  }

  return (
    <div className={`printable-document ${className}`}>
      <style jsx global>{`
        @media print {
          @page {
            size: ${template?.paperSize === 'custom' && settings
              ? `${settings.customWidth}mm ${settings.customHeight}mm`
              : template?.paperSize || 'A4'};
            margin: ${settings?.marginTop || 10}mm ${settings?.marginRight || 10}mm ${settings?.marginBottom || 10}mm ${settings?.marginLeft || 10}mm;
            orientation: ${template?.orientation || 'portrait'};
          }

          body {
            font-family: ${settings?.fontFamily || 'Arial'}, sans-serif;
            font-size: ${settings?.fontSize || 12}pt;
            line-height: 1.3;
            background: white !important;
            color: black !important;
          }

          .print-hidden {
            display: none !important;
          }

          /* More print styles... */
        }
      `}</style>

      {/* Print controls (hidden when printing) */}
      {showControls && (
        <div className="print-hidden mb-4 border-b border-gray-200 p-4 flex justify-between items-center">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="html">HTML</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <PrintButton
              documentType={documentType}
              documentData={documentData}
              language={language}
            />
          </div>
        </div>
      )}

      {/* Document content */}
      <Tabs value={activeTab} className="w-full">
        <TabsContent value="preview" className="m-0">
          <div className="print-content bg-white p-4 border rounded-md shadow-sm">
            {/* Logo */}
            {settings?.showLogo && settings.companyLogo && (
              <div style={{ textAlign: settings.logoPosition as any, marginBottom: '10px' }}>
                <img src={settings.companyLogo} alt="Company Logo" style={{ maxHeight: '50px', maxWidth: '200px' }} />
              </div>
            )}

            {/* Rendered template content */}
            {renderedContent ? (
              <div dangerouslySetInnerHTML={{ __html: renderedContent }} />
            ) : (
              children // Fallback to children if no template is available
            )}

            {/* Footer */}
            {settings?.footerText && (
              <div className="text-center mt-8" style={{ color: settings.primaryColor }}>
                <p>{settings.footerText}</p>
              </div>
            )}

            {/* Terms */}
            {settings?.termsText && (
              <div className="mt-4 pt-4 border-t text-xs" style={{ borderColor: settings.secondaryColor }}>
                <p>{settings.termsText}</p>
              </div>
            )}

            {/* Barcode */}
            {settings?.showBarcode && documentData.barcode && (
              <div className="text-center mt-4">
                <BarcodeComponent
                  value={documentData.barcode}
                  type="barcode"
                  height={50}
                />
              </div>
            )}

            {/* QR Code */}
            {settings?.showQRCode && documentData.id && (
              <div className="text-center mt-4">
                <BarcodeComponent
                  value={JSON.stringify({
                    type: documentType,
                    id: documentData.id,
                    number: documentData.invoiceNumber || documentData.receiptNumber || documentData.poNumber
                  })}
                  type="qrcode"
                  width={100}
                />
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="html" className="m-0">
          <div className="p-6">
            <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-[600px] text-xs">
              {renderedContent}
            </pre>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
