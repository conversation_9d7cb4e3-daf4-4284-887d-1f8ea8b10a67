import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import bcrypt from "bcryptjs";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// POST /api/system/initialize-core-data - Initialize core data
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only administrators can initialize core data" },
        { status: 403 }
      );
    }

    console.log("Starting core data initialization...");
    
    // Initialize results object
    const results = {
      branch: { created: 0, existing: 0 },
      warehouse: { created: 0, existing: 0 },
      permissions: { created: 0, existing: 0 },
      admin: { created: 0, existing: 0, updated: 0 },
      journals: { created: 0, existing: 0 },
      accounts: { created: 0, existing: 0 },
      fiscalYear: { created: 0, existing: 0 },
      paymentMethods: { created: 0, existing: 0 },
    };

    // 1. Ensure at least one branch exists
    const branch = await db.branch.findFirst();
    
    if (!branch) {
      console.log("No branch found. Creating a new branch...");
      const newBranch = await db.branch.create({
        data: {
          name: "Main Branch",
          code: "A",
          address: "Cairo, Egypt",
          phone: "***********",
        },
      });
      console.log(`Created new branch: ${newBranch.name}`);
      results.branch.created++;
    } else {
      console.log(`Found existing branch: ${branch.name}`);
      results.branch.existing++;
    }

    // 2. Ensure at least one warehouse exists
    const warehouse = await db.warehouse.findFirst();
    
    if (!warehouse) {
      console.log("No warehouse found. Creating a new warehouse...");
      const branchToUse = branch || await db.branch.findFirst();
      
      if (!branchToUse) {
        throw new Error("No branch available to create warehouse");
      }
      
      const newWarehouse = await db.warehouse.create({
        data: {
          name: "Main Warehouse",
          branchId: branchToUse.id,
          isActive: true,
        },
      });
      console.log(`Created new warehouse: ${newWarehouse.name}`);
      results.warehouse.created++;
    } else {
      console.log(`Found existing warehouse: ${warehouse.name}`);
      results.warehouse.existing++;
    }

    // 3. Ensure all required permissions exist
    const defaultPermissions = [
      { name: "view_users", description: "View users" },
      { name: "add_users", description: "Add new users" },
      { name: "edit_users", description: "Edit existing users" },
      { name: "delete_users", description: "Delete users" },
      { name: "view_branches", description: "View branches" },
      { name: "add_branches", description: "Add new branches" },
      { name: "edit_branches", description: "Edit existing branches" },
      { name: "delete_branches", description: "Delete branches" },
      { name: "view_warehouses", description: "View warehouses" },
      { name: "add_warehouses", description: "Add new warehouses" },
      { name: "edit_warehouses", description: "Edit existing warehouses" },
      { name: "delete_warehouses", description: "Delete warehouses" },
      { name: "view_products", description: "View products" },
      { name: "add_products", description: "Add new products" },
      { name: "edit_products", description: "Edit existing products" },
      { name: "delete_products", description: "Delete products" },
      { name: "view_inventory", description: "View inventory" },
      { name: "add_inventory", description: "Add inventory" },
      { name: "edit_inventory", description: "Edit inventory" },
      { name: "delete_inventory", description: "Delete inventory" },
      { name: "view_sales", description: "View sales" },
      { name: "add_sales", description: "Add new sales" },
      { name: "edit_sales", description: "Edit existing sales" },
      { name: "delete_sales", description: "Delete sales" },
      { name: "view_purchases", description: "View purchases" },
      { name: "add_purchases", description: "Add new purchases" },
      { name: "edit_purchases", description: "Edit existing purchases" },
      { name: "delete_purchases", description: "Delete purchases" },
      { name: "view_contacts", description: "View contacts" },
      { name: "add_contacts", description: "Add new contacts" },
      { name: "edit_contacts", description: "Edit existing contacts" },
      { name: "delete_contacts", description: "Delete contacts" },
      { name: "view_reports", description: "View reports" },
      { name: "view_settings", description: "View settings" },
      { name: "edit_settings", description: "Edit settings" },
      { name: "manage_accounts", description: "Manage accounting accounts" },
      { name: "view_journals", description: "View journal entries" },
      { name: "add_journals", description: "Add journal entries" },
      { name: "edit_journals", description: "Edit journal entries" },
      { name: "delete_journals", description: "Delete journal entries" },
    ];

    // Create permissions
    const createdPermissions = [];
    for (const permission of defaultPermissions) {
      // Check if permission already exists
      const existingPermission = await db.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        const newPermission = await db.permission.create({
          data: permission,
        });
        createdPermissions.push(newPermission);
        console.log(`Created permission: ${permission.name}`);
        results.permissions.created++;
      } else {
        createdPermissions.push(existingPermission);
        console.log(`Permission already exists: ${permission.name}`);
        results.permissions.existing++;
      }
    }

    // 4. Ensure admin user exists and has all permissions
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
      include: {
        permissions: true,
      },
    });

    if (!adminUser) {
      console.log("Admin user not found. Creating a new admin user...");
      
      // Find a branch
      const branchToUse = branch || await db.branch.findFirst();
      
      if (!branchToUse) {
        throw new Error("No branch available to create admin user");
      }
      
      // Create a new admin user
      const hashedPassword = await bcrypt.hash("admin123", 10);
      const newAdmin = await db.user.create({
        data: {
          name: "Admin User",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "ADMIN",
          branchId: branchToUse.id,
          isActive: true,
          permissions: {
            connect: createdPermissions.map(p => ({ id: p.id })),
          },
        },
      });
      
      // Connect admin to warehouse
      const warehouseToUse = warehouse || await db.warehouse.findFirst();
      
      if (warehouseToUse) {
        await db.userWarehouse.create({
          data: {
            userId: newAdmin.id,
            warehouseId: warehouseToUse.id,
          },
        });
      }
      
      console.log("Admin user created successfully");
      results.admin.created++;
    } else {
      console.log("Admin user already exists");
      results.admin.existing++;
      
      // Update admin permissions if needed
      const currentPermissionIds = adminUser.permissions.map(p => p.id);
      const missingPermissions = createdPermissions.filter(
        p => !currentPermissionIds.includes(p.id)
      );
      
      if (missingPermissions.length > 0) {
        await db.user.update({
          where: { id: adminUser.id },
          data: {
            permissions: {
              connect: missingPermissions.map(p => ({ id: p.id })),
            },
          },
        });
        console.log(`Updated admin user with ${missingPermissions.length} new permissions`);
        results.admin.updated++;
      }
    }

    // 5. Ensure basic journals exist
    const requiredJournals = [
      { name: "General Journal", code: "GJ" },
      { name: "Cash Journal", code: "CJ" },
      { name: "Sales Journal", code: "SJ" },
      { name: "Purchase Journal", code: "PJ" },
      { name: "Bank Journal", code: "BJ" },
    ];
    
    for (const journal of requiredJournals) {
      const existingJournal = await db.journal.findFirst({
        where: { code: journal.code },
      });
      
      if (!existingJournal) {
        await db.journal.create({
          data: journal,
        });
        console.log(`Created journal: ${journal.name}`);
        results.journals.created++;
      } else {
        console.log(`Journal already exists: ${journal.name}`);
        results.journals.existing++;
      }
    }

    // 6. Ensure basic payment methods exist
    const requiredPaymentMethods = [
      { name: "Cash", code: "CASH" },
      { name: "Bank Transfer", code: "BANK" },
      { name: "Visa", code: "VISA" },
      { name: "Vodafone Cash", code: "VODAFONE" },
    ];
    
    for (const method of requiredPaymentMethods) {
      const existingMethod = await db.paymentMethod.findFirst({
        where: { code: method.code },
      });
      
      if (!existingMethod) {
        await db.paymentMethod.create({
          data: method,
        });
        console.log(`Created payment method: ${method.name}`);
        results.paymentMethods.created++;
      } else {
        console.log(`Payment method already exists: ${method.name}`);
        results.paymentMethods.existing++;
      }
    }

    console.log("Core data initialization completed successfully");
    
    return NextResponse.json({
      success: true,
      message: "Core data initialized successfully",
      results,
    });
  } catch (error) {
    console.error("Error initializing core data:", error);
    return NextResponse.json(
      {
        success: false,
        error: "An error occurred while initializing core data",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
