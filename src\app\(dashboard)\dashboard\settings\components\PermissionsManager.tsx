"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";

interface Permission {
  id: string;
  name: string;
  description: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: Permission[];
}

export default function PermissionsManager() {
  const [users, setUsers] = useState<User[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [userPermissions, setUserPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Group permissions by type
  const groupedPermissions = permissions.reduce((groups, permission) => {
    const type = permission.name.split('_')[0];
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(permission);
    return groups;
  }, {} as Record<string, Permission[]>);

  // Fetch users and permissions
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch users
        const usersResponse = await fetch('/api/users');
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setUsers(usersData);
        }

        // Fetch permissions
        const permissionsResponse = await fetch('/api/permissions');
        if (permissionsResponse.ok) {
          const permissionsData = await permissionsResponse.json();
          setPermissions(permissionsData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load users and permissions");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Update user permissions when user selection changes
  useEffect(() => {
    if (selectedUser) {
      const user = users.find(u => u.id === selectedUser);
      if (user) {
        setUserPermissions(user.permissions.map(p => p.id));
      }
    } else {
      setUserPermissions([]);
    }
  }, [selectedUser, users]);

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string) => {
    setUserPermissions(prev => {
      if (prev.includes(permissionId)) {
        return prev.filter(id => id !== permissionId);
      } else {
        return [...prev, permissionId];
      }
    });
  };

  // Handle permission group toggle
  const handleGroupToggle = (groupPermissions: Permission[]) => {
    const groupIds = groupPermissions.map(p => p.id);
    const allSelected = groupPermissions.every(p => userPermissions.includes(p.id));
    
    if (allSelected) {
      // Remove all permissions in this group
      setUserPermissions(prev => prev.filter(id => !groupIds.includes(id)));
    } else {
      // Add all permissions in this group
      setUserPermissions(prev => {
        const newPermissions = [...prev];
        groupIds.forEach(id => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        });
        return newPermissions;
      });
    }
  };

  // Save user permissions
  const savePermissions = async () => {
    if (!selectedUser) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/users/${selectedUser}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissionIds: userPermissions }),
      });

      if (response.ok) {
        toast.success("Permissions updated successfully");
        
        // Update local state
        setUsers(prev => prev.map(user => {
          if (user.id === selectedUser) {
            return {
              ...user,
              permissions: permissions.filter(p => userPermissions.includes(p.id)),
            };
          }
          return user;
        }));
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to update permissions");
      }
    } catch (error) {
      console.error("Error updating permissions:", error);
      toast.error("Failed to update permissions");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading...</div>;
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">
          User Permissions Manager
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Manage permissions for each user in the system
        </p>
      </div>

      <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
        <div className="mb-6">
          <label htmlFor="user-select" className="block text-sm font-medium text-gray-700">
            Select User
          </label>
          <select
            id="user-select"
            value={selectedUser}
            onChange={(e) => setSelectedUser(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
          >
            <option value="">Select a user</option>
            {users.map((user) => (
              <option key={user.id} value={user.id}>
                {user.name} ({user.email}) - {user.role}
              </option>
            ))}
          </select>
        </div>

        {selectedUser && (
          <>
            <div className="mb-4 flex justify-between items-center">
              <h4 className="text-md font-medium text-gray-900">Permissions</h4>
              <button
                type="button"
                onClick={savePermissions}
                disabled={isSaving}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400"
              >
                {isSaving ? "Saving..." : "Save Permissions"}
              </button>
            </div>

            <div className="space-y-6">
              {Object.entries(groupedPermissions).map(([group, groupPermissions]) => {
                const allSelected = groupPermissions.every(p => userPermissions.includes(p.id));
                const someSelected = groupPermissions.some(p => userPermissions.includes(p.id));
                
                return (
                  <div key={group} className="border rounded-md p-4">
                    <div className="flex items-center mb-2">
                      <input
                        id={`group-${group}`}
                        type="checkbox"
                        checked={allSelected}
                        onChange={() => handleGroupToggle(groupPermissions)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        ref={el => {
                          if (el) {
                            el.indeterminate = someSelected && !allSelected;
                          }
                        }}
                      />
                      <label htmlFor={`group-${group}`} className="ml-2 block text-sm font-medium text-gray-900 capitalize">
                        {group} Permissions
                      </label>
                    </div>
                    <div className="ml-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {groupPermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center">
                          <input
                            id={permission.id}
                            type="checkbox"
                            checked={userPermissions.includes(permission.id)}
                            onChange={() => handlePermissionToggle(permission.id)}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                          <label htmlFor={permission.id} className="ml-2 block text-sm text-gray-700">
                            {permission.description}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
