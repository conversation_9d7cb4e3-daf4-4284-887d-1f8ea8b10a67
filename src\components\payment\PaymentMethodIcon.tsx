"use client";

import React from 'react';
import {
  CreditCard,
  Banknote,
  Building,
  Smartphone,
  Wallet,
  DollarSign,
  User,
  CreditCard as CardIcon,
  Landmark,
  QrCode,
  Receipt,
  Coins,
  CircleDollarSign,
  Percent,
  ShoppingCart,
  Store,
  Truck,
  Briefcase,
  Gift,
  Scan,
  Bitcoin,
  Repeat,
  ArrowLeftRight,
  BadgeCheck,
  BadgeDollarSign
} from 'lucide-react';

interface PaymentMethodIconProps {
  methodCode: string;
  iconName?: string | null;
  iconUrl?: string | null;
  color?: string | null;
  size?: number;
}

export default function PaymentMethodIcon({
  methodCode,
  iconName,
  iconUrl,
  color = "#3895e7",
  size = 20
}: PaymentMethodIconProps) {
  // If custom icon URL is provided, use it
  if (iconUrl) {
    return (
      <div
        style={{
          width: size,
          height: size,
          backgroundImage: `url(${iconUrl})`,
          backgroundSize: 'contain',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      />
    );
  }

  // If custom icon name is provided, use it
  if (iconName) {
    switch (iconName) {
      // Basic payment icons
      case 'cash':
        return <Banknote size={size} color={color} />;
      case 'credit-card':
        return <CreditCard size={size} color={color} />;
      case 'bank':
        return <Building size={size} color={color} />;
      case 'mobile':
        return <Smartphone size={size} color={color} />;
      case 'wallet':
        return <Wallet size={size} color={color} />;
      case 'money':
        return <DollarSign size={size} color={color} />;

      // Additional payment icons
      case 'landmark':
        return <Landmark size={size} color={color} />;
      case 'qr-code':
        return <QrCode size={size} color={color} />;
      case 'receipt':
        return <Receipt size={size} color={color} />;
      case 'coins':
        return <Coins size={size} color={color} />;
      case 'circle-dollar':
        return <CircleDollarSign size={size} color={color} />;
      case 'percent':
        return <Percent size={size} color={color} />;
      case 'shopping-cart':
        return <ShoppingCart size={size} color={color} />;
      case 'store':
        return <Store size={size} color={color} />;
      case 'truck':
        return <Truck size={size} color={color} />;
      case 'briefcase':
        return <Briefcase size={size} color={color} />;
      case 'gift':
        return <Gift size={size} color={color} />;
      case 'scan':
        return <Scan size={size} color={color} />;

      // Digital payment icons
      case 'bitcoin':
        return <Bitcoin size={size} color={color} />;
      case 'paypal':
        return <DollarSign size={size} color={color} />;

      // Transaction icons
      case 'repeat':
        return <Repeat size={size} color={color} />;
      case 'transfer':
        return <ArrowLeftRight size={size} color={color} />;
      case 'badge-check':
        return <BadgeCheck size={size} color={color} />;
      case 'badge-dollar':
        return <BadgeDollarSign size={size} color={color} />;

      default:
        // Fallback to default icons based on method code
        break;
    }
  }

  // Default icons based on method code
  switch (methodCode) {
    case 'CASH':
      return <Banknote size={size} color={color} />;
    case 'VODAFONE_CASH':
      return <Smartphone size={size} color={color || "#e60000"} />;
    case 'BANK_TRANSFER':
      return <Building size={size} color={color || "#307aa8"} />;
    case 'VISA':
    case 'CREDIT_CARD':
      return <CreditCard size={size} color={color || "#1a1f71"} />;
    case 'CUSTOMER_ACCOUNT':
    case 'SUPPLIER_ACCOUNT':
      return <User size={size} color={color || "#6b7280"} />;
    default:
      return <DollarSign size={size} color={color} />;
  }
}
