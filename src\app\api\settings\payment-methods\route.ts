import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/settings/payment-methods - Get all payment methods
export async function GET(req: NextRequest) {
  try {
    console.log("GET /api/settings/payment-methods - Request received");

    const session = await getServerSession(authOptions);
    console.log("Session:", session ? `User: ${session.user.email}` : "No session");

    if (!session) {
      console.log("Unauthorized - No session");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if the request is coming from the settings page
    const url = new URL(req.url);
    const fromSettings = url.searchParams.get('from') === 'settings';
    console.log("Request from settings:", fromSettings);

    // Skip permission check if the request is from the settings page
    if (!fromSettings) {
      console.log("Checking permissions for non-settings request");
      // Check if user has permission to view settings
      const hasViewPermission = await hasPermission("view_settings");
      console.log("Has view_settings permission:", hasViewPermission);
      console.log("User role:", session.user.role);

      if (!hasViewPermission && session.user.role !== "ADMIN") {
        console.log("Permission denied - Not admin and no view_settings permission");
        return NextResponse.json(
          { error: "You don't have permission to view payment methods" },
          { status: 403 }
        );
      }
    } else {
      console.log("Skipping permission check for settings request");
    }

    console.log("Fetching payment methods from database");
    // Get all payment methods
    const paymentMethods = await db.paymentMethodSettings.findMany({
      orderBy: {
        sequence: "asc",
      },
    });

    console.log(`Found ${paymentMethods.length} payment methods`);
    return NextResponse.json(paymentMethods);
  } catch (error) {
    console.error("Error fetching payment methods:", error);
    // Log the full error stack
    if (error instanceof Error) {
      console.error(error.stack);
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch payment methods" },
      { status: 500 }
    );
  }
}

// POST /api/settings/payment-methods - Create a new payment method
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage settings
    const hasManagePermission = await hasPermission("manage_settings");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to manage payment methods" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.code || !data.name) {
      return NextResponse.json(
        { error: "Code and name are required" },
        { status: 400 }
      );
    }

    // Check if payment method with this code already exists
    const existingMethod = await db.paymentMethodSettings.findFirst({
      where: {
        code: {
          equals: data.code,
          mode: 'insensitive',
        },
      },
    });

    if (existingMethod) {
      return NextResponse.json(
        { error: "Payment method with this code already exists" },
        { status: 400 }
      );
    }

    // Get the highest sequence number
    const highestSequence = await db.paymentMethodSettings.findFirst({
      orderBy: {
        sequence: "desc",
      },
      select: {
        sequence: true,
      },
    });

    const sequence = highestSequence ? highestSequence.sequence + 1 : 1;

    // Create the payment method
    const paymentMethod = await db.paymentMethodSettings.create({
      data: {
        code: data.code,
        name: data.name,
        isActive: data.isActive !== undefined ? data.isActive : true,
        accountId: data.accountId || null,
        journalId: data.journalId || null,
        iconName: data.iconName || null,
        color: data.color || null,
        branchIds: data.branchIds || [],
        sequence,
      },
    });

    return NextResponse.json(paymentMethod);
  } catch (error) {
    console.error("Error creating payment method:", error);
    return NextResponse.json(
      { error: "Failed to create payment method" },
      { status: 500 }
    );
  }
}
