"use client";

import { SessionProvider } from "./SessionProvider";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { Toaster } from "@/components/ui/sonner";
import { AuthErrorHandler } from "@/components/auth/AuthErrorHandler";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <LanguageProvider>
        {children}
        <AuthErrorHandler />
        <Toaster />
      </LanguageProvider>
    </SessionProvider>
  );
}
