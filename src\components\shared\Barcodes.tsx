"use client";

import React, { useEffect, useState } from 'react';
import { QRCodeSVG } from 'qrcode.react';
import JsBarcode from 'jsbarcode';

interface BarcodeProps {
  value: string;
  type: 'barcode' | 'qrcode';
  width?: number;
  height?: number;
  className?: string;
  displayValue?: boolean;
  format?: string;
}

export function BarcodeComponent({
  value,
  type,
  width,
  height,
  className = '',
  displayValue = true,
  format = 'CODE128'
}: BarcodeProps) {
  const [barcodeUrl, setBarcodeUrl] = useState<string | null>(null);

  useEffect(() => {
    if (type === 'barcode' && value) {
      const canvas = document.createElement('canvas');
      try {
        JsBarcode(canvas, value, {
          format,
          width: width ? width / 100 : 2,
          height: height || 50,
          displayValue,
          fontSize: 12,
          margin: 10
        });
        setBarcodeUrl(canvas.toDataURL('image/png'));
      } catch (error) {
        console.error('Failed to generate barcode:', error);
        setBarcodeUrl(null);
      }
    }
  }, [type, value, width, height, displayValue, format]);

  if (!value) {
    return null;
  }

  if (type === 'qrcode') {
    return (
      <QRCodeSVG
        value={value}
        size={width || 128}
        level="M"
        includeMargin={true}
        className={className}
      />
    );
  } else {
    return barcodeUrl ? (
      <img
        src={barcodeUrl}
        alt={`Barcode: ${value}`}
        className={className}
        style={{ maxWidth: '100%' }}
      />
    ) : null;
  }
}
