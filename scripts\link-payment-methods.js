// Script to link payment methods to accounts and journals
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting payment methods linking process...');

    // Get all accounts
    const accounts = await prisma.account.findMany({
      where: {
        isActive: true,
      },
    });

    console.log(`Found ${accounts.length} accounts`);

    // Get all journals
    const journals = await prisma.journal.findMany({
      where: {
        isActive: true,
      },
    });

    console.log(`Found ${journals.length} journals`);

    // Get all payment methods
    const paymentMethods = await prisma.paymentMethodSettings.findMany({
      where: {
        isActive: true,
      },
      include: {
        account: true,
        journal: true,
      },
    });

    console.log(`Found ${paymentMethods.length} payment methods`);

    // Create maps for easy lookup
    const accountsMap = {};
    accounts.forEach(account => {
      accountsMap[account.code] = account;
      accountsMap[account.name.toLowerCase()] = account;
    });

    const journalsMap = {};
    journals.forEach(journal => {
      journalsMap[journal.code] = journal;
      journalsMap[journal.name.toLowerCase()] = journal;
      journalsMap[journal.type.toLowerCase()] = journal;
    });

    // Define mappings
    const paymentMethodMappings = {
      'CASH': {
        accountCode: '1000', // Cash account
        journalCode: 'CASH', // Cash journal
      },
      'VODAFONE_CASH': {
        accountCode: '1010', // Vodafone Cash account
        journalCode: 'VFCASH', // Vodafone Cash journal
      },
      'BANK_TRANSFER': {
        accountCode: '1020', // Bank account
        journalCode: 'BANK', // Bank journal
      },
      'VISA': {
        accountCode: '1030', // Credit Card account
        journalCode: 'VISA', // Credit Card journal
      },
      'CREDIT_CARD': {
        accountCode: '1030', // Credit Card account
        journalCode: 'VISA', // Credit Card journal
      },
      'CUSTOMER_ACCOUNT': {
        accountCode: '1100', // Accounts Receivable account
        journalCode: 'CUST', // Customer Account journal
      },
    };

    // Link payment methods to accounts and journals
    for (const paymentMethod of paymentMethods) {
      console.log(`Processing payment method: ${paymentMethod.name} (${paymentMethod.code})`);

      const mapping = paymentMethodMappings[paymentMethod.code];
      if (!mapping) {
        console.log(`No mapping found for payment method: ${paymentMethod.code}`);
        continue;
      }

      const account = accountsMap[mapping.accountCode];
      const journal = journalsMap[mapping.journalCode];

      if (!account) {
        console.log(`Account not found for code: ${mapping.accountCode}`);
        continue;
      }

      if (!journal) {
        console.log(`Journal not found for code: ${mapping.journalCode}`);
        continue;
      }

      // Check if payment method already has the correct links
      if (paymentMethod.accountId === account.id && paymentMethod.journalId === journal.id) {
        console.log(`Payment method ${paymentMethod.name} already linked correctly`);
        continue;
      }

      // Update payment method
      await prisma.paymentMethodSettings.update({
        where: {
          id: paymentMethod.id,
        },
        data: {
          accountId: account.id,
          journalId: journal.id,
        },
      });

      console.log(`Updated payment method ${paymentMethod.name}:`);
      console.log(`  Account: ${account.name} (${account.code})`);
      console.log(`  Journal: ${journal.name} (${journal.code})`);
    }

    console.log('Payment methods linking process completed successfully!');
  } catch (error) {
    console.error('Error linking payment methods:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
