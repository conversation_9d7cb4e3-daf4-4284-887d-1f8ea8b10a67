import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/currency-accounts - Get currency accounts
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const accountId = url.searchParams.get("accountId");
    const currencyId = url.searchParams.get("currencyId");
    
    // Build query filters
    const filters: any = {};
    if (accountId) filters.accountId = accountId;
    if (currencyId) filters.currencyId = currencyId;
    
    // Get currency accounts
    const currencyAccounts = await db.currencyAccount.findMany({
      where: filters,
      include: {
        account: true,
        currency: true,
      },
      orderBy: [
        { updatedAt: "desc" },
      ],
    });
    
    return NextResponse.json({
      data: currencyAccounts,
    });
  } catch (error: any) {
    console.error("Error fetching currency accounts:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch currency accounts" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/currency-accounts - Create or update a currency account
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to manage accounts
    const hasManagePermission = await hasPermission("manage_accounts");
    if (!hasManagePermission) {
      return NextResponse.json(
        { error: "You don't have permission to manage currency accounts" },
        { status: 403 }
      );
    }
    
    // Parse request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.accountId) {
      return NextResponse.json({ error: "Account ID is required" }, { status: 400 });
    }
    
    if (!data.currencyId) {
      return NextResponse.json({ error: "Currency ID is required" }, { status: 400 });
    }
    
    // Check if account exists
    const account = await db.account.findUnique({
      where: { id: data.accountId },
    });
    
    if (!account) {
      return NextResponse.json({ error: "Account not found" }, { status: 404 });
    }
    
    // Check if currency exists
    const currency = await db.currency.findUnique({
      where: { id: data.currencyId },
    });
    
    if (!currency) {
      return NextResponse.json({ error: "Currency not found" }, { status: 404 });
    }
    
    // Check if currency account already exists
    const existingCurrencyAccount = await db.currencyAccount.findFirst({
      where: {
        accountId: data.accountId,
        currencyId: data.currencyId,
      },
    });
    
    let currencyAccount;
    
    if (existingCurrencyAccount) {
      // Update existing currency account
      currencyAccount = await db.currencyAccount.update({
        where: { id: existingCurrencyAccount.id },
        data: {
          balance: data.balance !== undefined ? data.balance : existingCurrencyAccount.balance,
        },
        include: {
          account: true,
          currency: true,
        },
      });
    } else {
      // Create new currency account
      currencyAccount = await db.currencyAccount.create({
        data: {
          accountId: data.accountId,
          currencyId: data.currencyId,
          balance: data.balance || 0,
        },
        include: {
          account: true,
          currency: true,
        },
      });
    }
    
    return NextResponse.json(currencyAccount);
  } catch (error: any) {
    console.error("Error managing currency account:", error);
    return NextResponse.json(
      { error: error.message || "Failed to manage currency account" },
      { status: 500 }
    );
  }
}
