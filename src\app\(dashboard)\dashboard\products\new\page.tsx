"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

// Define types for data
interface Category {
  id: string;
  name: string;
  type?: string;
}

interface Specification {
  name: string;
  value: string;
}

interface Warehouse {
  id: string;
  name: string;
}

export default function NewProductPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [componentTypes, setComponentTypes] = useState<{id: string; name: string}[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [basePrice, setBasePrice] = useState<number>(0);
  const [costPrice, setCostPrice] = useState<number>(0);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedCategoryType, setSelectedCategoryType] = useState<string | null>(null);
  const [isCustomizable, setIsCustomizable] = useState(false);
  const [isComponent, setIsComponent] = useState(false);
  const [componentType, setComponentType] = useState("");
  const [specifications, setSpecifications] = useState<Specification[]>([]);
  const [inventory, setInventory] = useState<{ warehouseId: string; quantity: number }[]>([]);

  // Fetch categories and warehouses
  useEffect(() => {
    const fetchData = async () => {
      setDataLoading(true);
      setError(null);

      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/categories');
        if (!categoriesResponse.ok) {
          throw new Error(`Failed to fetch categories: ${categoriesResponse.statusText}`);
        }
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData);

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        if (!warehousesResponse.ok) {
          throw new Error(`Failed to fetch warehouses: ${warehousesResponse.statusText}`);
        }
        const warehousesData = await warehousesResponse.json();
        setWarehouses(warehousesData);

        // Fetch component types
        const componentTypesResponse = await fetch('/api/component-types');
        if (!componentTypesResponse.ok) {
          throw new Error(`Failed to fetch component types: ${componentTypesResponse.statusText}`);
        }
        const componentTypesData = await componentTypesResponse.json();
        setComponentTypes(componentTypesData);

        // Initialize inventory with zero quantity for each warehouse
        setInventory(warehousesData.map((warehouse: Warehouse) => ({
          warehouseId: warehouse.id,
          quantity: 0
        })));
      } catch (error) {
        console.error("Error fetching data:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setDataLoading(false);
      }
    };

    fetchData();
  }, []);

  // Add a new specification
  const addSpecification = () => {
    setSpecifications([...specifications, { name: "", value: "" }]);
  };

  // Update specification
  const updateSpecification = (index: number, field: 'name' | 'value', value: string) => {
    const updatedSpecs = [...specifications];
    updatedSpecs[index][field] = value;
    setSpecifications(updatedSpecs);
  };

  // Remove specification
  const removeSpecification = (index: number) => {
    const updatedSpecs = [...specifications];
    updatedSpecs.splice(index, 1);
    setSpecifications(updatedSpecs);
  };

  // Update inventory quantity
  const updateInventoryQuantity = (warehouseId: string, quantity: number) => {
    const updatedInventory = inventory.map(item =>
      item.warehouseId === warehouseId ? { ...item, quantity } : item
    );
    setInventory(updatedInventory);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !selectedCategory || basePrice < 0 || costPrice < 0) {
      alert("Please fill in all required fields with valid values.");
      return;
    }

    setIsLoading(true);

    // Prepare product data
    const productData = {
      name,
      description,
      basePrice,
      costPrice,
      categoryId: selectedCategory,
      isCustomizable,
      isComponent,
      componentTypeId: isComponent ? componentType : null,
      specifications: specifications.filter(spec => spec.name && spec.value),
      inventory: inventory.filter(item => item.quantity > 0)
    };

    // Send data to API
    try {
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      const createdProduct = await response.json();

      // If inventory items were specified, create them
      if (inventory.some(item => item.quantity > 0)) {
        for (const item of inventory.filter(item => item.quantity > 0)) {
          await fetch('/api/inventory', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              productId: createdProduct.id,
              warehouseId: item.warehouseId,
              quantity: item.quantity
            }),
          });
        }
      }

      setIsLoading(false);
      // Redirect to product details
      router.push(`/dashboard/products/${createdProduct.id}`);
    } catch (error) {
      console.error('Error creating product:', error);
      alert(`Error creating product: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Add New Product
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/dashboard/products"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </Link>
        </div>
      </div>

      {dataLoading ? (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
          <p className="text-center mt-4 text-gray-500">Loading data...</p>
        </div>
      ) : error ? (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
          <div className="text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">Error</h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                {/* Product Name */}
                <div className="sm:col-span-3">
                  <label htmlFor="name" className="block text-base font-bold text-black">
                    Product Name <span className="text-red-600">*</span>
                  </label>
                  <div className="mt-2">
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                      className="block w-full py-3 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black"
                    />
                  </div>
                </div>

                {/* Category */}
                <div className="sm:col-span-3">
                  <label htmlFor="category" className="block text-base font-bold text-black">
                    Category <span className="text-red-600">*</span>
                  </label>
                  <div className="mt-2">
                    <select
                      id="category"
                      name="category"
                      value={selectedCategory}
                      onChange={(e) => {
                        const categoryId = e.target.value;
                        setSelectedCategory(categoryId);

                        // Find the selected category and update the type
                        if (categoryId) {
                          const category = categories.find(c => c.id === categoryId);
                          setSelectedCategoryType(category?.type || null);

                          // If it's a service category, reset values that don't apply
                          if (category?.type === "SERVICE") {
                            setBasePrice(0);
                            setCostPrice(0);
                            setIsCustomizable(false);
                            setIsComponent(false);
                            setSpecifications([]);
                            setInventory(warehouses.map(w => ({ warehouseId: w.id, quantity: 0 })));
                          }
                        } else {
                          setSelectedCategoryType(null);
                        }
                      }}
                      required
                      className="block w-full py-3 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black"
                    >
                      <option value="">Select a category</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name} {category.type === "SERVICE" ? "(Service)" : ""}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Description */}
                <div className="sm:col-span-6">
                  <label htmlFor="description" className="block text-base font-bold text-black">
                    Description
                  </label>
                  <div className="mt-2">
                    <textarea
                      id="description"
                      name="description"
                      rows={3}
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      className="block w-full py-3 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black"
                    />
                  </div>
                </div>

                {/* Base Price */}
                <div className="sm:col-span-3">
                  <label htmlFor="basePrice" className="block text-base font-bold text-black">
                    Base Price <span className="text-red-600">*</span>
                  </label>
                  <div className="mt-2">
                    <input
                      type="number"
                      id="basePrice"
                      name="basePrice"
                      min="0"
                      step="0.01"
                      value={basePrice}
                      onChange={(e) => setBasePrice(parseFloat(e.target.value) || 0)}
                      required
                      disabled={selectedCategoryType === "SERVICE"}
                      className={`block w-full py-3 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black ${selectedCategoryType === "SERVICE" ? "bg-gray-100" : ""}`}
                    />
                  </div>
                  {selectedCategoryType === "SERVICE" && (
                    <p className="mt-1 text-sm text-gray-500">
                      Services don't have a base price. This will be set to 0.
                    </p>
                  )}
                </div>

                {/* Cost Price */}
                <div className="sm:col-span-3">
                  <label htmlFor="costPrice" className="block text-base font-bold text-black">
                    Cost Price <span className="text-red-600">*</span>
                  </label>
                  <div className="mt-2">
                    <input
                      type="number"
                      id="costPrice"
                      name="costPrice"
                      min="0"
                      step="0.01"
                      value={costPrice}
                      onChange={(e) => setCostPrice(parseFloat(e.target.value) || 0)}
                      required
                      disabled={selectedCategoryType === "SERVICE"}
                      className={`block w-full py-3 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black ${selectedCategoryType === "SERVICE" ? "bg-gray-100" : ""}`}
                    />
                  </div>
                  {selectedCategoryType === "SERVICE" && (
                    <p className="mt-1 text-sm text-gray-500">
                      Services don't have a cost price. This will be set to 0.
                    </p>
                  )}
                </div>

                {/* Is Customizable - Only for products */}
                {selectedCategoryType !== "SERVICE" && (
                  <div className="sm:col-span-3">
                    <div className="flex items-center">
                      <input
                        id="isCustomizable"
                        name="isCustomizable"
                        type="checkbox"
                        checked={isCustomizable}
                        onChange={(e) => setIsCustomizable(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isCustomizable" className="ml-2 block text-base font-bold text-black">
                        Customizable Product
                      </label>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">
                      Check this if the product can be customized (e.g., computers with different RAM/storage options)
                    </p>
                  </div>
                )}

                {/* Is Component - Only for products */}
                {selectedCategoryType !== "SERVICE" && (
                  <div className="sm:col-span-3">
                    <div className="flex items-center">
                      <input
                        id="isComponent"
                        name="isComponent"
                        type="checkbox"
                        checked={isComponent}
                        onChange={(e) => setIsComponent(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isComponent" className="ml-2 block text-base font-bold text-black">
                        Component
                      </label>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">
                      Check this if the product is a component that can be used in customizable products
                    </p>
                  </div>
                )}

                {/* Component Type (only shown if isComponent is true) */}
                {isComponent && (
                  <div className="sm:col-span-3">
                    <label htmlFor="componentType" className="block text-base font-bold text-black">
                      Component Type <span className="text-red-600">*</span>
                    </label>
                    <div className="mt-2">
                      <select
                        id="componentType"
                        name="componentType"
                        value={componentType}
                        onChange={(e) => setComponentType(e.target.value)}
                        required={isComponent}
                        className="block w-full py-3 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black"
                      >
                        <option value="">Select a component type</option>
                        {componentTypes.map((type) => (
                          <option key={type.id} value={type.id}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}
              </div>

              {/* Specifications - Only for products */}
              {selectedCategoryType !== "SERVICE" && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-900">Specifications</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Add technical specifications for this product.
                  </p>
                  <div className="mt-4 space-y-4">
                    {specifications.map((spec, index) => (
                      <div key={index} className="flex items-center space-x-4">
                        <div className="flex-1">
                          <input
                            type="text"
                            placeholder="Name (e.g., Processor)"
                            value={spec.name}
                            onChange={(e) => updateSpecification(index, 'name', e.target.value)}
                            className="block w-full py-2 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black"
                          />
                        </div>
                        <div className="flex-1">
                          <input
                            type="text"
                            placeholder="Value (e.g., Intel Core i7)"
                            value={spec.value}
                            onChange={(e) => updateSpecification(index, 'value', e.target.value)}
                            className="block w-full py-2 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={() => removeSpecification(index)}
                          className="inline-flex items-center p-2 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={addSpecification}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                      Add Specification
                    </button>
                  </div>
                </div>
              )}

              {/* Initial Inventory - Only for products */}
              {selectedCategoryType !== "SERVICE" && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-900">Initial Inventory</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Set initial stock levels for each warehouse.
                  </p>
                  <div className="mt-4">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Warehouse
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Quantity
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {warehouses.map((warehouse) => {
                          const inventoryItem = inventory.find(item => item.warehouseId === warehouse.id);
                          return (
                            <tr key={warehouse.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {warehouse.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <input
                                  type="number"
                                  min="0"
                                  value={inventoryItem?.quantity || 0}
                                  onChange={(e) => updateInventoryQuantity(warehouse.id, parseInt(e.target.value) || 0)}
                                  className="block w-full py-2 px-3 text-base font-medium text-black bg-white border-2 border-gray-300 rounded-lg shadow-sm focus:ring-black focus:border-black"
                                />
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Service Note - Only for services */}
              {selectedCategoryType === "SERVICE" && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-blue-800">Service Product</h3>
                      <div className="mt-2 text-sm text-blue-700">
                        <p>This is a service product. Services don't have inventory, cost price, or specifications. They are used for services you provide to customers.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
              <button
                type="submit"
                disabled={isLoading}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating...
                  </>
                ) : (
                  'Create Product'
                )}
              </button>
            </div>
          </div>
        </form>
      )}
    </div>
  );
}
