"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Modal from "../settings/components/Modal";
import ContactForm from "./components/ContactForm";
import ImportContactsModal from "./components/ImportContactsModal";

interface Contact {
  id: string;
  name: string;
  phone: string;
  address?: string;
  isCustomer: boolean;
  isSupplier: boolean;
  isActive: boolean;
  balance: number;
  creditLimit?: number;
  creditPeriod?: number;
  openingBalance?: number;
  openingBalanceDate?: string;
}

export default function ContactsPage() {
  const router = useRouter();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [calculatedBalances, setCalculatedBalances] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<"all" | "customer" | "supplier">("all");
  const [balanceFilter, setBalanceFilter] = useState<"all" | "non-zero">("all");

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  // Fetch contacts
  const fetchContacts = async () => {
    setIsLoading(true);
    try {
      let url = "/api/contacts";

      // Add filter parameters
      const params = new URLSearchParams();
      if (filterType !== "all") {
        params.append("type", filterType);
      }
      if (searchTerm) {
        params.append("search", searchTerm);
      }
      if (balanceFilter !== "all") {
        params.append("balance", balanceFilter);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        // Clone the response before reading it as JSON
        const clonedResponse = response.clone();
        try {
          const errorData = await clonedResponse.json();
          throw new Error(errorData.error || "Failed to fetch contacts");
        } catch (jsonError) {
          // If we can't parse the JSON, just use the status text
          throw new Error(`Failed to fetch contacts: ${response.statusText}`);
        }
      }

      const data = await response.json();
      setContacts(data);
      // Clear any previous errors
      setError("");
    } catch (error: any) {
      console.error("Error fetching contacts:", error);

      // Check if it's a permission error
      const errorMessage = error.message || "An error occurred";
      if (errorMessage.includes("permission")) {
        setError("You don't have permission to view contacts. Please contact your administrator.");
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch calculated balances
  const fetchCalculatedBalances = async () => {
    try {
      const response = await fetch('/api/contacts/calculated-balances');

      if (response.ok) {
        const data = await response.json();
        setCalculatedBalances(data);
      } else {
        console.error('Failed to fetch calculated balances');
      }
    } catch (error) {
      console.error('Error fetching calculated balances:', error);
    }
  };

  useEffect(() => {
    fetchContacts();
    fetchCalculatedBalances();
  }, [filterType, balanceFilter]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchContacts();
  };

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Debounce search to avoid too many requests
    const timeoutId = setTimeout(() => {
      // Only perform search if there's a search term
      fetchContacts();
    }, 300); // 300ms delay for faster response

    return () => clearTimeout(timeoutId);
  };

  // Open modal to add/edit contact
  const openContactModal = (contact?: Contact) => {
    setSelectedContact(contact || null);
    setIsModalOpen(true);
  };

  // Close modals
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedContact(null);
  };

  const openImportModal = () => {
    setIsImportModalOpen(true);
  };

  const closeImportModal = () => {
    setIsImportModalOpen(false);
  };

  // Handle export to Excel
  const handleExport = () => {
    // Create URL with current filters
    let exportUrl = `/api/contacts/export?nocache=${Date.now()}`;
    if (searchTerm) exportUrl += `&search=${searchTerm}`;
    if (filterType !== "all") exportUrl += `&type=${filterType}`;
    if (balanceFilter !== "all") exportUrl += `&balance=${balanceFilter}`;

    // Open the URL in a new tab
    window.open(exportUrl, '_blank');
  };

  // Handle synchronize balances
  const [isSyncing, setIsSyncing] = useState(false);
  const handleSyncBalances = async () => {
    if (confirm("This will recalculate and update the balance for all contacts based on their transactions. Continue?")) {
      setIsSyncing(true);
      try {
        const response = await fetch('/api/contacts/sync-balances', {
          method: 'POST',
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || 'Failed to synchronize balances');
        }

        const data = await response.json();
        alert(`Balances synchronized successfully. ${data.results.filter(r => r.success).length} contacts updated.`);
        fetchContacts(); // Refresh the contacts list
        fetchCalculatedBalances(); // Refresh calculated balances
      } catch (error) {
        console.error('Error synchronizing balances:', error);
        alert(error instanceof Error ? error.message : 'An error occurred while synchronizing balances');
      } finally {
        setIsSyncing(false);
      }
    }
  };

  // Handle successful form submission
  const handleSuccess = () => {
    closeModal();
    fetchContacts();
    fetchCalculatedBalances();
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Contacts</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleSyncBalances}
            disabled={isSyncing}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium flex items-center disabled:opacity-50"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-2 ${isSyncing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {isSyncing ? 'Syncing Balances...' : 'Sync Balances'}
          </button>
          <button
            onClick={handleExport}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Export to Excel
          </button>
          <button
            onClick={openImportModal}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
            </svg>
            Import from Excel
          </button>
          <button
            onClick={() => openContactModal()}
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-teal-500 text-white rounded-md hover:from-blue-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 font-medium flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add Contact
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="flex space-x-2">
              <button
                onClick={() => setFilterType("all")}
                className={`px-3 py-1 rounded-md font-medium ${
                  filterType === "all"
                    ? "bg-indigo-100 text-indigo-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                All
              </button>
              <button
                onClick={() => setFilterType("customer")}
                className={`px-3 py-1 rounded-md font-medium ${
                  filterType === "customer"
                    ? "bg-indigo-100 text-indigo-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                Customers
              </button>
              <button
                onClick={() => setFilterType("supplier")}
                className={`px-3 py-1 rounded-md font-medium ${
                  filterType === "supplier"
                    ? "bg-indigo-100 text-indigo-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                Suppliers
              </button>
            </div>

            <div className="flex space-x-2">
              <span className="text-sm font-medium text-gray-700 self-center">Balance:</span>
              <button
                onClick={() => setBalanceFilter("all")}
                className={`px-3 py-1 rounded-md font-medium ${
                  balanceFilter === "all"
                    ? "bg-indigo-100 text-indigo-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                All
              </button>
              <button
                onClick={() => setBalanceFilter("non-zero")}
                className={`px-3 py-1 rounded-md font-medium ${
                  balanceFilter === "non-zero"
                    ? "bg-indigo-100 text-indigo-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                With Balance
              </button>
            </div>
          </div>

          <form onSubmit={handleSearch} className="flex w-full sm:w-auto">
            <div className="relative w-full sm:w-64">
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Search by name or phone..."
                className="border border-gray-300 rounded-l-md px-4 py-2 w-full text-gray-900 font-medium"
              />
              {searchTerm && (
                <button
                  type="button"
                  onClick={() => {
                    setSearchTerm("");
                    // Explicitly set search parameter to empty and fetch contacts
                    const fetchAllContacts = async () => {
                      setIsLoading(true);
                      try {
                        let url = "/api/contacts";

                        // Add filter parameters
                        const params = new URLSearchParams();
                        if (filterType !== "all") {
                          params.append("type", filterType);
                        }
                        if (balanceFilter !== "all") {
                          params.append("balance", balanceFilter);
                        }

                        if (params.toString()) {
                          url += `?${params.toString()}`;
                        }

                        const response = await fetch(url);

                        if (!response.ok) {
                          // Clone the response before reading it as JSON
                          const clonedResponse = response.clone();
                          try {
                            const errorData = await clonedResponse.json();
                            throw new Error(errorData.error || "Failed to fetch contacts");
                          } catch (jsonError) {
                            // If we can't parse the JSON, just use the status text
                            throw new Error(`Failed to fetch contacts: ${response.statusText}`);
                          }
                        }

                        const data = await response.json();
                        setContacts(data);
                        // Clear any previous errors
                        setError("");
                      } catch (error: any) {
                        console.error("Error fetching contacts:", error);

                        // Check if it's a permission error
                        const errorMessage = error.message || "An error occurred";
                        if (errorMessage.includes("permission")) {
                          setError("You don't have permission to view contacts. Please contact your administrator.");
                        } else {
                          setError(errorMessage);
                        }
                      } finally {
                        setIsLoading(false);
                      }
                    };

                    fetchAllContacts();
                  }}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              )}
            </div>
            <button
              type="submit"
              className="bg-indigo-600 text-white px-4 py-2 rounded-r-md hover:bg-indigo-700 font-medium"
            >
              Search
            </button>
          </form>
        </div>

        <div className="overflow-x-auto">
          {Object.keys(calculatedBalances).length > 0 &&
            Object.keys(calculatedBalances).some(id =>
              contacts.some(c => c.id === id && calculatedBalances[id] !== c.balance)
            ) && (
              <div className="p-2 text-xs text-amber-700 bg-amber-50 border-b border-amber-100">
                <span className="font-medium">Note:</span> Balances marked with an asterisk (*) are calculated from transactions and differ from the stored balance. Click 'Details' to update.
              </div>
            )
          }
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Phone
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Balance
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Credit Limit
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    Loading...
                  </td>
                </tr>
              ) : contacts.length > 0 ? (
                contacts.map((contact) => (
                  <tr key={contact.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-semibold text-gray-900">{contact.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">
                      {contact.phone}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-1">
                        {contact.isCustomer && (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Customer
                          </span>
                        )}
                        {contact.isSupplier && (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            Supplier
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {calculatedBalances[contact.id] !== undefined ? (
                        <span className={`${calculatedBalances[contact.id] > 0 ? 'text-red-600' : calculatedBalances[contact.id] < 0 ? 'text-green-600' : 'text-gray-500'}`}>
                          {calculatedBalances[contact.id].toFixed(2)} ج.م
                          {calculatedBalances[contact.id] !== contact.balance && (
                            <span
                              className="ml-1 text-xs text-amber-600 cursor-help"
                              title="This balance is calculated from transactions and differs from the stored balance in the database. Click 'Details' to update."
                            >
                              *
                            </span>
                          )}
                        </span>
                      ) : (
                        <span className={`${contact.balance > 0 ? 'text-red-600' : contact.balance < 0 ? 'text-green-600' : 'text-gray-500'}`}>
                          {contact.balance.toFixed(2)} ج.م
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {contact.isCustomer ? (
                        <span className="text-gray-700">
                          {(contact.creditLimit || 0).toFixed(2)} ج.م
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        contact.isActive
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}>
                        {contact.isActive ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => openContactModal(contact)}
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => router.push(`/dashboard/contacts/${contact.id}`)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        Details
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    No contacts found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {isModalOpen && (
        <Modal isOpen={isModalOpen} onClose={closeModal} size="md">
          <ContactForm
            contact={selectedContact || undefined}
            onClose={closeModal}
            onSuccess={handleSuccess}
          />
        </Modal>
      )}

      {isImportModalOpen && (
        <Modal isOpen={isImportModalOpen} onClose={closeImportModal} size="lg">
          <ImportContactsModal
            onClose={closeImportModal}
            onSuccess={handleSuccess}
          />
        </Modal>
      )}
    </div>
  );
}
