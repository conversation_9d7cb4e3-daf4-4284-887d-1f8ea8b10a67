import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/contacts/:id/statement - Get contact statement
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Get the contact
    const contact = await db.contact.findUnique({
      where: {
        id,
      },
    });

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    // Check permissions based on contact type
    const hasViewCustomersPermission = await hasPermission("view_customers");
    const hasViewSuppliersPermission = await hasPermission("view_suppliers");
    const hasViewFinancePermission = await hasPermission("view_finance");
    const isAdmin = session.user.role === "ADMIN";

    if (contact.isCustomer && !hasViewCustomersPermission && !hasViewFinancePermission && !isAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to view customer statements" },
        { status: 403 }
      );
    }

    if (contact.isSupplier && !hasViewSuppliersPermission && !hasViewFinancePermission && !isAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to view supplier statements" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");

    // Build date filter
    const dateFilter: any = {};

    if (startDate && endDate) {
      dateFilter.gte = new Date(startDate);
      dateFilter.lte = new Date(endDate);
    } else if (startDate) {
      dateFilter.gte = new Date(startDate);
    } else if (endDate) {
      dateFilter.lte = new Date(endDate);
    }

    // Get all transactions for this contact
    const transactions = await db.transaction.findMany({
      where: {
        contactId: id,
        ...(Object.keys(dateFilter).length > 0 ? { date: dateFilter } : {}),
      },
      include: {
        account: true,
      },
      orderBy: {
        date: "asc",
      },
    });

    // Get sales for this contact
    const sales = await db.sale.findMany({
      where: {
        contactId: id,
        ...(Object.keys(dateFilter).length > 0 ? { date: dateFilter } : {}),
      },
      orderBy: {
        date: "asc",
      },
    });

    // Get purchases for this contact
    const purchases = await db.purchase.findMany({
      where: {
        contactId: id,
        ...(Object.keys(dateFilter).length > 0 ? { date: dateFilter } : {}),
      },
      orderBy: {
        date: "asc",
      },
    });

    // Calculate opening balance
    let openingBalance = contact.openingBalance;

    // If start date is provided, calculate balance up to that date
    if (startDate) {
      const startDateObj = new Date(startDate);

      // Get transactions before start date
      const previousTransactions = await db.transaction.findMany({
        where: {
          contactId: id,
          date: {
            lt: startDateObj,
          },
        },
      });

      // Get sales before start date
      const previousSales = await db.sale.findMany({
        where: {
          contactId: id,
          date: {
            lt: startDateObj,
          },
        },
      });

      // Get purchases before start date
      const previousPurchases = await db.purchase.findMany({
        where: {
          contactId: id,
          date: {
            lt: startDateObj,
          },
        },
      });

      // Calculate balance from previous transactions
      for (const transaction of previousTransactions) {
        if (transaction.referenceType === "RECEIPT" && transaction.type === "DEBIT") {
          // Customer payment received - decrease balance
          openingBalance -= transaction.amount;
        } else if (transaction.referenceType === "PAYMENT" && transaction.type === "CREDIT") {
          // Payment to supplier - decrease balance
          openingBalance -= transaction.amount;
        } else if (transaction.referenceType === "SALE" && transaction.type === "DEBIT") {
          // Sale to customer - increase balance
          openingBalance += transaction.amount;
        } else if (transaction.referenceType === "PURCHASE" && transaction.type === "CREDIT") {
          // Purchase from supplier - increase balance
          openingBalance += transaction.amount;
        }
      }

      // Add sales to opening balance
      for (const sale of previousSales) {
        openingBalance += sale.totalAmount;
      }

      // Add purchases to opening balance
      for (const purchase of previousPurchases) {
        openingBalance += purchase.totalAmount;
      }
    }

    // Combine all transactions into a statement
    const statement = [
      // Add opening balance as first item
      {
        date: startDate ? new Date(startDate) : contact.openingBalanceDate,
        description: "Opening Balance",
        reference: "",
        debit: openingBalance > 0 ? openingBalance : 0,
        credit: openingBalance < 0 ? Math.abs(openingBalance) : 0,
        balance: openingBalance,
        type: "OPENING_BALANCE",
      },
    ];

    let runningBalance = openingBalance;

    // Add all sales to the statement
    for (const sale of sales) {
      // For customers, sales increase their balance (they owe us money)
      if (contact.isCustomer) {
        runningBalance += sale.totalAmount;
        statement.push({
          date: sale.date,
          description: `Sale Invoice #${sale.invoiceNumber}`,
          reference: sale.invoiceNumber,
          debit: sale.totalAmount, // Debit means they owe us
          credit: 0,
          balance: runningBalance,
          type: "SALE",
          paymentStatus: sale.paymentStatus,
        });
      }
      // For suppliers, sales decrease their balance (we owe them less)
      else if (contact.isSupplier && !contact.isCustomer) {
        runningBalance -= sale.totalAmount;
        statement.push({
          date: sale.date,
          description: `Sale Invoice #${sale.invoiceNumber}`,
          reference: sale.invoiceNumber,
          debit: 0,
          credit: sale.totalAmount, // Credit means we owe them less
          balance: runningBalance,
          type: "SALE",
          paymentStatus: sale.paymentStatus,
        });
      }
    }

    // We don't need to add purchases here as they will be included in transactions
    // Purchases always have corresponding transactions in our system

    // Add other transactions
    for (const transaction of transactions) {
      // Skip transactions that are already included in sales
      if (transaction.referenceType === "SALE" && sales.some(s => s.invoiceNumber === transaction.reference)) {
        continue;
      }

      let debit = 0;
      let credit = 0;
      let description = transaction.description;

      // For customer payments
      if (transaction.referenceType === "RECEIPT" && transaction.type === "DEBIT") {
        // Customer payment received - add payment details
        credit = transaction.amount;
        runningBalance -= transaction.amount;

        // Enhance description with payment method if available
        if (transaction.paymentMethod) {
          description = `Payment Received - ${transaction.paymentMethod.replace('_', ' ')}`;
        }

        // Add invoice number if available
        if (transaction.reference) {
          description += ` - Invoice #${transaction.reference}`;
        }
      }
      // For supplier payments
      else if (transaction.referenceType === "PAYMENT" && transaction.type === "CREDIT") {
        // Payment to supplier - add payment details
        debit = transaction.amount;
        runningBalance -= transaction.amount;

        // Enhance description with payment method if available
        if (transaction.paymentMethod) {
          description = `Payment Made - ${transaction.paymentMethod.replace('_', ' ')}`;
        }

        // Add invoice number if available
        if (transaction.reference) {
          description += ` - Invoice #${transaction.reference}`;
        }
      }
      // For other debit transactions
      else if (transaction.type === "DEBIT") {
        // Other debit transaction
        debit = transaction.amount;
        runningBalance += transaction.amount;
      }
      // For other credit transactions
      else if (transaction.type === "CREDIT") {
        // Other credit transaction
        credit = transaction.amount;
        runningBalance -= transaction.amount;
      }

      statement.push({
        date: transaction.date,
        description: description,
        reference: transaction.reference || transaction.transactionNumber || "",
        debit,
        credit,
        balance: runningBalance,
        type: transaction.referenceType || "OTHER",
        paymentMethod: transaction.paymentMethod,
      });
    }

    // Sort statement by date
    statement.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Calculate totals
    const totals = {
      debit: statement.reduce((sum, item) => sum + item.debit, 0),
      credit: statement.reduce((sum, item) => sum + item.credit, 0),
      balance: runningBalance,
    };

    return NextResponse.json({
      contact,
      statement,
      totals,
    });
  } catch (error) {
    console.error("Error fetching contact statement:", error);
    return NextResponse.json(
      { error: "Failed to fetch contact statement" },
      { status: 500 }
    );
  }
}
