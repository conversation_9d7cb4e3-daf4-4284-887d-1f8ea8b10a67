"use client";

import { useState } from "react";
import { AlertTriangle } from "lucide-react";

interface DatabaseInitFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

export default function DatabaseInitForm({ onClose, onSuccess }: DatabaseInitFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [selectedOption, setSelectedOption] = useState<"reset" | "seed" | "clean">("seed");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Confirm text validation
    if (confirmText !== "CONFIRM") {
      setError("Please type CONFIRM to proceed");
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch(`/api/system/database/${selectedOption}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (response.ok) {
        setSuccess(`Database ${selectedOption === "reset" ? "reset" : selectedOption === "seed" ? "seeded" : "cleaned"} successfully!`);
        setTimeout(() => {
          onSuccess();
        }, 2000);
      } else if (contentType && contentType.indexOf("application/json") !== -1) {
        try {
          const data = await response.json();
          setError(data.error || "An error occurred");
        } catch (jsonError) {
          console.error("Error parsing JSON:", jsonError);
          setError("Error parsing server response");
        }
      } else {
        // Handle non-JSON response
        const textResponse = await response.text();
        console.error("Non-JSON response:", textResponse);
        setError("Server returned an invalid response format");
      }
    } catch (err) {
      setError("Failed to connect to server");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
      <div className="sm:flex sm:items-start">
        <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
          <AlertTriangle className="h-6 w-6 text-red-600" />
        </div>
        <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Database Management</h3>

          <div className="mt-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Select Operation</label>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center">
                    <input
                      id="reset"
                      name="database-option"
                      type="radio"
                      checked={selectedOption === "reset"}
                      onChange={() => setSelectedOption("reset")}
                      className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                    />
                    <label htmlFor="reset" className="ml-3 block text-sm font-medium text-gray-700">
                      Reset Database (⚠️ Deletes all data and recreates tables)
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="seed"
                      name="database-option"
                      type="radio"
                      checked={selectedOption === "seed"}
                      onChange={() => setSelectedOption("seed")}
                      className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                    />
                    <label htmlFor="seed" className="ml-3 block text-sm font-medium text-gray-700">
                      Seed Database (Adds sample data)
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="clean"
                      name="database-option"
                      type="radio"
                      checked={selectedOption === "clean"}
                      onChange={() => setSelectedOption("clean")}
                      className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                    />
                    <label htmlFor="clean" className="ml-3 block text-sm font-medium text-gray-700">
                      Clean Database (Removes all data but keeps tables)
                    </label>
                  </div>
                </div>
              </div>

              <div className="rounded-md bg-yellow-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Warning</h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        {selectedOption === "reset" && "This will delete ALL data in your database and recreate the tables. This action cannot be undone."}
                        {selectedOption === "seed" && "This will add sample data to your database. If you have existing data, it may cause conflicts."}
                        {selectedOption === "clean" && "This will remove ALL data from your database but keep the table structure. This action cannot be undone."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="confirm" className="block text-sm font-medium text-gray-700">
                  Type CONFIRM to proceed
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="confirm"
                    id="confirm"
                    value={confirmText}
                    onChange={(e) => setConfirmText(e.target.value)}
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    placeholder="CONFIRM"
                  />
                </div>
              </div>

              {error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Error</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {success && (
                <div className="rounded-md bg-green-50 p-4">
                  <div className="flex">
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">Success</h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>{success}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
        <button
          type="button"
          onClick={handleSubmit}
          disabled={isLoading || confirmText !== "CONFIRM"}
          className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white sm:ml-3 sm:w-auto sm:text-sm ${
            isLoading || confirmText !== "CONFIRM"
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          }`}
        >
          {isLoading ? "Processing..." : "Proceed"}
        </button>
        <button
          type="button"
          onClick={onClose}
          className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}
