import { PrismaClient, Role, AccountType } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Clear all existing data in the correct order to avoid foreign key constraints
  console.log('Clearing all existing data...');

  // First, delete records from tables with foreign key dependencies
  await prisma.userWarehouse.deleteMany();
  await prisma.transaction.deleteMany();
  await prisma.saleItemComponent.deleteMany();
  await prisma.saleItem.deleteMany();
  await prisma.sale.deleteMany();
  await prisma.purchaseItem.deleteMany();
  await prisma.purchase.deleteMany();
  await prisma.creditNotePayment.deleteMany();
  await prisma.creditNoteItem.deleteMany();
  await prisma.creditNote.deleteMany();
  // Delete maintenance data
  await prisma.maintenancePayment.deleteMany();
  await prisma.maintenancePart.deleteMany();
  await prisma.maintenanceStatusHistory.deleteMany();
  await prisma.maintenanceService.deleteMany();
  await prisma.inventory.deleteMany();
  await prisma.specification.deleteMany();
  await prisma.product.deleteMany();
  await prisma.category.deleteMany();
  await prisma.warehouse.deleteMany();
  await prisma.contact.deleteMany();
  await prisma.account.deleteMany();
  await prisma.user.deleteMany();
  await prisma.permission.deleteMany();
  await prisma.branch.deleteMany();

  console.log('All data cleared successfully');

  // Create default permissions
  console.log('Creating default permissions...');
  const permissions = await createDefaultPermissions();

  // Create a real branch
  const mainBranch = await prisma.branch.create({
    data: {
      name: 'Main Branch',
      code: 'A', // Branch code for invoice numbering
      address: 'Cairo, Egypt',
      phone: '***********',
    },
  });

  // Create a warehouse for the main branch
  const mainWarehouse = await prisma.warehouse.create({
    data: {
      name: 'Main Warehouse',
      branchId: mainBranch.id,
    },
  });

  // Create an admin user with real credentials
  const hashedPassword = await bcrypt.hash('admin123', 10);
  const adminUser = await prisma.user.create({
    data: {
      name: 'Admin User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: Role.ADMIN,
      branchId: mainBranch.id,
      isActive: true,
      permissions: {
        connect: permissions.map(p => ({ id: p.id })),
      },
    },
  });

  // Connect admin user to warehouse
  await prisma.userWarehouse.create({
    data: {
      userId: adminUser.id,
      warehouseId: mainWarehouse.id,
    },
  });

  // Create basic accounting accounts
  console.log('Creating basic accounting accounts...');

  await prisma.account.create({
    data: {
      name: 'Cash',
      accountNumber: '1001',
      type: AccountType.ASSET,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Vodafone Cash',
      accountNumber: '1002',
      type: AccountType.ASSET,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Bank Account',
      accountNumber: '1003',
      type: AccountType.ASSET,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Credit Card',
      accountNumber: '1004',
      type: AccountType.ASSET,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Accounts Receivable',
      accountNumber: '1100',
      type: AccountType.ASSET,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Inventory',
      accountNumber: '1200',
      type: AccountType.ASSET,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Accounts Payable',
      accountNumber: '2000',
      type: AccountType.LIABILITY,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Sales Revenue',
      accountNumber: '4000',
      type: AccountType.REVENUE,
      balance: 0,
    },
  });

  await prisma.account.create({
    data: {
      name: 'Cost of Goods Sold',
      accountNumber: '5000',
      type: AccountType.EXPENSE,
      balance: 0,
    },
  });

  // Create basic product categories
  console.log('Creating product categories...');

  const laptopCategory = await prisma.category.create({
    data: {
      name: 'Laptops',
      description: 'Portable computers',
    },
  });

  const desktopCategory = await prisma.category.create({
    data: {
      name: 'Desktop Computers',
      description: 'Desktop computers and workstations',
    },
  });

  const componentsCategory = await prisma.category.create({
    data: {
      name: 'Computer Components',
      description: 'Computer parts and components',
    },
  });

  const accessoriesCategory = await prisma.category.create({
    data: {
      name: 'Accessories',
      description: 'Computer peripherals and accessories',
    },
  });

  // Create component products
  console.log('Creating component products...');

  const ramProduct = await prisma.product.create({
    data: {
      name: 'DDR4 RAM 8GB',
      description: 'DDR4 RAM with 8GB capacity',
      basePrice: 800,
      costPrice: 600,
      isComponent: true,
      componentType: 'RAM',
      categoryId: componentsCategory.id,
    },
  });

  const ssdProduct = await prisma.product.create({
    data: {
      name: 'SSD 256GB',
      description: 'SSD storage with 256GB capacity',
      basePrice: 1200,
      costPrice: 900,
      isComponent: true,
      componentType: 'SSD',
      categoryId: componentsCategory.id,
    },
  });

  const hddProduct = await prisma.product.create({
    data: {
      name: 'HDD 1TB',
      description: 'HDD storage with 1TB capacity',
      basePrice: 900,
      costPrice: 700,
      isComponent: true,
      componentType: 'HDD',
      categoryId: componentsCategory.id,
    },
  });

  // Create a customizable laptop product
  console.log('Creating laptop product...');

  const laptop = await prisma.product.create({
    data: {
      name: 'HP ProBook Laptop',
      description: 'HP ProBook laptop with customizable specifications',
      basePrice: 12000,
      costPrice: 9000,
      isCustomizable: true,
      categoryId: laptopCategory.id,
      specifications: {
        create: [
          {
            name: 'Processor',
            value: 'Intel Core i5-11th Gen',
          },
          {
            name: 'Display',
            value: '15.6 inch FHD',
          },
        ],
      },
    },
  });

  // Add inventory for components
  console.log('Adding inventory for products...');

  // Add inventory for RAM
  await prisma.inventory.create({
    data: {
      productId: ramProduct.id,
      warehouseId: mainWarehouse.id,
      quantity: 50,
      costPrice: 600,
    },
  });

  // Add inventory for SSD
  await prisma.inventory.create({
    data: {
      productId: ssdProduct.id,
      warehouseId: mainWarehouse.id,
      quantity: 30,
      costPrice: 900,
    },
  });

  // Add inventory for HDD
  await prisma.inventory.create({
    data: {
      productId: hddProduct.id,
      warehouseId: mainWarehouse.id,
      quantity: 40,
      costPrice: 700,
    },
  });

  // Add inventory for laptop
  await prisma.inventory.create({
    data: {
      productId: laptop.id,
      warehouseId: mainWarehouse.id,
      quantity: 10,
      costPrice: 9000,
    },
  });

  // Create a sample contact (customer and supplier)
  console.log('Creating sample contacts...');

  await prisma.contact.create({
    data: {
      name: 'Sample Customer',
      phone: '01012345678',
      address: 'Cairo, Egypt',
      isCustomer: true,
      isSupplier: false,
    },
  });

  await prisma.contact.create({
    data: {
      name: 'Sample Supplier',
      phone: '01098765432',
      address: 'Cairo, Egypt',
      isCustomer: false,
      isSupplier: true,
    },
  });

  console.log('Database seeded successfully');
}

// Function to create default permissions
async function createDefaultPermissions() {
  const permissionsList = [
    { name: 'view_dashboard', description: 'View dashboard' },
    { name: 'view_sales', description: 'View sales' },
    { name: 'add_sales', description: 'Add sales' },
    { name: 'edit_sales', description: 'Edit sales' },
    { name: 'delete_sales', description: 'Delete sales' },
    { name: 'view_purchases', description: 'View purchases' },
    { name: 'add_purchases', description: 'Add purchases' },
    { name: 'edit_purchases', description: 'Edit purchases' },
    { name: 'delete_purchases', description: 'Delete purchases' },
    { name: 'view_products', description: 'View products' },
    { name: 'add_products', description: 'Add products' },
    { name: 'edit_products', description: 'Edit products' },
    { name: 'delete_products', description: 'Delete products' },
    { name: 'view_inventory', description: 'View inventory' },
    { name: 'add_inventory', description: 'Add inventory' },
    { name: 'edit_inventory', description: 'Edit inventory' },
    { name: 'view_contacts', description: 'View contacts' },
    { name: 'add_contacts', description: 'Add contacts' },
    { name: 'edit_contacts', description: 'Edit contacts' },
    { name: 'delete_contacts', description: 'Delete contacts' },
    { name: 'view_reports', description: 'View reports' },
    { name: 'view_settings', description: 'View settings' },
    { name: 'edit_settings', description: 'Edit settings' },
    { name: 'view_users', description: 'View users' },
    { name: 'add_users', description: 'Add users' },
    { name: 'edit_users', description: 'Edit users' },
    { name: 'delete_users', description: 'Delete users' },
    { name: 'view_branches', description: 'View branches' },
    { name: 'add_branches', description: 'Add branches' },
    { name: 'edit_branches', description: 'Edit branches' },
    { name: 'delete_branches', description: 'Delete branches' },
    { name: 'view_warehouses', description: 'View warehouses' },
    { name: 'add_warehouses', description: 'Add warehouses' },
    { name: 'edit_warehouses', description: 'Edit warehouses' },
    { name: 'delete_warehouses', description: 'Delete warehouses' },
    { name: 'view_accounts', description: 'View accounts' },
    { name: 'add_accounts', description: 'Add accounts' },
    { name: 'edit_accounts', description: 'Edit accounts' },
    { name: 'delete_accounts', description: 'Delete accounts' },
    { name: 'view_credit_notes', description: 'View credit notes' },
    { name: 'add_credit_notes', description: 'Add credit notes' },
    { name: 'edit_credit_notes', description: 'Edit credit notes' },
    { name: 'delete_credit_notes', description: 'Delete credit notes' },
    { name: 'view_finance', description: 'View finance' },
    { name: 'manage_finance', description: 'Manage finance' },
    { name: 'view_maintenance', description: 'View maintenance services' },
    { name: 'add_maintenance', description: 'Add maintenance services' },
    { name: 'edit_maintenance', description: 'Edit maintenance services' },
    { name: 'delete_maintenance', description: 'Delete maintenance services' },
  ];

  const permissions = [];
  for (const perm of permissionsList) {
    const permission = await prisma.permission.create({
      data: perm,
    });
    permissions.push(permission);
  }

  return permissions;
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
