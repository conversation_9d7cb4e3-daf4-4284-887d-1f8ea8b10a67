"use client";

import { AIKnowledgeExplorer } from "@/components/ai-assistant/AIKnowledgeExplorer";
import { AIAssistant } from "@/components/ai-assistant/AIAssistant";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Book, Bot, Database, Layers } from "lucide-react";

export default function KnowledgePage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">قاعدة المعرفة</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bot className="h-5 w-5 mr-2 text-blue-600" />
              المساعد الذكي
            </CardTitle>
            <CardDescription>
              استخدم المساعد الذكي للاستعلام عن المعرفة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              يمكنك استخدام المساعد الذكي للاستعلام عن معلومات النظام والوحدات والعمليات.
            </p>
            <p className="text-sm text-gray-500">
              جرب الأسئلة التالية:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
              <li>معلومات عن النظام</li>
              <li>معلومات عن وحدة المبيعات</li>
              <li>شرح عملية إنشاء فاتورة مبيعات</li>
              <li>ابحث عن الفواتير</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Layers className="h-5 w-5 mr-2 text-blue-600" />
              وحدات النظام
            </CardTitle>
            <CardDescription>
              معلومات عن وحدات النظام المختلفة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              يحتوي النظام على عدة وحدات رئيسية، كل وحدة مسؤولة عن مجموعة من الوظائف.
            </p>
            <p className="text-sm text-gray-500">
              الوحدات الرئيسية:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
              <li>المبيعات: إدارة فواتير المبيعات ومرتجعات المبيعات</li>
              <li>المشتريات: إدارة فواتير المشتريات ومرتجعات المشتريات</li>
              <li>المخزون: إدارة المنتجات والمستودعات</li>
              <li>المحاسبة: إدارة الحسابات والمعاملات المالية</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-blue-600" />
              قاعدة البيانات
            </CardTitle>
            <CardDescription>
              معلومات عن هيكل قاعدة البيانات
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              يستخدم النظام قاعدة بيانات PostgreSQL لتخزين البيانات.
            </p>
            <p className="text-sm text-gray-500">
              الجداول الرئيسية:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
              <li>المبيعات (sale): تخزين فواتير المبيعات</li>
              <li>المشتريات (purchase): تخزين فواتير المشتريات</li>
              <li>المنتجات (product): تخزين بيانات المنتجات</li>
              <li>جهات الاتصال (contact): تخزين بيانات العملاء والموردين</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <AIKnowledgeExplorer />

      {/* The AIAssistant component is already included in the layout */}
    </div>
  );
}
