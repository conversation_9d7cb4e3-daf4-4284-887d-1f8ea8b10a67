// This script adds realistic sample data to the database
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Adding realistic sample data to the database...');

    // Check if admin user exists
    const adminUser = await prisma.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    });

    if (!adminUser) {
      console.error('Admin user not found. Please run the restore-admin-user script first.');
      return;
    }

    // 1. Create branches
    console.log('Creating branches...');
    const mainBranch = await prisma.branch.findFirst({
      where: { code: 'A' }
    });

    const branches = [
      { name: 'Alexandria Branch', code: 'B', address: 'Alexandria, Egypt', phone: '01123456789' },
      { name: 'Giza Branch', code: 'C', address: 'Giza, Egypt', phone: '01223456789' },
    ];

    const createdBranches = [];
    for (const branch of branches) {
      const existingBranch = await prisma.branch.findFirst({
        where: { code: branch.code }
      });

      if (!existingBranch) {
        const newBranch = await prisma.branch.create({
          data: branch
        });
        createdBranches.push(newBranch);
        console.log(`Created branch: ${branch.name}`);
      } else {
        createdBranches.push(existingBranch);
        console.log(`Branch already exists: ${branch.name}`);
      }
    }

    // Add main branch to the array if it exists
    if (mainBranch) {
      createdBranches.unshift(mainBranch);
    }

    // 2. Create warehouses for each branch
    console.log('Creating warehouses...');
    for (const branch of createdBranches) {
      const existingWarehouse = await prisma.warehouse.findFirst({
        where: { branchId: branch.id }
      });

      if (!existingWarehouse) {
        await prisma.warehouse.create({
          data: {
            name: `${branch.name} Warehouse`,
            branchId: branch.id,
            isActive: true,
          }
        });
        console.log(`Created warehouse for ${branch.name}`);
      } else {
        console.log(`Warehouse already exists for ${branch.name}`);
      }
    }

    // 3. Create users with different roles
    console.log('Creating users...');
    const hashedPassword = await bcrypt.hash('password123', 10);

    const users = [
      { name: 'Mohamed Manager', email: '<EMAIL>', role: 'MANAGER', branchId: createdBranches[0].id },
      { name: 'Ahmed Employee', email: '<EMAIL>', role: 'EMPLOYEE', branchId: createdBranches[0].id },
      { name: 'Sara Employee', email: '<EMAIL>', role: 'EMPLOYEE', branchId: createdBranches[1].id },
      { name: 'Khaled Employee', email: '<EMAIL>', role: 'EMPLOYEE', branchId: createdBranches[2].id },
    ];

    for (const userData of users) {
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (!existingUser) {
        const user = await prisma.user.create({
          data: {
            name: userData.name,
            email: userData.email,
            password: hashedPassword,
            role: userData.role,
            branchId: userData.branchId,
            isActive: true,
          }
        });

        // Assign permissions based on role
        const permissions = await prisma.permission.findMany();
        let userPermissions = [];

        if (userData.role === 'MANAGER') {
          // Managers get all permissions except delete permissions
          userPermissions = permissions.filter(p => !p.name.startsWith('delete_'));
        } else {
          // Employees get view permissions and some add/edit permissions
          userPermissions = permissions.filter(p =>
            p.name.startsWith('view_') ||
            p.name === 'add_sales' ||
            p.name === 'edit_sales' ||
            p.name === 'add_purchases' ||
            p.name === 'edit_purchases'
          );
        }

        await prisma.user.update({
          where: { id: user.id },
          data: {
            permissions: {
              connect: userPermissions.map(p => ({ id: p.id }))
            }
          }
        });

        // Connect user to warehouse
        const warehouse = await prisma.warehouse.findFirst({
          where: { branchId: userData.branchId }
        });

        if (warehouse) {
          await prisma.userWarehouse.create({
            data: {
              userId: user.id,
              warehouseId: warehouse.id
            }
          });
        }

        console.log(`Created user: ${userData.name}`);
      } else {
        console.log(`User already exists: ${userData.email}`);
      }
    }

    // 4. Create product categories
    console.log('Creating product categories...');
    const categories = [
      { name: 'Laptops', description: 'Portable computers' },
      { name: 'Desktop Computers', description: 'Desktop workstations' },
      { name: 'Components', description: 'Computer components' },
      { name: 'Accessories', description: 'Computer accessories' },
      { name: 'Networking', description: 'Networking equipment' },
    ];

    const createdCategories = [];
    for (const category of categories) {
      const existingCategory = await prisma.category.findFirst({
        where: { name: category.name }
      });

      if (!existingCategory) {
        const newCategory = await prisma.category.create({
          data: category
        });
        createdCategories.push(newCategory);
        console.log(`Created category: ${category.name}`);
      } else {
        createdCategories.push(existingCategory);
        console.log(`Category already exists: ${category.name}`);
      }
    }

    // 5. Create components
    console.log('Creating components...');
    const components = [
      { name: 'RAM 8GB DDR4', description: '8GB DDR4 RAM module', basePrice: 800, costPrice: 600, categoryId: createdCategories[2].id, isComponent: true, componentType: 'RAM' },
      { name: 'RAM 16GB DDR4', description: '16GB DDR4 RAM module', basePrice: 1500, costPrice: 1200, categoryId: createdCategories[2].id, isComponent: true, componentType: 'RAM' },
      { name: 'SSD 256GB', description: '256GB Solid State Drive', basePrice: 1000, costPrice: 800, categoryId: createdCategories[2].id, isComponent: true, componentType: 'STORAGE' },
      { name: 'SSD 512GB', description: '512GB Solid State Drive', basePrice: 1800, costPrice: 1500, categoryId: createdCategories[2].id, isComponent: true, componentType: 'STORAGE' },
      { name: 'HDD 1TB', description: '1TB Hard Disk Drive', basePrice: 900, costPrice: 700, categoryId: createdCategories[2].id, isComponent: true, componentType: 'STORAGE' },
    ];

    const createdComponents = [];
    for (const component of components) {
      const existingComponent = await prisma.product.findFirst({
        where: { name: component.name }
      });

      if (!existingComponent) {
        const newComponent = await prisma.product.create({
          data: component
        });
        createdComponents.push(newComponent);
        console.log(`Created component: ${component.name}`);
      } else {
        createdComponents.push(existingComponent);
        console.log(`Component already exists: ${component.name}`);
      }
    }

    // 6. Create products
    console.log('Creating products...');
    const products = [
      {
        name: 'Dell Inspiron 15',
        description: 'Dell Inspiron 15 laptop with customizable specs',
        basePrice: 12000,
        costPrice: 10000,
        categoryId: createdCategories[0].id,
        isCustomizable: true
      },
      {
        name: 'HP Pavilion',
        description: 'HP Pavilion laptop with customizable specs',
        basePrice: 11000,
        costPrice: 9000,
        categoryId: createdCategories[0].id,
        isCustomizable: true
      },
      {
        name: 'Lenovo ThinkPad',
        description: 'Lenovo ThinkPad business laptop',
        basePrice: 15000,
        costPrice: 12500,
        categoryId: createdCategories[0].id,
        isCustomizable: true
      },
      {
        name: 'Custom Desktop PC',
        description: 'Custom built desktop PC with customizable specs',
        basePrice: 10000,
        costPrice: 8000,
        categoryId: createdCategories[1].id,
        isCustomizable: true
      },
      {
        name: 'Gaming Desktop PC',
        description: 'High-performance gaming desktop PC',
        basePrice: 20000,
        costPrice: 16000,
        categoryId: createdCategories[1].id,
        isCustomizable: true
      },
      {
        name: 'Wireless Mouse',
        description: 'Wireless optical mouse',
        basePrice: 300,
        costPrice: 200,
        categoryId: createdCategories[3].id,
        isCustomizable: false
      },
      {
        name: 'Mechanical Keyboard',
        description: 'Mechanical gaming keyboard',
        basePrice: 800,
        costPrice: 600,
        categoryId: createdCategories[3].id,
        isCustomizable: false
      },
      {
        name: 'Wireless Router',
        description: 'High-speed wireless router',
        basePrice: 1200,
        costPrice: 900,
        categoryId: createdCategories[4].id,
        isCustomizable: false
      },
    ];

    const createdProducts = [];
    for (const product of products) {
      const existingProduct = await prisma.product.findFirst({
        where: { name: product.name }
      });

      if (!existingProduct) {
        const newProduct = await prisma.product.create({
          data: product
        });
        createdProducts.push(newProduct);
        console.log(`Created product: ${product.name}`);
      } else {
        createdProducts.push(existingProduct);
        console.log(`Product already exists: ${product.name}`);
      }
    }

    // 7. Add specifications to customizable products
    console.log('Adding specifications to products...');
    for (const product of createdProducts) {
      if (product.isCustomizable) {
        // Add RAM specifications
        for (const component of createdComponents) {
          if (component.componentType === 'RAM' || component.componentType === 'STORAGE') {
            const existingSpec = await prisma.specification.findFirst({
              where: {
                productId: product.id,
                name: component.componentType,
                value: component.name
              }
            });

            if (!existingSpec) {
              await prisma.specification.create({
                data: {
                  productId: product.id,
                  name: component.componentType,
                  value: component.name
                }
              });
              console.log(`Added ${component.name} specification to ${product.name}`);
            } else {
              console.log(`Specification already exists for ${product.name} with ${component.name}`);
            }
          }
        }
      }
    }

    // 8. Add inventory
    console.log('Adding inventory...');
    const warehouses = await prisma.warehouse.findMany();

    for (const product of [...createdProducts, ...createdComponents]) {
      for (const warehouse of warehouses) {
        const existingInventory = await prisma.inventory.findFirst({
          where: {
            productId: product.id,
            warehouseId: warehouse.id
          }
        });

        if (!existingInventory) {
          // Random quantity between 10 and 50
          const quantity = Math.floor(Math.random() * 41) + 10;

          await prisma.inventory.create({
            data: {
              productId: product.id,
              warehouseId: warehouse.id,
              quantity
            }
          });
          console.log(`Added ${quantity} units of ${product.name} to ${warehouse.name}`);
        } else {
          console.log(`Inventory already exists for ${product.name} in ${warehouse.name}`);
        }
      }
    }

    // 9. Create contacts (customers and suppliers)
    console.log('Creating contacts...');
    const contacts = [
      { name: 'Cairo Electronics', phone: '01012345678', address: 'Cairo, Egypt', isSupplier: true, isCustomer: false, creditLimit: 50000 },
      { name: 'Tech Imports', phone: '01112345678', address: 'Alexandria, Egypt', isSupplier: true, isCustomer: false, creditLimit: 100000 },
      { name: 'Mohamed Ali', phone: '01212345678', address: 'Cairo, Egypt', isSupplier: false, isCustomer: true, creditLimit: 5000 },
      { name: 'Ahmed Hassan', phone: '01312345678', address: 'Giza, Egypt', isSupplier: false, isCustomer: true, creditLimit: 3000 },
      { name: 'Sara Mohamed', phone: '01412345678', address: 'Alexandria, Egypt', isSupplier: false, isCustomer: true, creditLimit: 10000 },
      { name: 'Computer World', phone: '01512345678', address: 'Cairo, Egypt', isSupplier: true, isCustomer: true, creditLimit: 20000 },
    ];

    for (const contact of contacts) {
      const existingContact = await prisma.contact.findFirst({
        where: { phone: contact.phone }
      });

      if (!existingContact) {
        await prisma.contact.create({
          data: contact
        });
        console.log(`Created contact: ${contact.name}`);
      } else {
        console.log(`Contact already exists: ${contact.name}`);
      }
    }

    console.log('Sample data added successfully!');
    console.log('You can now log in with the following credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Manager: <EMAIL> / password123');
    console.log('Employees: <EMAIL>, <EMAIL>, <EMAIL> / password123');

  } catch (error) {
    console.error('Error adding sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
