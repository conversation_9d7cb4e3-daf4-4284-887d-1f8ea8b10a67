import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { hasPermission } from "@/lib/permissions";

// GET /api/purchases/:id/payments - Get all payments for a purchase
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to view purchases
    const hasViewPermission = await hasPermission("view_purchases");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view purchase payments" },
        { status: 403 }
      );
    }
    
    const id = params.id;
    
    // Check if purchase exists
    const purchase = await prisma.purchase.findUnique({
      where: { id }
    });
    
    if (!purchase) {
      return NextResponse.json(
        { error: "Purchase not found" },
        { status: 404 }
      );
    }
    
    // Get payments
    const payments = await prisma.purchasePayment.findMany({
      where: {
        purchaseId: id
      },
      orderBy: {
        paymentDate: "desc"
      }
    });
    
    return NextResponse.json(payments);
  } catch (error) {
    console.error("Error fetching purchase payments:", error);
    return NextResponse.json(
      { error: "Failed to fetch purchase payments" },
      { status: 500 }
    );
  }
}

// POST /api/purchases/:id/payments - Create a new payment for a purchase
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to edit purchases
    const hasEditPermission = await hasPermission("edit_purchases");
    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add purchase payments" },
        { status: 403 }
      );
    }
    
    const id = params.id;
    const data = await request.json();
    
    // Validate required fields
    if (!data.amount || !data.paymentMethod || !data.paymentDate) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Check if purchase exists
    const purchase = await prisma.purchase.findUnique({
      where: { id },
      include: {
        payments: true
      }
    });
    
    if (!purchase) {
      return NextResponse.json(
        { error: "Purchase not found" },
        { status: 404 }
      );
    }
    
    // Calculate total paid amount
    const totalPaid = purchase.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = purchase.totalAmount - totalPaid;
    
    // Validate payment amount
    if (data.amount <= 0) {
      return NextResponse.json(
        { error: "Payment amount must be greater than zero" },
        { status: 400 }
      );
    }
    
    if (data.amount > remainingAmount) {
      return NextResponse.json(
        { error: "Payment amount cannot exceed the remaining amount" },
        { status: 400 }
      );
    }
    
    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create payment
      const payment = await tx.purchasePayment.create({
        data: {
          purchaseId: id,
          amount: data.amount,
          paymentMethod: data.paymentMethod,
          paymentDate: new Date(data.paymentDate),
          notes: data.notes
        }
      });
      
      // Calculate new total paid amount
      const newTotalPaid = totalPaid + data.amount;
      
      // Update purchase payment status
      let paymentStatus = "UNPAID";
      if (newTotalPaid >= purchase.totalAmount) {
        paymentStatus = "PAID";
      } else if (newTotalPaid > 0) {
        paymentStatus = "PARTIALLY_PAID";
      }
      
      // Update purchase
      const updatedPurchase = await tx.purchase.update({
        where: { id },
        data: {
          paymentStatus
        }
      });
      
      return { payment, updatedPurchase };
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error creating purchase payment:", error);
    return NextResponse.json(
      { error: "Failed to create purchase payment" },
      { status: 500 }
    );
  }
}
