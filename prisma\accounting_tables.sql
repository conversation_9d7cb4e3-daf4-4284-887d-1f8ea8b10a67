-- Create AccountType enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'accounttype') THEN
        CREATE TYPE "AccountType" AS ENUM ('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE');
    END IF;
END
$$;

-- Create JournalType enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'journaltype') THEN
        CREATE TYPE "JournalType" AS ENUM ('CASH', 'VODAFONE_CASH', 'BANK_TRANSFER', 'VISA', 'CUSTOMER_ACCOUNT', 'GENERAL');
    END IF;
END
$$;

-- Create Account table if it doesn't exist
CREATE TABLE IF NOT EXISTS "Account" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "AccountType" NOT NULL,
    "parentId" TEXT,
    "balance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "branchId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- Create unique index on Account code
CREATE UNIQUE INDEX IF NOT EXISTS "Account_code_key" ON "Account"("code");

-- Create indexes on Account
CREATE INDEX IF NOT EXISTS "Account_code_idx" ON "Account"("code");
CREATE INDEX IF NOT EXISTS "Account_type_idx" ON "Account"("type");
CREATE INDEX IF NOT EXISTS "Account_parentId_idx" ON "Account"("parentId");
CREATE INDEX IF NOT EXISTS "Account_isActive_idx" ON "Account"("isActive");

-- Create Journal table if it doesn't exist
CREATE TABLE IF NOT EXISTS "Journal" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "JournalType" NOT NULL,
    "branchId" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "Journal_pkey" PRIMARY KEY ("id")
);

-- Create unique index on Journal code
CREATE UNIQUE INDEX IF NOT EXISTS "Journal_code_key" ON "Journal"("code");

-- Create indexes on Journal
CREATE INDEX IF NOT EXISTS "Journal_code_idx" ON "Journal"("code");
CREATE INDEX IF NOT EXISTS "Journal_type_idx" ON "Journal"("type");
CREATE INDEX IF NOT EXISTS "Journal_isActive_idx" ON "Journal"("isActive");

-- Create FiscalYear table if it doesn't exist
CREATE TABLE IF NOT EXISTS "FiscalYear" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "isClosed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "FiscalYear_pkey" PRIMARY KEY ("id")
);

-- Create index on FiscalYear
CREATE INDEX IF NOT EXISTS "FiscalYear_isClosed_idx" ON "FiscalYear"("isClosed");

-- Create FiscalPeriod table if it doesn't exist
CREATE TABLE IF NOT EXISTS "FiscalPeriod" (
    "id" TEXT NOT NULL,
    "fiscalYearId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "isClosed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "FiscalPeriod_pkey" PRIMARY KEY ("id")
);

-- Create indexes on FiscalPeriod
CREATE INDEX IF NOT EXISTS "FiscalPeriod_fiscalYearId_idx" ON "FiscalPeriod"("fiscalYearId");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_isClosed_idx" ON "FiscalPeriod"("isClosed");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_startDate_endDate_idx" ON "FiscalPeriod"("startDate", "endDate");

-- Create JournalEntry table if it doesn't exist
CREATE TABLE IF NOT EXISTS "JournalEntry" (
    "id" TEXT NOT NULL,
    "journalId" TEXT NOT NULL,
    "entryNumber" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "description" TEXT NOT NULL,
    "debitAccountId" TEXT NOT NULL,
    "creditAccountId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "reference" TEXT,
    "referenceType" TEXT,
    "contactId" TEXT,
    "isPosted" BOOLEAN NOT NULL DEFAULT false,
    "fiscalPeriodId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "JournalEntry_pkey" PRIMARY KEY ("id")
);

-- Create unique index on JournalEntry
CREATE UNIQUE INDEX IF NOT EXISTS "JournalEntry_entryNumber_journalId_key" ON "JournalEntry"("entryNumber", "journalId");

-- Create indexes on JournalEntry
CREATE INDEX IF NOT EXISTS "JournalEntry_journalId_idx" ON "JournalEntry"("journalId");
CREATE INDEX IF NOT EXISTS "JournalEntry_date_idx" ON "JournalEntry"("date");
CREATE INDEX IF NOT EXISTS "JournalEntry_debitAccountId_idx" ON "JournalEntry"("debitAccountId");
CREATE INDEX IF NOT EXISTS "JournalEntry_creditAccountId_idx" ON "JournalEntry"("creditAccountId");
CREATE INDEX IF NOT EXISTS "JournalEntry_isPosted_idx" ON "JournalEntry"("isPosted");

-- Add foreign key constraints
ALTER TABLE "Account" ADD CONSTRAINT "Account_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Account" ADD CONSTRAINT "Account_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Journal" ADD CONSTRAINT "Journal_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "FiscalPeriod" ADD CONSTRAINT "FiscalPeriod_fiscalYearId_fkey" FOREIGN KEY ("fiscalYearId") REFERENCES "FiscalYear"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_journalId_fkey" FOREIGN KEY ("journalId") REFERENCES "Journal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_debitAccountId_fkey" FOREIGN KEY ("debitAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_creditAccountId_fkey" FOREIGN KEY ("creditAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_fiscalPeriodId_fkey" FOREIGN KEY ("fiscalPeriodId") REFERENCES "FiscalPeriod"("id") ON DELETE SET NULL ON UPDATE CASCADE;
