"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { SignatureCanvas } from "@/components/shared/SignatureCanvas";
import { Trash2 } from "lucide-react";

interface SignatureSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

export default function SignatureSettings({ settings, onUpdate }: SignatureSettingsProps) {
  const [showSignatures, setShowSignatures] = useState(true);
  const [managerSignature, setManagerSignature] = useState<string | null>(null);
  const [accountantSignature, setAccountantSignature] = useState<string | null>(null);
  const [customerSignature, setCustomerSignature] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("manager");
  
  // Load settings
  useEffect(() => {
    if (settings?.signatureSettings) {
      const signatureSettings = settings.signatureSettings;
      setShowSignatures(signatureSettings.showSignatures !== false);
      setManagerSignature(signatureSettings.managerSignature || null);
      setAccountantSignature(signatureSettings.accountantSignature || null);
      setCustomerSignature(signatureSettings.customerSignature || null);
    }
  }, [settings]);
  
  // Handle save
  const handleSave = () => {
    const signatureSettings = {
      showSignatures,
      managerSignature,
      accountantSignature,
      customerSignature
    };
    
    onUpdate({ signatureSettings });
  };
  
  // Handle signature save
  const handleSignatureSave = (type: string, signature: string) => {
    switch (type) {
      case "manager":
        setManagerSignature(signature);
        break;
      case "accountant":
        setAccountantSignature(signature);
        break;
      case "customer":
        setCustomerSignature(signature);
        break;
    }
  };
  
  // Handle signature clear
  const handleSignatureClear = (type: string) => {
    switch (type) {
      case "manager":
        setManagerSignature(null);
        break;
      case "accountant":
        setAccountantSignature(null);
        break;
      case "customer":
        setCustomerSignature(null);
        break;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Signature Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Label htmlFor="show-signatures" className="font-medium">
              Show Signatures on Documents
            </Label>
            <Switch
              id="show-signatures"
              checked={showSignatures}
              onCheckedChange={setShowSignatures}
            />
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="manager">Manager</TabsTrigger>
              <TabsTrigger value="accountant">Accountant</TabsTrigger>
              <TabsTrigger value="customer">Customer</TabsTrigger>
            </TabsList>
            
            <TabsContent value="manager">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Manager Signature</h3>
                  {managerSignature && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSignatureClear("manager")}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear
                    </Button>
                  )}
                </div>
                
                {managerSignature ? (
                  <div className="border rounded-md p-4">
                    <img
                      src={managerSignature}
                      alt="Manager Signature"
                      className="max-w-full h-auto"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => setManagerSignature(null)}
                    >
                      Change Signature
                    </Button>
                  </div>
                ) : (
                  <SignatureCanvas
                    width={400}
                    height={200}
                    onSave={(signature) => handleSignatureSave("manager", signature)}
                    title="Draw Manager Signature"
                  />
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="accountant">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Accountant Signature</h3>
                  {accountantSignature && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSignatureClear("accountant")}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear
                    </Button>
                  )}
                </div>
                
                {accountantSignature ? (
                  <div className="border rounded-md p-4">
                    <img
                      src={accountantSignature}
                      alt="Accountant Signature"
                      className="max-w-full h-auto"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => setAccountantSignature(null)}
                    >
                      Change Signature
                    </Button>
                  </div>
                ) : (
                  <SignatureCanvas
                    width={400}
                    height={200}
                    onSave={(signature) => handleSignatureSave("accountant", signature)}
                    title="Draw Accountant Signature"
                  />
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="customer">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Customer Signature Example</h3>
                  {customerSignature && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSignatureClear("customer")}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear
                    </Button>
                  )}
                </div>
                
                <p className="text-sm text-gray-500">
                  This signature will be used as a placeholder for customer signatures on documents.
                  Actual customer signatures should be collected at the time of transaction.
                </p>
                
                {customerSignature ? (
                  <div className="border rounded-md p-4">
                    <img
                      src={customerSignature}
                      alt="Customer Signature"
                      className="max-w-full h-auto"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => setCustomerSignature(null)}
                    >
                      Change Signature
                    </Button>
                  </div>
                ) : (
                  <SignatureCanvas
                    width={400}
                    height={200}
                    onSave={(signature) => handleSignatureSave("customer", signature)}
                    title="Draw Customer Signature Example"
                  />
                )}
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="mt-6 flex justify-end">
            <Button onClick={handleSave}>Save Settings</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
