import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import bcrypt from "bcryptjs";

// Function to create admin user if it doesn't exist
async function ensureAdminUser() {
  try {
    // Check if admin user exists
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    });

    if (adminUser) {
      console.log("Admin user already exists");
      return;
    }

    console.log("Admin user not found. Creating a new admin user...");

    // Find a branch or create one
    let branch = await db.branch.findFirst();

    if (!branch) {
      console.log("No branch found. Creating a new branch...");
      branch = await db.branch.create({
        data: {
          name: "Main Branch",
          code: "A",
          address: "Cairo, Egypt",
          phone: "01000000000",
        },
      });
      console.log(`Created new branch: ${branch.name}`);
    }

    // Create default permissions
    const defaultPermissions = [
      { name: "view_users", description: "View users" },
      { name: "add_users", description: "Add new users" },
      { name: "edit_users", description: "Edit existing users" },
      { name: "delete_users", description: "Delete users" },
      { name: "view_branches", description: "View branches" },
      { name: "add_branches", description: "Add new branches" },
      { name: "edit_branches", description: "Edit existing branches" },
      { name: "delete_branches", description: "Delete branches" },
      { name: "view_warehouses", description: "View warehouses" },
      { name: "add_warehouses", description: "Add new warehouses" },
      { name: "edit_warehouses", description: "Edit existing warehouses" },
      { name: "delete_warehouses", description: "Delete warehouses" },
      { name: "view_products", description: "View products" },
      { name: "add_products", description: "Add new products" },
      { name: "edit_products", description: "Edit existing products" },
      { name: "delete_products", description: "Delete products" },
      { name: "view_inventory", description: "View inventory" },
      { name: "add_inventory", description: "Add inventory" },
      { name: "edit_inventory", description: "Edit inventory" },
      { name: "delete_inventory", description: "Delete inventory" },
      { name: "view_sales", description: "View sales" },
      { name: "add_sales", description: "Add new sales" },
      { name: "edit_sales", description: "Edit existing sales" },
      { name: "delete_sales", description: "Delete sales" },
      { name: "view_purchases", description: "View purchases" },
      { name: "add_purchases", description: "Add new purchases" },
      { name: "edit_purchases", description: "Edit existing purchases" },
      { name: "delete_purchases", description: "Delete purchases" },
      { name: "view_contacts", description: "View contacts" },
      { name: "add_contacts", description: "Add new contacts" },
      { name: "edit_contacts", description: "Edit existing contacts" },
      { name: "delete_contacts", description: "Delete contacts" },
      { name: "view_reports", description: "View reports" },
      { name: "view_settings", description: "View settings" },
      { name: "edit_settings", description: "Edit settings" },
    ];

    // Create permissions
    const createdPermissions = [];
    for (const permission of defaultPermissions) {
      // Check if permission already exists
      const existingPermission = await db.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        const newPermission = await db.permission.create({
          data: permission,
        });
        createdPermissions.push(newPermission);
        console.log(`Created permission: ${permission.name}`);
      } else {
        createdPermissions.push(existingPermission);
        console.log(`Permission already exists: ${permission.name}`);
      }
    }

    // Create a new admin user
    const hashedPassword = await bcrypt.hash("admin123", 10);
    const newAdmin = await db.user.create({
      data: {
        name: "Admin User",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "ADMIN",
        branchId: branch.id,
        isActive: true,
        permissions: {
          connect: createdPermissions.map(p => ({ id: p.id })),
        },
      },
    });

    // Create a warehouse if none exists
    const warehouse = await db.warehouse.findFirst({
      where: {
        branchId: branch.id,
      },
    });

    if (!warehouse) {
      const newWarehouse = await db.warehouse.create({
        data: {
          name: "Main Warehouse",
          branchId: branch.id,
          isActive: true,
        },
      });

      // Connect admin to warehouse
      await db.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: newWarehouse.id,
        },
      });
    } else {
      // Connect admin to existing warehouse
      await db.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: warehouse.id,
        },
      });
    }

    console.log("Admin user created successfully");
    console.log("Email: <EMAIL>");
    console.log("Password: admin123");
  } catch (error) {
    console.error("Error creating admin user:", error);
    throw error;
  }
}

// POST /api/system/clean-database - Clean database
export async function POST(req: NextRequest) {
  try {
    console.log("Starting database cleaning process...");

    // Clean database (delete all data but keep tables)
    const tables = await db.$queryRaw`
      SELECT tablename FROM pg_tables
      WHERE schemaname = 'public'
      AND tablename != '_prisma_migrations'
    `;

    console.log("Tables to clean:", tables);

    // Disable foreign key checks
    await db.$executeRawUnsafe(`SET session_replication_role = 'replica';`);

    // Delete data from all tables
    for (const { tablename } of tables as { tablename: string }[]) {
      console.log(`Truncating table: ${tablename}`);
      await db.$executeRawUnsafe(`TRUNCATE TABLE "${tablename}" CASCADE;`);
    }

    // Re-enable foreign key checks
    await db.$executeRawUnsafe(`SET session_replication_role = 'origin';`);

    // Ensure admin user exists
    console.log("Recreating admin user...");
    await ensureAdminUser();

    console.log("Database cleaning completed successfully");
    return NextResponse.json({ 
      success: true,
      message: "Database cleaned successfully. Admin user has been preserved/recreated." 
    });
  } catch (error) {
    console.error("Error cleaning database:", error);
    return NextResponse.json(
      { 
        success: false,
        error: "An error occurred while cleaning the database",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
