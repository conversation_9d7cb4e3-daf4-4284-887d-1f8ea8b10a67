import { db } from "./db";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines multiple class names into a single string
 * This is used for conditional class names in components
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Generates a sequential invoice number for a branch
 * Format: [Branch Code]-[Sequential Number]
 * Example: A-1, A-2, B-1, etc.
 *
 * This function uses a transaction to ensure that the generated number
 * is always the next in sequence, even with concurrent users.
 */
export async function generateInvoiceNumber(branchId: string): Promise<string> {
  // Use a transaction to ensure consistency when multiple users generate numbers simultaneously
  return await db.$transaction(async (tx) => {
    // First, get the branch code from the branch ID
    const branch = await tx.branch.findUnique({
      where: {
        id: branchId,
      },
      select: {
        code: true,
      },
    });

    if (!branch) {
      throw new Error(`Branch with ID ${branchId} not found`);
    }

    const branchCode = branch.code;

    // Get the latest invoice for this branch with a lock to prevent race conditions
    const latestSale = await tx.sale.findFirst({
      where: {
        branchId: branchId,
      },
      orderBy: {
        invoiceNumber: "desc",
      },
      select: {
        invoiceNumber: true,
      },
    });

    let nextNumber = 1;

    if (latestSale) {
      // Extract the number part from the invoice number
      const parts = latestSale.invoiceNumber.split("-");
      if (parts.length >= 2) {
        const currentNumber = parseInt(parts[1]);
        if (!isNaN(currentNumber)) {
          nextNumber = currentNumber + 1;
        }
      }
    }

    // Generate the new invoice number (without timestamp)
    // We don't need a timestamp anymore since we're using a transaction
    return `${branchCode}-${nextNumber}`;
  });
}

/**
 * Generates a sequential credit note number for a branch
 * Format: CN-[Branch Code]-[Sequential Number]
 * Example: CN-A-1, CN-A-2, CN-B-1, etc.
 *
 * This function uses a transaction to ensure that the generated number
 * is always the next in sequence, even with concurrent users.
 */
export async function generateCreditNoteNumber(branchId: string): Promise<string> {
  // Use a transaction to ensure consistency when multiple users generate numbers simultaneously
  return await db.$transaction(async (tx) => {
    // First, get the branch code from the branch ID
    const branch = await tx.branch.findUnique({
      where: {
        id: branchId,
      },
      select: {
        code: true,
      },
    });

    if (!branch) {
      throw new Error(`Branch with ID ${branchId} not found`);
    }

    const branchCode = branch.code;

    // Get the latest credit note for this branch with a lock to prevent race conditions
    const latestCreditNote = await tx.creditNote.findFirst({
      where: {
        branchId: branchId,
      },
      orderBy: {
        creditNoteNumber: "desc",
      },
      select: {
        creditNoteNumber: true,
      },
    });

    let nextNumber = 1;

    if (latestCreditNote) {
      // Extract the number part from the credit note number
      const parts = latestCreditNote.creditNoteNumber.split("-");
      if (parts.length >= 3) {
        const currentNumber = parseInt(parts[2]);
        if (!isNaN(currentNumber)) {
          nextNumber = currentNumber + 1;
        }
      }
    }

    // Generate the new credit note number (without timestamp)
    // We don't need a timestamp anymore since we're using a transaction
    return `CN-${branchCode}-${nextNumber}`;
  });
}

/**
 * Format a number as currency
 *
 * This function formats a number as currency, using the Egyptian Pound symbol (ج.م) by default.
 * It ensures consistent currency formatting throughout the application.
 *
 * @param amount - The amount to format
 * @param currency - The currency code (default: "EGP")
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = "EGP"): string {
  // Handle null or undefined amount
  if (amount === null || amount === undefined) {
    amount = 0;
  }

  // Use EGP for Egyptian Pound
  if (currency === "EGP") {
    return `EGP ${amount.toFixed(2)}`;
  }

  // For other currencies, use the provided currency code
  return `${amount.toFixed(2)} ${currency}`;
}

/**
 * Calculate tax amount based on subtotal and tax rate
 */
export function calculateTaxAmount(subtotal: number, taxRate: number): number {
  return subtotal * (taxRate / 100);
}

/**
 * Calculate total amount based on subtotal, tax amount, and discount
 */
export function calculateTotalAmount(
  subtotal: number,
  taxAmount: number,
  discount: number
): number {
  return subtotal + taxAmount - discount;
}

/**
 * Format a date as a string
 *
 * This function formats a date as a string in the format "DD/MM/YYYY".
 * It ensures consistent date formatting throughout the application.
 *
 * @param date - The date to format (Date object or ISO string)
 * @returns Formatted date string
 */
export function formatDate(date: Date | string): string {
  // Handle null or undefined date
  if (!date) {
    return '';
  }

  // Convert string to Date object if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) {
    return '';
  }

  // Format the date as DD/MM/YYYY
  const day = dateObj.getDate().toString().padStart(2, '0');
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const year = dateObj.getFullYear();

  return `${day}/${month}/${year}`;
}
