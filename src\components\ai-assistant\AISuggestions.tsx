"use client";

import { useState, useEffect } from "react";
import { <PERSON>bulb, X, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";

interface Suggestion {
  id: string;
  title: string;
  description: string;
  actionUrl?: string;
  actionLabel?: string;
}

export function AISuggestions() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [activeSuggestion, setActiveSuggestion] = useState<Suggestion | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch suggestions when the path changes
  useEffect(() => {
    if (session?.user) {
      fetchSuggestions();
    }
  }, [session, pathname]);

  const fetchSuggestions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/ai-assistant/suggestions?path=${encodeURIComponent(pathname || '')}`);
      
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
        
        // If we have suggestions, show the first one
        if (data.suggestions && data.suggestions.length > 0) {
          setActiveSuggestion(data.suggestions[0]);
          setIsVisible(true);
        } else {
          setActiveSuggestion(null);
          setIsVisible(false);
        }
      }
    } catch (error) {
      console.error("Error fetching suggestions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const dismissSuggestion = () => {
    setIsVisible(false);
    
    // After a delay, show the next suggestion if available
    setTimeout(() => {
      const currentIndex = suggestions.findIndex(s => s.id === activeSuggestion?.id);
      if (currentIndex >= 0 && currentIndex < suggestions.length - 1) {
        setActiveSuggestion(suggestions[currentIndex + 1]);
        setIsVisible(true);
      }
    }, 500);
  };

  // If no active suggestion or not visible, don't render anything
  if (!activeSuggestion || !isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-24 right-6 z-40 max-w-sm">
      <Card className="bg-white shadow-lg border-2 border-blue-100 rounded-lg overflow-hidden">
        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-500 to-teal-500 text-white">
          <div className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            <h3 className="font-medium">اقتراح</h3>
          </div>
          <button
            onClick={dismissSuggestion}
            className="text-white hover:text-gray-200"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        
        <div className="p-4">
          <h4 className="font-medium text-gray-900 mb-2">{activeSuggestion.title}</h4>
          <p className="text-sm text-gray-600 mb-4">{activeSuggestion.description}</p>
          
          {activeSuggestion.actionUrl && (
            <a
              href={activeSuggestion.actionUrl}
              className="flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              {activeSuggestion.actionLabel || "المزيد"}
              <ChevronRight className="h-4 w-4 ml-1" />
            </a>
          )}
        </div>
      </Card>
    </div>
  );
}
