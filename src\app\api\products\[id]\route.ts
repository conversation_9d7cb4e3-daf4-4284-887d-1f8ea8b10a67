import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/products/[id] - Get a specific product
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view products
    const hasViewPermission = await hasPermission("view_products") || session.user.role === "ADMIN";

    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view product details" },
        { status: 403 }
      );
    }

    // Log user information
    console.log("User accessing product details:", {
      email: session.user.email,
      role: session.user.role,
      productId: params.id
    });

    const { id } = params;

    // Get product from database
    const product = await db.product.findUnique({
      where: {
        id,
      },
      include: {
        category: true,
        specifications: true,
        inventory: {
          include: {
            warehouse: true,
          },
        },
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Transform the data to include warehouse names
    const transformedProduct = {
      ...product,
      inventory: product.inventory.map(inv => ({
        warehouseId: inv.warehouseId,
        warehouseName: inv.warehouse.name,
        quantity: inv.quantity,
      })),
    };

    return NextResponse.json(transformedProduct);
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { error: "Failed to fetch product" },
      { status: 500 }
    );
  }
}

// PUT /api/products/[id] - Update a product
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit products
    const hasEditPermission = await hasPermission("edit_products") || session.user.role === "ADMIN";

    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to edit products" },
        { status: 403 }
      );
    }

    // Log user information
    console.log("User editing product:", {
      email: session.user.email,
      role: session.user.role,
      productId: params.id
    });

    const { id } = params;
    const data = await req.json();

    // Only validate required fields if we're doing a full update
    // For partial updates (like toggling isCustomizable), we don't need all fields
    if (Object.keys(data).length > 3 && (!data.name || !data.categoryId || data.basePrice === undefined)) {
      return NextResponse.json(
        { error: "Missing required fields for full update" },
        { status: 400 }
      );
    }

    // Check if product exists
    const existingProduct = await db.product.findUnique({
      where: {
        id,
      },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    // Only update fields that are provided in the request
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.basePrice !== undefined) updateData.basePrice = data.basePrice;
    if (data.costPrice !== undefined) updateData.costPrice = data.costPrice;
    if (data.categoryId !== undefined) updateData.categoryId = data.categoryId;
    if (data.isCustomizable !== undefined) updateData.isCustomizable = data.isCustomizable;
    if (data.isComponent !== undefined) updateData.isComponent = data.isComponent;
    if (data.componentType !== undefined) updateData.componentType = data.componentType;

    console.log("Updating product with data:", updateData);

    // Update the product
    const product = await db.product.update({
      where: {
        id,
      },
      data: updateData,
    });

    // Update specifications if provided
    if (data.specifications) {
      // Delete existing specifications
      await db.specification.deleteMany({
        where: {
          productId: id,
        },
      });

      // Create new specifications
      for (const spec of data.specifications) {
        await db.specification.create({
          data: {
            name: spec.name,
            value: spec.value,
            productId: id,
          },
        });
      }
    }

    // Update inventory if provided
    if (data.inventory) {
      // Process each inventory item
      for (const item of data.inventory) {
        // Check if inventory item already exists
        const existingInventory = await db.inventory.findFirst({
          where: {
            productId: id,
            warehouseId: item.warehouseId,
          },
        });

        if (existingInventory) {
          // Update existing inventory
          await db.inventory.update({
            where: {
              id: existingInventory.id,
            },
            data: {
              quantity: item.quantity,
            },
          });
        } else {
          // Create new inventory item
          await db.inventory.create({
            data: {
              productId: id,
              warehouseId: item.warehouseId,
              quantity: item.quantity,
            },
          });
        }
      }

      // Get all warehouse IDs from the provided inventory
      const providedWarehouseIds = data.inventory.map(item => item.warehouseId);

      // Delete inventory items that are not in the provided inventory
      await db.inventory.deleteMany({
        where: {
          productId: id,
          warehouseId: {
            notIn: providedWarehouseIds,
          },
        },
      });
    }

    // Get the updated product with specifications and inventory
    const updatedProduct = await db.product.findUnique({
      where: {
        id,
      },
      include: {
        category: true,
        specifications: true,
        inventory: {
          include: {
            warehouse: true,
          },
        },
      },
    });

    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update product" },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id] - Delete a product
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to delete products
    const hasDeletePermission = await hasPermission("delete_products") || session.user.role === "ADMIN";

    if (!hasDeletePermission) {
      return NextResponse.json(
        { error: "You don't have permission to delete products" },
        { status: 403 }
      );
    }

    // Log user information
    console.log("User deleting product:", {
      email: session.user.email,
      role: session.user.role,
      productId: params.id
    });

    const { id } = params;

    // Check if product exists
    const existingProduct = await db.product.findUnique({
      where: {
        id,
      },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if product is used in sales
    const salesItems = await db.saleItem.findFirst({
      where: {
        productId: id,
      },
    });

    if (salesItems) {
      return NextResponse.json(
        { error: "Cannot delete product because it is used in sales. Please remove the product from all sales first." },
        { status: 400 }
      );
    }

    // Check if product is used in purchases
    const purchaseItems = await db.purchaseItem.findFirst({
      where: {
        productId: id,
      },
    });

    if (purchaseItems) {
      return NextResponse.json(
        { error: "Cannot delete product because it is used in purchases. Please remove the product from all purchases first." },
        { status: 400 }
      );
    }

    // Check if product is used in credit notes
    const creditNoteItems = await db.creditNoteItem.findFirst({
      where: {
        productId: id,
      },
    });

    if (creditNoteItems) {
      return NextResponse.json(
        { error: "Cannot delete product because it is used in credit notes. Please remove the product from all credit notes first." },
        { status: 400 }
      );
    }

    // Check if product is used in transfers
    const transferItems = await db.inventoryTransferItem.findFirst({
      where: {
        productId: id,
      },
    });

    if (transferItems) {
      return NextResponse.json(
        { error: "Cannot delete product because it is used in inventory transfers. Please remove the product from all transfers first." },
        { status: 400 }
      );
    }

    // Delete specifications
    await db.specification.deleteMany({
      where: {
        productId: id,
      },
    });

    // Delete inventory
    await db.inventory.deleteMany({
      where: {
        productId: id,
      },
    });

    // Delete product
    await db.product.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to delete product" },
      { status: 500 }
    );
  }
}
