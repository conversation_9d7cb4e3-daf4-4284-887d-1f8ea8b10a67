"use client";

import { <PERSON>, Sun } from "lucide-react";
import { useApp } from "@/contexts/AppContext";

export function ThemeToggle() {
  const { isDarkMode, setIsDarkMode, language } = useApp();

  const labels = {
    ar: {
      light: 'الوضع الفاتح',
      dark: 'الوضع المظلم',
      theme: 'المظهر'
    },
    en: {
      light: 'Light Mode',
      dark: 'Dark Mode',
      theme: 'Theme'
    }
  };

  const currentLabels = labels[language];

  return (
    <button
      onClick={() => setIsDarkMode(!isDarkMode)}
      className="flex items-center justify-center h-8 w-8 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
      title={isDarkMode ? currentLabels.light : currentLabels.dark}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">{currentLabels.theme}</span>
    </button>
  );
}
