"use client";

import { <PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useApp } from "@/contexts/AppContext";

export function ThemeToggle() {
  const { isDarkMode, setIsDarkMode, language } = useApp();

  const labels = {
    ar: {
      light: 'الوضع الفاتح',
      dark: 'الوضع المظلم',
      theme: 'المظهر'
    },
    en: {
      light: 'Light Mode',
      dark: 'Dark Mode',
      theme: 'Theme'
    }
  };

  const currentLabels = labels[language];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 px-0">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">{currentLabels.theme}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem 
          onClick={() => setIsDarkMode(false)}
          className={`cursor-pointer ${!isDarkMode ? 'bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-300' : ''}`}
        >
          <Sun className="mr-2 h-4 w-4" />
          <span>{currentLabels.light}</span>
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setIsDarkMode(true)}
          className={`cursor-pointer ${isDarkMode ? 'bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-300' : ''}`}
        >
          <Moon className="mr-2 h-4 w-4" />
          <span>{currentLabels.dark}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
