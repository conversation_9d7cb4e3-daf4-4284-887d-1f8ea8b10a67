import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// POST /api/seed/journals - Seed default journals
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add journals
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add journals" },
        { status: 403 }
      );
    }

    // Define default journals
    const defaultJournals = [
      {
        name: "Cash Journal",
        description: "Journal for cash transactions",
        paymentMethod: "CASH",
      },
      {
        name: "Vodafone Cash Journal",
        description: "Journal for Vodafone Cash transactions",
        paymentMethod: "VODAFONE_CASH",
      },
      {
        name: "Bank Transfer Journal",
        description: "Journal for bank transfer transactions",
        paymentMethod: "BANK_TRANSFER",
      },
      {
        name: "Credit Card Journal",
        description: "Journal for credit card transactions",
        paymentMethod: "CREDIT_CARD",
      },
    ];

    // Get existing journals
    const existingJournals = await db.journal.findMany();

    // Filter out payment methods that already have journals
    const existingPaymentMethods = existingJournals.map(journal => journal.paymentMethod);
    const journalsToCreate = defaultJournals.filter(
      journal => !existingPaymentMethods.includes(journal.paymentMethod)
    );

    if (journalsToCreate.length === 0) {
      return NextResponse.json(
        { message: "All default journals already exist", count: existingJournals.length },
        { status: 200 }
      );
    }

    // Create missing journals
    const createdJournals = await Promise.all(
      journalsToCreate.map(async (journal) => {
        return await db.journal.create({
          data: {
            name: journal.name,
            description: journal.description,
            paymentMethod: journal.paymentMethod,
            isActive: true,
          },
        });
      })
    );

    return NextResponse.json(
      {
        message: "Missing journals created successfully",
        created: createdJournals,
        existing: existingJournals.length,
        total: existingJournals.length + createdJournals.length
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error seeding journals:", error);
    return NextResponse.json(
      { error: "Failed to seed journals" },
      { status: 500 }
    );
  }
}
