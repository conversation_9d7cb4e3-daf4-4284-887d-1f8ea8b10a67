"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Modal from "../../settings/components/Modal";
import ContactForm from "../components/ContactForm";
import { use } from "react";

interface Contact {
  id: string;
  name: string;
  phone: string;
  address?: string;
  isCustomer: boolean;
  isSupplier: boolean;
  isActive: boolean;
  balance: number;
  openingBalance: number;
  openingBalanceDate: string;
  createdAt: string;
  updatedAt: string;
}

interface Transaction {
  id: string;
  amount: number;
  type: string;
  description: string;
  date: string;
  reference?: string;
}

export default function ContactDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id } = use(params);

  const [contact, setContact] = useState<Contact | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [calculatedBalance, setCalculatedBalance] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch contact details
  useEffect(() => {
    const fetchContactDetails = async () => {
      setIsLoading(true);
      try {
        // Fetch contact
        const contactResponse = await fetch(`/api/contacts/${id}`);

        if (!contactResponse.ok) {
          const errorData = await contactResponse.json();
          throw new Error(errorData.error || "Failed to fetch contact");
        }

        const contactData = await contactResponse.json();
        setContact(contactData);

        // Fetch statement data to get the calculated balance
        try {
          const statementResponse = await fetch(`/api/contacts/${id}/statement`);

          if (statementResponse.ok) {
            const statementData = await statementResponse.json();
            // Set the calculated balance from the statement
            setCalculatedBalance(statementData.totals.balance);
          }
        } catch (statementError) {
          console.error("Error fetching statement:", statementError);
          // If we can't get the statement, we'll use the contact's balance
        }

        // TODO: Fetch transactions for this contact
        // This will be implemented when we create the transactions API
        setTransactions([]);

      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContactDetails();
  }, [id]);

  // Open edit modal
  const openEditModal = () => {
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Handle successful form submission
  const handleSuccess = () => {
    closeModal();
    // Refresh contact data
    router.refresh();
    // Reload the page to get updated data
    window.location.reload();
  };

  // Update contact balance to match calculated balance
  const [isUpdatingBalance, setIsUpdatingBalance] = useState(false);
  const updateContactBalance = async () => {
    if (calculatedBalance === null || !contact) return;

    setIsUpdatingBalance(true);
    try {
      const response = await fetch(`/api/contacts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...contact,
          balance: calculatedBalance,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update contact balance');
      }

      // Reload the page to get updated data
      window.location.reload();
    } catch (error) {
      console.error('Error updating contact balance:', error);
      alert(error instanceof Error ? error.message : 'An error occurred while updating the balance');
    } finally {
      setIsUpdatingBalance(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-500">Loading contact details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 p-4 rounded-md text-red-700 mb-4">
          {error}
        </div>
        <button
          onClick={() => router.push("/dashboard/contacts")}
          className="text-indigo-600 hover:text-indigo-900"
        >
          Back to Contacts
        </button>
      </div>
    );
  }

  if (!contact) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-500">Contact not found</p>
          <button
            onClick={() => router.push("/dashboard/contacts")}
            className="mt-4 text-indigo-600 hover:text-indigo-900"
          >
            Back to Contacts
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Contact Details</h1>
        <div className="space-x-2">
          <button
            onClick={() => router.push("/dashboard/contacts")}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium"
          >
            Back
          </button>
          <button
            onClick={() => router.push(`/dashboard/contacts/${id}/statement`)}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium"
          >
            View Statement
          </button>
          <button
            onClick={openEditModal}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium"
          >
            Edit Contact
          </button>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-xl font-bold mb-4 text-gray-900">Contact Information</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-600">Name</p>
                  <p className="font-semibold text-gray-900">{contact.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Phone</p>
                  <p className="font-semibold text-gray-900">{contact.phone}</p>
                </div>

                {contact.address && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Address</p>
                    <p className="font-semibold text-gray-900">{contact.address}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-gray-600">Type</p>
                  <div className="flex space-x-2 mt-1">
                    {contact.isCustomer && (
                      <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        Customer
                      </span>
                    )}
                    {contact.isSupplier && (
                      <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        Supplier
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Status</p>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    contact.isActive
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}>
                    {contact.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-xl font-bold mb-4 text-gray-900">Financial Information</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Balance</p>
                  <p className={`font-semibold text-lg ${
                    calculatedBalance !== null
                      ? (calculatedBalance > 0 ? "text-red-700" : calculatedBalance < 0 ? "text-green-700" : "text-gray-700")
                      : (contact.balance > 0 ? "text-red-700" : contact.balance < 0 ? "text-green-700" : "text-gray-700")
                  }`}>
                    {calculatedBalance !== null ? calculatedBalance.toFixed(2) : contact.balance.toFixed(2)} EGP
                  </p>
                  {calculatedBalance !== null && calculatedBalance !== contact.balance && (
                    <div className="mt-1 text-xs text-amber-600">
                      <span className="font-medium">Note:</span> Balance calculated from transactions.
                      <div className="mt-2 flex space-x-2">
                        <button
                          onClick={() => router.push(`/dashboard/contacts/${id}/statement`)}
                          className="px-2 py-1 text-xs border border-amber-300 rounded text-amber-700 hover:bg-amber-50"
                        >
                          View statement
                        </button>
                        <button
                          onClick={updateContactBalance}
                          disabled={isUpdatingBalance}
                          className="px-2 py-1 text-xs bg-amber-100 border border-amber-300 rounded text-amber-700 hover:bg-amber-200 disabled:opacity-50 flex items-center"
                        >
                          {isUpdatingBalance ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-amber-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Updating...
                            </>
                          ) : (
                            'Update balance in database'
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-600">Created At</p>
                  <p className="font-semibold text-gray-900">
                    {new Date(contact.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Last Updated</p>
                  <p className="font-semibold text-gray-900">
                    {new Date(contact.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b">
          <h2 className="text-xl font-bold text-gray-900">Transaction History</h2>
        </div>

        {transactions.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reference
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr key={transaction.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(transaction.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {transaction.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.reference || "-"}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                      transaction.type === "DEBIT" ? "text-red-600" : "text-green-600"
                    }`}>
                      {transaction.type === "DEBIT" ? "-" : "+"}{transaction.amount.toFixed(2)} EGP
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-6 text-center text-gray-500">
            No transactions found for this contact.
          </div>
        )}
      </div>

      {isModalOpen && (
        <Modal isOpen={isModalOpen} onClose={closeModal} size="md">
          <ContactForm
            contact={contact}
            onClose={closeModal}
            onSuccess={handleSuccess}
          />
        </Modal>
      )}
    </div>
  );
}
