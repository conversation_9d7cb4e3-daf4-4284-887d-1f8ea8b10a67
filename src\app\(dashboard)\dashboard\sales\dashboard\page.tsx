"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from "recharts";
import {
  Download,
  Filter,
  Calendar,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  Building,
  FileText,
  Printer
} from "lucide-react";
import { format, subDays, subMonths, startOfMonth, endOfMonth } from "date-fns";
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";

// Define chart colors
const COLORS = ['#3895e7', '#307aa8', '#4bc0c0', '#ffcd56', '#ff9f40', '#ff6384'];

export default function SalesDashboardPage() {
  // State for date range and filters
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedBranch, setSelectedBranch] = useState<string>("all");
  const [timeFrame, setTimeFrame] = useState<string>("month");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // State for dashboard data
  const [dashboardData, setDashboardData] = useState<any>({
    summary: {
      totalSales: 0,
      totalAmount: 0,
      averageOrderValue: 0,
      totalDiscount: 0,
      totalTax: 0,
      byStatus: {},
      byPaymentStatus: {}
    },
    salesByDate: [],
    topProducts: [],
    paymentMethods: [],
    topCustomers: []
  });

  // State for branches
  const [branches, setBranches] = useState<any[]>([]);

  // Fetch branches
  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (response.ok) {
          const data = await response.json();
          setBranches(data);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
      }
    };

    fetchBranches();
  }, []);

  // Update date range based on time frame
  useEffect(() => {
    const now = new Date();

    switch (timeFrame) {
      case "week":
        setStartDate(subDays(now, 7));
        setEndDate(now);
        break;
      case "month":
        setStartDate(subDays(now, 30));
        setEndDate(now);
        break;
      case "quarter":
        setStartDate(subDays(now, 90));
        setEndDate(now);
        break;
      case "year":
        setStartDate(subDays(now, 365));
        setEndDate(now);
        break;
      case "custom":
        // Don't change dates for custom timeframe
        break;
      default:
        setStartDate(subDays(now, 30));
        setEndDate(now);
    }
  }, [timeFrame]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);

      try {
        const formattedStartDate = format(startDate, 'yyyy-MM-dd');
        const formattedEndDate = format(endDate, 'yyyy-MM-dd');

        const response = await fetch(
          `/api/sales/dashboard?startDate=${formattedStartDate}&endDate=${formattedEndDate}&branchId=${selectedBranch}&timeFrame=${timeFrame}`
        );

        if (response.ok) {
          const data = await response.json();
          setDashboardData(data);
        } else {
          console.error('Error fetching dashboard data:', await response.text());
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [startDate, endDate, selectedBranch, timeFrame]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Export dashboard data to Excel
  const exportToExcel = () => {
    try {
      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Create summary worksheet
      const summaryData = [
        ['Sales Dashboard Summary'],
        [`Period: ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`],
        [''],
        ['Metric', 'Value'],
        ['Total Sales', dashboardData.summary.totalSales],
        ['Total Amount', dashboardData.summary.totalAmount],
        ['Average Order Value', dashboardData.summary.averageOrderValue],
        ['Total Discount', dashboardData.summary.totalDiscount],
        ['Total Tax', dashboardData.summary.totalTax]
      ];

      const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summaryWs, 'Summary');

      // Create top products worksheet
      const topProductsData = [
        ['Top Selling Products'],
        ['Product', 'Quantity Sold', 'Total Amount']
      ];

      dashboardData.topProducts.forEach((product: any) => {
        topProductsData.push([
          product.productName,
          product.totalQuantity,
          product.totalAmount
        ]);
      });

      const topProductsWs = XLSX.utils.aoa_to_sheet(topProductsData);
      XLSX.utils.book_append_sheet(workbook, topProductsWs, 'Top Products');

      // Create sales by date worksheet
      const salesByDateData = [
        ['Sales by Date'],
        ['Date', 'Total Sales', 'Total Amount']
      ];

      dashboardData.salesByDate.forEach((item: any) => {
        salesByDateData.push([
          item.date,
          item.totalSales,
          item.totalAmount
        ]);
      });

      const salesByDateWs = XLSX.utils.aoa_to_sheet(salesByDateData);
      XLSX.utils.book_append_sheet(workbook, salesByDateWs, 'Sales by Date');

      // Create payment methods worksheet
      const paymentMethodsData = [
        ['Payment Methods'],
        ['Method', 'Count', 'Amount', 'Percentage']
      ];

      dashboardData.paymentMethods.forEach((method: any) => {
        paymentMethodsData.push([
          method.method,
          method.count,
          method.amount,
          method.percentage
        ]);
      });

      const paymentMethodsWs = XLSX.utils.aoa_to_sheet(paymentMethodsData);
      XLSX.utils.book_append_sheet(workbook, paymentMethodsWs, 'Payment Methods');

      // Create top customers worksheet
      const topCustomersData = [
        ['Top Customers'],
        ['Customer', 'Order Count', 'Total Spent']
      ];

      dashboardData.topCustomers.forEach((customer: any) => {
        topCustomersData.push([
          customer.name,
          customer.orderCount,
          customer.totalSpent
        ]);
      });

      const topCustomersWs = XLSX.utils.aoa_to_sheet(topCustomersData);
      XLSX.utils.book_append_sheet(workbook, topCustomersWs, 'Top Customers');

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      // Save file
      saveAs(blob, `sales_dashboard_${format(new Date(), 'yyyy-MM-dd')}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
    }
  };

  // Print dashboard
  const printDashboard = () => {
    window.print();
  };

  return (
    <div className="space-y-6 print:space-y-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 print:hidden">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Sales Dashboard</h2>
          <p className="text-muted-foreground">
            Analyze your sales performance and trends
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={exportToExcel}>
            <Download className="mr-2 h-4 w-4" />
            Export to Excel
          </Button>
          <Button variant="outline" onClick={printDashboard}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="print:hidden">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Time Frame</label>
              <Tabs defaultValue={timeFrame} onValueChange={setTimeFrame} className="w-full">
                <TabsList className="grid grid-cols-5 w-full">
                  <TabsTrigger value="week">Week</TabsTrigger>
                  <TabsTrigger value="month">Month</TabsTrigger>
                  <TabsTrigger value="quarter">Quarter</TabsTrigger>
                  <TabsTrigger value="year">Year</TabsTrigger>
                  <TabsTrigger value="custom">Custom</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {timeFrame === "custom" && (
              <>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Start Date</label>
                  <DatePicker date={startDate} setDate={setStartDate} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">End Date</label>
                  <DatePicker date={endDate} setDate={setEndDate} />
                </div>
              </>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium">Branch</label>
              <Select value={selectedBranch} onValueChange={setSelectedBranch}>
                <SelectTrigger>
                  <SelectValue placeholder="Select branch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Branches</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-3 text-lg text-gray-700">Loading dashboard data...</span>
        </div>
      ) : (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Sales</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.summary.totalSales}</h3>
                    <p className="text-sm text-gray-500 mt-1">Orders</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <ShoppingCart className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(dashboardData.summary.totalAmount)}</h3>
                    <p className="text-sm text-gray-500 mt-1">Sales amount</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Average Order Value</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(dashboardData.summary.averageOrderValue)}</h3>
                    <p className="text-sm text-gray-500 mt-1">Per order</p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-full">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Customers</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.topCustomers.length}</h3>
                    <p className="text-sm text-gray-500 mt-1">Unique customers</p>
                  </div>
                  <div className="p-3 bg-yellow-100 rounded-full">
                    <Users className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Sales Trend</CardTitle>
                <CardDescription>
                  Sales over time for the selected period
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={dashboardData.salesByDate}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" orientation="left" stroke="#3895e7" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip formatter={(value, name) => [
                      name === 'totalAmount' ? formatCurrency(value as number) : value,
                      name === 'totalAmount' ? 'Revenue' : 'Orders'
                    ]} />
                    <Legend />
                    <Area
                      yAxisId="right"
                      type="monotone"
                      dataKey="totalSales"
                      name="Orders"
                      stroke="#3895e7"
                      fill="#3895e7"
                      fillOpacity={0.3}
                    />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="totalAmount"
                      name="Revenue"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>
                  Distribution of sales by payment method
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={dashboardData.paymentMethods}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                      nameKey="method"
                      label={({ method, percent }) => `${method}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {dashboardData.paymentMethods.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [formatCurrency(value as number), 'Amount']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Products and Customers */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Selling Products</CardTitle>
                <CardDescription>
                  Products with the highest sales volume
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.topProducts.slice(0, 5).map((product: any, index: number) => (
                    <div key={product.productId} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-800 font-bold mr-3">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{product.productName}</p>
                          <p className="text-sm text-gray-500">{product.totalQuantity} units sold</p>
                        </div>
                      </div>
                      <p className="font-bold">{formatCurrency(product.totalAmount)}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Customers</CardTitle>
                <CardDescription>
                  Customers with the highest purchase value
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.topCustomers.slice(0, 5).map((customer: any, index: number) => (
                    <div key={customer.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-800 font-bold mr-3">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{customer.name}</p>
                          <p className="text-sm text-gray-500">{customer.orderCount} orders</p>
                        </div>
                      </div>
                      <p className="font-bold">{formatCurrency(customer.totalSpent)}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
