"use client";

import { useState } from "react";
import * as XLSX from "xlsx";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Download, Upload, FileSpreadsheet, AlertCircle, CheckCircle2, FileDown } from "lucide-react";
import { format } from "date-fns";

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
  balance: number;
}

interface OpeningBalanceImportProps {
  accounts: Account[];
  onImportComplete: () => void;
}

export default function OpeningBalanceImport({ accounts, onImportComplete }: OpeningBalanceImportProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importValidation, setImportValidation] = useState<{
    isValid: boolean;
    message: string;
    data?: any[];
  } | null>(null);

  // Generate template for opening balances
  const generateTemplate = async () => {
    try {
      setIsExporting(true);

      // Prepare data for template
      const templateData = accounts.map(account => ({
        Code: account.code,
        Name: account.name,
        Type: account.type,
        CurrentBalance: account.balance,
        OpeningBalance: 0, // This is what the user will fill in
      }));

      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(templateData);
      
      // Add column widths
      const columnWidths = [
        { wch: 10 }, // Code
        { wch: 30 }, // Name
        { wch: 15 }, // Type
        { wch: 15 }, // CurrentBalance
        { wch: 15 }, // OpeningBalance
      ];
      worksheet["!cols"] = columnWidths;

      // Create workbook
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Opening Balances");

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
      const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      
      // Save file
      const fileName = `opening_balances_template_${format(new Date(), "yyyy-MM-dd")}.xlsx`;
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      toast.success("Opening balance template generated successfully");
    } catch (error) {
      console.error("Error generating template:", error);
      toast.error("Failed to generate template");
    } finally {
      setIsExporting(false);
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const data = new Uint8Array(event.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        
        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        // Validate data
        validateImportData(jsonData);
      } catch (error) {
        console.error("Error reading Excel file:", error);
        setImportValidation({
          isValid: false,
          message: "Invalid Excel file format"
        });
      }
    };
    reader.readAsArrayBuffer(file);
  };

  // Validate import data
  const validateImportData = (data: any[]) => {
    if (!data || data.length === 0) {
      setImportValidation({
        isValid: false,
        message: "No data found in the Excel file"
      });
      return;
    }

    // Check required fields
    const requiredFields = ["Code", "OpeningBalance"];
    const missingFields = requiredFields.filter(field => 
      !data.every(item => item[field] !== undefined)
    );

    if (missingFields.length > 0) {
      setImportValidation({
        isValid: false,
        message: `Missing required fields: ${missingFields.join(", ")}`
      });
      return;
    }

    // Check if all account codes exist
    const accountCodes = accounts.map(a => a.code);
    const invalidCodes = data
      .filter(item => !accountCodes.includes(item.Code))
      .map(item => item.Code);
    
    if (invalidCodes.length > 0) {
      setImportValidation({
        isValid: false,
        message: `Invalid account codes: ${invalidCodes.join(", ")}`
      });
      return;
    }

    // Check if opening balances are valid numbers
    const invalidBalances = data
      .filter(item => isNaN(parseFloat(item.OpeningBalance)))
      .map(item => item.Code);
    
    if (invalidBalances.length > 0) {
      setImportValidation({
        isValid: false,
        message: `Invalid opening balances for accounts: ${invalidBalances.join(", ")}`
      });
      return;
    }

    // Calculate total debits and credits
    const totalDebits = data
      .filter(item => parseFloat(item.OpeningBalance) > 0)
      .reduce((sum, item) => sum + parseFloat(item.OpeningBalance), 0);
    
    const totalCredits = data
      .filter(item => parseFloat(item.OpeningBalance) < 0)
      .reduce((sum, item) => sum + Math.abs(parseFloat(item.OpeningBalance)), 0);
    
    // Check if debits equal credits
    const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;
    
    if (!isBalanced) {
      setImportValidation({
        isValid: false,
        message: `Opening balances are not balanced. Total debits: ${totalDebits.toFixed(2)}, Total credits: ${totalCredits.toFixed(2)}`
      });
      return;
    }

    // Valid data
    setImportValidation({
      isValid: true,
      message: `${data.length} opening balances ready to import. Total amount: ${totalDebits.toFixed(2)}`,
      data
    });
  };

  // Import opening balances
  const importOpeningBalances = async () => {
    if (!importValidation?.isValid || !importValidation.data) {
      return;
    }

    try {
      setIsImporting(true);

      // Transform data for API
      const importData = importValidation.data.map(item => ({
        accountCode: item.Code,
        openingBalance: parseFloat(item.OpeningBalance),
      }));

      // Call API to import opening balances
      const response = await fetch("/api/accounting/journals/opening-balance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ entries: importData }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to import opening balances");
      }

      toast.success("Opening balances imported successfully");
      setImportValidation(null);
      onImportComplete();
    } catch (error: any) {
      console.error("Error importing opening balances:", error);
      toast.error(error.message || "Failed to import opening balances");
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center">
          <FileSpreadsheet className="h-5 w-5 mr-2 text-orange-600" />
          Import Opening Balances / استيراد الأرصدة الافتتاحية
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Template Section */}
          <div className="space-y-4">
            <h3 className="font-medium">Download Template / تنزيل النموذج</h3>
            <p className="text-sm text-gray-500">
              Download an Excel template with all accounts. Fill in the opening balances and upload it back.
            </p>
            <Button 
              onClick={generateTemplate} 
              disabled={isExporting || accounts.length === 0}
              className="w-full"
              variant="outline"
            >
              {isExporting ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Generating...
                </>
              ) : (
                <>
                  <FileDown className="h-4 w-4 mr-2" />
                  Download Template
                </>
              )}
            </Button>
          </div>

          {/* Import Section */}
          <div className="space-y-4">
            <h3 className="font-medium">Import Opening Balances / استيراد الأرصدة الافتتاحية</h3>
            <p className="text-sm text-gray-500">
              Import opening balances from the Excel template. Make sure debits equal credits.
            </p>
            <div className="flex flex-col space-y-2">
              <Input 
                type="file" 
                accept=".xlsx, .xls" 
                onChange={handleFileUpload}
                disabled={isImporting}
                className="w-full"
              />
              
              {importValidation && (
                <div className={`mt-2 p-3 rounded-md text-sm ${
                  importValidation.isValid ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                }`}>
                  <div className="flex items-start">
                    {importValidation.isValid ? (
                      <CheckCircle2 className="h-5 w-5 mr-2 flex-shrink-0" />
                    ) : (
                      <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                    )}
                    <div>
                      <p className="font-medium">{importValidation.message}</p>
                      {importValidation.isValid && (
                        <Button 
                          onClick={importOpeningBalances} 
                          disabled={isImporting}
                          className="mt-2"
                          size="sm"
                        >
                          {isImporting ? (
                            <>
                              <span className="animate-spin mr-2">⏳</span>
                              Importing...
                            </>
                          ) : (
                            <>
                              <Upload className="h-4 w-4 mr-2" />
                              Import Opening Balances
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
