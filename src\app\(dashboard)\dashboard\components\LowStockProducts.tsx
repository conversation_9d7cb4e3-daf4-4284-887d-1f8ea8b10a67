"use client";

import { motion } from "framer-motion";
import Link from "next/link";

interface Product {
  product: {
    id: string;
    name: string;
    basePrice: number;
  };
  warehouse: {
    id: string;
    name: string;
  };
  quantity: number;
}

interface LowStockProductsProps {
  products: Product[];
}

export default function LowStockProducts({ products }: LowStockProductsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.3 }}
      className="bg-white shadow-lg rounded-lg overflow-hidden"
    >
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          Low Stock Products
        </h3>
        {products.length > 0 && (
          <span className="bg-red-100 text-red-800 text-xs font-semibold px-2.5 py-0.5 rounded">
            {products.length}
          </span>
        )}
      </div>

      {products.length > 0 ? (
        <ul className="divide-y divide-gray-200">
          {products.map((item, index) => (
            <motion.li
              key={`${item.product.id}-${item.warehouse.id}`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2, delay: 0.1 * index }}
              className="px-6 py-4 hover:bg-gray-50"
            >
              <Link href={`/dashboard/inventory?product=${item.product.id}`} className="block">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {item.product.name}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Warehouse: {item.warehouse.name}
                    </p>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className="text-sm font-semibold text-gray-900">
                      {item.product.basePrice.toFixed(2)} EGP
                    </span>
                    <span className={`mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      item.quantity <= 0 
                        ? 'bg-red-100 text-red-800' 
                        : item.quantity <= 2 
                          ? 'bg-orange-100 text-orange-800' 
                          : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {item.quantity} in stock
                    </span>
                  </div>
                </div>
              </Link>
            </motion.li>
          ))}
        </ul>
      ) : (
        <div className="px-6 py-8 text-center">
          <p className="text-sm text-gray-500">No low stock products</p>
        </div>
      )}

      <div className="bg-gray-50 px-6 py-3">
        <div className="text-sm">
          <Link
            href="/dashboard/inventory"
            className="font-medium text-indigo-600 hover:text-indigo-500 hover:underline flex items-center justify-between"
          >
            <span>View all inventory</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
