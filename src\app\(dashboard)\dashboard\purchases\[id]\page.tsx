'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { Arrow<PERSON><PERSON><PERSON>, Edit, Printer, Trash } from 'lucide-react';
import Link from 'next/link';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

// Add print styles
const printStyles = `
  @media print {
    @page {
      size: A4;
      margin: 1cm;
    }

    body {
      font-size: 12pt;
      color: black;
      background-color: white;
      margin: 0;
      padding: 0;
    }

    .no-print {
      display: none !important;
    }

    .print-container {
      width: 100%;
      margin: 0;
      padding: 0;
    }

    .print-header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #000;
      width: 100%;
    }

    .print-title {
      font-size: 18pt;
      font-weight: bold;
      color: black;
      margin: 0;
      padding: 0;
    }

    .print-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      break-inside: avoid;
      margin-bottom: 8px;
      width: 100%;
    }

    .print-label {
      width: 150px;
      font-weight: 600;
      color: black;
      white-space: nowrap;
    }

    .print-value {
      color: black;
      flex: 1;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    th, td {
      border: 1px solid #000;
      padding: 8px;
      text-align: left;
      color: black;
    }

    th {
      font-weight: bold;
      background-color: #f2f2f2;
    }

    h2 {
      color: black;
      font-size: 14pt;
      margin-top: 15px;
      margin-bottom: 10px;
    }

    .print-section {
      margin-bottom: 15px;
      page-break-inside: avoid;
    }

    .print-summary {
      margin-top: 15px;
      border-top: 1px solid #000;
      padding-top: 10px;
      page-break-inside: avoid;
    }
  }
`;

interface PurchaseItem {
  id: string;
  productId: string;
  product: {
    id: string;
    name: string;
    sku: string;
  };
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

interface Purchase {
  id: string;
  invoiceNumber: string;
  date: string;
  dueDate: string | null;
  status: string;
  paymentMethod: string;
  paymentStatus: string;
  subtotalAmount: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  notes: string | null;
  currency: string;
  reminderSent: boolean;
  lastReminderDate: string | null;
  contact: {
    id: string;
    name: string;
    phone: string;
    email: string | null;
    address: string | null;
  };
  branch: {
    id: string;
    name: string;
  };
  items: PurchaseItem[];
  payments: PurchasePayment[];
  createdAt: string;
  updatedAt: string;
}

interface PurchasePayment {
  id: string;
  amount: number;
  paymentMethod: string;
  paymentDate: string;
  notes: string | null;
  createdAt: string;
}

export default function PurchaseDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [purchase, setPurchase] = useState<Purchase | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPurchase = async () => {
      try {
        const response = await fetch(`/api/purchases/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch purchase');
        }
        const data = await response.json();
        setPurchase(data);
      } catch (err) {
        setError('Error loading purchase details');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchPurchase();
  }, [params.id]);

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this purchase?')) {
      return;
    }

    try {
      const response = await fetch(`/api/purchases/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete purchase');
      }

      router.push('/dashboard/purchases');
    } catch (err) {
      console.error('Error deleting purchase:', err);
      alert('Failed to delete purchase');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-700">Loading purchase details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !purchase) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          <p>{error || 'Purchase not found'}</p>
          <Link href="/dashboard/purchases" className="text-red-600 underline mt-2 inline-block">
            Return to purchases
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 print-container">
      <style dangerouslySetInnerHTML={{ __html: printStyles }} />

      <div className="flex justify-between items-center mb-6 no-print">
        <div className="flex items-center">
          <Link href="/dashboard/purchases" className="mr-4">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Link>
          <h1 className="text-2xl font-bold text-black">Purchase #{purchase.invoiceNumber}</h1>
        </div>
        <div className="flex space-x-2">
          <Link
            href={`/dashboard/purchases/${purchase.id}/print`}
            className="flex items-center px-3 py-2 bg-gray-100 rounded-md text-gray-700 hover:bg-gray-200"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Link>
          {purchase.paymentStatus !== 'PAID' && (
            <Link
              href={`/dashboard/purchases/due-invoices`}
              className="flex items-center px-3 py-2 bg-yellow-600 rounded-md text-white hover:bg-yellow-700"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Due Invoices
            </Link>
          )}
          <Link
            href={`/dashboard/purchases/${purchase.id}/edit`}
            className="flex items-center px-3 py-2 bg-blue-600 rounded-md text-white hover:bg-blue-700"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Link>
          <button
            onClick={handleDelete}
            className="flex items-center px-3 py-2 bg-red-600 rounded-md text-white hover:bg-red-700"
          >
            <Trash className="h-4 w-4 mr-2" />
            Delete
          </button>
        </div>
      </div>

      {/* Print-only title */}
      <div className="hidden print:block print-header">
        <h1 className="print-title">Purchase #{purchase.invoiceNumber}</h1>
        <p className="text-black mt-2">Date: {format(new Date(purchase.date), 'PPP')}</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="p-6">
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div className="print-section">
              <h2 className="text-xl font-bold mb-4 text-black">Purchase Information</h2>
              <div className="space-y-2">
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Invoice Number:</span>
                  <span className="font-medium text-black print-value">{purchase.invoiceNumber}</span>
                </div>
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Date:</span>
                  <span className="text-black print-value">{format(new Date(purchase.date), 'PPP')}</span>
                </div>
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Status:</span>
                  <span className={`px-2 py-1 rounded-full text-sm font-bold ${
                    purchase.status === 'COMPLETED' ? 'bg-green-200 text-green-900' : 'bg-yellow-200 text-yellow-900'
                  } print-value`}>
                    {purchase.status}
                  </span>
                </div>
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Payment:</span>
                  <span className={`px-2 py-1 rounded-full text-sm font-bold ${
                    purchase.paymentStatus === 'PAID' ? 'bg-green-200 text-green-900' :
                    purchase.paymentStatus === 'PARTIALLY_PAID' ? 'bg-yellow-200 text-yellow-900' :
                    'bg-red-200 text-red-900'
                  } print-value`}>
                    {purchase.paymentStatus}
                  </span>
                </div>
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Payment Method:</span>
                  <span className="text-black print-value">{purchase.paymentMethod.replace(/_/g, ' ')}</span>
                </div>
                {purchase.dueDate && (
                  <div className="flex print-row">
                    <span className="w-32 text-gray-700 font-medium print-label">Due Date:</span>
                    <span className={`text-black print-value ${
                      new Date(purchase.dueDate) < new Date() && purchase.paymentStatus !== 'PAID'
                        ? 'text-red-600 font-bold'
                        : ''
                    }`}>
                      {format(new Date(purchase.dueDate), 'PPP')}
                      {new Date(purchase.dueDate) < new Date() && purchase.paymentStatus !== 'PAID' && (
                        <span className="ml-2 text-red-600 text-sm">(Overdue)</span>
                      )}
                    </span>
                  </div>
                )}
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Branch:</span>
                  <span className="text-black print-value">{purchase.branch.name}</span>
                </div>
              </div>
            </div>
            <div className="print-section">
              <h2 className="text-xl font-bold mb-4 text-black">Supplier Information</h2>
              <div className="space-y-2">
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Name:</span>
                  <span className="font-medium text-black print-value">{purchase.contact.name}</span>
                </div>
                <div className="flex print-row">
                  <span className="w-32 text-gray-700 font-medium print-label">Phone:</span>
                  <span className="text-black print-value">{purchase.contact.phone}</span>
                </div>
                {purchase.contact.email && (
                  <div className="flex print-row">
                    <span className="w-32 text-gray-700 font-medium print-label">Email:</span>
                    <span className="text-black print-value">{purchase.contact.email}</span>
                  </div>
                )}
                {purchase.contact.address && (
                  <div className="flex print-row">
                    <span className="w-32 text-gray-700 font-medium print-label">Address:</span>
                    <span className="text-black print-value">{purchase.contact.address}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Items */}
      <div className="no-print">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="print-section">
              <h2 className="text-xl font-bold mb-4 text-black">Items</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Product</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Unit Price</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Quantity</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Total</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {purchase.items.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-black">{item.product.name}</div>
                          {item.product.sku && (
                            <div className="text-sm text-gray-700">{item.product.sku}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-black">
                          {item.unitPrice.toFixed(2)} {purchase.currency}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-black">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-black">
                          {item.totalPrice.toFixed(2)} {purchase.currency}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="print-summary">
              <div className="flex justify-end">
                <div className="w-64">
                  <div className="flex justify-between py-2 print-row">
                    <span className="text-gray-700 font-medium print-label">Subtotal:</span>
                    <span className="text-black print-value">{purchase.subtotalAmount.toFixed(2)} {purchase.currency}</span>
                  </div>
                  {purchase.discountAmount > 0 && (
                    <div className="flex justify-between py-2 print-row">
                      <span className="text-gray-700 font-medium print-label">Discount:</span>
                      <span className="text-black print-value">-{purchase.discountAmount.toFixed(2)} {purchase.currency}</span>
                    </div>
                  )}
                  {purchase.taxAmount > 0 && (
                    <div className="flex justify-between py-2 print-row">
                      <span className="text-gray-700 font-medium print-label">Tax:</span>
                      <span className="text-black print-value">{purchase.taxAmount.toFixed(2)} {purchase.currency}</span>
                    </div>
                  )}
                  <div className="flex justify-between py-2 font-bold border-t border-gray-200 mt-2 pt-2 print-row">
                    <span className="text-black print-label">Total:</span>
                    <span className="text-black print-value">{purchase.totalAmount.toFixed(2)} {purchase.currency}</span>
                  </div>
                </div>
              </div>
            </div>

            {purchase.notes && (
              <div className="print-section mt-6">
                <h2 className="text-xl font-bold mb-2 text-black">Notes</h2>
                <p className="text-black print-value">{purchase.notes}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Print-only content */}
      <div className="hidden print:block">
        <div className="print-section">
          <h2 className="text-xl font-bold mb-4 text-black">Items</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Unit Price</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Quantity</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Total</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {purchase.items.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-black">{item.product.name}</div>
                      {item.product.sku && (
                        <div className="text-sm text-gray-700">{item.product.sku}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-black">
                      {item.unitPrice.toFixed(2)} {purchase.currency}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-black">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-black">
                      {item.totalPrice.toFixed(2)} {purchase.currency}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="print-summary">
          <div className="flex justify-end">
            <div className="w-64">
              <div className="flex justify-between py-2 print-row">
                <span className="text-gray-700 font-medium print-label">Subtotal:</span>
                <span className="text-black print-value">{purchase.subtotalAmount.toFixed(2)} ج.م</span>
              </div>
              {purchase.discountAmount > 0 && (
                <div className="flex justify-between py-2 print-row">
                  <span className="text-gray-700 font-medium print-label">Discount:</span>
                  <span className="text-black print-value">-{purchase.discountAmount.toFixed(2)} ج.م</span>
                </div>
              )}
              {purchase.taxAmount > 0 && (
                <div className="flex justify-between py-2 print-row">
                  <span className="text-gray-700 font-medium print-label">Tax:</span>
                  <span className="text-black print-value">{purchase.taxAmount.toFixed(2)} {purchase.currency}</span>
                </div>
              )}
              <div className="flex justify-between py-2 font-bold border-t border-gray-200 mt-2 pt-2 print-row">
                <span className="text-black print-label">Total:</span>
                <span className="text-black print-value">{purchase.totalAmount.toFixed(2)} {purchase.currency}</span>
              </div>
            </div>
          </div>
        </div>

        {purchase.notes && (
          <div className="print-section">
            <h2 className="text-xl font-bold mb-2 text-black">Notes</h2>
            <p className="text-black print-value">{purchase.notes}</p>
          </div>
        )}
      </div>
    </div>
  );
}
