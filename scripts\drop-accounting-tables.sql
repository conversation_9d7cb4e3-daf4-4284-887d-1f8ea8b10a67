-- <PERSON><PERSON><PERSON> to completely remove accounting module tables
-- This script removes all tables related to the accounting module

-- Temporarily disable foreign key constraints
SET session_replication_role = 'replica';

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS "ReconciliationItem" CASCADE;
DROP TABLE IF EXISTS "AccountReconciliation" CASCADE;
DROP TABLE IF EXISTS "FinancialReport" CASCADE;
DROP TABLE IF EXISTS "GeneralLedgerEntry" CASCADE;
DROP TABLE IF EXISTS "JournalEntry" CASCADE;
DROP TABLE IF EXISTS "Journal" CASCADE;
DROP TABLE IF EXISTS "Transaction" CASCADE;
DROP TABLE IF EXISTS "FiscalPeriod" CASCADE;
DROP TABLE IF EXISTS "FiscalYear" CASCADE;
DROP TABLE IF EXISTS "AccountingSettings" CASCADE;
DROP TABLE IF EXISTS "Account" CASCADE;

-- Restore foreign key constraints
SET session_replication_role = 'origin';

-- Confirmation message
SELECT 'All accounting tables have been successfully removed.' AS "Message";
