"use client";

import { motion } from "framer-motion";
import Link from "next/link";

interface Customer {
  id: string;
  name: string;
  phone: string;
  balance: number;
  creditLimit: number;
}

interface OverdueCustomersProps {
  customers: Customer[];
}

export default function OverdueCustomers({ customers }: OverdueCustomersProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
      className="bg-white shadow-lg rounded-lg overflow-hidden"
    >
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-yellow-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          Customers with Outstanding Balance
        </h3>
        {customers.length > 0 && (
          <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-2.5 py-0.5 rounded">
            {customers.length}
          </span>
        )}
      </div>

      {customers.length > 0 ? (
        <ul className="divide-y divide-gray-200">
          {customers.map((customer, index) => {
            // Calculate percentage of credit limit used
            const creditUsagePercent = customer.creditLimit > 0 
              ? Math.min(100, Math.round((customer.balance / customer.creditLimit) * 100))
              : 100;
            
            // Determine color based on usage
            const getColorClass = (percent: number) => {
              if (percent >= 90) return "bg-red-500";
              if (percent >= 75) return "bg-orange-500";
              if (percent >= 50) return "bg-yellow-500";
              return "bg-green-500";
            };
            
            return (
              <motion.li
                key={customer.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: 0.1 * index }}
                className="px-6 py-4 hover:bg-gray-50"
              >
                <Link href={`/dashboard/contacts/${customer.id}`} className="block">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {customer.name}
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        {customer.phone}
                      </p>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className="text-sm font-semibold text-gray-900">
                        {customer.balance.toFixed(2)} EGP
                      </span>
                      {customer.creditLimit > 0 && (
                        <span className="text-xs text-gray-500 mt-1">
                          Credit Limit: {customer.creditLimit.toFixed(2)} EGP
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Credit usage progress bar */}
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`${getColorClass(creditUsagePercent)} h-2 rounded-full`} 
                        style={{ width: `${creditUsagePercent}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="text-xs text-gray-500">
                        {creditUsagePercent}% used
                      </span>
                      {creditUsagePercent >= 90 && (
                        <span className="text-xs font-medium text-red-600">
                          Credit limit exceeded
                        </span>
                      )}
                    </div>
                  </div>
                </Link>
              </motion.li>
            );
          })}
        </ul>
      ) : (
        <div className="px-6 py-8 text-center">
          <p className="text-sm text-gray-500">No customers with outstanding balance</p>
        </div>
      )}

      <div className="bg-gray-50 px-6 py-3">
        <div className="text-sm">
          <Link
            href="/dashboard/contacts?type=customer"
            className="font-medium text-indigo-600 hover:text-indigo-500 hover:underline flex items-center justify-between"
          >
            <span>View all customers</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
