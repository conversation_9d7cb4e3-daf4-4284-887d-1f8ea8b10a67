"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, FileText, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { format } from "date-fns";
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";

interface PaymentVoucher {
  id: string;
  voucherNumber: string;
  date: string;
  amount: number;
  description: string;
  paymentMethodId: string;
  contactId: string | null;
  status: string;
  reference: string | null;
  referenceType: string | null;
  createdAt: string;
  paymentMethod: {
    id: string;
    name: string;
    code: string;
    account: {
      id: string;
      code: string;
      name: string;
      balance: number;
    } | null;
  };
  contact: {
    id: string;
    name: string;
    phone: string;
    isCustomer: boolean;
    isSupplier: boolean;
  } | null;
  user: {
    id: string;
    name: string;
  };
  branch: {
    id: string;
    name: string;
    code: string;
  };
  journalEntry: {
    id: string;
    entryNumber: string;
    date: string;
    description: string;
    amount: number;
    journal: {
      id: string;
      code: string;
      name: string;
    };
    debitAccount: {
      id: string;
      code: string;
      name: string;
      type: string;
    };
    creditAccount: {
      id: string;
      code: string;
      name: string;
      type: string;
    };
  } | null;
}

export default function PaymentVoucherDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { id } = params;

  const [voucher, setVoucher] = useState<PaymentVoucher | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCancelling, setIsCancelling] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  // Fetch payment voucher details
  useEffect(() => {
    const fetchVoucherDetails = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/accounting/payment-vouchers/${id}`);
        if (response.ok) {
          const data = await response.json();
          setVoucher(data.data);
        } else {
          toast.error("Failed to load payment voucher details");
          router.push("/dashboard/accounting/payment-vouchers");
        }
      } catch (error) {
        console.error("Error fetching payment voucher:", error);
        toast.error("Failed to load payment voucher details");
        router.push("/dashboard/accounting/payment-vouchers");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchVoucherDetails();
    }
  }, [id, router]);

  // Cancel payment voucher
  const cancelVoucher = async () => {
    setIsCancelling(true);
    try {
      const response = await fetch(`/api/accounting/payment-vouchers/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Payment voucher cancelled successfully");
        // Refresh voucher data
        const updatedResponse = await fetch(`/api/accounting/payment-vouchers/${id}`);
        if (updatedResponse.ok) {
          const data = await updatedResponse.json();
          setVoucher(data.data);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to cancel payment voucher");
      }
    } catch (error) {
      console.error("Error cancelling payment voucher:", error);
      toast.error("Failed to cancel payment voucher");
    } finally {
      setIsCancelling(false);
      setShowCancelDialog(false);
    }
  };

  // Print voucher
  const printVoucher = () => {
    // Implement printing functionality
    toast.info("Printing functionality will be implemented soon");
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500 text-lg">Loading payment voucher details...</span>
      </div>
    );
  }

  if (!voucher) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <Link href="/dashboard/accounting/payment-vouchers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Payment Vouchers
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <AlertTriangle className="h-16 w-16 text-yellow-500 mb-4" />
            <h2 className="text-xl font-bold mb-2">Payment Voucher Not Found</h2>
            <p className="text-gray-500 mb-4">The payment voucher you are looking for does not exist or has been deleted.</p>
            <Link href="/dashboard/accounting/payment-vouchers">
              <Button>
                Go to Payment Vouchers
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/dashboard/accounting/payment-vouchers">
            <Button variant="outline" size="icon" className="mr-4">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Payment Voucher / إذن صرف</h1>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={printVoucher}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          {voucher.status !== "CANCELLED" && (
            <Button variant="destructive" onClick={() => setShowCancelDialog(true)}>
              Cancel Voucher
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Voucher Details / تفاصيل الإذن</CardTitle>
            <CardDescription>Payment voucher information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Voucher Number / رقم الإذن</h3>
                  <p className="text-lg font-semibold">{voucher.voucherNumber}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Date / التاريخ</h3>
                  <p className="text-lg font-semibold">{format(new Date(voucher.date), 'MMM d, yyyy')}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Amount / المبلغ</h3>
                  <p className="text-lg font-semibold">{formatCurrency(voucher.amount)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Status / الحالة</h3>
                  <p className={`text-lg font-semibold ${voucher.status === "CANCELLED" ? "text-red-500" : "text-green-500"}`}>
                    {voucher.status === "CANCELLED" ? "Cancelled / ملغي" : "Completed / مكتمل"}
                  </p>
                </div>
                <div className="col-span-2">
                  <h3 className="text-sm font-medium text-gray-500">Description / الوصف</h3>
                  <p className="text-lg">{voucher.description}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Payment Method / طريقة الدفع</h3>
                  <p className="text-lg font-semibold">{voucher.paymentMethod.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Branch / الفرع</h3>
                  <p className="text-lg font-semibold">{voucher.branch.name}</p>
                </div>
                {voucher.contact && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium text-gray-500">Contact / جهة الاتصال</h3>
                    <p className="text-lg font-semibold">
                      {voucher.contact.name} ({voucher.contact.phone})
                      {voucher.contact.isCustomer && <span className="text-sm text-blue-500 ml-2">Customer</span>}
                      {voucher.contact.isSupplier && <span className="text-sm text-green-500 ml-2">Supplier</span>}
                    </p>
                  </div>
                )}
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Created By / بواسطة</h3>
                  <p className="text-lg font-semibold">{voucher.user.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Created At / تاريخ الإنشاء</h3>
                  <p className="text-lg font-semibold">{format(new Date(voucher.createdAt), 'MMM d, yyyy h:mm a')}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Account Information / معلومات الحساب</CardTitle>
            <CardDescription>Payment method account details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Account / الحساب</h3>
                <p className="text-lg font-semibold">
                  {voucher.paymentMethod.account ? (
                    <>
                      {voucher.paymentMethod.account.name} ({voucher.paymentMethod.account.code})
                    </>
                  ) : (
                    <span className="text-red-500">No account linked</span>
                  )}
                </p>
              </div>
              {voucher.paymentMethod.account && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Account Balance / رصيد الحساب</h3>
                  <p className="text-lg font-semibold">{formatCurrency(voucher.paymentMethod.account.balance)}</p>
                </div>
              )}
              {voucher.journalEntry && (
                <>
                  <div className="pt-4 border-t">
                    <h3 className="text-sm font-medium text-gray-500">Journal Entry / قيد اليومية</h3>
                    <p className="text-lg font-semibold">{voucher.journalEntry.entryNumber}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Journal / اليومية</h3>
                    <p className="text-lg font-semibold">{voucher.journalEntry.journal.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Debit Account / حساب المدين</h3>
                    <p className="text-lg font-semibold">{voucher.journalEntry.debitAccount.name} ({voucher.journalEntry.debitAccount.code})</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Credit Account / حساب الدائن</h3>
                    <p className="text-lg font-semibold">{voucher.journalEntry.creditAccount.name} ({voucher.journalEntry.creditAccount.code})</p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-center border-t pt-4">
            {voucher.journalEntry ? (
              <Link href={`/dashboard/accounting/journals/entries/${voucher.journalEntry.id}`}>
                <Button variant="outline">
                  <FileText className="h-4 w-4 mr-2" />
                  View Journal Entry
                </Button>
              </Link>
            ) : (
              <p className="text-sm text-gray-500">No journal entry linked to this voucher</p>
            )}
          </CardFooter>
        </Card>
      </div>

      {/* Cancel Voucher Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Payment Voucher / إلغاء إذن الصرف</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this payment voucher? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-gray-500">
              Cancelling this voucher will mark it as cancelled in the system. Any associated journal entries will remain in the system but will be marked as cancelled.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
              No, Keep Voucher
            </Button>
            <Button variant="destructive" onClick={cancelVoucher} disabled={isCancelling}>
              {isCancelling ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cancelling...
                </>
              ) : (
                "Yes, Cancel Voucher"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
