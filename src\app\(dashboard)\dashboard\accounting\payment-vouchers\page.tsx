"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select-radix";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Loader2, Save, RefreshCw, Plus, Search, FileText, ArrowRight, Printer } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { format } from "date-fns";
import { DatePicker } from "@/components/ui/date-picker";
import { useRouter } from "next/navigation";

interface PaymentVoucher {
  id: string;
  voucherNumber: string;
  date: string;
  amount: number;
  description: string;
  paymentMethodId: string;
  contactId: string | null;
  status: string;
  reference: string | null;
  referenceType: string | null;
  createdAt: string;
  paymentMethod: {
    id: string;
    name: string;
    code: string;
  };
  contact: {
    id: string;
    name: string;
    phone: string;
  } | null;
  user: {
    id: string;
    name: string;
  };
  branch: {
    id: string;
    name: string;
    code: string;
  };
}

interface PaymentMethod {
  id: string;
  code: string;
  name: string;
  isActive: boolean;
}

interface Contact {
  id: string;
  name: string;
  phone: string;
  isSupplier: boolean;
  isCustomer: boolean;
}

export default function PaymentVouchersPage() {
  const router = useRouter();
  const [vouchers, setVouchers] = useState<PaymentVoucher[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [period, setPeriod] = useState<string>("month");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(false);
  const [isLoadingContacts, setIsLoadingContacts] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    amount: "",
    description: "",
    paymentMethodId: "",
    contactId: "",
    expenseAccountId: "",
    date: new Date(),
  });

  // State for expense accounts
  const [expenseAccounts, setExpenseAccounts] = useState<Array<{id: string, code: string, name: string}>>([]);
  const [isLoadingExpenseAccounts, setIsLoadingExpenseAccounts] = useState(false);

  // Fetch payment vouchers
  useEffect(() => {
    fetchVouchers();
  }, [startDate, endDate]);

  // Fetch payment methods, contacts, and expense accounts when dialog opens
  useEffect(() => {
    if (isCreateDialogOpen) {
      fetchPaymentMethods();
      fetchContacts();
      fetchExpenseAccounts();
    }
  }, [isCreateDialogOpen]);

  // Handle period change
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
    const today = new Date();

    switch (value) {
      case "day":
        setStartDate(today);
        setEndDate(today);
        break;
      case "week":
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        setStartDate(weekStart);
        setEndDate(today);
        break;
      case "month":
        setStartDate(new Date(today.getFullYear(), today.getMonth(), 1));
        setEndDate(today);
        break;
      case "year":
        setStartDate(new Date(today.getFullYear(), 0, 1));
        setEndDate(today);
        break;
      case "custom":
        // Keep current dates for custom period
        break;
    }
  };

  // Fetch payment vouchers
  const fetchVouchers = async () => {
    setIsLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }

      if (endDate) {
        params.append("endDate", endDate.toISOString());
      }

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      const response = await fetch(`/api/accounting/payment-vouchers?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setVouchers(data.data || []);
      } else {
        toast.error("Failed to load payment vouchers");
      }
    } catch (error) {
      console.error("Error fetching payment vouchers:", error);
      toast.error("Failed to load payment vouchers");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    setIsLoadingPaymentMethods(true);
    try {
      // Try to fetch from accounting payment methods first
      let response = await fetch("/api/accounting/payment-methods");

      if (response.ok) {
        const data = await response.json();
        if (data.data && Array.isArray(data.data) && data.data.length > 0) {
          setPaymentMethods(data.data.filter((method: PaymentMethod) => method.isActive) || []);
        } else {
          // If no data from accounting payment methods, try settings payment methods
          response = await fetch("/api/settings/payment-methods");
          if (response.ok) {
            const settingsData = await response.json();
            setPaymentMethods(settingsData.filter((method: PaymentMethod) => method.isActive) || []);
          } else {
            toast.error("Failed to load payment methods");
          }
        }
      } else {
        // If accounting payment methods fails, try settings payment methods
        response = await fetch("/api/settings/payment-methods");
        if (response.ok) {
          const settingsData = await response.json();
          setPaymentMethods(settingsData.filter((method: PaymentMethod) => method.isActive) || []);
        } else {
          toast.error("Failed to load payment methods");
        }
      }
    } catch (error) {
      console.error("Error fetching payment methods:", error);
      toast.error("Failed to load payment methods");
    } finally {
      setIsLoadingPaymentMethods(false);
    }
  };

  // Fetch contacts
  const fetchContacts = async () => {
    setIsLoadingContacts(true);
    try {
      const response = await fetch("/api/contacts?isActive=true");
      if (response.ok) {
        const data = await response.json();
        // Check if data is an array (direct response) or has a contacts property
        if (Array.isArray(data)) {
          setContacts(data || []);
        } else if (data.contacts && Array.isArray(data.contacts)) {
          setContacts(data.contacts || []);
        } else {
          // If neither format works, try to extract any array from the response
          const possibleContacts = Object.values(data).find(val => Array.isArray(val));
          if (possibleContacts) {
            setContacts(possibleContacts as Contact[]);
          } else {
            console.error("Unexpected contacts data format:", data);
            toast.error("Failed to parse contacts data");
          }
        }
      } else {
        toast.error("Failed to load contacts");
      }
    } catch (error) {
      console.error("Error fetching contacts:", error);
      toast.error("Failed to load contacts");
    } finally {
      setIsLoadingContacts(false);
    }
  };

  // Fetch expense accounts
  const fetchExpenseAccounts = async () => {
    setIsLoadingExpenseAccounts(true);
    try {
      const response = await fetch("/api/accounting/accounts?type=EXPENSE&isActive=true");
      if (response.ok) {
        const data = await response.json();
        setExpenseAccounts(data.data || []);
      } else {
        toast.error("Failed to load expense accounts");
      }
    } catch (error) {
      console.error("Error fetching expense accounts:", error);
      toast.error("Failed to load expense accounts");
    } finally {
      setIsLoadingExpenseAccounts(false);
    }
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({ ...prev, date }));
    }
  };

  // Create payment voucher
  const createPaymentVoucher = async () => {
    if (!formData.amount || !formData.description || !formData.paymentMethodId || !formData.expenseAccountId) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/accounting/payment-vouchers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: parseFloat(formData.amount),
          description: formData.description,
          paymentMethodId: formData.paymentMethodId,
          contactId: formData.contactId === "none" ? null : formData.contactId || null,
          expenseAccountId: formData.expenseAccountId || null,
          date: formData.date,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("Payment voucher created successfully");
        setIsCreateDialogOpen(false);
        fetchVouchers();

        // Reset form
        setFormData({
          amount: "",
          description: "",
          paymentMethodId: "",
          contactId: "",
          expenseAccountId: "",
          date: new Date(),
        });

        // Navigate to the voucher details page
        router.push(`/dashboard/accounting/payment-vouchers/${data.data.id}`);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to create payment voucher");
      }
    } catch (error) {
      console.error("Error creating payment voucher:", error);
      toast.error("Failed to create payment voucher");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Payment Vouchers / أذونات الصرف</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Payment Voucher
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Vouchers / أذونات الصرف</CardTitle>
          <CardDescription>View and manage payment vouchers</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="search"
                    placeholder="Search vouchers..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && fetchVouchers()}
                  />
                </div>
              </div>
              <div className="flex flex-col md:flex-row gap-4">
                <div>
                  <Select value={period} onValueChange={handlePeriodChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="year">This Year</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <DatePicker date={startDate} setDate={setStartDate} />
                </div>
                <div>
                  <DatePicker date={endDate} setDate={setEndDate} />
                </div>
                <Button variant="outline" onClick={fetchVouchers}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                <span className="ml-2 text-gray-500">Loading payment vouchers...</span>
              </div>
            ) : vouchers.length > 0 ? (
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Voucher #</th>
                        <th className="px-4 py-3 text-left">Date</th>
                        <th className="px-4 py-3 text-left">Description</th>
                        <th className="px-4 py-3 text-left">Payment Method</th>
                        <th className="px-4 py-3 text-left">Contact</th>
                        <th className="px-4 py-3 text-right">Amount</th>
                        <th className="px-4 py-3 text-center">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {vouchers.map(voucher => (
                        <tr key={voucher.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3 text-left">{voucher.voucherNumber}</td>
                          <td className="px-4 py-3 text-left">{format(new Date(voucher.date), 'MMM d, yyyy')}</td>
                          <td className="px-4 py-3 text-left">{voucher.description}</td>
                          <td className="px-4 py-3 text-left">{voucher.paymentMethod.name}</td>
                          <td className="px-4 py-3 text-left">{voucher.contact?.name || '-'}</td>
                          <td className="px-4 py-3 text-right">{formatCurrency(voucher.amount)}</td>
                          <td className="px-4 py-3 text-center">
                            <div className="flex justify-center space-x-2">
                              <Link href={`/dashboard/accounting/payment-vouchers/${voucher.id}`}>
                                <Button variant="outline" size="sm">
                                  View
                                </Button>
                              </Link>
                              <Button variant="outline" size="sm">
                                <Printer className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <div className="text-center p-4 text-gray-500">
                No payment vouchers found for the selected period.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Create Payment Voucher Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create Payment Voucher / إنشاء إذن صرف</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2">
                <Label htmlFor="amount">Amount / المبلغ *</Label>
                <Input
                  id="amount"
                  name="amount"
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={handleInputChange}
                  placeholder="Enter amount"
                  required
                />
              </div>
              <div className="col-span-2">
                <Label htmlFor="description">Description / الوصف *</Label>
                <Input
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter description"
                  required
                />
              </div>
              <div className="col-span-2">
                <Label htmlFor="paymentMethodId">Payment Method / طريقة الدفع *</Label>
                <Select
                  value={formData.paymentMethodId}
                  onValueChange={(value) => handleSelectChange("paymentMethodId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingPaymentMethods ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                        <span className="ml-2 text-gray-500">Loading...</span>
                      </div>
                    ) : (
                      paymentMethods.map(method => (
                        <SelectItem key={method.id} value={method.id}>
                          {method.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-2">
                <Label htmlFor="contactId">Contact / جهة الاتصال</Label>
                <Select
                  value={formData.contactId}
                  onValueChange={(value) => handleSelectChange("contactId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select contact (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {isLoadingContacts ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                        <span className="ml-2 text-gray-500">Loading...</span>
                      </div>
                    ) : (
                      contacts.map(contact => (
                        <SelectItem key={contact.id} value={contact.id}>
                          {contact.name} ({contact.phone})
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-2">
                <Label htmlFor="expenseAccountId">Expense Account / حساب المصروف *</Label>
                <Select
                  value={formData.expenseAccountId}
                  onValueChange={(value) => handleSelectChange("expenseAccountId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select expense account" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingExpenseAccounts ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                        <span className="ml-2 text-gray-500">Loading...</span>
                      </div>
                    ) : (
                      expenseAccounts.map(account => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-2">
                <Label htmlFor="date">Date / التاريخ</Label>
                <DatePicker date={formData.date} setDate={handleDateChange} />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={createPaymentVoucher} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Voucher
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
