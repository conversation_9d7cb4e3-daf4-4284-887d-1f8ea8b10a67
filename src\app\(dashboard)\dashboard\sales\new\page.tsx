"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import CustomizeProductModal, { CustomizedProduct } from "../components/CustomizeProductModal";
import ProductCustomizationWizard from "../components/ProductCustomizationWizard";
import ContactSearchInput from "@/components/contacts/ContactSearchInput";
import DiscountSelector from "../components/DiscountSelector";
import AppliedDiscountsList from "../components/AppliedDiscountsList";
import RedeemPoints from "./redeem-points";

// Define types for data
interface Contact {
  id: string;
  name: string;
  phone: string;
  isCustomer: boolean;
}

interface Branch {
  id: string;
  name: string;
  code: string;
}

interface Specification {
  id: string;
  name: string;
  value: string;
}

interface InventoryItem {
  warehouseId: string;
  warehouseName: string;
  quantity: number;
}

interface Product {
  id: string;
  name: string;
  basePrice: number;
  costPrice: number;
  isCustomizable: boolean;
  specifications: Specification[];
  inventory: InventoryItem[];
}

interface Warehouse {
  id: string;
  name: string;
  branchId: string;
}

export default function NewSalePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedContact, setSelectedContact] = useState("");
  const [selectedBranch, setSelectedBranch] = useState("");
  const [items, setItems] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [unitPrice, setUnitPrice] = useState(0);
  const [notes, setNotes] = useState("");
  const [availableWarehouses, setAvailableWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState("");
  const [isCustomizeModalOpen, setIsCustomizeModalOpen] = useState(false);
  const [isWizardModalOpen, setIsWizardModalOpen] = useState(false);
  const [productToCustomize, setProductToCustomize] = useState<any>(null);
  const [applyTax, setApplyTax] = useState(false);
  const [discount, setDiscount] = useState(0);
  const [availableDiscounts, setAvailableDiscounts] = useState<any[]>([]);
  const [selectedInvoiceDiscounts, setSelectedInvoiceDiscounts] = useState<any[]>([]);
  const [selectedItemDiscounts, setSelectedItemDiscounts] = useState<{[key: string]: any[]}>({});
  const [isDiscountModalOpen, setIsDiscountModalOpen] = useState(false);
  const [itemForDiscount, setItemForDiscount] = useState<string | null>(null);
  // Generate invoice number based on branch
  const [invoiceNumber, setInvoiceNumber] = useState("DRAFT-0000");

  // Payment methods
  interface PaymentMethodSetting {
    id: string;
    name: string;
    code: string;
    isActive: boolean;
  }

  interface PaymentDetail {
    method: string;
    amount: number;
  }

  const [paymentMethods, setPaymentMethods] = useState<PaymentDetail[]>([]);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState<PaymentMethodSetting[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('CASH');
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentMethodsLoading, setPaymentMethodsLoading] = useState(false);

  // State for real data
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [dataLoading, setDataLoading] = useState(true);

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      setPaymentMethodsLoading(true);
      const response = await fetch('/api/settings/payment-methods');

      if (response.ok) {
        const data = await response.json();
        // Filter only active payment methods
        const activeMethods = data.filter((method: PaymentMethodSetting) =>
          method.isActive && method.code !== 'SUPPLIER_ACCOUNT'
        );

        setAvailablePaymentMethods(activeMethods);

        // Set default selected payment method if available
        if (activeMethods.length > 0) {
          setSelectedPaymentMethod(activeMethods[0].code);
        }
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    } finally {
      setPaymentMethodsLoading(false);
    }
  };

  // Fetch real data from API
  useEffect(() => {
    const fetchData = async () => {
      setDataLoading(true);
      try {
        // Fetch customers (contacts that are customers)
        const contactsResponse = await fetch('/api/contacts?type=customer');
        if (contactsResponse.ok) {
          const contactsData = await contactsResponse.json();
          setContacts(contactsData);
        }

        // Fetch branches
        const branchesResponse = await fetch('/api/branches');
        if (branchesResponse.ok) {
          const branchesData = await branchesResponse.json();
          setBranches(branchesData);
        }

        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          setProducts(productsData);
        }

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        if (warehousesResponse.ok) {
          const warehousesData = await warehousesResponse.json();
          setWarehouses(warehousesData);
        }

        // Fetch available discounts
        const discountsResponse = await fetch('/api/discounts?isActive=true');
        if (discountsResponse.ok) {
          const discountsData = await discountsResponse.json();
          setAvailableDiscounts(discountsData);
        }

        // Fetch payment methods
        await fetchPaymentMethods();
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setDataLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get next invoice number when branch changes
  useEffect(() => {
    if (selectedBranch) {
      // Find the branch in our real data
      const branch = branches.find(b => b.id === selectedBranch);
      if (branch) {
        // Use the branch code from the database
        const branchCode = branch.code || branch.name.charAt(0).toUpperCase();

        // Set a temporary invoice number immediately
        const tempInvoiceNumber = `${branchCode}-0001`;
        setInvoiceNumber(tempInvoiceNumber);

        // Try to fetch the next invoice number from the API
        try {
          fetch(`/api/invoices/next?branchId=${selectedBranch}&type=sale`)
            .then(response => {
              if (!response.ok) {
                // If the response is not OK, use the fallback
                console.log(`API returned status ${response.status}. Using fallback invoice number.`);
                return { invoiceNumber: tempInvoiceNumber };
              }
              return response.json();
            })
            .then(data => {
              if (data && data.invoiceNumber) {
                setInvoiceNumber(data.invoiceNumber);
              } else {
                // Fallback if the response doesn't contain an invoice number
                console.log('API response missing invoice number. Using fallback.');
                setInvoiceNumber(tempInvoiceNumber);
              }
            })
            .catch(error => {
              console.error('Error fetching invoice number:', error);
              // Fallback to a default format if API fails
              setInvoiceNumber(tempInvoiceNumber);
            });
        } catch (error) {
          console.error('Exception in fetch operation:', error);
          // Fallback if the fetch operation throws an exception
          setInvoiceNumber(tempInvoiceNumber);
        }
      } else {
        // If branch not found, use a generic invoice number
        setInvoiceNumber(`INV-${Date.now().toString().slice(-6)}`);
      }
    } else {
      // If no branch is selected, use a draft number
      setInvoiceNumber("DRAFT-0000");
    }
  }, [selectedBranch, branches]);

  // Calculate total
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const taxRate = 0.14; // 14% tax

  // Apply tax first
  const taxAmount = applyTax ? subtotal * taxRate : 0;
  const subtotalWithTax = subtotal + taxAmount;

  // Use the optimized discount calculation API
  const [discountCalculation, setDiscountCalculation] = useState({
    invoiceDiscounts: [],
    itemDiscounts: {},
    manualDiscountAmount: 0,
    automaticDiscountAmount: 0,
    itemDiscountsTotal: 0,
    totalDiscountAmount: 0,
    calculationTime: 0
  });

  // Calculate discounts using the API
  useEffect(() => {
    const calculateDiscounts = async () => {
      try {
        // Only calculate if we have items
        if (items.length === 0) {
          setDiscountCalculation({
            invoiceDiscounts: [],
            itemDiscounts: {},
            manualDiscountAmount: 0,
            automaticDiscountAmount: 0,
            itemDiscountsTotal: 0,
            totalDiscountAmount: 0,
            calculationTime: 0
          });
          return;
        }

        const response = await fetch('/api/discounts/calculate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            items: items.map(item => ({
              id: item.id,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              total: item.total
            })),
            subtotal,
            contactId: selectedContact,
            branchId: selectedBranch,
            applyTax,
            taxRate: 0.14
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setDiscountCalculation(data);
        }
      } catch (error) {
        console.error('Error calculating discounts:', error);
      }
    };

    // Debounce the calculation to avoid too many API calls
    const timeoutId = setTimeout(() => {
      calculateDiscounts();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [items, subtotal, selectedContact, selectedBranch, applyTax, selectedInvoiceDiscounts, selectedItemDiscounts]);

  // Then apply manual invoice discount
  const manualDiscountAmount = discount > 0 ? (discount > subtotalWithTax ? subtotalWithTax : discount) : 0;

  // Use the calculated values from the API
  const automaticDiscountsTotal = discountCalculation.automaticDiscountAmount;
  const itemDiscountsTotal = discountCalculation.itemDiscountsTotal;

  // Total discount amount (ensure it doesn't exceed the subtotal with tax)
  const calculatedDiscount = manualDiscountAmount + automaticDiscountsTotal + itemDiscountsTotal;
  const discountAmount = Math.min(calculatedDiscount, subtotalWithTax);

  // Final total
  const total = subtotalWithTax - discountAmount;

  // Calculate total paid amount and remaining amount
  const totalPaid = paymentMethods.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingAmount = Math.max(0, total - totalPaid);

  // Add payment method
  const addPaymentMethod = () => {
    if (selectedPaymentMethod && paymentAmount > 0 && paymentAmount <= remainingAmount) {
      // Check if this payment method already exists
      const existingPaymentIndex = paymentMethods.findIndex(p => p.method === selectedPaymentMethod);

      if (existingPaymentIndex >= 0) {
        // Update existing payment method
        const updatedPaymentMethods = [...paymentMethods];
        updatedPaymentMethods[existingPaymentIndex].amount += paymentAmount;
        setPaymentMethods(updatedPaymentMethods);
      } else {
        // Add new payment method
        setPaymentMethods([...paymentMethods, { method: selectedPaymentMethod, amount: paymentAmount }]);
      }

      // Reset payment amount
      setPaymentAmount(remainingAmount > 0 ? remainingAmount : 0);
    }
  };

  // Remove payment method
  const removePaymentMethod = (index: number) => {
    const updatedPaymentMethods = [...paymentMethods];
    updatedPaymentMethods.splice(index, 1);
    setPaymentMethods(updatedPaymentMethods);
  };

  // Determine payment status based on total paid amount
  const getPaymentStatus = () => {
    if (totalPaid === 0) return 'UNPAID';
    if (totalPaid < total) return 'PARTIALLY_PAID';
    return 'PAID';
  };

  // Get payment status display text
  const getPaymentStatusText = () => {
    if (totalPaid === 0) return 'Unpaid (Customer Account)';
    if (totalPaid < total) return 'Partially Paid (Remaining to Customer Account)';
    return 'Paid';
  };

  // Update available warehouses when branch changes
  useEffect(() => {
    if (selectedBranch) {
      const branchWarehouses = warehouses.filter(wh => wh.branchId === selectedBranch);
      setAvailableWarehouses(branchWarehouses);
      if (branchWarehouses.length > 0) {
        setSelectedWarehouse(branchWarehouses[0].id);
      } else {
        setSelectedWarehouse("");
      }
    } else {
      setAvailableWarehouses([]);
      setSelectedWarehouse("");
    }
  }, [selectedBranch, warehouses]);

  // Update unit price when product changes
  useEffect(() => {
    if (selectedProduct) {
      const product = products.find(p => p.id === selectedProduct);
      if (product) {
        // Ensure we never set a zero or negative price
        if (product.basePrice > 0) {
          setUnitPrice(product.basePrice);
        } else {
          // If product has a zero or negative base price, set a default minimum price
          setUnitPrice(1);
        }
      }
    } else {
      // When no product is selected, reset the unit price field
      setUnitPrice(0);
    }
  }, [selectedProduct, products]);

  // Update payment amount when total or payment methods change
  useEffect(() => {
    // Set payment amount to remaining amount
    setPaymentAmount(remainingAmount);
  }, [total, paymentMethods.length]);

  // Add item to sale
  const addItem = () => {
    // Only require warehouse selection
    if (!selectedWarehouse) return;

    // Ensure quantity and unit price are valid
    let finalQuantity = quantity;
    let finalUnitPrice = unitPrice;

    if (finalQuantity <= 0) {
      finalQuantity = 1;
      setQuantity(1);
    }

    // Check if a product is selected
    if (!selectedProduct) {
      // If no product is selected, create a custom/manual entry
      const manualItem = {
        id: `item-${Date.now()}`,
        productId: "manual-entry",
        productName: "Manual Entry",
        unitPrice: finalUnitPrice,
        costPrice: 0,
        quantity: finalQuantity,
        total: finalUnitPrice * finalQuantity,
        warehouseId: selectedWarehouse,
        warehouseName: warehouses.find(w => w.id === selectedWarehouse)?.name || "Unknown",
        isCustomized: false,
      };

      setItems([...items, manualItem]);
      setQuantity(1);
      setUnitPrice(0);
      return;
    }

    // If a product is selected, get the product details
    const product = products.find(p => p.id === selectedProduct);
    if (!product) return;

    // Allow any unit price, including zero
    // We'll use whatever value the user entered, even if it's zero or negative
    // This allows for free items or special promotions

    // Check if product is in stock
    const inventoryItem = product.inventory.find(inv => inv.warehouseId === selectedWarehouse);
    if (!inventoryItem || inventoryItem.quantity < finalQuantity) {
      alert("Not enough stock available!");
      return;
    }

    // If product is customizable, open the customize modal
    if (product.isCustomizable) {
      console.log("Opening customize modal for product:", product);
      setProductToCustomize(product);

      // Use the new wizard interface instead of the old modal
      setIsWizardModalOpen(true);
      return;
    } else {
      console.log("Product is not customizable:", product);
    }

    // Create a new item
    const newItem = {
      id: `item-${Date.now()}`,
      productId: product.id,
      productName: product.name,
      unitPrice: finalUnitPrice,
      costPrice: product.costPrice || 0,
      quantity: finalQuantity,
      total: finalUnitPrice * finalQuantity,
      warehouseId: selectedWarehouse,
      warehouseName: warehouses.find(w => w.id === selectedWarehouse)?.name || "Default Warehouse",
      specifications: product.specifications,
      isCustomized: false,
    };

    setItems([...items, newItem]);
    setSelectedProduct("");
    setQuantity(1);
    setUnitPrice(0);
  };

  // Handle customized product confirmation
  const handleCustomizedProduct = (customizedProduct: CustomizedProduct, existingItemId?: string) => {
    console.log("Handling customized product:", customizedProduct, "Existing item ID:", existingItemId);

    const product = products.find(p => p.id === customizedProduct.productId);
    if (!product) {
      console.error("Product not found:", customizedProduct.productId);
      return;
    }

    // If we're editing an existing item
    if (existingItemId) {
      console.log("Editing existing item:", existingItemId);

      // Find the existing item
      const existingItem = items.find(item => item.id === existingItemId);
      if (!existingItem) {
        console.error("Existing item not found:", existingItemId);
        return;
      }

      // Create specifications array from selected components
      const selectedSpecs = Object.entries(customizedProduct.selectedComponents).map(([type, component]) => {
        if (!component) return null; // Skip if component is undefined

        const count = component.count || 1;
        const displayValue = type === "RAM" && count > 1
          ? `${component.name} (${count} sticks)`
          : component.name;

        return {
          id: component.id,
          name: type,
          value: displayValue,
          count: count
        };
      }).filter(Boolean); // Filter out null values

      console.log("Created specifications:", selectedSpecs);

      // Update the existing item
      const updatedItems = items.map(item => {
        if (item.id === existingItemId) {
          const updatedItem = {
            ...item,
            unitPrice: customizedProduct.totalPrice,
            costPrice: customizedProduct.costPrice,
            total: customizedProduct.totalPrice * item.quantity,
            specifications: selectedSpecs,
            customizedComponents: JSON.parse(JSON.stringify(customizedProduct.selectedComponents)), // Deep copy
            isCustomized: true, // Ensure this is marked as customized
          };
          console.log("Updated item:", updatedItem);
          return updatedItem;
        }
        return item;
      });

      console.log("Setting updated items:", updatedItems);
      setItems(updatedItems);

      // Reset product to customize to avoid duplicate edits
      setProductToCustomize(null);

      return;
    }

    // For new items, check if product is in stock
    const inventoryItem = product.inventory.find(inv => inv.warehouseId === selectedWarehouse);
    if (!inventoryItem || inventoryItem.quantity < quantity) {
      alert("Not enough stock available!");
      return;
    }

    // Check if all components have enough stock for the requested quantity
    // For components with count (like RAM), we need to check if there's enough stock for count * quantity
    let insufficientComponents = [];

    for (const [type, component] of Object.entries(customizedProduct.selectedComponents)) {
      if (!component) continue; // Skip if component is undefined

      const componentCount = component.count || 1;
      const totalNeeded = componentCount * quantity;

      if (totalNeeded > component.stock) {
        insufficientComponents.push({
          name: component.name,
          type,
          available: component.stock,
          needed: totalNeeded
        });
      }
    }

    if (insufficientComponents.length > 0) {
      const message = insufficientComponents.map(c =>
        `${c.name} (${c.type}): Need ${c.needed}, but only ${c.available} available`
      ).join('\n');

      alert(`Not enough components in stock:\n${message}`);
      return;
    }

    // Create specifications array from selected components
    const selectedSpecs = Object.entries(customizedProduct.selectedComponents).map(([type, component]) => {
      if (!component) return null; // Skip if component is undefined

      const count = component.count || 1;
      return {
        id: component.id,
        name: type,
        value: count > 1 ? `${component.name} (${count} sticks)` : component.name,
        count: count
      };
    }).filter(Boolean); // Filter out null values

    // Create a new item
    const newItem = {
      id: `item-${Date.now()}`,
      productId: product.id,
      productName: product.name,
      unitPrice: customizedProduct.totalPrice,
      costPrice: customizedProduct.costPrice,
      quantity,
      total: customizedProduct.totalPrice * quantity,
      warehouseId: selectedWarehouse,
      warehouseName: warehouses.find(w => w.id === selectedWarehouse)?.name || "Default Warehouse",
      specifications: selectedSpecs,
      isCustomized: true,
      customizedComponents: customizedProduct.selectedComponents,
    };

    setItems([...items, newItem]);
    setSelectedProduct("");
    setQuantity(1);
  };

  // Remove item from sale
  const removeItem = (itemId: string) => {
    setItems(items.filter(item => item.id !== itemId));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // The ONLY validation we do is to check if there are items in the sales list
    if (items.length === 0) {
      alert("Please add at least one product to the invoice.");
      return;
    }

    // We don't need to check selectedContact or selectedBranch here
    // If they're not selected, we'll use default values

    // We're allowing items with zero prices now
    // No need to validate unit prices anymore

    // If no product is selected but unit price is 0, that's okay
    // We only validate unit price when a product is selected

    setIsLoading(true);

    // Simplify the items processing to make it faster
    const processedItems = items.map(item => {
      // Only include essential data for the API
      const processedItem = {
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.total,
        warehouseId: item.warehouseId,
      };

      // For customized items, include component details
      if (item.isCustomized && item.customizedComponents) {
        return {
          ...processedItem,
          components: Object.entries(item.customizedComponents).map(([type, component]) => {
            // Type guard for component
            if (!component || typeof component !== 'object' || !('id' in component)) {
              return {
                id: 'unknown',
                name: 'Unknown Component',
                type,
                count: 1,
                totalCount: item.quantity
              };
            }

            const count = (component as any).count || 1;
            return {
              id: (component as any).id,
              name: (component as any).name, // Include the component name
              type,
              count: count,
              totalCount: count * item.quantity,
              price: (component as any).price, // Include the component price
            };
          }),
        };
      }

      return processedItem;
    });

    // Make sure we have a valid invoice number
    const finalInvoiceNumber = invoiceNumber || `DRAFT-${Date.now().toString().slice(-6)}`;

    // Use default values if not selected
    const finalContactId = selectedContact || (contacts.length > 0 ? contacts[0].id : "");
    const finalBranchId = selectedBranch || (branches.length > 0 ? branches[0].id : "");

    // Prepare sale data
    const saleData = {
      invoiceNumber: finalInvoiceNumber,
      contactId: finalContactId,
      branchId: finalBranchId,
      items: processedItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.total,
        warehouseId: item.warehouseId,
        components: 'components' in item ? item.components : [],
      })),
      subtotal,
      discount: discountAmount,
      taxAmount,
      total,
      payments: paymentMethods.length > 0
        ? (remainingAmount > 0
            ? [...paymentMethods, { method: 'CUSTOMER_ACCOUNT', amount: remainingAmount }]
            : paymentMethods)
        : [{ method: 'CUSTOMER_ACCOUNT', amount: total }],
      paymentStatus: getPaymentStatus(),
      notes,
      date: new Date().toISOString(),
      status: "COMPLETED", // Mark as completed since we're creating a final invoice
      currency: "EGP", // Egyptian Pound
      // Add discounts data
      discounts: selectedInvoiceDiscounts.map(discount => ({
        id: discount.id,
        type: discount.type,
        value: discount.value,
        amount: discount.type === "PERCENTAGE"
          ? subtotalWithTax * (discount.value / 100)
          : discount.value
      })),
      // Add item discounts data
      itemDiscounts: Object.fromEntries(
        Object.entries(selectedItemDiscounts).map(([itemId, discounts]) => {
          // Find the item to calculate the discount amount
          const item = items.find(i => i.id === itemId);
          const itemTotal = item ? item.total : 0;

          return [
            itemId,
            discounts.map(discount => ({
              id: discount.id,
              type: discount.type,
              value: discount.value,
              amount: discount.type === "PERCENTAGE"
                ? itemTotal * (discount.value / 100)
                : discount.value
            }))
          ];
        })
      )
    };

    console.log("Sale data:", saleData);

    // Send data to API with a timeout to prevent long waits
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      // Show processing feedback to the user
      alert("Sale is being processed... Please wait.");

      // Make the API request and wait for the response
      const response = await fetch('/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(saleData),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error from server:', errorData);
        setIsLoading(false);
        alert(`Error creating sale: ${errorData.error || 'Unknown error'}`);
        return;
      }

      // Get the response data
      const responseData = await response.json();
      console.log('Sale created successfully:', responseData);

      // Show success message and redirect
      alert("Sale created successfully! Redirecting to sales list...");
      window.location.href = "/dashboard/sales";
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('Error creating sale:', error);

      // Type guard for error
      if (error && typeof error === 'object' && 'name' in error) {
        if (error.name === 'AbortError') {
          alert("Request timed out. The sale might still be processing. Please check the sales list.");
        } else {
          setIsLoading(false);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          alert(`Error creating sale: ${errorMessage}`);
        }
      } else {
        setIsLoading(false);
        alert(`Error creating sale: Unknown error`);
      }
    }
  };

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-black sm:text-3xl sm:truncate">
            New Sale
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/dashboard/sales"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </Link>
        </div>
      </div>

      {/* Customize Product Modal */}
      {productToCustomize && (
        <>
          <CustomizeProductModal
            isOpen={isCustomizeModalOpen}
            onClose={() => setIsCustomizeModalOpen(false)}
            product={productToCustomize}
            onConfirm={handleCustomizedProduct}
          />
          <ProductCustomizationWizard
            isOpen={isWizardModalOpen}
            onClose={() => {
              setIsWizardModalOpen(false);
              // Reset product to customize after closing to avoid state issues
              setTimeout(() => setProductToCustomize(null), 300);
            }}
            product={productToCustomize}
            onConfirm={handleCustomizedProduct}
          />
        </>
      )}

      <form onSubmit={handleSubmit}>
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:p-6">
            <div className="mb-6 bg-red-50 p-4 rounded-md border-l-4 border-red-500">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-8 w-8 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-bold text-red-800">IMPORTANT: THE PRODUCTS TABLE IS THE ONLY REQUIREMENT</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p className="font-medium mb-2 text-base">To create a sales invoice, you ONLY need to add products to the PRODUCTS TABLE. All other fields are optional.</p>

                    <div className="mt-3 p-3 bg-white border border-red-200 rounded-md">
                      <h4 className="font-bold text-red-800 mb-2">How to create a sales invoice:</h4>
                      <ol className="list-decimal list-inside space-y-1">
                        <li>Add products to the PRODUCTS TABLE using the form below</li>
                        <li>Optionally select a customer and branch</li>
                        <li>Configure payment methods if needed</li>
                        <li>Click "Create Sale" to save the invoice</li>
                      </ol>
                    </div>

                    <div className="mt-4 p-3 bg-yellow-100 border-l-4 border-yellow-500 rounded">
                      <p className="font-bold text-yellow-800 text-base">The "Create Sale" button will ONLY be enabled when there is at least one product in the PRODUCTS TABLE.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              {/* Customer Selection */}
              <div className="sm:col-span-3">
                <label htmlFor="contact" className="block text-base font-bold text-black">
                  Customer
                </label>
                <div className="mt-2">
                  <ContactSearchInput
                    onSelectContact={(contact) => setSelectedContact(contact.id)}
                    placeholder="Search customer by name or phone"
                    contactType="customer"
                    selectedContactId={selectedContact}
                    buttonLabel="Add New Customer"
                    className="w-full"
                  />
                </div>
              </div>

              {/* Branch Selection */}
              <div className="sm:col-span-3">
                <label htmlFor="branch" className="block text-base font-bold text-black">
                  Branch
                </label>
                <div className="mt-2">
                  <div className="relative bg-white border-2 border-gray-300 rounded-lg shadow-sm">
                    <select
                      id="branch"
                      name="branch"
                      className="block w-full py-3 pl-3 pr-10 text-base font-medium text-black bg-white focus:ring-black focus:border-black rounded-lg"
                      value={selectedBranch}
                      onChange={(e) => setSelectedBranch(e.target.value)}
                    >
                      <option value="" className="text-black font-medium">Select Branch</option>
                      {branches.map((branch) => (
                        <option key={branch.id} value={branch.id} className="text-black font-medium">
                          {branch.name}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Date */}
              <div className="sm:col-span-3">
                <label htmlFor="date" className="block text-base font-bold text-black">
                  Date
                </label>
                <div className="mt-2">
                  <div className="relative bg-white border-2 border-gray-300 rounded-lg shadow-sm">
                    <input
                      type="date"
                      name="date"
                      id="date"
                      className="block w-full py-3 pl-3 pr-10 text-base font-medium text-black bg-white focus:ring-black focus:border-black rounded-lg"
                      defaultValue={new Date().toISOString().split('T')[0]}
                      readOnly
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Invoice Number */}
              <div className="sm:col-span-3">
                <label htmlFor="invoice-number" className="block text-base font-bold text-black">
                  Invoice Number
                </label>
                <div className="mt-2">
                  <div className="relative bg-white border-2 border-gray-300 rounded-lg shadow-sm">
                    <input
                      type="text"
                      name="invoice-number"
                      id="invoice-number"
                      className="block w-full py-3 pl-3 pr-10 text-base font-medium text-black bg-white focus:ring-black focus:border-black rounded-lg"
                      value={invoiceNumber || "DRAFT-0000"}
                      readOnly
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add Items */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md mt-6">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-black mb-4">
              Add Products
            </h3>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              {/* Product Selection */}
              <div className="sm:col-span-2">
                <label htmlFor="product" className="block text-sm font-medium text-black">
                  Product
                </label>
                <div className="mt-1">
                  <select
                    id="product"
                    name="product"
                    className="shadow-sm focus:ring-black focus:border-black block w-full sm:text-sm border-gray-300 rounded-md bg-white text-black font-medium"
                    value={selectedProduct}
                    onChange={(e) => setSelectedProduct(e.target.value)}
                  >
                    <option value="" className="text-black font-medium">Select Product</option>
                    {products.map((product) => (
                      <option key={product.id} value={product.id} className="text-black font-medium">
                        {product.name} - {product.basePrice.toFixed(2)} ج.م
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Warehouse Selection */}
              <div className="sm:col-span-1">
                <label htmlFor="warehouse" className="block text-sm font-medium text-black">
                  Warehouse
                </label>
                <div className="mt-1">
                  <select
                    id="warehouse"
                    name="warehouse"
                    className="shadow-sm focus:ring-black focus:border-black block w-full sm:text-sm border-gray-300 rounded-md bg-white text-black font-medium"
                    value={selectedWarehouse}
                    onChange={(e) => setSelectedWarehouse(e.target.value)}
                    disabled={!selectedBranch}
                  >
                    <option value="" className="text-black font-medium">Select Warehouse</option>
                    {availableWarehouses.map((warehouse) => (
                      <option key={warehouse.id} value={warehouse.id} className="text-black font-medium">
                        {warehouse.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Unit Price */}
              <div className="sm:col-span-1">
                <label htmlFor="unitPrice" className="block text-sm font-medium text-black">
                  Unit Price
                </label>
                <div className="mt-1">
                  <div className="relative">
                    <input
                      type="number"
                      name="unitPrice"
                      id="unitPrice"
                      min="0"
                      step="0.01"
                      className={`shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md text-black`}
                      value={unitPrice}
                      onChange={(e) => setUnitPrice(parseFloat(e.target.value) || 0)}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-black sm:text-sm">ج.م</span>
                    </div>
                  </div>
                  {/* We're now allowing zero prices */}
                </div>
              </div>

              {/* Quantity */}
              <div className="sm:col-span-1">
                <label htmlFor="quantity" className="block text-sm font-medium text-black">
                  Quantity
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="quantity"
                    id="quantity"
                    min="1"
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md text-black"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              {/* Add Button */}
              <div className="sm:col-span-1 flex items-end">
                <button
                  type="button"
                  onClick={addItem}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  disabled={!selectedWarehouse} // Only require warehouse selection
                >
                  Add
                </button>
              </div>
            </div>

            {/* Items Table */}
            <div className="mt-6">
              <div className="flex items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-black">
                  Products Table
                </h3>
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Required
                </span>
                <p className="ml-4 text-sm text-gray-500">This table must contain at least one product to create a sale</p>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 border border-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Product
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Warehouse
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Total
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {items.length > 0 ? (
                      items.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 text-sm font-medium text-black">
                            <div>
                              {item.productName}
                              {item.isCustomized && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                  Customized
                                </span>
                              )}
                            </div>
                            {item.specifications && (
                              <ul className="mt-1 text-xs text-black list-disc list-inside">
                                {item.specifications.map((spec: any, index: number) => (
                                  <li key={index}>
                                    <span className="font-medium">{spec.name}:</span> {spec.value}
                                  </li>
                                ))}
                              </ul>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            {item.warehouseName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            <div className="flex items-center">
                              <input
                                type="number"
                                min={item.isCustomized ? item.costPrice || 0 : 0}
                                step="0.01"
                                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-24 sm:text-sm border-gray-300 rounded-md text-black"
                                value={item.unitPrice}
                                onChange={(e) => {
                                  const newPrice = parseFloat(e.target.value);
                                  if (!isNaN(newPrice) && newPrice > 0) {
                                    // Update the price without validation during typing
                                    const updatedItems = items.map(i => {
                                      if (i.id === item.id) {
                                        return {
                                          ...i,
                                          unitPrice: newPrice,
                                          total: newPrice * i.quantity
                                        };
                                      }
                                      return i;
                                    });
                                    setItems(updatedItems);
                                  }
                                }}
                                onBlur={(e) => {
                                  // Validate price only when the field loses focus
                                  const newPrice = parseFloat(e.target.value);
                                  if (!isNaN(newPrice) && newPrice > 0) {
                                    // Don't allow price below cost for customized items
                                    if (item.isCustomized && item.costPrice && newPrice < item.costPrice) {
                                      alert(`Price cannot be lower than cost price (${item.costPrice.toFixed(2)} ج.م)`);

                                      // Reset to cost price
                                      const updatedItems = items.map(i => {
                                        if (i.id === item.id) {
                                          return {
                                            ...i,
                                            unitPrice: item.costPrice,
                                            total: item.costPrice * i.quantity
                                          };
                                        }
                                        return i;
                                      });
                                      setItems(updatedItems);
                                    }
                                  }
                                }}
                              />
                              <span className="ml-2 text-black">ج.م</span>
                            </div>
                            {item.isCustomized && item.costPrice && (
                              <span className="block text-xs text-green-600">
                                Min: {item.costPrice.toFixed(2)} ج.م
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            <div className="flex items-center">
                              <div className="relative w-20">
                                <div className="flex items-center bg-white border border-gray-300 rounded-md">
                                  <button
                                    type="button"
                                    className="px-1 py-0.5 text-black hover:text-gray-700 focus:outline-none"
                                    onClick={() => {
                                      if (item.quantity > 1) {
                                        const updatedItems = items.map(i => {
                                          if (i.id === item.id) {
                                            return {
                                              ...i,
                                              quantity: i.quantity - 1,
                                              total: i.unitPrice * (i.quantity - 1)
                                            };
                                          }
                                          return i;
                                        });
                                        setItems(updatedItems);
                                      }
                                    }}
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                  </button>
                                  <input
                                    id={`quantity-${item.id}`}
                                    type="number"
                                    min="1"
                                    className="block w-8 text-center py-1 text-sm font-medium text-black border-0 focus:ring-0 focus:outline-none"
                                    value={item.quantity}
                                    onChange={(e) => {
                                      const newQuantity = parseInt(e.target.value);
                                      if (!isNaN(newQuantity) && newQuantity > 0) {
                                        const updatedItems = items.map(i => {
                                          if (i.id === item.id) {
                                            return {
                                              ...i,
                                              quantity: newQuantity,
                                              total: i.unitPrice * newQuantity
                                            };
                                          }
                                          return i;
                                        });
                                        setItems(updatedItems);
                                      }
                                    }}
                                  />
                                  <button
                                    type="button"
                                    className="px-1 py-0.5 text-black hover:text-gray-700 focus:outline-none"
                                    onClick={() => {
                                      const updatedItems = items.map(i => {
                                        if (i.id === item.id) {
                                          return {
                                            ...i,
                                            quantity: i.quantity + 1,
                                            total: i.unitPrice * (i.quantity + 1)
                                          };
                                        }
                                        return i;
                                      });
                                      setItems(updatedItems);
                                    }}
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            {item.total.toFixed(2)} ج.م
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                            <div className="flex space-x-4">
                              {item.isCustomized && (
                                <button
                                  type="button"
                                  onClick={() => {
                                    // Find the product
                                    const product = products.find(p => p.id === item.productId);
                                    if (product) {
                                      // Set the product to customize
                                      // Log the current components for debugging
                                      console.log("Editing item with components:", item.customizedComponents);

                                      // Create a deep copy of the product with all necessary properties
                                      setProductToCustomize({
                                        ...product,
                                        editingItemId: item.id, // Pass the existing item ID for editing
                                        currentComponents: JSON.parse(JSON.stringify(item.customizedComponents || {})), // Deep copy to avoid reference issues
                                        totalPrice: item.unitPrice, // Pass the current unit price
                                        costPrice: item.costPrice, // Pass the current cost price
                                        quantity: item.quantity, // Pass the current quantity
                                      });
                                      // Close the customize modal if it's open
                                      setIsCustomizeModalOpen(false);
                                      // Open the wizard modal
                                      setIsWizardModalOpen(true);
                                    }
                                  }}
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  Edit
                                </button>
                              )}
                              <button
                                type="button"
                                onClick={() => {
                                  setItemForDiscount(item.id);
                                  setIsDiscountModalOpen(true);
                                }}
                                className="text-blue-600 hover:text-blue-900"
                                title="Apply discounts"
                              >
                                Discount
                              </button>
                              <button
                                type="button"
                                onClick={() => removeItem(item.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                Remove
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="px-6 py-8 whitespace-nowrap text-center">
                          <div className="flex flex-col items-center">
                            <svg className="h-12 w-12 text-red-400 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <p className="text-lg font-bold text-red-600 mb-1">No products added yet</p>
                            <p className="text-sm text-gray-500">You must add at least one product to the table to create a sale</p>
                            <p className="text-sm text-gray-500 mt-2">Use the form above to add products</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Totals and Notes */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md mt-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              {/* Notes */}
              <div className="sm:col-span-4">
                <label htmlFor="notes" className="block text-sm font-medium text-black">
                  Notes
                </label>
                <div className="mt-1">
                  <textarea
                    id="notes"
                    name="notes"
                    rows={3}
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md text-black"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>
              </div>

              {/* Totals */}
              <div className="sm:col-span-2">
                <div className="bg-gray-50 p-4 rounded-lg border-2 border-gray-300 shadow-sm">
                  <div className="flex justify-between py-2 text-base">
                    <span className="font-bold text-black">Subtotal:</span>
                    <span className="text-black font-bold">{subtotal.toFixed(2)} ج.م</span>
                  </div>

                  {/* Tax */}
                  <div className="py-2">
                    <div className="flex justify-between text-sm mb-1">
                      <div className="flex items-center">
                        <input
                          id="apply-tax"
                          name="apply-tax"
                          type="checkbox"
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          checked={applyTax}
                          onChange={(e) => setApplyTax(e.target.checked)}
                        />
                        <label htmlFor="apply-tax" className="ml-2 font-medium text-black">
                          Apply Tax (14%):
                        </label>
                      </div>
                      <span className="text-black font-medium">{taxAmount.toFixed(2)} ج.م</span>
                    </div>
                    {applyTax && (
                      <div className="text-xs text-indigo-600 ml-6">
                        14% of {subtotal.toFixed(2)} ج.م
                      </div>
                    )}
                  </div>

                  {/* Subtotal with Tax */}
                  <div className="flex justify-between py-2 text-sm border-t border-gray-200 mt-2 pt-2">
                    <span className="font-medium text-black">Subtotal with Tax:</span>
                    <span className="text-black font-medium">{subtotalWithTax.toFixed(2)} ج.م</span>
                  </div>

                  {/* Discount */}
                  <div className="py-2">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="font-medium text-black">Discount:</span>
                      <span className="text-black font-medium">{discountAmount.toFixed(2)} ج.م</span>
                    </div>
                    <div className="flex items-center bg-white border border-gray-300 rounded-md">
                      <div className="flex-grow relative">
                        <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                          <span className="text-black text-xs">ج.م</span>
                        </div>
                        <input
                          type="number"
                          min="0"
                          max={subtotalWithTax}
                          step="0.01"
                          className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-8 pr-8 py-1 text-sm font-medium text-black border-0"
                          value={discount}
                          onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
                        />
                        {discount > 0 && (
                          <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                            <button
                              type="button"
                              className="text-black hover:text-gray-600 focus:outline-none"
                              onClick={() => setDiscount(0)}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                        )}
                      </div>
                      <button
                        type="button"
                        className="px-3 py-1 bg-blue-50 text-blue-600 hover:bg-blue-100 rounded-r-md text-sm font-medium"
                        onClick={() => {
                          setItemForDiscount(null);
                          setIsDiscountModalOpen(true);
                        }}
                      >
                        Discounts
                      </button>
                    </div>
                    {/* Show applied discounts list */}
                    <div className="mt-2">
                      <AppliedDiscountsList
                        discounts={selectedInvoiceDiscounts.map(discount => ({
                          id: discount.id,
                          name: discount.name,
                          type: discount.type as "PERCENTAGE" | "FIXED_AMOUNT",
                          scope: discount.scope as "INVOICE" | "ITEM" | "CUSTOMER",
                          value: discount.value,
                          amount: discount.type === "PERCENTAGE"
                            ? subtotalWithTax * (discount.value / 100)
                            : discount.value
                        }))}
                        onRemoveDiscount={(id) => {
                          setSelectedInvoiceDiscounts(
                            selectedInvoiceDiscounts.filter(d => d.id !== id)
                          );
                        }}
                        subtotal={subtotalWithTax}
                        maxHeight="150px"
                        title="Applied Discounts"
                      />
                    </div>
                  </div>

                  <div className="flex justify-between py-3 text-lg font-bold border-t-2 border-gray-300 mt-3 pt-3 bg-gray-50 rounded-lg p-3">
                    <span className="text-black">Total:</span>
                    <span className="text-black text-xl">{total.toFixed(2)} ج.م</span>
                  </div>

                  {/* Payment Methods */}
                  <div className="py-3 mt-4 border-t border-gray-200">
                    <h4 className="text-base font-bold text-black mb-2">Payment Methods:</h4>

                    {/* Payment Method Selection */}
                    {paymentMethodsLoading ? (
                      <div className="flex justify-center items-center py-4">
                        <svg className="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="ml-2 text-gray-600">Loading payment methods...</span>
                      </div>
                    ) : availablePaymentMethods.length === 0 ? (
                      <div className="text-center py-4 text-gray-500">
                        No payment methods available. Please add payment methods in settings.
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-2 mb-4">
                        {availablePaymentMethods.map((method) => (
                          <div
                            key={method.id}
                            className={`p-2 border rounded-md cursor-pointer flex items-center ${selectedPaymentMethod === method.code ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                            onClick={() => setSelectedPaymentMethod(method.code)}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium text-black">{method.name}</span>
                          </div>
                        ))}
                        <div
                          className={`p-2 border rounded-md cursor-pointer flex items-center ${selectedPaymentMethod === 'CUSTOMER_ACCOUNT' ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                          onClick={() => setSelectedPaymentMethod('CUSTOMER_ACCOUNT')}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                          <span className="font-medium text-black">Customer Account</span>
                        </div>
                      </div>
                    )}

                    {/* Payment Amount Input */}
                    <div className="flex items-center mb-4">
                      <div className="flex-1 mr-2">
                        <label htmlFor="paymentAmount" className="block text-sm font-medium text-black mb-1">
                          Payment Amount
                        </label>
                        <div className="relative rounded-md shadow-sm">
                          <input
                            type="number"
                            id="paymentAmount"
                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-3 pr-12 sm:text-sm border-gray-300 rounded-md text-black"
                            placeholder="0.00"
                            min="0"
                            max={remainingAmount}
                            step="0.01"
                            value={paymentAmount}
                            onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <span className="text-black sm:text-sm">ج.م</span>
                          </div>
                        </div>
                      </div>
                      <button
                        type="button"
                        className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        onClick={addPaymentMethod}
                        disabled={!selectedPaymentMethod || paymentAmount <= 0 || paymentAmount > remainingAmount}
                      >
                        Add Payment
                      </button>
                    </div>

                    {/* Payment Methods List */}
                    {paymentMethods.length > 0 && (
                      <div className="mt-4 border rounded-md overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                                Method
                              </th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                                Amount
                              </th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {paymentMethods.map((payment, index) => (
                              <tr key={index}>
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-black">
                                  {payment.method === 'CUSTOMER_ACCOUNT' ? 'Customer Account' :
                                    availablePaymentMethods.find(m => m.code === payment.method)?.name || payment.method}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                                  {payment.amount.toFixed(2)} ج.م
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                                  <button
                                    type="button"
                                    onClick={() => removePaymentMethod(index)}
                                    className="text-red-600 hover:text-red-900"
                                  >
                                    Remove
                                  </button>
                                </td>
                              </tr>
                            ))}
                            <tr className="bg-gray-50">
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                Total Paid
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                {totalPaid.toFixed(2)} ج.م
                              </td>
                              <td></td>
                            </tr>
                            <tr>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                Remaining
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                {remainingAmount.toFixed(2)} ج.م
                              </td>
                              <td></td>
                            </tr>
                            {remainingAmount > 0 && (
                              <tr className="bg-yellow-50">
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                                  Payment Status
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black" colSpan={2}>
                                  {getPaymentStatusText()}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="pt-5">
          <div className="flex justify-end">
            <Link
              href="/dashboard/sales"
              className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isLoading || items.length === 0}
              className={`ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(isLoading || items.length === 0) ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoading ? 'Saving...' : items.length === 0 ? 'Add Products First' : 'Create Sale'}
            </button>
          </div>
        </div>
      </form>

      {/* Discount Selector Modal */}
      <DiscountSelector
        isOpen={isDiscountModalOpen}
        onClose={() => setIsDiscountModalOpen(false)}
        availableDiscounts={availableDiscounts}
        selectedDiscounts={itemForDiscount
          ? (selectedItemDiscounts[itemForDiscount] || [])
          : selectedInvoiceDiscounts}
        onSelectDiscounts={(discounts) => {
          if (itemForDiscount) {
            // Update item discounts
            setSelectedItemDiscounts({
              ...selectedItemDiscounts,
              [itemForDiscount]: discounts
            });
          } else {
            // Update invoice discounts
            setSelectedInvoiceDiscounts(discounts);
          }
        }}
        scope={itemForDiscount ? "ITEM" : "INVOICE"}
        itemId={itemForDiscount || undefined}
        subtotal={itemForDiscount
          ? (items.find(item => item.id === itemForDiscount)?.total || 0)
          : subtotal}
        contactId={selectedContact}
      />
    </div>
  );
}