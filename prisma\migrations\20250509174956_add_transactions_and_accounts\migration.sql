/*
  Warnings:

  - The values [COST_OF_GOODS_SOLD,OTHER_INCOME,OTHER_EXPENSE] on the enum `AccountType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `branchId` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `category` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `code` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `isArchived` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `isDefault` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `isGroup` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `isSystemAccount` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `level` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `parentId` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `subtype` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `tags` on the `Account` table. All the data in the column will be lost.
  - You are about to drop the column `fiscalPeriodId` on the `Transaction` table. All the data in the column will be lost.
  - The `paymentMethod` column on the `Transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `AccountReconciliation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `AccountingSettings` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `FinancialReport` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `FiscalPeriod` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `FiscalYear` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `GeneralLedgerEntry` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Journal` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `JournalEntry` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `ReconciliationItem` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[accountNumber]` on the table `Account` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[transactionNumber]` on the table `Transaction` will be added. If there are existing duplicate values, this will fail.
  - Made the column `accountNumber` on table `Account` required. This step will fail if there are existing NULL values in that column.
  - Made the column `contactId` on table `Transaction` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `referenceType` to the `Transaction` table without a default value. This is not possible if the table is not empty.
  - Made the column `transactionNumber` on table `Transaction` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "CreditNoteStatus" AS ENUM ('PENDING', 'COMPLETED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ReferenceType" AS ENUM ('SALE', 'PURCHASE', 'RECEIPT', 'PAYMENT', 'CREDIT_NOTE', 'OPENING_BALANCE', 'OTHER');

-- AlterEnum
BEGIN;
CREATE TYPE "AccountType_new" AS ENUM ('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE');
ALTER TABLE "Account" ALTER COLUMN "type" TYPE "AccountType_new" USING ("type"::text::"AccountType_new");
ALTER TYPE "AccountType" RENAME TO "AccountType_old";
ALTER TYPE "AccountType_new" RENAME TO "AccountType";
DROP TYPE "AccountType_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "Account" DROP CONSTRAINT "Account_branchId_fkey";

-- DropForeignKey
ALTER TABLE "Account" DROP CONSTRAINT "Account_parentId_fkey";

-- DropForeignKey
ALTER TABLE "AccountReconciliation" DROP CONSTRAINT "AccountReconciliation_accountId_fkey";

-- DropForeignKey
ALTER TABLE "FinancialReport" DROP CONSTRAINT "FinancialReport_fiscalPeriodId_fkey";

-- DropForeignKey
ALTER TABLE "FinancialReport" DROP CONSTRAINT "FinancialReport_fiscalYearId_fkey";

-- DropForeignKey
ALTER TABLE "FiscalPeriod" DROP CONSTRAINT "FiscalPeriod_fiscalYearId_fkey";

-- DropForeignKey
ALTER TABLE "GeneralLedgerEntry" DROP CONSTRAINT "GeneralLedgerEntry_accountId_fkey";

-- DropForeignKey
ALTER TABLE "GeneralLedgerEntry" DROP CONSTRAINT "GeneralLedgerEntry_journalEntryId_fkey";

-- DropForeignKey
ALTER TABLE "GeneralLedgerEntry" DROP CONSTRAINT "GeneralLedgerEntry_transactionId_fkey";

-- DropForeignKey
ALTER TABLE "Journal" DROP CONSTRAINT "Journal_branchId_fkey";

-- DropForeignKey
ALTER TABLE "JournalEntry" DROP CONSTRAINT "JournalEntry_contactId_fkey";

-- DropForeignKey
ALTER TABLE "JournalEntry" DROP CONSTRAINT "JournalEntry_creditAccountId_fkey";

-- DropForeignKey
ALTER TABLE "JournalEntry" DROP CONSTRAINT "JournalEntry_debitAccountId_fkey";

-- DropForeignKey
ALTER TABLE "JournalEntry" DROP CONSTRAINT "JournalEntry_fiscalPeriodId_fkey";

-- DropForeignKey
ALTER TABLE "JournalEntry" DROP CONSTRAINT "JournalEntry_journalId_fkey";

-- DropForeignKey
ALTER TABLE "ReconciliationItem" DROP CONSTRAINT "ReconciliationItem_ledgerEntryId_fkey";

-- DropForeignKey
ALTER TABLE "ReconciliationItem" DROP CONSTRAINT "ReconciliationItem_reconciliationId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_accountId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_contactId_fkey";

-- DropForeignKey
ALTER TABLE "Transaction" DROP CONSTRAINT "Transaction_fiscalPeriodId_fkey";

-- DropIndex
DROP INDEX "Account_code_idx";

-- DropIndex
DROP INDEX "Account_isArchived_idx";

-- DropIndex
DROP INDEX "Account_parentId_idx";

-- DropIndex
DROP INDEX "Account_type_idx";

-- DropIndex
DROP INDEX "Transaction_date_idx";

-- AlterTable
ALTER TABLE "Account" DROP COLUMN "branchId",
DROP COLUMN "category",
DROP COLUMN "code",
DROP COLUMN "description",
DROP COLUMN "isArchived",
DROP COLUMN "isDefault",
DROP COLUMN "isGroup",
DROP COLUMN "isSystemAccount",
DROP COLUMN "level",
DROP COLUMN "parentId",
DROP COLUMN "subtype",
DROP COLUMN "tags",
ALTER COLUMN "accountNumber" SET NOT NULL;

-- AlterTable
ALTER TABLE "Transaction" DROP COLUMN "fiscalPeriodId",
ALTER COLUMN "accountId" DROP NOT NULL,
ALTER COLUMN "contactId" SET NOT NULL,
DROP COLUMN "paymentMethod",
ADD COLUMN     "paymentMethod" "PaymentMethod",
DROP COLUMN "referenceType",
ADD COLUMN     "referenceType" "ReferenceType" NOT NULL,
ALTER COLUMN "transactionNumber" SET NOT NULL;

-- DropTable
DROP TABLE "AccountReconciliation";

-- DropTable
DROP TABLE "AccountingSettings";

-- DropTable
DROP TABLE "FinancialReport";

-- DropTable
DROP TABLE "FiscalPeriod";

-- DropTable
DROP TABLE "FiscalYear";

-- DropTable
DROP TABLE "GeneralLedgerEntry";

-- DropTable
DROP TABLE "Journal";

-- DropTable
DROP TABLE "JournalEntry";

-- DropTable
DROP TABLE "ReconciliationItem";

-- DropEnum
DROP TYPE "EntryType";

-- DropEnum
DROP TYPE "ReportType";

-- CreateTable
CREATE TABLE "CreditNote" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "contactId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "originalInvoiceId" TEXT,
    "originalInvoiceNumber" TEXT,
    "creditNoteNumber" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "CreditNoteStatus" NOT NULL DEFAULT 'PENDING',
    "paymentStatus" "PaymentStatus" NOT NULL DEFAULT 'UNPAID',
    "paymentMethod" "PaymentMethod",
    "totalAmount" DOUBLE PRECISION NOT NULL,
    "subtotalAmount" DOUBLE PRECISION NOT NULL,
    "taxAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "taxRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "discountAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "applyTax" BOOLEAN NOT NULL DEFAULT false,
    "currency" TEXT NOT NULL DEFAULT 'EGP',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CreditNote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CreditNoteItem" (
    "id" TEXT NOT NULL,
    "creditNoteId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "unitPrice" DOUBLE PRECISION NOT NULL,
    "totalPrice" DOUBLE PRECISION NOT NULL,
    "reason" TEXT NOT NULL,
    "originalItemId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CreditNoteItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CreditNotePayment" (
    "id" TEXT NOT NULL,
    "creditNoteId" TEXT NOT NULL,
    "method" "PaymentMethod" NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CreditNotePayment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CreditNote_creditNoteNumber_key" ON "CreditNote"("creditNoteNumber");

-- CreateIndex
CREATE UNIQUE INDEX "Account_accountNumber_key" ON "Account"("accountNumber");

-- CreateIndex
CREATE UNIQUE INDEX "Transaction_transactionNumber_key" ON "Transaction"("transactionNumber");

-- AddForeignKey
ALTER TABLE "CreditNote" ADD CONSTRAINT "CreditNote_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNote" ADD CONSTRAINT "CreditNote_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNote" ADD CONSTRAINT "CreditNote_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNoteItem" ADD CONSTRAINT "CreditNoteItem_creditNoteId_fkey" FOREIGN KEY ("creditNoteId") REFERENCES "CreditNote"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNoteItem" ADD CONSTRAINT "CreditNoteItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNotePayment" ADD CONSTRAINT "CreditNotePayment_creditNoteId_fkey" FOREIGN KEY ("creditNoteId") REFERENCES "CreditNote"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;
