import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { hasPermission } from "@/lib/permissions";
import cache from "@/lib/cache";

// GET /api/discounts/campaigns/[id] - Get a specific discount campaign
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to view discounts
    const hasViewPermission = await hasPermission("view_discounts");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view discount campaigns" },
        { status: 403 }
      );
    }
    
    const { id } = params;
    
    // Get campaign from database
    const campaign = await prisma.discountCampaign.findUnique({
      where: {
        id,
      },
      include: {
        campaignDiscounts: {
          include: {
            discount: true
          }
        }
      },
    });
    
    if (!campaign) {
      return NextResponse.json(
        { error: "Campaign not found" },
        { status: 404 }
      );
    }
    
    // Format response
    const formattedCampaign = {
      id: campaign.id,
      name: campaign.name,
      description: campaign.description,
      startDate: campaign.startDate.toISOString(),
      endDate: campaign.endDate.toISOString(),
      createdAt: campaign.createdAt.toISOString(),
      updatedAt: campaign.updatedAt.toISOString(),
      discounts: campaign.campaignDiscounts.map(cd => cd.discount)
    };
    
    return NextResponse.json(formattedCampaign);
  } catch (error) {
    console.error("Error fetching discount campaign:", error);
    return NextResponse.json(
      { error: "Failed to fetch discount campaign" },
      { status: 500 }
    );
  }
}

// PUT /api/discounts/campaigns/[id] - Update a discount campaign
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to edit discounts
    const hasEditPermission = await hasPermission("edit_discounts");
    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to edit discount campaigns" },
        { status: 403 }
      );
    }
    
    const { id } = params;
    const data = await req.json();
    
    // Validate required fields
    if (!data.name || !data.startDate || !data.endDate || !data.discountIds || !Array.isArray(data.discountIds)) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Validate dates
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format" },
        { status: 400 }
      );
    }
    
    if (startDate > endDate) {
      return NextResponse.json(
        { error: "Start date must be before end date" },
        { status: 400 }
      );
    }
    
    // Check if campaign exists
    const existingCampaign = await prisma.discountCampaign.findUnique({
      where: {
        id,
      },
      include: {
        campaignDiscounts: true
      }
    });
    
    if (!existingCampaign) {
      return NextResponse.json(
        { error: "Campaign not found" },
        { status: 404 }
      );
    }
    
    // Verify all discounts exist
    const discounts = await prisma.discount.findMany({
      where: {
        id: {
          in: data.discountIds
        }
      }
    });
    
    if (discounts.length !== data.discountIds.length) {
      return NextResponse.json(
        { error: "One or more discount IDs are invalid" },
        { status: 400 }
      );
    }
    
    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the campaign
      const updatedCampaign = await tx.discountCampaign.update({
        where: {
          id,
        },
        data: {
          name: data.name,
          description: data.description,
          startDate,
          endDate
        }
      });
      
      // Get existing discount IDs
      const existingDiscountIds = existingCampaign.campaignDiscounts.map(cd => cd.discountId);
      
      // Determine which discounts to add and which to remove
      const discountIdsToAdd = data.discountIds.filter(id => !existingDiscountIds.includes(id));
      const discountIdsToRemove = existingDiscountIds.filter(id => !data.discountIds.includes(id));
      
      // Remove discounts that are no longer in the campaign
      if (discountIdsToRemove.length > 0) {
        await tx.campaignDiscount.deleteMany({
          where: {
            campaignId: id,
            discountId: {
              in: discountIdsToRemove
            }
          }
        });
      }
      
      // Add new discounts to the campaign
      for (const discountId of discountIdsToAdd) {
        await tx.campaignDiscount.create({
          data: {
            campaignId: id,
            discountId
          }
        });
        
        // Update discount dates if needed
        const discount = discounts.find(d => d.id === discountId);
        if (discount) {
          const updateData: any = {};
          
          // If discount has no start date or its start date is after campaign start date
          if (!discount.startDate || discount.startDate > startDate) {
            updateData.startDate = startDate;
          }
          
          // If discount has no end date or its end date is before campaign end date
          if (!discount.endDate || discount.endDate < endDate) {
            updateData.endDate = endDate;
          }
          
          // Only update if there are changes
          if (Object.keys(updateData).length > 0) {
            await tx.discount.update({
              where: { id: discountId },
              data: updateData
            });
          }
        }
      }
      
      return updatedCampaign;
    });
    
    // Clear cache
    cache.clear(/^discounts_/);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error updating discount campaign:", error);
    return NextResponse.json(
      { error: "Failed to update discount campaign" },
      { status: 500 }
    );
  }
}

// DELETE /api/discounts/campaigns/[id] - Delete a discount campaign
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to delete discounts
    const hasDeletePermission = await hasPermission("delete_discounts");
    if (!hasDeletePermission) {
      return NextResponse.json(
        { error: "You don't have permission to delete discount campaigns" },
        { status: 403 }
      );
    }
    
    const { id } = params;
    
    // Check if campaign exists
    const existingCampaign = await prisma.discountCampaign.findUnique({
      where: {
        id,
      }
    });
    
    if (!existingCampaign) {
      return NextResponse.json(
        { error: "Campaign not found" },
        { status: 404 }
      );
    }
    
    // Delete the campaign (cascade will delete campaign-discount relationships)
    await prisma.discountCampaign.delete({
      where: {
        id,
      }
    });
    
    // Clear cache
    cache.clear(/^discounts_/);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting discount campaign:", error);
    return NextResponse.json(
      { error: "Failed to delete discount campaign" },
      { status: 500 }
    );
  }
}
