"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DatePicker } from "@/components/ui/date-picker";
import { Loader2, ArrowLeft, Download, Search, RefreshCw, Plus, ArrowUp, ArrowDown } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { format } from "date-fns";

interface PaymentMethod {
  id: string;
  code: string;
  name: string;
  iconName: string;
  color: string;
  isActive: boolean;
  accountId: string | null;
  journalId: string | null;
  account: {
    id: string;
    name: string;
    code: string;
    balance: number;
  } | null;
  journal: {
    id: string;
    name: string;
    code: string;
  } | null;
}

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  type: "DEBIT" | "CREDIT";
  account: {
    id: string;
    code: string;
    name: string;
  };
  contact: {
    id: string;
    name: string;
    phone: string;
  } | null;
  reference: string | null;
  referenceType: string | null;
}

interface TransactionSummary {
  totalDebits: number;
  totalCredits: number;
  balance: number;
}

export default function PaymentMethodDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { id } = params;

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [summary, setSummary] = useState<TransactionSummary>({
    totalDebits: 0,
    totalCredits: 0,
    balance: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [period, setPeriod] = useState<string>("month");
  const [activeTab, setActiveTab] = useState<string>("transactions");

  // Fetch payment method details
  useEffect(() => {
    const fetchPaymentMethod = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/accounting/payment-methods/${id}`);
        if (response.ok) {
          const data = await response.json();
          setPaymentMethod(data.data);
        } else {
          toast.error("Failed to load payment method details");
          router.push("/dashboard/accounting/payment-methods");
        }
      } catch (error) {
        console.error("Error fetching payment method:", error);
        toast.error("Failed to load payment method details");
        router.push("/dashboard/accounting/payment-methods");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchPaymentMethod();
    }
  }, [id, router]);

  // Fetch transactions when payment method, dates, or period changes
  useEffect(() => {
    if (paymentMethod) {
      fetchTransactions();
    }
  }, [paymentMethod, startDate, endDate, period]);

  // Handle period change
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
    const today = new Date();
    
    switch (value) {
      case "day":
        setStartDate(today);
        setEndDate(today);
        break;
      case "week":
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        setStartDate(weekStart);
        setEndDate(today);
        break;
      case "month":
        setStartDate(new Date(today.getFullYear(), today.getMonth(), 1));
        setEndDate(today);
        break;
      case "year":
        setStartDate(new Date(today.getFullYear(), 0, 1));
        setEndDate(today);
        break;
      case "custom":
        // Keep current dates for custom period
        break;
    }
  };

  // Fetch transactions
  const fetchTransactions = async () => {
    if (!paymentMethod) return;
    
    setIsLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("paymentMethodId", paymentMethod.id);
      
      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }
      
      if (endDate) {
        params.append("endDate", endDate.toISOString());
      }
      
      params.append("period", period);
      
      const response = await fetch(`/api/accounting/payment-methods/transactions?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setTransactions(data.data.transactions || []);
        setSummary(data.data.summary || {
          totalDebits: 0,
          totalCredits: 0,
          balance: 0,
        });
      } else {
        toast.error("Failed to load transactions");
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error("Failed to load transactions");
    } finally {
      setIsLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Export to CSV
  const exportToCSV = () => {
    if (transactions.length === 0) {
      toast.error("No transactions to export");
      return;
    }

    // Create CSV content
    const headers = ["Date", "Description", "Type", "Amount", "Account", "Contact", "Reference"];
    const rows = transactions.map(t => [
      format(new Date(t.date), "yyyy-MM-dd"),
      t.description,
      t.type,
      t.amount.toString(),
      `${t.account.name} (${t.account.code})`,
      t.contact ? t.contact.name : "-",
      t.reference || "-"
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map(row => row.join(","))
    ].join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `${paymentMethod?.name}_transactions_${format(new Date(), "yyyyMMdd")}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (isLoading && !paymentMethod) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500 text-lg">Loading payment method details...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/dashboard/accounting/payment-methods">
            <Button variant="outline" size="icon" className="mr-4">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">{paymentMethod?.name}</h1>
          {paymentMethod?.code && (
            <span className="ml-2 text-sm text-gray-500">({paymentMethod.code})</span>
          )}
        </div>
        <Button onClick={exportToCSV} disabled={transactions.length === 0}>
          <Download className="h-4 w-4 mr-2" />
          Export to CSV
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Account</p>
                <h3 className="text-lg font-medium mt-1">
                  {paymentMethod?.account ? (
                    <>
                      {paymentMethod.account.name}
                      <span className="text-sm text-gray-500 ml-1">
                        ({paymentMethod.account.code})
                      </span>
                    </>
                  ) : (
                    <span className="text-gray-500">No account linked</span>
                  )}
                </h3>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Journal</p>
                <h3 className="text-lg font-medium mt-1">
                  {paymentMethod?.journal ? (
                    <>
                      {paymentMethod.journal.name}
                      <span className="text-sm text-gray-500 ml-1">
                        ({paymentMethod.journal.code})
                      </span>
                    </>
                  ) : (
                    <span className="text-gray-500">No journal linked</span>
                  )}
                </h3>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Balance</p>
                <h3 className="text-2xl font-bold mt-1">
                  {formatCurrency(summary.balance)}
                </h3>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Transactions</CardTitle>
          <CardDescription>
            View all transactions for this payment method
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium">Period</label>
                <Select value={period} onValueChange={handlePeriodChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="day">Today</SelectItem>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                    <SelectItem value="year">This Year</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium">Start Date</label>
                <DatePicker date={startDate} setDate={setStartDate} />
              </div>
              
              <div>
                <label className="text-sm font-medium">End Date</label>
                <DatePicker date={endDate} setDate={setEndDate} />
              </div>
              
              <div className="flex items-end">
                <Button onClick={fetchTransactions} disabled={isLoading}>
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  Refresh
                </Button>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium">
                  Transaction List
                  <span className="ml-2 text-sm font-normal text-gray-500">
                    {startDate && endDate && `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`}
                  </span>
                </h3>
              </div>
              <div className="flex space-x-4">
                <div className="text-right">
                  <div className="text-sm text-gray-500">Total Debits</div>
                  <div className="text-lg font-semibold text-green-600">{formatCurrency(summary.totalDebits)}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-500">Total Credits</div>
                  <div className="text-lg font-semibold text-red-600">{formatCurrency(summary.totalCredits)}</div>
                </div>
              </div>
            </div>
            
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                <span className="ml-2 text-gray-500">Loading transactions...</span>
              </div>
            ) : transactions.length > 0 ? (
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Date</th>
                        <th className="px-4 py-3 text-left">Description</th>
                        <th className="px-4 py-3 text-left">Account</th>
                        <th className="px-4 py-3 text-left">Contact</th>
                        <th className="px-4 py-3 text-left">Reference</th>
                        <th className="px-4 py-3 text-right">Debit</th>
                        <th className="px-4 py-3 text-right">Credit</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map(transaction => (
                        <tr key={transaction.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3 text-left">{format(new Date(transaction.date), 'MMM d, yyyy')}</td>
                          <td className="px-4 py-3 text-left">{transaction.description}</td>
                          <td className="px-4 py-3 text-left">{transaction.account.name} ({transaction.account.code})</td>
                          <td className="px-4 py-3 text-left">{transaction.contact?.name || '-'}</td>
                          <td className="px-4 py-3 text-left">{transaction.reference || '-'}</td>
                          <td className="px-4 py-3 text-right">
                            {transaction.type === 'DEBIT' ? formatCurrency(transaction.amount) : '-'}
                          </td>
                          <td className="px-4 py-3 text-right">
                            {transaction.type === 'CREDIT' ? formatCurrency(transaction.amount) : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <div className="text-center p-4 text-gray-500">
                No transactions found for this payment method in the selected period.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
