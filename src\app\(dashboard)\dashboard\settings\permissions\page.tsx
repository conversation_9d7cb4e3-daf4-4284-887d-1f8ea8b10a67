"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import Link from "next/link";

interface Permission {
  id: string;
  name: string;
  description: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: Permission[];
}

export default function PermissionsPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [userPermissions, setUserPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [newPermission, setNewPermission] = useState({ name: "", description: "" });
  const [isCreating, setIsCreating] = useState(false);

  // Group permissions by type
  const groupedPermissions = permissions.reduce((groups, permission) => {
    const type = permission.name.split('_')[0];
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(permission);
    return groups;
  }, {} as Record<string, Permission[]>);

  // Fetch users and permissions
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch users
        const usersResponse = await fetch('/api/users');
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setUsers(usersData);
        }

        // Fetch permissions
        const permissionsResponse = await fetch('/api/permissions');
        if (permissionsResponse.ok) {
          const permissionsData = await permissionsResponse.json();
          setPermissions(permissionsData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load users and permissions");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Update user permissions when user selection changes
  useEffect(() => {
    if (selectedUser) {
      const user = users.find(u => u.id === selectedUser);
      if (user) {
        setUserPermissions(user.permissions.map(p => p.id));
      }
    } else {
      setUserPermissions([]);
    }
  }, [selectedUser, users]);

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string) => {
    setUserPermissions(prev => {
      if (prev.includes(permissionId)) {
        return prev.filter(id => id !== permissionId);
      } else {
        return [...prev, permissionId];
      }
    });
  };

  // Handle permission group toggle
  const handleGroupToggle = (groupPermissions: Permission[]) => {
    const groupIds = groupPermissions.map(p => p.id);
    const allSelected = groupPermissions.every(p => userPermissions.includes(p.id));

    if (allSelected) {
      // Remove all permissions in this group
      setUserPermissions(prev => prev.filter(id => !groupIds.includes(id)));
    } else {
      // Add all permissions in this group
      setUserPermissions(prev => {
        const newPermissions = [...prev];
        groupIds.forEach(id => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        });
        return newPermissions;
      });
    }
  };

  // Save user permissions
  const savePermissions = async () => {
    if (!selectedUser) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/users/${selectedUser}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissionIds: userPermissions }),
      });

      if (response.ok) {
        toast.success("Permissions updated successfully");

        // Update local state
        setUsers(prev => prev.map(user => {
          if (user.id === selectedUser) {
            return {
              ...user,
              permissions: permissions.filter(p => userPermissions.includes(p.id)),
            };
          }
          return user;
        }));
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to update permissions");
      }
    } catch (error) {
      console.error("Error updating permissions:", error);
      toast.error("Failed to update permissions");
    } finally {
      setIsSaving(false);
    }
  };

  // Create new permission
  const createPermission = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newPermission.name) {
      toast.error("Permission name is required");
      return;
    }

    // Validate permission name format
    const namePattern = /^[a-z]+(_[a-z]+)+$/;
    if (!namePattern.test(newPermission.name)) {
      toast.error("Permission name must be in format: action_resource (e.g., view_sales)");
      return;
    }

    // Check if permission already exists
    const permissionExists = permissions.some(p => p.name === newPermission.name);
    if (permissionExists) {
      toast.error("Permission with this name already exists");
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch('/api/permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newPermission),
      });

      if (response.ok) {
        const createdPermission = await response.json();
        setPermissions(prev => [...prev, createdPermission]);
        setNewPermission({ name: "", description: "" });
        toast.success("Permission created successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to create permission");
      }
    } catch (error) {
      console.error("Error creating permission:", error);
      toast.error("Failed to create permission");
    } finally {
      setIsCreating(false);
    }
  };

  // Filter users by search term
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <p className="ml-4 text-gray-600 font-medium">Loading permissions...</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-blue-500 to-teal-500 p-2 rounded-lg mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                  Permissions Management
                </h2>
                <p className="mt-1 text-sm text-gray-500">
                  Manage user permissions and create new permissions
                </p>
              </div>
            </div>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <Link
              href="/dashboard/settings"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to Settings
            </Link>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Selection Panel */}
        <div>
          <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Select User
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Choose a user to manage their permissions
              </p>
            </div>
            <div className="px-4 py-5 sm:p-6">
              <div className="mb-4">
                <label htmlFor="search" className="sr-only">Search Users</label>
                <div className="relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    id="search"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search users..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="mt-4 space-y-2 max-h-96 overflow-y-auto">
                {filteredUsers.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No users found</p>
                ) : (
                  filteredUsers.map((user) => (
                    <button
                      key={user.id}
                      onClick={() => setSelectedUser(user.id)}
                      className={`w-full text-left px-4 py-3 rounded-md transition-colors ${
                        selectedUser === user.id
                          ? 'bg-blue-50 border-blue-500 border'
                          : 'hover:bg-gray-50 border border-gray-200'
                      }`}
                    >
                      <div className="font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                      <div className="text-xs mt-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.role === 'ADMIN'
                            ? 'bg-purple-100 text-purple-800'
                            : user.role === 'MANAGER'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-green-100 text-green-800'
                        }`}>
                          {user.role}
                        </span>
                        <span className="ml-2 text-gray-500">
                          {user.permissions.length} permissions
                        </span>
                      </div>
                    </button>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Create New Permission Panel */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Create New Permission
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Add a new permission to the system
              </p>
            </div>
            <div className="px-4 py-5 sm:p-6">
              <form onSubmit={createPermission} className="space-y-4">
                <div>
                  <label htmlFor="permissionName" className="block text-sm font-medium text-gray-700">
                    Permission Name <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="permissionName"
                      value={newPermission.name}
                      onChange={(e) => setNewPermission({...newPermission, name: e.target.value})}
                      placeholder="e.g., view_maintenance"
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      required
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Format: action_resource (e.g., view_sales, edit_users)
                  </p>
                </div>

                <div>
                  <label htmlFor="permissionDescription" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="permissionDescription"
                      value={newPermission.description}
                      onChange={(e) => setNewPermission({...newPermission, description: e.target.value})}
                      placeholder="e.g., View maintenance services"
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={isCreating}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-500 to-teal-500 hover:from-blue-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {isCreating ? "Creating..." : "Create Permission"}
                  </button>
                </div>
              </form>

              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Or run script to create default permissions</h4>
                <button
                  type="button"
                  onClick={() => {
                    toast.info("Running script to create default permissions...");
                    fetch('/api/permissions/create-defaults', { method: 'POST' })
                      .then(response => {
                        if (response.ok) {
                          toast.success("Default permissions created successfully");
                          // Refresh permissions list
                          fetch('/api/permissions')
                            .then(res => res.json())
                            .then(data => setPermissions(data))
                            .catch(err => console.error("Error refreshing permissions:", err));
                        } else {
                          toast.error("Failed to create default permissions");
                        }
                      })
                      .catch(error => {
                        console.error("Error creating default permissions:", error);
                        toast.error("Failed to create default permissions");
                      });
                  }}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Create Default Permissions
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Permissions Panel */}
        <div className="lg:col-span-2 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              User Permissions
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              {selectedUser
                ? `Manage permissions for ${users.find(u => u.id === selectedUser)?.name}`
                : "Select a user to manage their permissions"}
            </p>
          </div>

          {!selectedUser ? (
            <div className="px-4 py-12 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No user selected</h3>
              <p className="mt-1 text-sm text-gray-500">
                Select a user from the list to manage their permissions
              </p>
            </div>
          ) : (
            <div className="px-4 py-5 sm:p-6">
              <div className="mb-4 flex justify-between items-center">
                <h4 className="text-md font-medium text-gray-900">Permissions</h4>
                <button
                  type="button"
                  onClick={savePermissions}
                  disabled={isSaving}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-500 to-teal-500 hover:from-blue-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {isSaving ? "Saving..." : "Save Permissions"}
                </button>
              </div>

              <div className="space-y-6">
                {Object.entries(groupedPermissions).map(([group, groupPermissions]) => {
                  const allSelected = groupPermissions.every(p => userPermissions.includes(p.id));
                  const someSelected = groupPermissions.some(p => userPermissions.includes(p.id));

                  return (
                    <div key={group} className="border rounded-md p-4">
                      <div className="flex items-center mb-2">
                        <input
                          id={`group-${group}`}
                          type="checkbox"
                          checked={allSelected}
                          onChange={() => handleGroupToggle(groupPermissions)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          ref={el => {
                            if (el) {
                              el.indeterminate = someSelected && !allSelected;
                            }
                          }}
                        />
                        <label htmlFor={`group-${group}`} className="ml-2 block text-sm font-medium text-gray-900 capitalize">
                          {group} Permissions
                        </label>
                      </div>
                      <div className="ml-6 grid grid-cols-1 md:grid-cols-2 gap-2">
                        {groupPermissions.map((permission) => (
                          <div key={permission.id} className="flex items-center group">
                            <input
                              id={permission.id}
                              type="checkbox"
                              checked={userPermissions.includes(permission.id)}
                              onChange={() => handlePermissionToggle(permission.id)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <div className="ml-2 relative">
                              <label htmlFor={permission.id} className="block text-sm text-gray-700 hover:text-blue-600">
                                {permission.description || permission.name}
                              </label>
                              {permission.description && permission.name !== permission.description && (
                                <span className="text-xs text-gray-500 block">
                                  {permission.name}
                                </span>
                              )}
                              <div className="hidden group-hover:block absolute z-10 left-0 mt-1 w-64 p-2 bg-white text-gray-800 text-xs rounded shadow-lg border border-gray-200">
                                <strong>Permission:</strong> {permission.name}<br />
                                <strong>Description:</strong> {permission.description || "No description available"}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
