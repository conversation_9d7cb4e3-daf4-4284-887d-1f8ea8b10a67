import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// GET /api/accounts/:id - Get an account by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    const account = await db.account.findUnique({
      where: {
        id,
      },
      include: {
        branch: true,
      },
    });

    if (!account) {
      return NextResponse.json(
        { error: "Account not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(account);
  } catch (error) {
    console.error("Error fetching account:", error);
    return NextResponse.json(
      { error: "Failed to fetch account" },
      { status: 500 }
    );
  }
}

// PATCH /api/accounts/:id - Update an account
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    const data = await req.json();

    // Check if account exists
    const existingAccount = await db.account.findUnique({
      where: {
        id,
      },
    });

    if (!existingAccount) {
      return NextResponse.json(
        { error: "Account not found" },
        { status: 404 }
      );
    }

    // Update the account
    // Convert empty branchId string to null
    const branchId = data.branchId === "" ? null : data.branchId;
    const parentId = data.parentId === "" ? null : data.parentId;

    const account = await db.account.update({
      where: {
        id,
      },
      data: {
        name: data.name,
        type: data.type,
        balance: data.balance,
        branchId: branchId,
        parentId: parentId,
        isDefault: data.isDefault,
        code: data.code,
        accountNumber: data.accountNumber || existingAccount.accountNumber,
      },
    });

    return NextResponse.json(account);
  } catch (error) {
    console.error("Error updating account:", error);
    return NextResponse.json(
      { error: "Failed to update account" },
      { status: 500 }
    );
  }
}

// DELETE /api/accounts/:id - Delete an account
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Check if account exists
    const existingAccount = await db.account.findUnique({
      where: {
        id,
      },
    });

    if (!existingAccount) {
      return NextResponse.json(
        { error: "Account not found" },
        { status: 404 }
      );
    }

    // Check if account has transactions
    const transactionCount = await db.transaction.count({
      where: {
        accountId: id,
      },
    });

    if (transactionCount > 0) {
      return NextResponse.json(
        { error: "Cannot delete account with transactions" },
        { status: 400 }
      );
    }

    // Delete the account
    await db.account.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting account:", error);
    return NextResponse.json(
      { error: "Failed to delete account" },
      { status: 500 }
    );
  }
}
