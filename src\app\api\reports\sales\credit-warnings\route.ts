import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// GET /api/reports/sales/credit-warnings - Get credit limit warnings
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get customers with credit limits
    const customers = await db.contact.findMany({
      where: {
        isCustomer: true,
        creditLimit: {
          gt: 0, // Only customers with credit limits set
        },
        balance: {
          gt: 0, // Only customers with positive balance
        },
      },
      select: {
        id: true,
        name: true,
        balance: true,
        creditLimit: true,
        creditPeriod: true,
      },
      orderBy: {
        balance: 'desc', // Order by highest balance first
      },
    });

    // Filter customers who are approaching or exceeding their credit limit
    const warnings = customers
      .filter(customer => (customer.balance / customer.creditLimit) >= 0.7) // 70% or more of credit limit used
      .map(customer => ({
        contactId: customer.id,
        contactName: customer.name,
        balance: customer.balance,
        creditLimit: customer.creditLimit,
        creditPeriod: customer.creditPeriod,
        usagePercent: Math.round((customer.balance / customer.creditLimit) * 100),
      }))
      .sort((a, b) => b.usagePercent - a.usagePercent); // Sort by highest usage percent first

    return NextResponse.json({
      warnings,
    });
  } catch (error) {
    console.error("Error generating credit warnings:", error);
    return NextResponse.json(
      { error: "Failed to generate credit warnings" },
      { status: 500 }
    );
  }
}
