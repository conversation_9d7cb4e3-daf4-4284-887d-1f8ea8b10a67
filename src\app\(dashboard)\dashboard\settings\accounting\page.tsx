"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Save, RefreshCw, Plus, AlertCircle, CheckCircle2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";

interface Journal {
  id: string;
  code: string;
  name: string;
  type: string;
  paymentMethod: string | null;
  isActive: boolean;
}

interface FiscalYear {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  isClosed: boolean;
}

interface FiscalPeriod {
  id: string;
  fiscalYearId: string;
  name: string;
  startDate: string;
  endDate: string;
  isClosed: boolean;
}

export default function AccountingSettingsPage() {
  const [activeTab, setActiveTab] = useState("journals");
  const [journals, setJournals] = useState<Journal[]>([]);
  const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
  const [fiscalPeriods, setFiscalPeriods] = useState<FiscalPeriod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializeSuccess, setInitializeSuccess] = useState(false);
  const [initializeError, setInitializeError] = useState<string | null>(null);
  const [showAddJournalDialog, setShowAddJournalDialog] = useState(false);
  const [showAddFiscalYearDialog, setShowAddFiscalYearDialog] = useState(false);

  // Form state
  const [journalForm, setJournalForm] = useState({
    code: "",
    name: "",
    type: "GENERAL",
    paymentMethod: "",
  });

  const [fiscalYearForm, setFiscalYearForm] = useState({
    name: "",
    startDate: "",
    endDate: "",
  });

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch journals
        const journalsResponse = await fetch("/api/accounting/journals");
        if (journalsResponse.ok) {
          const journalsData = await journalsResponse.json();
          setJournals(journalsData.data || []);
        }

        // Fetch fiscal years
        const fiscalYearsResponse = await fetch("/api/accounting/fiscal-years");
        if (fiscalYearsResponse.ok) {
          const fiscalYearsData = await fiscalYearsResponse.json();
          setFiscalYears(fiscalYearsData.data || []);

          // If there are fiscal years, fetch periods for the first one
          if (fiscalYearsData.data && fiscalYearsData.data.length > 0) {
            fetchFiscalPeriods(fiscalYearsData.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching accounting settings:", error);
        toast.error("Failed to load accounting settings");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch fiscal periods for a specific fiscal year
  const fetchFiscalPeriods = async (fiscalYearId: string) => {
    try {
      const response = await fetch(`/api/accounting/fiscal-years/${fiscalYearId}/periods`);
      if (response.ok) {
        const data = await response.json();
        setFiscalPeriods(data.data || []);
      }
    } catch (error) {
      console.error("Error fetching fiscal periods:", error);
      toast.error("Failed to load fiscal periods");
    }
  };

  // Handle journal form input changes
  const handleJournalInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setJournalForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle journal form select changes
  const handleJournalSelectChange = (name: string, value: string) => {
    setJournalForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle fiscal year form input changes
  const handleFiscalYearInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFiscalYearForm(prev => ({ ...prev, [name]: value }));
  };

  // Create journal
  const createJournal = async () => {
    try {
      const response = await fetch("/api/accounting/journals", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: journalForm.code,
          name: journalForm.name,
          type: journalForm.type,
          paymentMethod: journalForm.paymentMethod || null,
        }),
      });

      if (response.ok) {
        const newJournal = await response.json();
        setJournals(prev => [...prev, newJournal.data]);
        setShowAddJournalDialog(false);
        setJournalForm({
          code: "",
          name: "",
          type: "GENERAL",
          paymentMethod: "",
        });
        toast.success("Journal created successfully");
      } else {
        const error = await response.json();
        toast.error(`Error creating journal: ${error.error}`);
      }
    } catch (error) {
      console.error("Error creating journal:", error);
      toast.error("Failed to create journal");
    }
  };

  // Create fiscal year
  const createFiscalYear = async () => {
    try {
      const response = await fetch("/api/accounting/fiscal-years", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: fiscalYearForm.name,
          startDate: fiscalYearForm.startDate,
          endDate: fiscalYearForm.endDate,
        }),
      });

      if (response.ok) {
        const newFiscalYear = await response.json();
        setFiscalYears(prev => [...prev, newFiscalYear.data]);
        setShowAddFiscalYearDialog(false);
        setFiscalYearForm({
          name: "",
          startDate: "",
          endDate: "",
        });
        toast.success("Fiscal year created successfully");
      } else {
        const error = await response.json();
        toast.error(`Error creating fiscal year: ${error.error}`);
      }
    } catch (error) {
      console.error("Error creating fiscal year:", error);
      toast.error("Failed to create fiscal year");
    }
  };

  // Handle initialize accounting
  const handleInitializeAccounting = async () => {
    if (!confirm("هل أنت متأكد من أنك تريد إعادة تهيئة وحدة المحاسبة؟ سيؤدي ذلك إلى التأكد من وجود جميع الحسابات واليوميات المطلوبة.")) {
      return;
    }

    setIsInitializing(true);
    setInitializeSuccess(false);
    setInitializeError(null);

    try {
      const response = await fetch("/api/accounting/initialize", {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "فشل في تهيئة وحدة المحاسبة");
      }

      const data = await response.json();
      setInitializeSuccess(true);
      toast.success("تم تهيئة وحدة المحاسبة بنجاح");
    } catch (error) {
      console.error("Error initializing accounting module:", error);
      setInitializeError(error instanceof Error ? error.message : "حدث خطأ");
      toast.error(error instanceof Error ? error.message : "فشل في تهيئة وحدة المحاسبة");
    } finally {
      setIsInitializing(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get journal type display name
  const getJournalTypeDisplay = (type: string) => {
    switch (type) {
      case "CASH":
        return "نقدي";
      case "VODAFONE_CASH":
        return "فودافون كاش";
      case "BANK_TRANSFER":
        return "تحويل بنكي";
      case "VISA":
        return "بطاقة ائتمان";
      case "CUSTOMER_ACCOUNT":
        return "حساب العميل";
      case "GENERAL":
        return "عام";
      default:
        return type;
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">إعدادات المحاسبة</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="journals">اليوميات المحاسبية</TabsTrigger>
          <TabsTrigger value="fiscal">السنة المالية والفترات</TabsTrigger>
          <TabsTrigger value="tools">أدوات النظام</TabsTrigger>
        </TabsList>

        <TabsContent value="journals" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>اليوميات المحاسبية</CardTitle>
              <Button onClick={() => setShowAddJournalDialog(true)}>
                <Plus className="h-4 w-4 ml-2" />
                إضافة يومية جديدة
              </Button>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                  <span className="mr-2 text-gray-500">جاري التحميل...</span>
                </div>
              ) : journals.length > 0 ? (
                <div className="rounded-md border overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="bg-gray-100 border-b">
                          <th className="px-4 py-3 text-right">الكود</th>
                          <th className="px-4 py-3 text-right">الاسم</th>
                          <th className="px-4 py-3 text-right">النوع</th>
                          <th className="px-4 py-3 text-right">طريقة الدفع</th>
                          <th className="px-4 py-3 text-center">الحالة</th>
                        </tr>
                      </thead>
                      <tbody>
                        {journals.map(journal => (
                          <tr key={journal.id} className="border-b hover:bg-gray-50">
                            <td className="px-4 py-3 text-right">{journal.code}</td>
                            <td className="px-4 py-3 text-right">{journal.name}</td>
                            <td className="px-4 py-3 text-right">{getJournalTypeDisplay(journal.type)}</td>
                            <td className="px-4 py-3 text-right">{journal.paymentMethod ? getJournalTypeDisplay(journal.paymentMethod) : "-"}</td>
                            <td className="px-4 py-3 text-center">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${journal.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                {journal.isActive ? "نشط" : "غير نشط"}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center p-4 text-gray-500">
                  لا توجد يوميات محاسبية. قم بإضافة يومية جديدة.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fiscal" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>السنة المالية</CardTitle>
              <Button onClick={() => setShowAddFiscalYearDialog(true)}>
                <Plus className="h-4 w-4 ml-2" />
                إضافة سنة مالية جديدة
              </Button>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                  <span className="mr-2 text-gray-500">جاري التحميل...</span>
                </div>
              ) : fiscalYears.length > 0 ? (
                <div className="space-y-6">
                  <div className="rounded-md border overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="bg-gray-100 border-b">
                            <th className="px-4 py-3 text-right">الاسم</th>
                            <th className="px-4 py-3 text-right">تاريخ البداية</th>
                            <th className="px-4 py-3 text-right">تاريخ النهاية</th>
                            <th className="px-4 py-3 text-center">الحالة</th>
                          </tr>
                        </thead>
                        <tbody>
                          {fiscalYears.map(fiscalYear => (
                            <tr
                              key={fiscalYear.id}
                              className="border-b hover:bg-gray-50 cursor-pointer"
                              onClick={() => fetchFiscalPeriods(fiscalYear.id)}
                            >
                              <td className="px-4 py-3 text-right">{fiscalYear.name}</td>
                              <td className="px-4 py-3 text-right">{formatDate(fiscalYear.startDate)}</td>
                              <td className="px-4 py-3 text-right">{formatDate(fiscalYear.endDate)}</td>
                              <td className="px-4 py-3 text-center">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${fiscalYear.isClosed ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                                  {fiscalYear.isClosed ? "مغلقة" : "مفتوحة"}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {fiscalPeriods.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium mb-3">الفترات المالية</h3>
                      <div className="rounded-md border overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="bg-gray-100 border-b">
                                <th className="px-4 py-3 text-right">الاسم</th>
                                <th className="px-4 py-3 text-right">تاريخ البداية</th>
                                <th className="px-4 py-3 text-right">تاريخ النهاية</th>
                                <th className="px-4 py-3 text-center">الحالة</th>
                              </tr>
                            </thead>
                            <tbody>
                              {fiscalPeriods.map(period => (
                                <tr key={period.id} className="border-b hover:bg-gray-50">
                                  <td className="px-4 py-3 text-right">{period.name}</td>
                                  <td className="px-4 py-3 text-right">{formatDate(period.startDate)}</td>
                                  <td className="px-4 py-3 text-right">{formatDate(period.endDate)}</td>
                                  <td className="px-4 py-3 text-center">
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${period.isClosed ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                                      {period.isClosed ? "مغلقة" : "مفتوحة"}
                                    </span>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center p-4 text-gray-500">
                  لا توجد سنوات مالية. قم بإضافة سنة مالية جديدة.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tools" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>أدوات النظام</CardTitle>
              <CardDescription>أدوات صيانة نظام المحاسبة</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {initializeSuccess && (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">تم بنجاح</AlertTitle>
                  <AlertDescription className="text-green-700">
                    تم تهيئة وحدة المحاسبة بنجاح. تم إنشاء جميع الحسابات واليوميات المطلوبة.
                  </AlertDescription>
                </Alert>
              )}

              {initializeError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>خطأ</AlertTitle>
                  <AlertDescription>{initializeError}</AlertDescription>
                </Alert>
              )}

              <div className="border rounded-lg p-4 bg-gray-50">
                <h3 className="text-lg font-medium mb-2">تهيئة وحدة المحاسبة</h3>
                <p className="text-gray-600 mb-4">
                  سيؤدي هذا إلى التأكد من وجود جميع الحسابات واليوميات والفترات المالية المطلوبة في النظام.
                  استخدم هذا إذا كنت تقوم بإعداد النظام لأول مرة أو إذا كنت تواجه مشاكل مع حسابات أو يوميات مفقودة.
                </p>
                <Button
                  onClick={handleInitializeAccounting}
                  disabled={isInitializing}
                  className="flex items-center"
                >
                  {isInitializing ? (
                    <>
                      <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
                      جاري التهيئة...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="ml-2 h-4 w-4" />
                      تهيئة وحدة المحاسبة
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Journal Dialog */}
      <Dialog open={showAddJournalDialog} onOpenChange={setShowAddJournalDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إضافة يومية محاسبية جديدة</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="code">كود اليومية</Label>
                <Input
                  id="code"
                  name="code"
                  value={journalForm.code}
                  onChange={handleJournalInputChange}
                  placeholder="مثال: CASH"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">نوع اليومية</Label>
                <Select value={journalForm.type} onValueChange={(value) => handleJournalSelectChange("type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع اليومية" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CASH">نقدي</SelectItem>
                    <SelectItem value="VODAFONE_CASH">فودافون كاش</SelectItem>
                    <SelectItem value="BANK_TRANSFER">تحويل بنكي</SelectItem>
                    <SelectItem value="VISA">بطاقة ائتمان</SelectItem>
                    <SelectItem value="CUSTOMER_ACCOUNT">حساب العميل</SelectItem>
                    <SelectItem value="GENERAL">عام</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">اسم اليومية</Label>
              <Input
                id="name"
                name="name"
                value={journalForm.name}
                onChange={handleJournalInputChange}
                placeholder="اسم اليومية"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="paymentMethod">طريقة الدفع (اختياري)</Label>
              <Select value={journalForm.paymentMethod} onValueChange={(value) => handleJournalSelectChange("paymentMethod", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر طريقة الدفع (اختياري)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون طريقة دفع</SelectItem>
                  <SelectItem value="CASH">نقدي</SelectItem>
                  <SelectItem value="VODAFONE_CASH">فودافون كاش</SelectItem>
                  <SelectItem value="BANK_TRANSFER">تحويل بنكي</SelectItem>
                  <SelectItem value="VISA">بطاقة ائتمان</SelectItem>
                  <SelectItem value="CUSTOMER_ACCOUNT">حساب العميل</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddJournalDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={createJournal}>
              إضافة اليومية
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Fiscal Year Dialog */}
      <Dialog open={showAddFiscalYearDialog} onOpenChange={setShowAddFiscalYearDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إضافة سنة مالية جديدة</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">اسم السنة المالية</Label>
              <Input
                id="name"
                name="name"
                value={fiscalYearForm.name}
                onChange={handleFiscalYearInputChange}
                placeholder="مثال: السنة المالية 2023"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">تاريخ البداية</Label>
                <Input
                  id="startDate"
                  name="startDate"
                  type="date"
                  value={fiscalYearForm.startDate}
                  onChange={handleFiscalYearInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">تاريخ النهاية</Label>
                <Input
                  id="endDate"
                  name="endDate"
                  type="date"
                  value={fiscalYearForm.endDate}
                  onChange={handleFiscalYearInputChange}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddFiscalYearDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={createFiscalYear}>
              إضافة السنة المالية
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
