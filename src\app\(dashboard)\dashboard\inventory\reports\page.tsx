"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { format, subDays } from "date-fns";
import { Package, AlertTriangle, TrendingUp, TrendingDown, FileText, Filter, Calendar, Building } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, <PERSON>Chart, Line } from "recharts";

// Mock report data
const mockReportData = {
  totalInventoryValue: 375000.50,
  lowStockItems: 12,
  outOfStockItems: 5,
  inventoryTurnover: 4.2,
  inventoryByWarehouse: [
    { name: "Main Warehouse", value: 225000 },
    { name: "Downtown Warehouse", value: 150000 }
  ],
  inventoryByCategory: [
    { name: "Computers", value: 150000 },
    { name: "Components", value: 95000 },
    { name: "Accessories", value: 75000 },
    { name: "Software", value: 35000 },
    { name: "Others", value: 20000 }
  ],
  inventoryMovement: [
    { name: "Jan", inflow: 45000, outflow: 38000 },
    { name: "Feb", inflow: 48000, outflow: 42000 },
    { name: "Mar", inflow: 52000, outflow: 45000 },
    { name: "Apr", inflow: 49000, outflow: 47000 },
    { name: "May", inflow: 53000, outflow: 50000 },
    { name: "Jun", inflow: 55000, outflow: 52000 },
    { name: "Jul", inflow: 58000, outflow: 54000 },
    { name: "Aug", inflow: 60000, outflow: 56000 },
    { name: "Sep", inflow: 62000, outflow: 58000 },
    { name: "Oct", inflow: 65000, outflow: 60000 },
    { name: "Nov", inflow: 68000, outflow: 63000 },
    { name: "Dec", inflow: 70000, outflow: 65000 }
  ],
  lowStockItemsList: [
    { id: "1", name: "Dell XPS 13", sku: "DELL-XPS13", currentStock: 3, minStock: 5, category: "Computers", warehouse: "Main Warehouse" },
    { id: "2", name: "HP Printer Ink", sku: "HP-INK-123", currentStock: 8, minStock: 15, category: "Accessories", warehouse: "Downtown Warehouse" },
    { id: "3", name: "Logitech MX Master", sku: "LOG-MX-MASTER", currentStock: 4, minStock: 10, category: "Accessories", warehouse: "Main Warehouse" }
  ]
};

// Colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export default function InventoryReportsPage() {
  const [startDate, setStartDate] = useState<Date | undefined>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [selectedWarehouse, setSelectedWarehouse] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [reportData, setReportData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [warehouses, setWarehouses] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("summary");

  // Fetch warehouses and categories
  useEffect(() => {
    fetchWarehouses();
    fetchCategories();
  }, []);

  // Fetch warehouses
  const fetchWarehouses = async () => {
    try {
      const response = await fetch('/api/warehouses');
      if (response.ok) {
        const data = await response.json();
        setWarehouses([
          { id: "all", name: "All Warehouses" },
          ...data.map((warehouse: any) => ({ id: warehouse.id, name: warehouse.name }))
        ]);
      }
    } catch (error) {
      console.error("Error fetching warehouses:", error);
      setWarehouses([
        { id: "all", name: "All Warehouses" },
        { id: "warehouse1", name: "Main Warehouse" },
        { id: "warehouse2", name: "Downtown Warehouse" }
      ]);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories([
          { id: "all", name: "All Categories" },
          ...data.map((category: any) => ({ id: category.id, name: category.name }))
        ]);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      setCategories([
        { id: "all", name: "All Categories" },
        { id: "category1", name: "Computers" },
        { id: "category2", name: "Components" }
      ]);
    }
  };

  // Generate report
  const generateReport = async () => {
    setIsLoading(true);

    try {
      // Format dates for API
      const formattedStartDate = startDate ? format(startDate, 'yyyy-MM-dd') : '';
      const formattedEndDate = endDate ? format(endDate, 'yyyy-MM-dd') : '';

      // Fetch report data from API
      const response = await fetch(
        `/api/reports/inventory?startDate=${formattedStartDate}&endDate=${formattedEndDate}&warehouseId=${selectedWarehouse}&categoryId=${selectedCategory}`
      );

      if (response.ok) {
        const data = await response.json();
        setReportData(data.data || data);
      } else {
        console.error("Error fetching report:", await response.text());
        // Fallback to mock data if API fails
        setReportData(mockReportData);
      }
    } catch (error) {
      console.error("Error generating report:", error);
      // Fallback to mock data if API fails
      setReportData(mockReportData);
    } finally {
      setIsLoading(false);
    }
  };

  // Call generateReport when filters change
  useEffect(() => {
    if (startDate && endDate) {
      generateReport();
    }
  }, [startDate, endDate, selectedWarehouse, selectedCategory]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Inventory Reports</h2>
          <p className="text-muted-foreground">
            Analyze and track your inventory data
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" onClick={() => window.print()}>
            <FileText className="mr-2 h-4 w-4" />
            Print Report
          </Button>
          <Link href="/dashboard/inventory">
            <Button variant="outline">
              Back to Inventory
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Report Filters</CardTitle>
          <CardDescription>
            Filter your inventory data by date range, warehouse, and category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
              <DatePicker
                date={startDate}
                setDate={setStartDate}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
              <DatePicker
                date={endDate}
                setDate={setEndDate}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Warehouse</label>
              <Select value={selectedWarehouse} onValueChange={setSelectedWarehouse}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Warehouse" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses.map((warehouse) => (
                    <SelectItem key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="byWarehouse">By Warehouse</TabsTrigger>
          <TabsTrigger value="byCategory">By Category</TabsTrigger>
          <TabsTrigger value="lowStock">Low Stock</TabsTrigger>
        </TabsList>

        {/* Summary Tab */}
        <TabsContent value="summary">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Summary</CardTitle>
              <CardDescription>
                Overview of your inventory data for the selected period
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                </div>
              ) : reportData ? (
                <div className="space-y-6">
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-white overflow-hidden rounded-lg border border-blue-100 p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-blue-100 rounded-md p-3 mr-4">
                          <Package className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Total Inventory Value</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.totalInventoryValue)}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white overflow-hidden rounded-lg border border-yellow-100 p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-yellow-100 rounded-md p-3 mr-4">
                          <AlertTriangle className="h-6 w-6 text-yellow-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Low Stock Items</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {reportData.lowStockItems}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white overflow-hidden rounded-lg border border-red-100 p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-red-100 rounded-md p-3 mr-4">
                          <AlertTriangle className="h-6 w-6 text-red-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Out of Stock Items</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {reportData.outOfStockItems}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white overflow-hidden rounded-lg border border-green-100 p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-green-100 rounded-md p-3 mr-4">
                          <TrendingUp className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Inventory Turnover</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {reportData.inventoryTurnover.toFixed(1)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Inventory Movement Chart */}
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Inventory Movement</h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={reportData.inventoryMovement}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip formatter={(value) => formatCurrency(value as number)} />
                          <Legend />
                          <Line type="monotone" dataKey="inflow" name="Inventory In" stroke="#3895e7" activeDot={{ r: 8 }} />
                          <Line type="monotone" dataKey="outflow" name="Inventory Out" stroke="#ff8042" />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available. Please adjust your filters or try again.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* By Warehouse Tab */}
        <TabsContent value="byWarehouse">
          <Card>
            <CardHeader>
              <CardTitle>Inventory by Warehouse</CardTitle>
              <CardDescription>
                Breakdown of inventory value by warehouse
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                </div>
              ) : reportData ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Pie Chart */}
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={reportData.inventoryByWarehouse}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {reportData.inventoryByWarehouse.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  {/* Table */}
                  <div>
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warehouse</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.inventoryByWarehouse.map((warehouse: any, index: number) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{warehouse.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(warehouse.value)}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {((warehouse.value / reportData.totalInventoryValue) * 100).toFixed(2)}%
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available. Please adjust your filters or try again.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* By Category Tab */}
        <TabsContent value="byCategory">
          <Card>
            <CardHeader>
              <CardTitle>Inventory by Category</CardTitle>
              <CardDescription>
                Breakdown of inventory value by product category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                </div>
              ) : reportData ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Pie Chart */}
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={reportData.inventoryByCategory}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {reportData.inventoryByCategory.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  {/* Table */}
                  <div>
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.inventoryByCategory.map((category: any, index: number) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{category.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(category.value)}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {((category.value / reportData.totalInventoryValue) * 100).toFixed(2)}%
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available. Please adjust your filters or try again.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Low Stock Tab */}
        <TabsContent value="lowStock">
          <Card>
            <CardHeader>
              <CardTitle>Low Stock Items</CardTitle>
              <CardDescription>
                Items that are below their minimum stock level
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                </div>
              ) : reportData && reportData.lowStockItemsList && reportData.lowStockItemsList.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Stock</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warehouse</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {reportData.lowStockItemsList.map((item: any) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.sku}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.currentStock}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.minStock}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.category}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.warehouse}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              item.currentStock === 0
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {item.currentStock === 0 ? 'Out of Stock' : 'Low Stock'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No low stock items found. All inventory levels are adequate.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
