const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking for existing payment methods...');

    // Check if any payment method already exists
    const existingPaymentMethods = await prisma.paymentMethodSettings.findMany();

    if (existingPaymentMethods.length > 0) {
      console.log('Payment methods already exist. Skipping creation.');
      return;
    }

    console.log('Creating default payment methods...');

    // Default payment methods
    const defaultPaymentMethods = [
      {
        name: 'Cash',
        code: 'CASH',
        iconName: 'cash',
        color: '#3895e7',
        sequence: 1,
      },
      {
        name: 'Vodafone Cash',
        code: 'VODAFONE_CASH',
        iconName: 'mobile',
        color: '#e60000',
        sequence: 2,
      },
      {
        name: 'Bank Transfer',
        code: 'BANK_TRANSFER',
        iconName: 'bank',
        color: '#307aa8',
        sequence: 3,
      },
      {
        name: 'Credit Card',
        code: 'VISA',
        iconName: 'credit-card',
        color: '#1a1f71',
        sequence: 4,
      },
    ];

    // Create payment methods
    for (const method of defaultPaymentMethods) {
      await prisma.paymentMethodSettings.create({
        data: {
          id: uuidv4(),
          name: method.name,
          code: method.code,
          iconName: method.iconName,
          color: method.color,
          sequence: method.sequence,
          isActive: true,
          branchIds: [],
          updatedAt: new Date(),
        },
      });
    }

    console.log('Default payment methods created successfully');

    // Create default accounts for payment methods
    console.log('Creating default accounts...');

    // Create chart of accounts
    const defaultAccounts = [
      {
        name: 'Cash',
        code: '1010',
        type: 'ASSET',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Vodafone Cash',
        code: '1020',
        type: 'ASSET',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Bank Account',
        code: '1030',
        type: 'ASSET',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Credit Card',
        code: '1040',
        type: 'ASSET',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Accounts Receivable',
        code: '1200',
        type: 'ASSET',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Inventory',
        code: '1300',
        type: 'ASSET',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Accounts Payable',
        code: '2000',
        type: 'LIABILITY',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Sales Revenue',
        code: '4000',
        type: 'REVENUE',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Cost of Goods Sold',
        code: '5000',
        type: 'EXPENSE',
        balance: 0,
        isActive: true,
      },
    ];

    const createdAccounts = {};

    for (const account of defaultAccounts) {
      const createdAccount = await prisma.account.create({
        data: {
          id: uuidv4(),
          name: account.name,
          code: account.code,
          type: account.type,
          balance: account.balance,
          isActive: account.isActive,
        },
      });

      createdAccounts[account.name] = createdAccount;
    }

    console.log('Default accounts created successfully');

    // Create default journals
    console.log('Creating default journals...');

    const defaultJournals = [
      {
        name: 'Cash Journal',
        code: 'CASH',
        type: 'CASH',
        isActive: true,
      },
      {
        name: 'Vodafone Cash Journal',
        code: 'VODAFONE',
        type: 'CASH',
        isActive: true,
      },
      {
        name: 'Bank Journal',
        code: 'BANK',
        type: 'BANK',
        isActive: true,
      },
      {
        name: 'Credit Card Journal',
        code: 'VISA',
        type: 'CASH',
        isActive: true,
      },
      {
        name: 'Sales Journal',
        code: 'SALES',
        type: 'SALE',
        isActive: true,
      },
      {
        name: 'Purchase Journal',
        code: 'PURCHASE',
        type: 'PURCHASE',
        isActive: true,
      },
      {
        name: 'General Journal',
        code: 'GENERAL',
        type: 'GENERAL',
        isActive: true,
      },
    ];

    const createdJournals = {};

    for (const journal of defaultJournals) {
      const createdJournal = await prisma.journal.create({
        data: {
          id: uuidv4(),
          name: journal.name,
          code: journal.code,
          type: journal.type,
          isActive: journal.isActive,
        },
      });

      createdJournals[journal.name] = createdJournal;
    }

    console.log('Default journals created successfully');

    // Link payment methods to accounts and journals
    console.log('Linking payment methods to accounts and journals...');

    const paymentMethodsMap = {
      'CASH': {
        account: createdAccounts['Cash'],
        journal: createdJournals['Cash Journal'],
      },
      'VODAFONE_CASH': {
        account: createdAccounts['Vodafone Cash'],
        journal: createdJournals['Vodafone Cash Journal'],
      },
      'BANK_TRANSFER': {
        account: createdAccounts['Bank Account'],
        journal: createdJournals['Bank Journal'],
      },
      'VISA': {
        account: createdAccounts['Credit Card'],
        journal: createdJournals['Credit Card Journal'],
      },
    };

    for (const [code, links] of Object.entries(paymentMethodsMap)) {
      await prisma.paymentMethodSettings.updateMany({
        where: {
          code: code,
        },
        data: {
          accountId: links.account.id,
          journalId: links.journal.id,
        },
      });
    }

    console.log('Payment methods linked to accounts and journals successfully');

    console.log('Default payment methods setup completed successfully');
  } catch (error) {
    console.error('Error creating default payment methods:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
