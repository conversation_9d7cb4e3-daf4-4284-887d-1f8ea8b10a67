"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

export default function PurchasesPage() {
  const [selectedBranch, setSelectedBranch] = useState("all");
  const [dateRange, setDateRange] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [purchases, setPurchases] = useState([]);
  const [branches, setBranches] = useState([
    { id: "all", name: "All Branches" }
  ]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch branches and purchases data
  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (response.ok) {
          const data = await response.json();
          setBranches([
            { id: "all", name: "All Branches" },
            ...data.map((branch: any) => ({
              id: branch.id,
              name: branch.name
            }))
          ]);
        }
      } catch (error) {
        console.error("Error fetching branches:", error);
      }
    };

    fetchBranches();
  }, []);

  // Fetch purchases when filters change
  useEffect(() => {
    const fetchPurchases = async () => {
      setIsLoading(true);
      try {
        // Build URL with query parameters
        let url = '/api/purchases';
        const params = new URLSearchParams();

        if (selectedBranch !== 'all') {
          params.append('branchId', selectedBranch);
        }

        if (statusFilter !== 'all') {
          params.append('status', statusFilter);
        }

        // Add date range parameters if needed
        if (dateRange === 'today') {
          const today = new Date();
          params.append('startDate', today.toISOString().split('T')[0]);
          params.append('endDate', today.toISOString().split('T')[0] + 'T23:59:59.999Z');
        } else if (dateRange === 'week') {
          const today = new Date();
          const weekAgo = new Date();
          weekAgo.setDate(today.getDate() - 7);
          params.append('startDate', weekAgo.toISOString());
          params.append('endDate', today.toISOString());
        } else if (dateRange === 'month') {
          const today = new Date();
          const monthAgo = new Date();
          monthAgo.setMonth(today.getMonth() - 1);
          params.append('startDate', monthAgo.toISOString());
          params.append('endDate', today.toISOString());
        }

        // Add query parameters to URL if there are any
        if (params.toString()) {
          url += '?' + params.toString();
        }

        const response = await fetch(url);
        if (response.ok) {
          const data = await response.json();
          setPurchases(data);
        }
      } catch (error) {
        console.error("Error fetching purchases:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPurchases();
  }, [selectedBranch, dateRange, statusFilter]);

  // Filter purchases based on search query only (other filters are handled by the API)
  const filteredPurchases = purchases.filter((purchase: any) => {
    const searchMatch = searchQuery === "" ||
      purchase.invoiceNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      purchase.contact?.name.toLowerCase().includes(searchQuery.toLowerCase());

    return searchMatch;
  });

  // Calculate total purchases
  const totalPurchases = filteredPurchases.reduce((sum: number, purchase: any) => sum + (purchase.totalAmount || 0), 0);

  return (
    <div className="space-y-6">
      {/* Purchases Summary Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${statusFilter === "all" ? 'bg-indigo-700' : 'bg-indigo-500'} rounded-md p-3`}>
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-black">
                    Total Purchases
                  </dt>
                  <dd>
                    <div
                      className="text-lg font-medium text-black cursor-pointer hover:underline"
                      onClick={() => setStatusFilter("all")}
                    >
                      EGP {totalPurchases.toFixed(2)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${statusFilter === "COMPLETED" ? 'bg-green-700' : 'bg-green-500'} rounded-md p-3`}>
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-black">
                    Completed Purchases
                  </dt>
                  <dd>
                    <div
                      className="text-lg font-medium text-black cursor-pointer hover:underline"
                      onClick={() => setStatusFilter(statusFilter === "COMPLETED" ? "all" : "COMPLETED")}
                    >
                      {filteredPurchases.filter(purchase => purchase.status === "COMPLETED").length}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${statusFilter === "PENDING" ? 'bg-yellow-700' : 'bg-yellow-500'} rounded-md p-3`}>
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-black">
                    Pending Purchases
                  </dt>
                  <dd>
                    <div
                      className="text-lg font-medium text-black cursor-pointer hover:underline"
                      onClick={() => setStatusFilter(statusFilter === "PENDING" ? "all" : "PENDING")}
                    >
                      {filteredPurchases.filter(purchase => purchase.status === "PENDING").length}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${statusFilter === "CANCELLED" ? 'bg-red-700' : 'bg-red-500'} rounded-md p-3`}>
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-black">
                    Cancelled Purchases
                  </dt>
                  <dd>
                    <div
                      className="text-lg font-medium text-black cursor-pointer hover:underline"
                      onClick={() => setStatusFilter(statusFilter === "CANCELLED" ? "all" : "CANCELLED")}
                    >
                      {filteredPurchases.filter(purchase => purchase.status === "CANCELLED").length}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Purchases List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <h3 className="text-lg leading-6 font-medium text-black">
            Purchases {statusFilter !== "all" && `(${statusFilter})`}
          </h3>
          <div className="flex space-x-3">
            <Link
              href="/dashboard/purchases/reports"
              className="inline-flex items-center px-4 py-2 border-2 border-gray-300 rounded-md shadow-sm text-sm font-medium text-black bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Reports
            </Link>
            <Link
              href="/dashboard/purchases/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              New Purchase
            </Link>
          </div>
        </div>
        <div className="border-t border-gray-200">
          <div className="bg-gray-50 px-4 py-5 sm:px-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
              {/* Branch Filter */}
              <div>
                <label htmlFor="branch" className="block text-sm font-medium text-black">Branch</label>
                <select
                  id="branch"
                  name="branch"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  value={selectedBranch}
                  onChange={(e) => setSelectedBranch(e.target.value)}
                >
                  {branches.map((branch) => (
                    <option key={branch.id} value={branch.id} className="text-black font-medium">
                      {branch.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Date Range Filter */}
              <div>
                <label htmlFor="date-range" className="block text-sm font-medium text-black">Date Range</label>
                <select
                  id="date-range"
                  name="date-range"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base font-medium text-black border-2 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                >
                  <option value="all" className="text-black font-medium">All Time</option>
                  <option value="today" className="text-black font-medium">Today</option>
                  <option value="week" className="text-black font-medium">Last 7 Days</option>
                  <option value="month" className="text-black font-medium">Last 30 Days</option>
                </select>
              </div>

              {/* Search */}
              <div className="sm:col-span-2">
                <label htmlFor="search" className="block text-sm font-medium text-black">Search</label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                    type="text"
                    name="search"
                    id="search"
                    className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pr-10 sm:text-sm font-medium text-black border-2 border-gray-300 rounded-md"
                    placeholder="Search by invoice # or supplier"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg
                      className="h-5 w-5 text-gray-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="px-4 py-5 sm:px-6">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Invoice #
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Supplier
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Branch
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Date
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Total
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Payment
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {isLoading ? (
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-black text-center" colSpan={8}>
                        Loading...
                      </td>
                    </tr>
                  ) : filteredPurchases.length > 0 ? (
                    filteredPurchases.map((purchase: any) => (
                      <tr key={purchase.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-indigo-600">
                          <Link href={`/dashboard/purchases/${purchase.id}`}>
                            {purchase.invoiceNumber || 'N/A'}
                          </Link>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {purchase.contact?.name || 'Unknown Supplier'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {purchase.branch?.name || 'Unknown Branch'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {purchase.date ? new Date(purchase.date).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          EGP {purchase.totalAmount?.toFixed(2) || '0.00'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              purchase.status === "COMPLETED"
                                ? "bg-green-100 text-green-800"
                                : purchase.status === "PENDING"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {purchase.status || 'PENDING'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              purchase.paymentStatus === "PAID"
                                ? "bg-green-100 text-green-800"
                                : purchase.paymentStatus === "PARTIALLY_PAID"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {purchase.paymentStatus || 'UNPAID'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          <Link
                            href={`/dashboard/purchases/${purchase.id}`}
                            className="text-indigo-600 hover:text-indigo-900 mr-3"
                          >
                            View
                          </Link>
                          <Link
                            href={`/dashboard/purchases/${purchase.id}/edit`}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            Edit
                          </Link>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-black text-center" colSpan={8}>
                        No purchases found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
