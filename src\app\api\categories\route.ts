import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/categories - Get all categories
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view categories
    // For categories, we'll use the view_products permission
    // If the user doesn't have this permission, we'll still allow access for basic functionality
    const hasViewPermission = await hasPermission("view_products");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      console.warn("User without view_products permission is accessing categories");
      // We'll continue anyway to avoid breaking the UI
    }

    // Get query parameters
    const url = new URL(req.url);
    const search = url.searchParams.get("search");

    // Build filter object
    const filter: any = {};

    // Add search filter if provided
    if (search) {
      filter.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get categories from database
    const categories = await db.category.findMany({
      where: filter,
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      { error: "Failed to fetch categories" },
      { status: 500 }
    );
  }
}

// POST /api/categories - Create a new category
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add categories
    const hasAddPermission = await hasPermission("add_products");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add categories" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      );
    }

    // Check if category with the same name already exists
    const existingCategory = await db.category.findFirst({
      where: {
        name: {
          equals: data.name,
          mode: "insensitive",
        },
      },
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: "A category with this name already exists" },
        { status: 400 }
      );
    }

    // Create the category
    const category = await db.category.create({
      data: {
        name: data.name,
        description: data.description || "",
        type: data.type || "PRODUCT",
      },
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create category" },
      { status: 500 }
    );
  }
}
