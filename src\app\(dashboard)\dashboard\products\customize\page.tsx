"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface Product {
  id: string;
  name: string;
  isCustomizable: boolean;
  isComponent: boolean;
  componentType: string | null;
}

export default function CustomizeProductsPage() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [componentTypes, setComponentTypes] = useState<any[]>([]);
  const [newComponentType, setNewComponentType] = useState("");

  // Fetch products and component types
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (!productsResponse.ok) {
          throw new Error(`Failed to fetch products: ${productsResponse.statusText}`);
        }
        const productsData = await productsResponse.json();
        setProducts(productsData);

        // Fetch component types
        const typesResponse = await fetch('/api/component-types');
        if (!typesResponse.ok) {
          throw new Error(`Failed to fetch component types: ${typesResponse.statusText}`);
        }
        const typesData = await typesResponse.json();
        setComponentTypes(typesData);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Toggle customizable status
  const toggleCustomizable = async (productId: string, currentValue: boolean) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isCustomizable: !currentValue,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update product: ${response.statusText}`);
      }

      // Update local state
      setProducts(products.map(product => 
        product.id === productId 
          ? { ...product, isCustomizable: !currentValue } 
          : product
      ));
    } catch (error) {
      console.error("Error updating product:", error);
      alert(error instanceof Error ? error.message : "An unknown error occurred");
    }
  };

  // Toggle component status
  const toggleComponent = async (productId: string, currentValue: boolean) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isComponent: !currentValue,
          componentType: !currentValue ? (componentTypes.length > 0 ? componentTypes[0].name : null) : null,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update product: ${response.statusText}`);
      }

      // Update local state
      setProducts(products.map(product => 
        product.id === productId 
          ? { 
              ...product, 
              isComponent: !currentValue,
              componentType: !currentValue ? (componentTypes.length > 0 ? componentTypes[0].name : null) : null,
            } 
          : product
      ));
    } catch (error) {
      console.error("Error updating product:", error);
      alert(error instanceof Error ? error.message : "An unknown error occurred");
    }
  };

  // Update component type
  const updateComponentType = async (productId: string, componentType: string) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          componentType,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update product: ${response.statusText}`);
      }

      // Update local state
      setProducts(products.map(product => 
        product.id === productId 
          ? { ...product, componentType } 
          : product
      ));
    } catch (error) {
      console.error("Error updating product:", error);
      alert(error instanceof Error ? error.message : "An unknown error occurred");
    }
  };

  // Add new component type
  const addComponentType = async () => {
    if (!newComponentType.trim()) {
      alert("Please enter a component type name");
      return;
    }

    try {
      const response = await fetch('/api/component-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newComponentType.trim(),
          description: `${newComponentType.trim()} components`,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to add component type: ${response.statusText}`);
      }

      const newType = await response.json();
      setComponentTypes([...componentTypes, newType]);
      setNewComponentType("");
    } catch (error) {
      console.error("Error adding component type:", error);
      alert(error instanceof Error ? error.message : "An unknown error occurred");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4">
        <p className="font-bold">Error</p>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Customize Products</h1>
        <Link
          href="/dashboard/products"
          className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300"
        >
          Back to Products
        </Link>
      </div>

      <div className="mb-8 bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">Add New Component Type</h2>
        <div className="flex gap-2">
          <input
            type="text"
            value={newComponentType}
            onChange={(e) => setNewComponentType(e.target.value)}
            placeholder="Enter component type name (e.g., RAM, CPU)"
            className="flex-1 px-4 py-2 border rounded-md"
          />
          <button
            onClick={addComponentType}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Add Type
          </button>
        </div>
        <div className="mt-4">
          <h3 className="font-bold mb-2">Current Component Types:</h3>
          <div className="flex flex-wrap gap-2">
            {componentTypes.length > 0 ? (
              componentTypes.map((type) => (
                <span key={type.id} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full">
                  {type.name}
                </span>
              ))
            ) : (
              <p className="text-gray-500">No component types defined yet.</p>
            )}
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Product Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customizable
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Component
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Component Type
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {products.map((product) => (
              <tr key={product.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{product.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleCustomizable(product.id, product.isCustomizable)}
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      product.isCustomizable
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {product.isCustomizable ? 'Yes' : 'No'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleComponent(product.id, product.isComponent)}
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      product.isComponent
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {product.isComponent ? 'Yes' : 'No'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {product.isComponent ? (
                    <select
                      value={product.componentType || ''}
                      onChange={(e) => updateComponentType(product.id, e.target.value)}
                      className="block w-full px-3 py-1.5 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    >
                      <option value="">Select Type</option>
                      {componentTypes.map((type) => (
                        <option key={type.id} value={type.name}>
                          {type.name}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <span className="text-gray-500">N/A</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
