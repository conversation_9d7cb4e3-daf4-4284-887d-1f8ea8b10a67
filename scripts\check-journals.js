// Script to check journals in the database
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking journals in the database...');

    // Get all journals
    const journals = await prisma.journal.findMany();
    
    console.log(`Found ${journals.length} journals:`);
    journals.forEach(journal => {
      console.log(`- ${journal.name} (${journal.code}) [Type: ${journal.type}]`);
    });

    console.log('\nChecking accounts in the database...');
    
    // Get all accounts
    const accounts = await prisma.account.findMany();
    
    console.log(`Found ${accounts.length} accounts:`);
    accounts.forEach(account => {
      console.log(`- ${account.name} (${account.code}) [Type: ${account.type}]`);
    });

    console.log('\nChecking payment methods in the database...');
    
    // Get all payment methods
    const paymentMethods = await prisma.paymentMethodSettings.findMany({
      include: {
        account: true,
        journal: true,
      }
    });
    
    console.log(`Found ${paymentMethods.length} payment methods:`);
    paymentMethods.forEach(method => {
      console.log(`- ${method.name} (${method.code})`);
      console.log(`  Account: ${method.account ? method.account.name : 'None'}`);
      console.log(`  Journal: ${method.journal ? method.journal.name : 'None'}`);
    });
  } catch (error) {
    console.error('Error checking journals:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
