// Script to reinitialize accounting module
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting accounting module reinitialization...');

    // 1. Ensure all required accounts exist
    console.log('Ensuring required accounts exist...');
    const requiredAccounts = [
      { code: "1000", name: "Cash", type: "ASSET" },
      { code: "1010", name: "Vodafone Cash", type: "ASSET" },
      { code: "1020", name: "Bank Account", type: "ASSET" },
      { code: "1030", name: "Credit Card", type: "ASSET" },
      { code: "1100", name: "Accounts Receivable", type: "ASSET" },
      { code: "1200", name: "Inventory", type: "ASSET" },
      { code: "2000", name: "Accounts Payable", type: "LIABILITY" },
      { code: "3000", name: "Owner's Equity", type: "EQUITY" },
      { code: "4000", name: "Sales Revenue", type: "REVENUE" },
      { code: "4100", name: "Sales Returns", type: "REVENUE" },
      { code: "5000", name: "Cost of Goods Sold", type: "EXPENSE" },
      { code: "5100", name: "Inventory Adjustment", type: "EXPENSE" },
      { code: "6000", name: "Operating Expenses", type: "EXPENSE" },
    ];

    const createdAccounts = {};

    // Check if each account exists, create if it doesn't
    for (const account of requiredAccounts) {
      let existingAccount = await prisma.account.findFirst({
        where: {
          OR: [
            { code: account.code },
            { name: account.name, type: account.type },
          ],
        },
      });

      if (!existingAccount) {
        console.log(`Creating account: ${account.name} (${account.code})`);
        existingAccount = await prisma.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            balance: 0,
            isActive: true,
          },
        });
      } else {
        console.log(`Account already exists: ${existingAccount.name} (${existingAccount.code})`);
      }

      createdAccounts[account.name] = existingAccount;
    }

    // 2. Ensure all required journals exist
    console.log('Ensuring required journals exist...');
    const requiredJournals = [
      { code: "CASH", name: "Cash Journal", type: "CASH" },
      { code: "VFCASH", name: "Vodafone Cash Journal", type: "VODAFONE_CASH" },
      { code: "BANK", name: "Bank Journal", type: "BANK_TRANSFER" },
      { code: "VISA", name: "Credit Card Journal", type: "VISA" },
      { code: "CUST", name: "Customer Account Journal", type: "CUSTOMER_ACCOUNT" },
      { code: "GENERAL", name: "General Journal", type: "GENERAL" },
    ];

    const createdJournals = {};

    // Check if each journal exists, create if it doesn't
    for (const journal of requiredJournals) {
      let existingJournal = await prisma.journal.findFirst({
        where: {
          OR: [
            { code: journal.code },
            { name: journal.name, type: journal.type },
          ],
        },
      });

      if (!existingJournal) {
        console.log(`Creating journal: ${journal.name} (${journal.code})`);
        existingJournal = await prisma.journal.create({
          data: {
            code: journal.code,
            name: journal.name,
            type: journal.type,
            isActive: true,
          },
        });
      } else {
        console.log(`Journal already exists: ${existingJournal.name} (${existingJournal.code})`);
      }

      createdJournals[journal.name] = existingJournal;
    }

    // 3. Update payment method settings
    console.log('Updating payment method settings...');
    const paymentMethodsMap = {
      'CASH': {
        account: createdAccounts['Cash'],
        journal: createdJournals['Cash Journal'],
        name: 'Cash',
        iconName: 'cash',
        color: '#3895e7',
      },
      'VODAFONE_CASH': {
        account: createdAccounts['Vodafone Cash'],
        journal: createdJournals['Vodafone Cash Journal'],
        name: 'Vodafone Cash',
        iconName: 'mobile',
        color: '#e60000',
      },
      'BANK_TRANSFER': {
        account: createdAccounts['Bank Account'],
        journal: createdJournals['Bank Journal'],
        name: 'Bank Transfer',
        iconName: 'bank',
        color: '#307aa8',
      },
      'VISA': {
        account: createdAccounts['Credit Card'],
        journal: createdJournals['Credit Card Journal'],
        name: 'Credit Card',
        iconName: 'credit-card',
        color: '#1a1f71',
      },
      'CUSTOMER_ACCOUNT': {
        account: createdAccounts['Accounts Receivable'],
        journal: createdJournals['Customer Account Journal'],
        name: 'Customer Account',
        iconName: 'user',
        color: '#6b7280',
      },
    };

    // Update each payment method
    for (const [code, config] of Object.entries(paymentMethodsMap)) {
      let paymentMethod = await prisma.paymentMethodSettings.findFirst({
        where: { code },
      });

      if (paymentMethod) {
        console.log(`Updating payment method: ${code}`);
        await prisma.paymentMethodSettings.update({
          where: { id: paymentMethod.id },
          data: {
            accountId: config.account.id,
            journalId: config.journal?.id || null,
            name: config.name,
            iconName: config.iconName,
            color: config.color,
            isActive: true,
          },
        });
      } else {
        console.log(`Creating payment method: ${code}`);
        await prisma.paymentMethodSettings.create({
          data: {
            code,
            name: config.name,
            accountId: config.account.id,
            journalId: config.journal?.id || null,
            iconName: config.iconName,
            color: config.color,
            isActive: true,
            sequence: Object.keys(paymentMethodsMap).indexOf(code) + 1,
          },
        });
      }
    }

    // 4. Create fiscal year and periods if they don't exist
    console.log('Ensuring fiscal year and periods exist...');
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1); // January 1st
    const endDate = new Date(currentYear, 11, 31); // December 31st

    let fiscalYear = await prisma.fiscalYear.findFirst({
      where: {
        startDate: {
          gte: new Date(currentYear, 0, 1),
          lt: new Date(currentYear + 1, 0, 1),
        },
      },
    });

    if (!fiscalYear) {
      console.log(`Creating fiscal year for ${currentYear}`);
      fiscalYear = await prisma.fiscalYear.create({
        data: {
          name: `Fiscal Year ${currentYear}`,
          startDate,
          endDate,
          isClosed: false,
        },
      });

      // Create fiscal periods (quarters)
      for (let i = 0; i < 4; i++) {
        const periodStartDate = new Date(currentYear, i * 3, 1);
        const periodEndDate = new Date(currentYear, (i + 1) * 3, 0);

        console.log(`Creating fiscal period: Q${i + 1} ${currentYear}`);
        await prisma.fiscalPeriod.create({
          data: {
            fiscalYearId: fiscalYear.id,
            name: `Q${i + 1} ${currentYear}`,
            startDate: periodStartDate,
            endDate: periodEndDate,
            isClosed: false,
          },
        });
      }
    } else {
      console.log(`Fiscal year for ${currentYear} already exists`);
    }

    console.log('Accounting module reinitialization completed successfully!');
  } catch (error) {
    console.error('Error in accounting module reinitialization:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
