import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

// GET /api/system/modules-status - Check status of all system modules
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check Sales module
    const salesStatus = await checkSalesModule();

    // Check Purchases module
    const purchasesStatus = await checkPurchasesModule();

    // Check Inventory module
    const inventoryStatus = await checkInventoryModule();

    // Check Accounting module
    const accountingStatus = await checkAccountingModule();

    // Check Contacts module
    const contactsStatus = await checkContactsModule();

    return NextResponse.json({
      modules: [
        salesStatus,
        purchasesStatus,
        inventoryStatus,
        accountingStatus,
        contactsStatus
      ]
    });
  } catch (error) {
    console.error("Error checking modules status:", error);
    return NextResponse.json(
      { error: "Failed to check modules status" },
      { status: 500 }
    );
  }
}

// Check Sales module status
async function checkSalesModule() {
  try {
    // Check if sales table exists and has data
    const salesCount = await prisma.sale.count();

    if (salesCount > 0) {
      return {
        name: "Sales",
        status: "connected",
        message: `${salesCount} sales records found`
      };
    } else {
      return {
        name: "Sales",
        status: "warning",
        message: "No sales records found"
      };
    }
  } catch (error) {
    console.error("Error checking Sales module:", error);
    return {
      name: "Sales",
      status: "disconnected",
      message: "Error connecting to Sales module"
    };
  }
}

// Check Purchases module status
async function checkPurchasesModule() {
  try {
    // Check if purchases table exists and has data
    const purchasesCount = await prisma.purchase.count();

    if (purchasesCount > 0) {
      return {
        name: "Purchases",
        status: "connected",
        message: `${purchasesCount} purchase records found`
      };
    } else {
      return {
        name: "Purchases",
        status: "warning",
        message: "No purchase records found"
      };
    }
  } catch (error) {
    console.error("Error checking Purchases module:", error);
    return {
      name: "Purchases",
      status: "disconnected",
      message: "Error connecting to Purchases module"
    };
  }
}

// Check Inventory module status
async function checkInventoryModule() {
  try {
    // Check if inventory table exists and has data
    const inventoryCount = await prisma.inventory.count();
    const productsCount = await prisma.product.count();

    if (inventoryCount > 0 && productsCount > 0) {
      return {
        name: "Inventory",
        status: "connected",
        message: `${inventoryCount} inventory records for ${productsCount} products`
      };
    } else if (productsCount > 0) {
      return {
        name: "Inventory",
        status: "warning",
        message: `${productsCount} products found but no inventory records`
      };
    } else {
      return {
        name: "Inventory",
        status: "warning",
        message: "No products or inventory records found"
      };
    }
  } catch (error) {
    console.error("Error checking Inventory module:", error);
    return {
      name: "Inventory",
      status: "disconnected",
      message: "Error connecting to Inventory module"
    };
  }
}

// Check Accounting module status
async function checkAccountingModule() {
  try {
    // Check if accounting tables exist and have data
    const accountsCount = await prisma.account.count().catch(() => 0);
    const journalsCount = await prisma.journal.count().catch(() => 0);

    if (accountsCount > 0 && journalsCount > 0) {
      return {
        name: "Accounting",
        status: "connected",
        message: `${accountsCount} accounts and ${journalsCount} journals found`
      };
    } else if (accountsCount > 0 || journalsCount > 0) {
      return {
        name: "Accounting",
        status: "warning",
        message: "Partial accounting setup detected"
      };
    } else {
      return {
        name: "Accounting",
        status: "warning",
        message: "Accounting module not initialized"
      };
    }
  } catch (error) {
    console.error("Error checking Accounting module:", error);
    return {
      name: "Accounting",
      status: "disconnected",
      message: "Error connecting to Accounting module"
    };
  }
}

// Check Contacts module status
async function checkContactsModule() {
  try {
    // Check if contacts table exists and has data
    const contactsCount = await prisma.contact.count();
    const customersCount = await prisma.contact.count({
      where: { isCustomer: true }
    });
    const suppliersCount = await prisma.contact.count({
      where: { isSupplier: true }
    });

    if (contactsCount > 0) {
      return {
        name: "Contacts",
        status: "connected",
        message: `${customersCount} customers and ${suppliersCount} suppliers found`
      };
    } else {
      return {
        name: "Contacts",
        status: "warning",
        message: "No contacts found"
      };
    }
  } catch (error) {
    console.error("Error checking Contacts module:", error);
    return {
      name: "Contacts",
      status: "disconnected",
      message: "Error connecting to Contacts module"
    };
  }
}
