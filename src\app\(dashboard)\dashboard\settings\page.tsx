"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Modal from "./components/Modal";
import BranchForm from "./components/BranchForm";
import WarehouseForm from "./components/WarehouseForm";
import UserForm from "./components/UserForm";
import DatabaseInitForm from "./components/DatabaseInitForm";
import DatabaseBackupForm from "./components/DatabaseBackupForm";
import AccountingInitialize from "./components/AccountingInitialize";

import InitializeCoreDataForm from "./components/InitializeCoreDataForm";
import AddMaintenancePermissions from "./components/AddMaintenancePermissions";
import PaymentMethodsSetup from "./components/PaymentMethodsSetup";
import PrintingSettings from "./components/PrintingSettings";
import CategorySettings from "./components/CategorySettings";
import ComponentTypeSettings from "./components/ComponentTypeSettings";
import SystemToolsSection from "./components/SystemToolsSection";

// Define types for data
interface Branch {
  id: string;
  name: string;
  address: string;
  phone: string;
  code: string;
  isActive: boolean;
}

interface Warehouse {
  id: string;
  name: string;
  branchId: string;
  branch: {
    name: string;
  };
  isActive: boolean;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  branch?: {
    id: string;
    name: string;
  };
  warehouses: {
    id: string;
    name: string;
  }[];
  permissions: {
    id: string;
    name: string;
  }[];
}



// Define tab types
type TabType = 'company' | 'branches' | 'warehouses' | 'categories' | 'users' | 'permissions' | 'tools' | 'payment-methods' | 'printing';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<TabType>('printing');

  // Set active tab based on URL query parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    if (tabParam && ['company', 'branches', 'warehouses', 'categories', 'users', 'permissions', 'tools', 'payment-methods', 'printing'].includes(tabParam)) {
      setActiveTab(tabParam as TabType);
    }
  }, []);
  const [companyName, setCompanyName] = useState("VERO Computer Store");
  const [taxId, setTaxId] = useState("");
  const [address, setAddress] = useState("");
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState("");
  const [branches, setBranches] = useState<Branch[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Handle logo file upload
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please upload an image file');
        return;
      }

      if (file.type !== 'image/png') {
        alert('Please upload a PNG image file');
        return;
      }

      setLogoFile(file);

      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Modal functions
  const openModal = (content: React.ReactNode) => {
    setModalContent(content);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setTimeout(() => {
      setModalContent(null);
    }, 300);
  };

  // Fetch data from API
  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Fetch branches
      try {
        const branchesResponse = await fetch('/api/branches?from=settings');
        if (branchesResponse.ok) {
          const branchesData = await branchesResponse.json();
          setBranches(branchesData);
        } else {
          console.error("Failed to fetch branches:", branchesResponse.status);
          setBranches([]);
        }
      } catch (error) {
        console.error("Error fetching branches:", error);
        setBranches([]);
      }

      // Fetch warehouses
      try {
        const warehousesResponse = await fetch('/api/warehouses?from=settings');
        if (warehousesResponse.ok) {
          const warehousesData = await warehousesResponse.json();
          setWarehouses(warehousesData);
        } else {
          console.error("Failed to fetch warehouses:", warehousesResponse.status);
          setWarehouses([]);
        }
      } catch (error) {
        console.error("Error fetching warehouses:", error);
        setWarehouses([]);
      }

      // Fetch users
      try {
        const usersResponse = await fetch('/api/users?from=settings');
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setUsers(usersData);
        } else {
          console.error("Failed to fetch users:", usersResponse.status);
          setUsers([]);
        }
      } catch (error) {
        console.error("Error fetching users:", error);
        setUsers([]);
      }


    } catch (error) {
      console.error("Error in fetchData:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Tab navigation
  const tabs = [
    { id: 'company', name: 'Company Information', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' },
    { id: 'branches', name: 'Branches', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' },
    { id: 'warehouses', name: 'Warehouses', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
    { id: 'categories', name: 'Product Categories', icon: 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z' },
    { id: 'component-types', name: 'Component Types', icon: 'M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z' },
    { id: 'users', name: 'Users', icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z' },
    { id: 'payment-methods', name: 'Payment Methods', icon: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z' },
    { id: 'printing', name: 'Printing Settings', icon: 'M6 5a2 2 0 012-2h8a2 2 0 012 2v2H6V5zm6 16a2 2 0 01-2-2V9h8v10a2 2 0 01-2 2h-4zm8-16a2 2 0 00-2-2h-8a2 2 0 00-2 2H4a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002 2h8a2 2 0 002-2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2z' },
    { id: 'permissions', name: 'Permissions', icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z' },
    { id: 'tools', name: 'System Tools', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' },
  ];

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-md">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">
          Settings
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Configure your ERP system settings
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        {/* Desktop Navigation */}
        <div className="hidden md:block">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex flex-wrap px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                return tab.id === 'permissions' ? (
                  <Link
                    key={tab.id}
                    href="/dashboard/settings/permissions"
                    className={`
                      ${activeTab === tab.id
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                      whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm flex items-center mr-6 mb-1
                    `}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2 flex-shrink-0"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={tab.icon}
                      />
                    </svg>
                    <span>{tab.name}</span>
                  </Link>
                ) : (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as TabType)}
                    className={`
                      ${activeTab === tab.id
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                      whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm flex items-center mr-6 mb-1
                    `}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2 flex-shrink-0"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={tab.icon}
                      />
                    </svg>
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden">
          <select
            value={activeTab}
            onChange={(e) => {
              if (e.target.value === 'permissions') {
                window.location.href = '/dashboard/settings/permissions';
              } else {
                setActiveTab(e.target.value as TabType);
              }
            }}
            className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            {tabs.map((tab) => (
              <option key={tab.id} value={tab.id}>
                {tab.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="px-4 py-5 sm:px-6">
        <div className="space-y-8">
          {/* Company Information Tab */}
          {activeTab === 'company' && (
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Company Information</h4>
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div className="sm:col-span-3">
                  <label htmlFor="company-name" className="block text-sm font-semibold text-gray-800">
                    Company Name
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      name="company-name"
                      id="company-name"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="tax-id" className="block text-sm font-semibold text-gray-800">
                    Tax ID / VAT Number
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      name="tax-id"
                      id="tax-id"
                      value={taxId}
                      onChange={(e) => setTaxId(e.target.value)}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <div className="sm:col-span-6">
                  <label htmlFor="address" className="block text-sm font-semibold text-gray-800">
                    Address
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="address"
                      name="address"
                      rows={3}
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="phone" className="block text-sm font-semibold text-gray-800">
                    Phone
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      name="phone"
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-800">
                    Email
                  </label>
                  <div className="mt-1">
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <div className="sm:col-span-6">
                  <label htmlFor="logo-upload" className="block text-sm font-semibold text-gray-800">
                    Company Logo (PNG only)
                  </label>
                  <div className="mt-1 flex items-center">
                    <input
                      type="file"
                      id="logo-upload"
                      accept="image/png"
                      onChange={handleLogoUpload}
                      className="sr-only"
                    />
                    <label
                      htmlFor="logo-upload"
                      className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Upload PNG Logo
                    </label>
                    {logoFile && (
                      <span className="ml-3 text-sm text-gray-600">
                        {logoFile.name}
                      </span>
                    )}
                  </div>
                </div>

                <div className="sm:col-span-6">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Save Company Information
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Branches Tab */}
          {activeTab === 'branches' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Branches</h4>
                <button
                  type="button"
                  onClick={() => openModal(
                    <BranchForm
                      onClose={closeModal}
                      onSuccess={() => {
                        closeModal();
                        fetchData();
                      }}
                    />
                  )}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add Branch
                </button>
              </div>

              {isLoading ? (
                <div className="text-center py-4">Loading branches...</div>
              ) : branches.length === 0 ? (
                <div className="text-center py-4 text-gray-500">No branches found. Add your first branch.</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {branches.map((branch) => (
                        <tr key={branch.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{branch.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{branch.code}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{branch.address}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{branch.phone}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${branch.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {branch.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => openModal(
                                <BranchForm
                                  branch={branch}
                                  onClose={closeModal}
                                  onSuccess={() => {
                                    closeModal();
                                    fetchData();
                                  }}
                                />
                              )}
                              className="text-indigo-600 hover:text-indigo-900 mr-4"
                            >
                              Edit
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Categories Tab */}
          {activeTab === 'categories' && (
            <CategorySettings />
          )}

          {/* Component Types Tab */}
          {activeTab === 'component-types' && (
            <ComponentTypeSettings />
          )}

          {/* Warehouses Tab */}
          {activeTab === 'warehouses' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Warehouses</h4>
                <button
                  type="button"
                  onClick={() => openModal(
                    <WarehouseForm
                      onClose={closeModal}
                      onSuccess={() => {
                        closeModal();
                        fetchData();
                      }}
                    />
                  )}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add Warehouse
                </button>
              </div>

              {isLoading ? (
                <div className="text-center py-4">Loading warehouses...</div>
              ) : warehouses.length === 0 ? (
                <div className="text-center py-4 text-gray-500">No warehouses found. Add your first warehouse.</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {warehouses.map((warehouse) => (
                        <tr key={warehouse.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{warehouse.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{warehouse.branch.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${warehouse.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {warehouse.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => openModal(
                                <WarehouseForm
                                  warehouse={warehouse}
                                  onClose={closeModal}
                                  onSuccess={() => {
                                    closeModal();
                                    fetchData();
                                  }}
                                />
                              )}
                              className="text-indigo-600 hover:text-indigo-900 mr-4"
                            >
                              Edit
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Users Tab */}
          {activeTab === 'users' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Users</h4>
                <button
                  type="button"
                  onClick={() => openModal(
                    <UserForm
                      onClose={closeModal}
                      onSuccess={() => {
                        closeModal();
                        fetchData();
                      }}
                    />
                  )}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Add User
                </button>
              </div>

              {isLoading ? (
                <div className="text-center py-4">Loading users...</div>
              ) : users.length === 0 ? (
                <div className="text-center py-4 text-gray-500">No users found. Add your first user.</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.role}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.branch?.name || 'N/A'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => openModal(
                                <UserForm
                                  user={user}
                                  onClose={closeModal}
                                  onSuccess={() => {
                                    closeModal();
                                    fetchData();
                                  }}
                                />
                              )}
                              className="text-indigo-600 hover:text-indigo-900 mr-4"
                            >
                              Edit
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}



          {/* Payment Methods Tab */}
          {activeTab === 'payment-methods' && (
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Payment Methods</h4>
              <PaymentMethodsSetup />
            </div>
          )}

          {/* Printing Settings Tab */}
          {activeTab === 'printing' && (
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Printing Settings</h4>
              <PrintingSettings />
            </div>
          )}

          {/* Permissions Tab - Redirected to dedicated page */}

          {/* System Tools Tab */}
          {activeTab === 'tools' && (
            <SystemToolsSection />
          )}

          {/* Modal */}
          <Modal isOpen={isModalOpen} onClose={closeModal} size="lg">
            {modalContent}
          </Modal>
        </div>
      </div>
    </div>
  );
}
