import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import { AccountType, JournalType, PaymentMethod } from "@prisma/client";
import { ensureAccountsExist, ensureJournalsExist } from "@/lib/accounting-new";

// Default accounts to create
const defaultAccounts = [
  // Asset accounts
  { accountNumber: "1000", name: "Cash", type: AccountType.ASSET },
  { accountNumber: "1010", name: "Vodafone Cash", type: AccountType.ASSET },
  { accountNumber: "1020", name: "Bank Account", type: AccountType.ASSET },
  { accountNumber: "1030", name: "Credit Card Payments", type: AccountType.ASSET },
  { accountNumber: "1200", name: "Accounts Receivable", type: AccountType.ASSET },
  { accountNumber: "1300", name: "Inventory", type: AccountType.ASSET },

  // Liability accounts
  { accountNumber: "2000", name: "Accounts Payable", type: AccountType.LIABILITY },
  { accountNumber: "2100", name: "Accrued Expenses", type: AccountType.LIABILITY },

  // Equity accounts
  { accountNumber: "3000", name: "Owner's Equity", type: AccountType.EQUITY },
  { accountNumber: "3100", name: "Retained Earnings", type: AccountType.EQUITY },

  // Revenue accounts
  { accountNumber: "4000", name: "Sales Revenue", type: AccountType.REVENUE },
  { accountNumber: "4900", name: "Sales Returns", type: AccountType.REVENUE },

  // Expense accounts
  { accountNumber: "5000", name: "Cost of Goods Sold", type: AccountType.EXPENSE },
  { accountNumber: "5100", name: "Salaries Expense", type: AccountType.EXPENSE },
  { accountNumber: "5200", name: "Rent Expense", type: AccountType.EXPENSE },
  { accountNumber: "5300", name: "Utilities Expense", type: AccountType.EXPENSE },
];

// Default journals to create
const defaultJournals = [
  { code: "CASH-001", name: "Cash Journal", type: JournalType.CASH, paymentMethod: PaymentMethod.CASH },
  { code: "VODA-001", name: "Vodafone Cash Journal", type: JournalType.VODAFONE_CASH, paymentMethod: PaymentMethod.VODAFONE_CASH },
  { code: "BANK-001", name: "Bank Journal", type: JournalType.BANK_TRANSFER, paymentMethod: PaymentMethod.BANK_TRANSFER },
  { code: "VISA-001", name: "Credit Card Journal", type: JournalType.VISA, paymentMethod: PaymentMethod.CREDIT_CARD },
  { code: "CUST-001", name: "Customer Accounts Journal", type: JournalType.CUSTOMER_ACCOUNT, paymentMethod: PaymentMethod.CUSTOMER_ACCOUNT },
  { code: "GEN-001", name: "General Journal", type: JournalType.GENERAL },
];

// POST /api/accounting/initialize - Initialize accounting module
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Log the session for debugging
    console.log("Session:", session);

    // Skip permission check for now
    // const hasInitPermission = await hasPermission("manage_accounts");
    // if (!hasInitPermission) {
    //   return NextResponse.json(
    //     { error: "You don't have permission to initialize accounting" },
    //     { status: 403 }
    //   );
    // }

    // Start a transaction
    const result = await db.$transaction(async (tx) => {
      // Use the new functions to ensure accounts and journals exist
      console.log("Ensuring accounts exist...");
      const accounts = await ensureAccountsExist();

      console.log("Ensuring journals exist...");
      const journals = await ensureJournalsExist();

      // Create current fiscal year and period if they don't exist
      const currentYear = new Date().getFullYear();
      const startDate = new Date(currentYear, 0, 1); // January 1st
      const endDate = new Date(currentYear, 11, 31); // December 31st

      // Check if fiscal year already exists
      let fiscalYear = await tx.fiscalYear.findFirst({
        where: {
          startDate: {
            gte: new Date(currentYear, 0, 1),
            lt: new Date(currentYear + 1, 0, 1),
          },
        },
      });

      if (!fiscalYear) {
        // Create fiscal year
        fiscalYear = await tx.fiscalYear.create({
          data: {
            name: `Fiscal Year ${currentYear}`,
            startDate,
            endDate,
            isClosed: false,
          },
        });

        // Create fiscal periods (quarters)
        for (let i = 0; i < 4; i++) {
          const periodStartDate = new Date(currentYear, i * 3, 1);
          const periodEndDate = new Date(currentYear, (i + 1) * 3, 0);

          await tx.fiscalPeriod.create({
            data: {
              fiscalYearId: fiscalYear.id,
              name: `Q${i + 1} ${currentYear}`,
              startDate: periodStartDate,
              endDate: periodEndDate,
              isClosed: false,
            },
          });
        }
      }

      return {
        accounts,
        journals,
        fiscalYear,
      };
    });

    return NextResponse.json({
      success: true,
      message: "Accounting module initialized successfully",
      data: result,
    });
  } catch (error: any) {
    console.error("Error initializing accounting module:", error);
    return NextResponse.json(
      { error: error.message || "Failed to initialize accounting module" },
      { status: 500 }
    );
  }
}

// GET /api/accounting/initialize - Check accounting module status
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if accounting module is initialized
    const accountsCount = await db.account.count();
    const journalsCount = await db.journal.count();
    const fiscalYearsCount = await db.fiscalYear.count();

    const isInitialized = accountsCount > 0 && journalsCount > 0 && fiscalYearsCount > 0;

    return NextResponse.json({
      isInitialized,
      counts: {
        accounts: accountsCount,
        journals: journalsCount,
        fiscalYears: fiscalYearsCount,
      },
    });
  } catch (error) {
    console.error("Error checking accounting module status:", error);
    return NextResponse.json(
      { error: "Failed to check accounting module status" },
      { status: 500 }
    );
  }
}
