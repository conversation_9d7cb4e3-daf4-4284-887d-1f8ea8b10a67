import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { isAdmin } from "@/lib/auth-utils";
import { 
  getApplicationRoutes,
  testUIComponent,
  checkCommonUIIssues,
  testWorkflow
} from "@/lib/ai-assistant-ui-tester";

// GET /api/ai-assistant/ui-tester - Test UI components and workflows
export async function GET(req: NextRequest) {
  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    const action = url.searchParams.get("action") || "routes";
    const path = url.searchParams.get("path");
    const workflow = url.searchParams.get("workflow");
    
    let result;
    
    switch (action) {
      case "routes":
        result = await getApplicationRoutes();
        break;
      case "test-component":
        if (!path) {
          return NextResponse.json(
            { error: "Path parameter is required" },
            { status: 400 }
          );
        }
        result = await testUIComponent(path);
        break;
      case "check-issues":
        result = await checkCommonUIIssues();
        break;
      case "test-workflow":
        if (!workflow) {
          return NextResponse.json(
            { error: "Workflow parameter is required" },
            { status: 400 }
          );
        }
        result = await testWorkflow(workflow);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in UI tester:", error);
    return NextResponse.json(
      { error: "Failed to execute UI tester action" },
      { status: 500 }
    );
  }
}
