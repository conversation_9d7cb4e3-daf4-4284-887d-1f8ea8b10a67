"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Loader2, Download, Search, RefreshCw } from "lucide-react";
import { format } from "date-fns";

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
  balance: number;
}

interface BalanceSheetData {
  asOfDate: string;
  assets: Account[];
  liabilities: Account[];
  equity: Account[];
  totalAssets: number;
  totalLiabilities: number;
  totalEquity: number;
  retainedEarnings: number;
}

export default function BalanceSheetPage() {
  const [data, setData] = useState<BalanceSheetData | null>(null);
  const [asOfDate, setAsOfDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState(false);

  // Fetch balance sheet data
  const fetchBalanceSheet = async () => {
    if (!asOfDate) {
      return;
    }

    setIsLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("asOfDate", asOfDate.toISOString());

      const response = await fetch(`/api/accounting/balance-sheet?${params.toString()}`);
      if (response.ok) {
        const responseData = await response.json();
        setData(responseData);
      }
    } catch (error) {
      console.error("Error fetching balance sheet:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    if (!data) return;

    // Create CSV content
    let csvContent = "Balance Sheet\n";
    csvContent += `As of ${format(new Date(data.asOfDate), "MMMM d, yyyy")}\n\n`;
    
    // Assets section
    csvContent += "ASSETS\n";
    data.assets.forEach(account => {
      csvContent += `${account.code},${account.name},${account.balance.toFixed(2)}\n`;
    });
    csvContent += `Total Assets,,${data.totalAssets.toFixed(2)}\n\n`;
    
    // Liabilities section
    csvContent += "LIABILITIES\n";
    data.liabilities.forEach(account => {
      csvContent += `${account.code},${account.name},${account.balance.toFixed(2)}\n`;
    });
    csvContent += `Total Liabilities,,${data.totalLiabilities.toFixed(2)}\n\n`;
    
    // Equity section
    csvContent += "EQUITY\n";
    data.equity.forEach(account => {
      csvContent += `${account.code},${account.name},${account.balance.toFixed(2)}\n`;
    });
    csvContent += `Retained Earnings,,${data.retainedEarnings.toFixed(2)}\n`;
    csvContent += `Total Equity,,${data.totalEquity.toFixed(2)}\n\n`;
    
    // Total Liabilities and Equity
    csvContent += `TOTAL LIABILITIES AND EQUITY,,${(data.totalLiabilities + data.totalEquity).toFixed(2)}\n`;
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `balance_sheet_${format(new Date(), "yyyyMMdd")}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Balance Sheet</h1>
        <Button variant="outline" onClick={exportToCSV} disabled={!data || isLoading}>
          <Download className="h-4 w-4 mr-2" />
          Export to CSV
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">As of Date</label>
              <DatePicker date={asOfDate} setDate={setAsOfDate} />
            </div>

            <div className="flex items-end">
              <Button onClick={fetchBalanceSheet} disabled={!asOfDate || isLoading}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {data && (
        <Card>
          <CardHeader>
            <CardTitle>
              Balance Sheet
              <span className="block text-sm font-normal mt-1">
                As of {format(new Date(data.asOfDate), "MMMM d, yyyy")}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Assets Section */}
              <div>
                <h3 className="text-lg font-medium mb-3">Assets</h3>
                <div className="rounded-md border overflow-hidden">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Account Code</th>
                        <th className="px-4 py-3 text-left">Account Name</th>
                        <th className="px-4 py-3 text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.assets.map(account => (
                        <tr key={account.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{account.code}</td>
                          <td className="px-4 py-3">{account.name}</td>
                          <td className="px-4 py-3 text-right">{account.balance.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-100 font-medium">
                        <td colSpan={2} className="px-4 py-3 text-right">Total Assets</td>
                        <td className="px-4 py-3 text-right">{data.totalAssets.toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              {/* Liabilities Section */}
              <div>
                <h3 className="text-lg font-medium mb-3">Liabilities</h3>
                <div className="rounded-md border overflow-hidden">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Account Code</th>
                        <th className="px-4 py-3 text-left">Account Name</th>
                        <th className="px-4 py-3 text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.liabilities.map(account => (
                        <tr key={account.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{account.code}</td>
                          <td className="px-4 py-3">{account.name}</td>
                          <td className="px-4 py-3 text-right">{account.balance.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-100 font-medium">
                        <td colSpan={2} className="px-4 py-3 text-right">Total Liabilities</td>
                        <td className="px-4 py-3 text-right">{data.totalLiabilities.toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              {/* Equity Section */}
              <div>
                <h3 className="text-lg font-medium mb-3">Equity</h3>
                <div className="rounded-md border overflow-hidden">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Account Code</th>
                        <th className="px-4 py-3 text-left">Account Name</th>
                        <th className="px-4 py-3 text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.equity.map(account => (
                        <tr key={account.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{account.code}</td>
                          <td className="px-4 py-3">{account.name}</td>
                          <td className="px-4 py-3 text-right">{account.balance.toFixed(2)}</td>
                        </tr>
                      ))}
                      <tr className="border-b hover:bg-gray-50">
                        <td className="px-4 py-3">3100</td>
                        <td className="px-4 py-3">Retained Earnings</td>
                        <td className="px-4 py-3 text-right">{data.retainedEarnings.toFixed(2)}</td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-100 font-medium">
                        <td colSpan={2} className="px-4 py-3 text-right">Total Equity</td>
                        <td className="px-4 py-3 text-right">{data.totalEquity.toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              {/* Total Liabilities and Equity */}
              <div className="rounded-md border overflow-hidden">
                <table className="w-full text-sm">
                  <tbody>
                    <tr className="font-bold text-lg bg-gray-100">
                      <td className="px-4 py-4 text-right">Total Liabilities and Equity</td>
                      <td className="px-4 py-4 text-right w-1/4">
                        {(data.totalLiabilities + data.totalEquity).toFixed(2)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
