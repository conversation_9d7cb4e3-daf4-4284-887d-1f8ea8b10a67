// AI Assistant models
model AIAssistantConversation {
  id          String               @id @default(uuid())
  userId      String
  title       String?              // Auto-generated title based on first message
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  isActive    Boolean              @default(true)
  
  user        User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages    AIAssistantMessage[]

  @@index([userId])
  @@index([createdAt])
}

model AIAssistantMessage {
  id              String                   @id @default(uuid())
  conversationId  String
  content         String                   @db.Text
  role            AIAssistantMessageRole
  createdAt       DateTime                 @default(now())
  metadata        String?                  @db.Text  // JSON string for additional data
  
  conversation    AIAssistantConversation  @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([role])
  @@index([createdAt])
}

enum AIAssistantMessageRole {
  USER
  ASSISTANT
  SYSTEM
}

model AIAssistantSettings {
  id                String   @id @default(uuid())
  userId            String   @unique
  isEnabled         Boolean  @default(true)
  autoSuggest       Boolean  @default(true)
  voiceEnabled      Boolean  @default(false)
  notificationsOn   Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
