-- CreateE<PERSON>
CREATE TYPE "MaintenanceStatus" AS ENUM ('RECEIVED', 'IN_PROGRESS', 'WAITING_FOR_PARTS', 'COMPLETED', 'DELIVERED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "MaintenancePriority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- CreateTable
CREATE TABLE "MaintenanceService" (
    "id" TEXT NOT NULL,
    "serviceNumber" TEXT NOT NULL,
    "contactId" TEXT NOT NULL,
    "deviceType" TEXT NOT NULL,
    "brand" TEXT NOT NULL,
    "model" TEXT,
    "serialNumber" TEXT,
    "problemDescription" TEXT NOT NULL,
    "receivedDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "estimatedCompletionDate" TIMESTAMP(3),
    "completionDate" TIMESTAMP(3),
    "deliveryDate" TIMESTAMP(3),
    "status" "MaintenanceStatus" NOT NULL DEFAULT 'RECEIVED',
    "priority" "MaintenancePriority" NOT NULL DEFAULT 'MEDIUM',
    "initialDiagnosis" TEXT,
    "technicalNotes" TEXT,
    "estimatedCost" DOUBLE PRECISION,
    "finalCost" DOUBLE PRECISION,
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "isWarranty" BOOLEAN NOT NULL DEFAULT false,
    "warrantyDetails" TEXT,
    "userId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MaintenanceService_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MaintenancePart" (
    "id" TEXT NOT NULL,
    "maintenanceServiceId" TEXT NOT NULL,
    "productId" TEXT,
    "partName" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "unitPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "isFromInventory" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MaintenancePart_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MaintenanceStatusHistory" (
    "id" TEXT NOT NULL,
    "maintenanceServiceId" TEXT NOT NULL,
    "status" "MaintenanceStatus" NOT NULL,
    "notes" TEXT,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MaintenanceStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MaintenanceService_serviceNumber_key" ON "MaintenanceService"("serviceNumber");

-- CreateIndex
CREATE INDEX "MaintenanceService_contactId_idx" ON "MaintenanceService"("contactId");

-- CreateIndex
CREATE INDEX "MaintenanceService_userId_idx" ON "MaintenanceService"("userId");

-- CreateIndex
CREATE INDEX "MaintenanceService_branchId_idx" ON "MaintenanceService"("branchId");

-- CreateIndex
CREATE INDEX "MaintenanceService_status_idx" ON "MaintenanceService"("status");

-- CreateIndex
CREATE INDEX "MaintenanceService_receivedDate_idx" ON "MaintenanceService"("receivedDate");

-- CreateIndex
CREATE INDEX "MaintenancePart_maintenanceServiceId_idx" ON "MaintenancePart"("maintenanceServiceId");

-- CreateIndex
CREATE INDEX "MaintenancePart_productId_idx" ON "MaintenancePart"("productId");

-- CreateIndex
CREATE INDEX "MaintenanceStatusHistory_maintenanceServiceId_idx" ON "MaintenanceStatusHistory"("maintenanceServiceId");

-- CreateIndex
CREATE INDEX "MaintenanceStatusHistory_userId_idx" ON "MaintenanceStatusHistory"("userId");

-- AddForeignKey
ALTER TABLE "MaintenanceService" ADD CONSTRAINT "MaintenanceService_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanceService" ADD CONSTRAINT "MaintenanceService_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanceService" ADD CONSTRAINT "MaintenanceService_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenancePart" ADD CONSTRAINT "MaintenancePart_maintenanceServiceId_fkey" FOREIGN KEY ("maintenanceServiceId") REFERENCES "MaintenanceService"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenancePart" ADD CONSTRAINT "MaintenancePart_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanceStatusHistory" ADD CONSTRAINT "MaintenanceStatusHistory_maintenanceServiceId_fkey" FOREIGN KEY ("maintenanceServiceId") REFERENCES "MaintenanceService"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanceStatusHistory" ADD CONSTRAINT "MaintenanceStatusHistory_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
