"use client";

import { useState, useEffect } from "react";
import { Database, AlertTriangle, CheckCircle, RefreshCw, Layers, Code } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";

interface UIIssue {
  success: boolean;
  path: string;
  component: string;
  description: string;
  error?: string;
}

interface TableInfo {
  name: string;
  recordCount: number;
  fields: { name: string; type: string }[];
}

export function AISystemTester() {
  const [activeTab, setActiveTab] = useState("ui-issues");
  const [isLoading, setIsLoading] = useState(false);
  const [uiIssues, setUIIssues] = useState<UIIssue[]>([]);
  const [dbTables, setDBTables] = useState<TableInfo[]>([]);
  const [workflows, setWorkflows] = useState<any[]>([]);

  // Fetch UI issues
  const fetchUIIssues = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/ai-assistant/ui-tester?action=check-issues");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.issues) {
          setUIIssues(data.issues);
        } else {
          toast.error(data.error || "Failed to fetch UI issues");
        }
      } else {
        toast.error("Failed to fetch UI issues");
      }
    } catch (error) {
      console.error("Error fetching UI issues:", error);
      toast.error("Error fetching UI issues");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch database schema
  const fetchDBSchema = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/ai-assistant/db-explorer?action=schema");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.schema) {
          // Convert schema object to array of tables
          const tables = Object.entries(data.schema).map(([name, info]: [string, any]) => ({
            name,
            recordCount: info.recordCount,
            fields: info.fields || [],
          }));
          
          // Sort by record count
          tables.sort((a, b) => b.recordCount - a.recordCount);
          
          setDBTables(tables);
        } else {
          toast.error(data.error || "Failed to fetch database schema");
        }
      } else {
        toast.error("Failed to fetch database schema");
      }
    } catch (error) {
      console.error("Error fetching database schema:", error);
      toast.error("Error fetching database schema");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch workflows
  const fetchWorkflows = async () => {
    setIsLoading(true);
    try {
      // Fetch sales invoice workflow as an example
      const response = await fetch("/api/ai-assistant/ui-tester?action=test-workflow&workflow=sales_invoice");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.steps) {
          setWorkflows([
            {
              name: "فاتورة المبيعات",
              steps: data.steps,
            },
          ]);
        } else {
          toast.error(data.error || "Failed to fetch workflows");
        }
      } else {
        toast.error("Failed to fetch workflows");
      }
    } catch (error) {
      console.error("Error fetching workflows:", error);
      toast.error("Error fetching workflows");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when tab changes
  useEffect(() => {
    if (activeTab === "ui-issues") {
      fetchUIIssues();
    } else if (activeTab === "db-schema") {
      fetchDBSchema();
    } else if (activeTab === "workflows") {
      fetchWorkflows();
    }
  }, [activeTab]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Code className="h-5 w-5 mr-2 text-blue-600" />
          اختبار النظام
        </CardTitle>
        <CardDescription>
          فحص واجهة المستخدم وقاعدة البيانات وعمليات النظام
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="ui-issues" className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              مشاكل الواجهة
            </TabsTrigger>
            <TabsTrigger value="db-schema" className="flex items-center">
              <Database className="h-4 w-4 mr-2" />
              قاعدة البيانات
            </TabsTrigger>
            <TabsTrigger value="workflows" className="flex items-center">
              <Layers className="h-4 w-4 mr-2" />
              العمليات
            </TabsTrigger>
          </TabsList>

          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          ) : (
            <>
              <TabsContent value="ui-issues" className="space-y-4">
                {uiIssues.length === 0 ? (
                  <div className="text-center p-4 text-gray-500">
                    لم يتم العثور على أي مشاكل في واجهة المستخدم
                  </div>
                ) : (
                  uiIssues.map((issue, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 bg-gray-50"
                    >
                      <div className="flex items-start">
                        <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 ml-2 flex-shrink-0" />
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {issue.component} ({issue.path})
                          </h3>
                          <p className="text-red-500 text-sm mt-1">{issue.error}</p>
                          <p className="text-gray-600 text-sm mt-1">{issue.description}</p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
                <Button
                  onClick={fetchUIIssues}
                  className="w-full mt-4"
                >
                  تحديث
                </Button>
              </TabsContent>

              <TabsContent value="db-schema" className="space-y-4">
                {dbTables.length === 0 ? (
                  <div className="text-center p-4 text-gray-500">
                    لم يتم العثور على أي جداول في قاعدة البيانات
                  </div>
                ) : (
                  dbTables.slice(0, 5).map((table, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 bg-gray-50"
                    >
                      <div className="flex items-start">
                        <Database className="h-5 w-5 text-blue-500 mt-0.5 ml-2 flex-shrink-0" />
                        <div className="w-full">
                          <div className="flex justify-between">
                            <h3 className="font-medium text-gray-900">{table.name}</h3>
                            <span className="text-sm text-gray-500">{table.recordCount} سجل</span>
                          </div>
                          {table.fields.length > 0 && (
                            <div className="mt-2">
                              <p className="text-sm text-gray-600 mb-1">الحقول:</p>
                              <div className="grid grid-cols-2 gap-2">
                                {table.fields.slice(0, 6).map((field, idx) => (
                                  <div key={idx} className="text-xs text-gray-600">
                                    {field.name} ({field.type})
                                  </div>
                                ))}
                                {table.fields.length > 6 && (
                                  <div className="text-xs text-gray-500">
                                    +{table.fields.length - 6} حقول أخرى
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
                <Button
                  onClick={fetchDBSchema}
                  className="w-full mt-4"
                >
                  تحديث
                </Button>
              </TabsContent>

              <TabsContent value="workflows" className="space-y-4">
                {workflows.length === 0 ? (
                  <div className="text-center p-4 text-gray-500">
                    لم يتم العثور على أي عمليات
                  </div>
                ) : (
                  workflows.map((workflow, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 bg-gray-50"
                    >
                      <h3 className="font-medium text-gray-900 mb-2">{workflow.name}</h3>
                      <div className="space-y-2">
                        {workflow.steps.map((step: any, stepIndex: number) => (
                          <div key={stepIndex} className="flex items-start">
                            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                              <span className="text-xs font-medium text-blue-600">{step.step}</span>
                            </div>
                            <div>
                              <p className="text-sm text-gray-700">{step.description}</p>
                              {step.path && (
                                <p className="text-xs text-gray-500">المسار: {step.path}</p>
                              )}
                              {step.component && (
                                <p className="text-xs text-gray-500">المكون: {step.component}</p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                )}
                <Button
                  onClick={fetchWorkflows}
                  className="w-full mt-4"
                >
                  تحديث
                </Button>
              </TabsContent>
            </>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
