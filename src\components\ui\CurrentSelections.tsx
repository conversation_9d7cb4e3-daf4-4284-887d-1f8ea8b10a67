"use client";

import { useState, useEffect } from "react";
import { Building, Warehouse, Monitor, ChevronDown } from "lucide-react";
import { useApp } from "@/contexts/AppContext";
import { useSession } from "next-auth/react";

interface Branch {
  id: string;
  name: string;
}

interface WarehouseItem {
  id: string;
  name: string;
  branchId: string;
}

interface POSItem {
  id: string;
  name: string;
  branchId: string;
}

export function CurrentSelections() {
  const { 
    currentBranch, 
    setCurrentBranch, 
    currentWarehouse, 
    setCurrentWarehouse,
    currentPOS,
    setCurrentPOS,
    language 
  } = useApp();
  
  const { data: session } = useSession();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [warehouses, setWarehouses] = useState<WarehouseItem[]>([]);
  const [posItems, setPOSItems] = useState<POSItem[]>([]);
  const [loading, setLoading] = useState(true);

  const labels = {
    ar: {
      currentBranch: 'الفرع الحالي',
      currentWarehouse: 'المستودع الحالي',
      currentPOS: 'نقطة البيع الحالية',
      selectBranch: 'اختر فرع',
      selectWarehouse: 'اختر مستودع',
      selectPOS: 'اختر نقطة بيع',
      noBranches: 'لا توجد فروع',
      noWarehouses: 'لا توجد مستودعات',
      noPOS: 'لا توجد نقاط بيع'
    },
    en: {
      currentBranch: 'Current Branch',
      currentWarehouse: 'Current Warehouse',
      currentPOS: 'Current POS',
      selectBranch: 'Select Branch',
      selectWarehouse: 'Select Warehouse',
      selectPOS: 'Select POS',
      noBranches: 'No branches available',
      noWarehouses: 'No warehouses available',
      noPOS: 'No POS available'
    }
  };

  const currentLabels = labels[language];

  // Fetch user's accessible branches, warehouses, and POS
  useEffect(() => {
    const fetchData = async () => {
      if (!session?.user?.id) return;

      try {
        setLoading(true);
        
        // Fetch branches
        const branchesResponse = await fetch('/api/branches');
        if (branchesResponse.ok) {
          const branchesData = await branchesResponse.json();
          setBranches(branchesData);
          
          // Set default branch if none selected
          if (!currentBranch && branchesData.length > 0) {
            setCurrentBranch(branchesData[0].id);
          }
        }

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        if (warehousesResponse.ok) {
          const warehousesData = await warehousesResponse.json();
          setWarehouses(warehousesData);
          
          // Set default warehouse if none selected
          if (!currentWarehouse && warehousesData.length > 0) {
            setCurrentWarehouse(warehousesData[0].id);
          }
        }

        // Fetch POS items (when implemented)
        // const posResponse = await fetch('/api/pos');
        // if (posResponse.ok) {
        //   const posData = await posResponse.json();
        //   setPOSItems(posData);
        // }

      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [session?.user?.id, currentBranch, currentWarehouse, setCurrentBranch, setCurrentWarehouse]);

  const getCurrentBranchName = () => {
    const branch = branches.find(b => b.id === currentBranch);
    return branch?.name || currentLabels.selectBranch;
  };

  const getCurrentWarehouseName = () => {
    const warehouse = warehouses.find(w => w.id === currentWarehouse);
    return warehouse?.name || currentLabels.selectWarehouse;
  };

  const getCurrentPOSName = () => {
    const pos = posItems.find(p => p.id === currentPOS);
    return pos?.name || currentLabels.selectPOS;
  };

  const getAvailableWarehouses = () => {
    if (!currentBranch) return warehouses;
    return warehouses.filter(w => w.branchId === currentBranch);
  };

  const getAvailablePOS = () => {
    if (!currentBranch) return posItems;
    return posItems.filter(p => p.branchId === currentBranch);
  };

  if (loading) {
    return (
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <div className="animate-pulse">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 text-sm">
      {/* Branch Selector */}
      <div className="relative">
        <button
          className="flex items-center h-8 px-2 text-xs bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
          onClick={() => {/* TODO: Implement dropdown */}}
        >
          <Building className="h-3 w-3 mr-1" />
          <span className="hidden sm:inline max-w-24 truncate">
            {getCurrentBranchName()}
          </span>
          <ChevronDown className="h-3 w-3 ml-1" />
        </button>
      </div>

      {/* Warehouse Selector */}
      <div className="relative">
        <button
          className="flex items-center h-8 px-2 text-xs bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
          onClick={() => {/* TODO: Implement dropdown */}}
        >
          <Warehouse className="h-3 w-3 mr-1" />
          <span className="hidden sm:inline max-w-24 truncate">
            {getCurrentWarehouseName()}
          </span>
          <ChevronDown className="h-3 w-3 ml-1" />
        </button>
      </div>

      {/* POS Selector (when implemented) */}
      {posItems.length > 0 && (
        <div className="relative">
          <button
            className="flex items-center h-8 px-2 text-xs bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
            onClick={() => {/* TODO: Implement dropdown */}}
          >
            <Monitor className="h-3 w-3 mr-1" />
            <span className="hidden sm:inline max-w-24 truncate">
              {getCurrentPOSName()}
            </span>
            <ChevronDown className="h-3 w-3 ml-1" />
          </button>
        </div>
      )}
    </div>
  );
}
