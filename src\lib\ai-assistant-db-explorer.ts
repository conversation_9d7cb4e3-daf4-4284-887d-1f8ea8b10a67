import { db } from "@/lib/db";
import { PrismaClient } from "@prisma/client";

/**
 * Get database schema information
 */
export async function getDatabaseSchema() {
  try {
    // Get all models from Prisma schema
    const prisma = db as PrismaClient;
    const models = Object.keys(prisma).filter(
      (key) => 
        typeof prisma[key as keyof typeof prisma] === "object" && 
        key !== "$on" && 
        key !== "$connect" && 
        key !== "$disconnect" && 
        key !== "$use" && 
        key !== "$transaction" && 
        key !== "$extends"
    );

    // Get table information for each model
    const schema: Record<string, any> = {};
    
    for (const model of models) {
      try {
        // Skip internal Prisma models
        if (model.startsWith("_") || model.startsWith("$")) continue;
        
        // Get sample record to determine fields
        const sampleRecord = await prisma[model as keyof typeof prisma].findFirst({
          take: 1,
        });
        
        if (sampleRecord) {
          schema[model] = {
            fields: Object.keys(sampleRecord).map(field => ({
              name: field,
              type: typeof sampleRecord[field],
              isId: field === "id",
              isDate: sampleRecord[field] instanceof Date,
            })),
            recordCount: await prisma[model as keyof typeof prisma].count(),
          };
        } else {
          // If no records, just get the count
          schema[model] = {
            fields: [],
            recordCount: 0,
          };
        }
      } catch (error) {
        console.error(`Error getting schema for model ${model}:`, error);
        schema[model] = { error: "Failed to get schema information" };
      }
    }
    
    return { success: true, schema };
  } catch (error) {
    console.error("Error getting database schema:", error);
    return { success: false, error: "Failed to get database schema" };
  }
}

/**
 * Get table data with pagination
 */
export async function getTableData(tableName: string, page = 1, pageSize = 10) {
  try {
    const prisma = db as PrismaClient;
    
    // Check if table exists
    if (!prisma[tableName as keyof typeof prisma]) {
      return { success: false, error: "Table not found" };
    }
    
    // Get total count
    const totalCount = await prisma[tableName as keyof typeof prisma].count();
    
    // Get data with pagination
    const data = await prisma[tableName as keyof typeof prisma].findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
    });
    
    return {
      success: true,
      data,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    };
  } catch (error) {
    console.error(`Error getting data for table ${tableName}:`, error);
    return { success: false, error: `Failed to get data for table ${tableName}` };
  }
}

/**
 * Execute a custom query
 */
export async function executeQuery(query: string, params: any[] = []) {
  try {
    // Only allow SELECT queries for security
    if (!query.trim().toLowerCase().startsWith("select")) {
      return { 
        success: false, 
        error: "Only SELECT queries are allowed for security reasons" 
      };
    }
    
    const result = await db.$queryRawUnsafe(query, ...params);
    return { success: true, result };
  } catch (error) {
    console.error("Error executing query:", error);
    return { 
      success: false, 
      error: `Failed to execute query: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}

/**
 * Get relationships between tables
 */
export async function getTableRelationships() {
  try {
    // This is a simplified approach - in a real implementation, you would
    // extract this information from Prisma's metadata or the database directly
    
    // Query PostgreSQL information schema for foreign keys
    const query = `
      SELECT
        tc.table_schema, 
        tc.constraint_name, 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_schema AS foreign_table_schema,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY';
    `;
    
    const relationships = await db.$queryRaw`${query}`;
    return { success: true, relationships };
  } catch (error) {
    console.error("Error getting table relationships:", error);
    return { success: false, error: "Failed to get table relationships" };
  }
}

/**
 * Get database statistics
 */
export async function getDatabaseStats() {
  try {
    const prisma = db as PrismaClient;
    const models = Object.keys(prisma).filter(
      (key) => 
        typeof prisma[key as keyof typeof prisma] === "object" && 
        !key.startsWith("$") && 
        !key.startsWith("_")
    );
    
    const stats: Record<string, number> = {};
    
    for (const model of models) {
      try {
        stats[model] = await prisma[model as keyof typeof prisma].count();
      } catch (error) {
        console.error(`Error getting count for model ${model}:`, error);
        stats[model] = -1; // Error indicator
      }
    }
    
    return { success: true, stats };
  } catch (error) {
    console.error("Error getting database stats:", error);
    return { success: false, error: "Failed to get database statistics" };
  }
}
