import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// GET /api/ai-assistant/conversations/active - Get user's active conversation
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get user's active conversation
    const conversation = await db.aIAssistantConversation.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
    });
    
    // If no active conversation, return null
    if (!conversation) {
      return NextResponse.json({ conversation: null, messages: [] });
    }
    
    // Get messages for this conversation
    const messages = await db.aIAssistantMessage.findMany({
      where: {
        conversationId: conversation.id,
        role: {
          not: "SYSTEM", // Exclude system messages
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });
    
    // Format messages for the frontend
    const formattedMessages = messages.map((message) => ({
      id: message.id,
      role: message.role.toLowerCase(),
      content: message.content,
      createdAt: message.createdAt.toISOString(),
    }));
    
    return NextResponse.json({
      conversation,
      messages: formattedMessages,
    });
  } catch (error) {
    console.error("Error fetching active conversation:", error);
    return NextResponse.json(
      { error: "Failed to fetch active conversation" },
      { status: 500 }
    );
  }
}
