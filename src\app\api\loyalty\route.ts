import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    const contactId = url.searchParams.get("contactId");
    
    if (contactId) {
      // Get loyalty account for specific contact
      const contact = await prisma.contact.findUnique({
        where: { id: contactId },
        select: {
          id: true,
          name: true,
          phone: true,
          isVIP: true,
          loyaltyPoints: true,
          loyaltyTier: true
        }
      });
      
      if (!contact) {
        return NextResponse.json(
          { error: "Contact not found" },
          { status: 404 }
        );
      }
      
      // Get recent transactions
      const recentTransactions = await prisma.$queryRaw`
        SELECT 
          CASE 
            WHEN s.id IS NOT NULL THEN 'SALE'
            WHEN cn.id IS NOT NULL THEN 'CREDIT_NOTE'
            ELSE 'ADJUSTMENT'
          END as type,
          CASE 
            WHEN s.id IS NOT NULL THEN s."invoiceNumber"
            WHEN cn.id IS NOT NULL THEN cn."creditNoteNumber"
            ELSE NULL
          END as reference,
          CASE 
            WHEN s.id IS NOT NULL THEN s.date
            WHEN cn.id IS NOT NULL THEN cn.date
            ELSE t."createdAt"
          END as date,
          t.points,
          t.description
        FROM "Contact" c
        LEFT JOIN (
          SELECT * FROM (
            SELECT 
              'EARN' as type,
              s.id as "saleId",
              NULL as "creditNoteId",
              c.id as "contactId",
              s."totalAmount" * 0.01 as points,
              'Points earned from purchase' as description,
              s."createdAt"
            FROM "Sale" s
            JOIN "Contact" c ON s."contactId" = c.id
            WHERE s.status = 'COMPLETED'
            
            UNION ALL
            
            SELECT 
              'REDEEM' as type,
              NULL as "saleId",
              cn.id as "creditNoteId",
              c.id as "contactId",
              cn."totalAmount" * 0.01 as points,
              'Points redeemed for credit note' as description,
              cn."createdAt"
            FROM "CreditNote" cn
            JOIN "Contact" c ON cn."contactId" = c.id
            WHERE cn.status = 'COMPLETED'
          ) as transactions
          WHERE "contactId" = ${contactId}
          ORDER BY "createdAt" DESC
          LIMIT 10
        ) as t ON c.id = t."contactId"
        LEFT JOIN "Sale" s ON t."saleId" = s.id
        LEFT JOIN "CreditNote" cn ON t."creditNoteId" = cn.id
        WHERE c.id = ${contactId}
        ORDER BY date DESC
      `;
      
      // Calculate available points value
      const pointValueInEGP = 0.01; // 1 point = 0.01 EGP
      const availableValue = contact.loyaltyPoints * pointValueInEGP;
      
      return NextResponse.json({
        contact,
        loyaltyInfo: {
          currentPoints: contact.loyaltyPoints,
          availableValue,
          tier: contact.loyaltyTier || 'BRONZE',
          isVIP: contact.isVIP
        },
        recentTransactions
      });
    } else {
      // Get top loyalty customers
      const topCustomers = await prisma.contact.findMany({
        where: {
          isCustomer: true,
          loyaltyPoints: { gt: 0 }
        },
        orderBy: {
          loyaltyPoints: 'desc'
        },
        take: 10,
        select: {
          id: true,
          name: true,
          phone: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          isVIP: true
        }
      });
      
      // Get loyalty tiers distribution
      const tiersDistribution = await prisma.$queryRaw`
        SELECT 
          COALESCE("loyaltyTier", 'BRONZE') as tier,
          COUNT(*) as count
        FROM "Contact"
        WHERE "isCustomer" = true
        GROUP BY COALESCE("loyaltyTier", 'BRONZE')
        ORDER BY count DESC
      `;
      
      // Get total points in system
      const totalPointsResult = await prisma.$queryRaw`
        SELECT SUM("loyaltyPoints") as total
        FROM "Contact"
        WHERE "isCustomer" = true
      `;
      
      const totalPoints = totalPointsResult[0]?.total || 0;
      
      return NextResponse.json({
        topCustomers,
        tiersDistribution,
        totalPoints,
        pointValueInEGP: 0.01
      });
    }
  } catch (error) {
    console.error("Error fetching loyalty data:", error);
    return NextResponse.json(
      { error: "Failed to fetch loyalty data" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const data = await req.json();
    
    if (!data.contactId) {
      return NextResponse.json(
        { error: "Contact ID is required" },
        { status: 400 }
      );
    }
    
    // Check if contact exists
    const contact = await prisma.contact.findUnique({
      where: { id: data.contactId }
    });
    
    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }
    
    // Handle different operations
    switch (data.operation) {
      case "ADD_POINTS":
        if (!data.points || data.points <= 0) {
          return NextResponse.json(
            { error: "Valid points value is required" },
            { status: 400 }
          );
        }
        
        // Add points to contact
        const updatedContact = await prisma.contact.update({
          where: { id: data.contactId },
          data: {
            loyaltyPoints: { increment: data.points }
          }
        });
        
        // Update loyalty tier based on new points total
        await updateLoyaltyTier(updatedContact.id, updatedContact.loyaltyPoints);
        
        return NextResponse.json({
          success: true,
          message: `Added ${data.points} points to ${contact.name}`,
          currentPoints: updatedContact.loyaltyPoints
        });
        
      case "REDEEM_POINTS":
        if (!data.points || data.points <= 0) {
          return NextResponse.json(
            { error: "Valid points value is required" },
            { status: 400 }
          );
        }
        
        if (contact.loyaltyPoints < data.points) {
          return NextResponse.json(
            { error: "Insufficient points" },
            { status: 400 }
          );
        }
        
        // Redeem points from contact
        const updatedContactAfterRedeem = await prisma.contact.update({
          where: { id: data.contactId },
          data: {
            loyaltyPoints: { decrement: data.points }
          }
        });
        
        return NextResponse.json({
          success: true,
          message: `Redeemed ${data.points} points from ${contact.name}`,
          currentPoints: updatedContactAfterRedeem.loyaltyPoints
        });
        
      case "SET_VIP":
        // Set VIP status
        const updatedVipContact = await prisma.contact.update({
          where: { id: data.contactId },
          data: {
            isVIP: data.isVIP
          }
        });
        
        return NextResponse.json({
          success: true,
          message: `${updatedVipContact.isVIP ? 'Added' : 'Removed'} VIP status for ${contact.name}`,
          isVIP: updatedVipContact.isVIP
        });
        
      default:
        return NextResponse.json(
          { error: "Invalid operation" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error updating loyalty data:", error);
    return NextResponse.json(
      { error: "Failed to update loyalty data" },
      { status: 500 }
    );
  }
}

// Helper function to update loyalty tier based on points
async function updateLoyaltyTier(contactId: string, points: number) {
  let tier = 'BRONZE';
  
  if (points >= 10000) {
    tier = 'PLATINUM';
  } else if (points >= 5000) {
    tier = 'GOLD';
  } else if (points >= 1000) {
    tier = 'SILVER';
  }
  
  await prisma.contact.update({
    where: { id: contactId },
    data: { loyaltyTier: tier }
  });
  
  return tier;
}
