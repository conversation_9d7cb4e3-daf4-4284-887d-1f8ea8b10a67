import { db } from "@/lib/db";
import { PrismaClient } from "@prisma/client";
import { 
  SystemKnowledge, 
  TableKnowledge, 
  FieldKnowledge,
  RelationshipKnowledge,
  ModuleKnowledge,
  veroERPKnowledge
} from "@/lib/ai-assistant-knowledge-base";

/**
 * Update the knowledge base with the latest information from the database
 */
export async function updateKnowledgeBase(): Promise<SystemKnowledge> {
  try {
    // Update database schema
    await updateDatabaseSchema();
    
    // Update modules information
    await updateModulesInformation();
    
    // Update last updated timestamp
    veroERPKnowledge.lastUpdated = new Date().toISOString();
    
    return veroERPKnowledge;
  } catch (error) {
    console.error("Error updating knowledge base:", error);
    throw error;
  }
}

/**
 * Update database schema information
 */
async function updateDatabaseSchema() {
  try {
    const prisma = db as PrismaClient;
    const models = Object.keys(prisma).filter(
      (key) => 
        typeof prisma[key as keyof typeof prisma] === "object" && 
        !key.startsWith("$") && 
        !key.startsWith("_")
    );
    
    const tables: TableKnowledge[] = [];
    
    // Map of Arabic table names
    const arabicTableNames: Record<string, string> = {
      "user": "المستخدمين",
      "sale": "المبيعات",
      "saleItem": "عناصر المبيعات",
      "purchase": "المشتريات",
      "purchaseItem": "عناصر المشتريات",
      "product": "المنتجات",
      "inventory": "المخزون",
      "contact": "جهات الاتصال",
      "branch": "الفروع",
      "warehouse": "المستودعات",
      "paymentMethod": "طرق الدفع",
      "paymentVoucher": "سندات الدفع",
      "receiptVoucher": "سندات القبض",
      "creditNote": "إشعارات الخصم",
      "creditNoteItem": "عناصر إشعارات الخصم"
    };
    
    // Map of module assignments
    const moduleAssignments: Record<string, string> = {
      "user": "الإعدادات",
      "sale": "المبيعات",
      "saleItem": "المبيعات",
      "purchase": "المشتريات",
      "purchaseItem": "المشتريات",
      "product": "المنتجات",
      "inventory": "المخزون",
      "contact": "جهات الاتصال",
      "branch": "الإعدادات",
      "warehouse": "المخزون",
      "paymentMethod": "المحاسبة",
      "paymentVoucher": "المحاسبة",
      "receiptVoucher": "المحاسبة",
      "creditNote": "المبيعات",
      "creditNoteItem": "المبيعات"
    };
    
    for (const model of models) {
      try {
        // Skip internal Prisma models
        if (model.startsWith("_") || model.startsWith("$")) continue;
        
        // Get sample record to determine fields
        const sampleRecord = await prisma[model as keyof typeof prisma].findFirst({
          take: 1,
        });
        
        const fields: FieldKnowledge[] = [];
        
        if (sampleRecord) {
          // Extract fields from sample record
          for (const [fieldName, fieldValue] of Object.entries(sampleRecord)) {
            fields.push({
              name: fieldName,
              arabicName: getArabicFieldName(fieldName),
              type: typeof fieldValue,
              description: getFieldDescription(model, fieldName),
              isRequired: isFieldRequired(model, fieldName),
              isUnique: isFieldUnique(model, fieldName)
            });
          }
        }
        
        // Create table knowledge
        tables.push({
          name: model,
          arabicName: arabicTableNames[model] || model,
          description: getTableDescription(model),
          fields,
          relatedTables: getRelatedTables(model),
          module: moduleAssignments[model] || "عام"
        });
      } catch (error) {
        console.error(`Error processing model ${model}:`, error);
      }
    }
    
    // Update the knowledge base with the new tables
    veroERPKnowledge.databaseSchema.tables = tables;
    
    // Update relationships
    await updateRelationships();
  } catch (error) {
    console.error("Error updating database schema:", error);
    throw error;
  }
}

/**
 * Update relationships between tables
 */
async function updateRelationships() {
  try {
    // Query PostgreSQL information schema for foreign keys
    const query = `
      SELECT
        tc.table_name AS source_table, 
        ccu.table_name AS target_table
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY';
    `;
    
    const relationships = await db.$queryRaw(query);
    
    const relationshipKnowledge: RelationshipKnowledge[] = [];
    
    for (const rel of relationships as any[]) {
      relationshipKnowledge.push({
        sourceTable: rel.source_table,
        targetTable: rel.target_table,
        type: "one-to-many", // Default assumption
        description: `${rel.source_table} مرتبط بـ ${rel.target_table}`
      });
    }
    
    // Update the knowledge base with the new relationships
    veroERPKnowledge.databaseSchema.relationships = relationshipKnowledge;
  } catch (error) {
    console.error("Error updating relationships:", error);
    // Don't throw, continue with other updates
  }
}

/**
 * Update modules information
 */
async function updateModulesInformation() {
  try {
    // This would typically involve analyzing the codebase
    // For now, we'll keep the existing modules information
    
    // Update sales module with latest information
    updateSalesModule();
    
    // Update inventory module with latest information
    updateInventoryModule();
    
    // Update accounting module with latest information
    updateAccountingModule();
  } catch (error) {
    console.error("Error updating modules information:", error);
    throw error;
  }
}

/**
 * Update sales module information
 */
function updateSalesModule() {
  const salesModule = veroERPKnowledge.modules.find(m => m.name === "المبيعات");
  if (salesModule) {
    // Update with latest information
    // This would typically involve analyzing the codebase
  }
}

/**
 * Update inventory module information
 */
function updateInventoryModule() {
  const inventoryModule = veroERPKnowledge.modules.find(m => m.name === "المخزون");
  if (inventoryModule) {
    // Update with latest information
    // This would typically involve analyzing the codebase
  }
}

/**
 * Update accounting module information
 */
function updateAccountingModule() {
  const accountingModule = veroERPKnowledge.modules.find(m => m.name === "المحاسبة");
  if (accountingModule) {
    // Update with latest information
    // This would typically involve analyzing the codebase
  }
}

/**
 * Get Arabic field name
 */
function getArabicFieldName(fieldName: string): string {
  const arabicFieldNames: Record<string, string> = {
    "id": "المعرف",
    "name": "الاسم",
    "description": "الوصف",
    "price": "السعر",
    "cost": "التكلفة",
    "quantity": "الكمية",
    "date": "التاريخ",
    "createdAt": "تاريخ الإنشاء",
    "updatedAt": "تاريخ التحديث",
    "email": "البريد الإلكتروني",
    "password": "كلمة المرور",
    "phone": "رقم الهاتف",
    "address": "العنوان",
    "status": "الحالة",
    "total": "الإجمالي",
    "subtotal": "المجموع الفرعي",
    "tax": "الضريبة",
    "discount": "الخصم",
    "paymentStatus": "حالة الدفع",
    "paymentMethod": "طريقة الدفع",
    "userId": "معرف المستخدم",
    "productId": "معرف المنتج",
    "contactId": "معرف جهة الاتصال",
    "branchId": "معرف الفرع",
    "warehouseId": "معرف المستودع"
  };
  
  return arabicFieldNames[fieldName] || fieldName;
}

/**
 * Get field description
 */
function getFieldDescription(tableName: string, fieldName: string): string {
  // This would typically involve analyzing the codebase or documentation
  // For now, return a generic description
  if (fieldName === "id") return "المعرف الفريد";
  if (fieldName === "name") return "الاسم";
  if (fieldName === "description") return "الوصف";
  if (fieldName === "price") return "السعر";
  if (fieldName === "cost") return "التكلفة";
  if (fieldName === "quantity") return "الكمية";
  if (fieldName === "date") return "التاريخ";
  if (fieldName === "createdAt") return "تاريخ الإنشاء";
  if (fieldName === "updatedAt") return "تاريخ التحديث";
  
  return `حقل ${fieldName} في جدول ${tableName}`;
}

/**
 * Check if field is required
 */
function isFieldRequired(tableName: string, fieldName: string): boolean {
  // This would typically involve analyzing the database schema
  // For now, assume id is always required
  return fieldName === "id";
}

/**
 * Check if field is unique
 */
function isFieldUnique(tableName: string, fieldName: string): boolean {
  // This would typically involve analyzing the database schema
  // For now, assume id is always unique
  return fieldName === "id";
}

/**
 * Get table description
 */
function getTableDescription(tableName: string): string {
  const tableDescriptions: Record<string, string> = {
    "user": "جدول المستخدمين في النظام",
    "sale": "جدول فواتير المبيعات",
    "saleItem": "جدول عناصر فواتير المبيعات",
    "purchase": "جدول فواتير المشتريات",
    "purchaseItem": "جدول عناصر فواتير المشتريات",
    "product": "جدول المنتجات",
    "inventory": "جدول المخزون",
    "contact": "جدول جهات الاتصال (العملاء والموردين)",
    "branch": "جدول الفروع",
    "warehouse": "جدول المستودعات",
    "paymentMethod": "جدول طرق الدفع",
    "paymentVoucher": "جدول سندات الدفع",
    "receiptVoucher": "جدول سندات القبض",
    "creditNote": "جدول إشعارات الخصم (مرتجعات المبيعات)",
    "creditNoteItem": "جدول عناصر إشعارات الخصم"
  };
  
  return tableDescriptions[tableName] || `جدول ${tableName}`;
}

/**
 * Get related tables
 */
function getRelatedTables(tableName: string): string[] {
  // This would typically involve analyzing the database schema
  // For now, return some common relationships
  const relationships: Record<string, string[]> = {
    "sale": ["saleItem", "contact", "user", "branch", "paymentMethod"],
    "saleItem": ["sale", "product"],
    "purchase": ["purchaseItem", "contact", "user", "branch", "paymentMethod"],
    "purchaseItem": ["purchase", "product"],
    "product": ["saleItem", "purchaseItem", "inventory"],
    "inventory": ["product", "warehouse"],
    "contact": ["sale", "purchase", "receiptVoucher", "paymentVoucher"],
    "user": ["sale", "purchase", "receiptVoucher", "paymentVoucher"],
    "branch": ["sale", "purchase", "warehouse", "user"],
    "warehouse": ["inventory", "branch"],
    "paymentMethod": ["sale", "purchase", "receiptVoucher", "paymentVoucher"],
    "creditNote": ["creditNoteItem", "sale", "contact", "user"],
    "creditNoteItem": ["creditNote", "product"]
  };
  
  return relationships[tableName] || [];
}
