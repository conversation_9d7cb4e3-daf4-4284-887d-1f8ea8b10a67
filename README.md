# VERO ERP System

A comprehensive ERP system for computer and laptop stores with customizable specifications.

## Features

- **Sales Management**: Track sales, manage customers, and generate invoices
- **Purchase Management**: Create purchase orders, manage suppliers
- **Inventory Management**: Track stock levels, manage warehouses
- **Product Configuration**: Manage products with customizable specifications
- **Accounting**: Track finances, generate reports
- **Multi-branch Support**: Manage multiple branches and warehouses

## Tech Stack

- **Frontend**: Next.js, React, TailwindCSS
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables:
   - Copy `.env.example` to `.env`
   - Update the database connection string and other variables

4. Run database migrations:
   ```bash
   npm run prisma:migrate
   ```

5. Seed the database:
   ```bash
   npm run db:seed
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

7. Open [http://localhost:3000](http://localhost:3000) in your browser

### Default Admin Credentials

- Email: <EMAIL>
- Password: admin123

## Project Structure

- `/src/app`: Next.js app router
- `/src/app/(auth)`: Authentication pages
- `/src/app/(dashboard)`: Dashboard and protected pages
- `/src/components`: Reusable components
- `/src/lib`: Utility functions and libraries
- `/prisma`: Database schema and migrations

## License

This project is licensed under the MIT License.
