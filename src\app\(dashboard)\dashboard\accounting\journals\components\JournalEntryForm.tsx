"use client";

import { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select-radix";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";

// Define the form schema
const journalEntrySchema = z.object({
  journalId: z.string().min(1, "Journal is required"),
  date: z.date(),
  description: z.string().min(1, "Description is required"),
  contactId: z.string().optional(),
  reference: z.string().optional(),
  entries: z.array(
    z.object({
      accountId: z.string().min(1, "Account is required"),
      isDebit: z.boolean(),
      amount: z.coerce.number().min(0.01, "Amount must be greater than 0"),
    })
  ).min(2, "At least two entries are required"),
});

type JournalEntryFormValues = z.infer<typeof journalEntrySchema>;

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
}

interface Journal {
  id: string;
  code: string;
  name: string;
  type: string;
}

interface Contact {
  id: string;
  name: string;
  phone: string;
}

export default function JournalEntryForm() {
  const [journals, setJournals] = useState<Journal[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form
  const form = useForm<JournalEntryFormValues>({
    resolver: zodResolver(journalEntrySchema),
    defaultValues: {
      date: new Date(),
      description: "",
      entries: [
        { accountId: "", isDebit: true, amount: 0 },
        { accountId: "", isDebit: false, amount: 0 },
      ],
    },
  });

  // Setup field array for entries
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "entries",
  });

  // Calculate totals
  const entries = form.watch("entries");
  const totalDebits = entries.reduce((sum, entry) => sum + (entry.isDebit ? Number(entry.amount) || 0 : 0), 0);
  const totalCredits = entries.reduce((sum, entry) => sum + (!entry.isDebit ? Number(entry.amount) || 0 : 0), 0);
  const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;

  // Fetch journals, accounts, and contacts
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch journals
        const journalsResponse = await fetch("/api/accounting/journals");
        if (journalsResponse.ok) {
          const journalsData = await journalsResponse.json();
          setJournals(journalsData.data || []);
        }

        // Fetch accounts
        const accountsResponse = await fetch("/api/accounting/accounts");
        if (accountsResponse.ok) {
          const accountsData = await accountsResponse.json();
          setAccounts(accountsData.data || []);
        }

        // Fetch contacts
        const contactsResponse = await fetch("/api/contacts");
        if (contactsResponse.ok) {
          const contactsData = await contactsResponse.json();
          setContacts(contactsData.data || []);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle form submission
  const onSubmit = async (data: JournalEntryFormValues) => {
    if (!isBalanced) {
      toast.error("Journal entry must be balanced (debits must equal credits)");
      return;
    }

    setIsSubmitting(true);
    try {
      // Transform data for API
      const apiData = {
        ...data,
        // Convert "none" to null or empty string for contactId
        contactId: data.contactId === "none" ? null : data.contactId,
        entries: data.entries.map(entry => ({
          accountId: entry.accountId,
          type: entry.isDebit ? "DEBIT" : "CREDIT",
          amount: entry.amount,
        })),
      };

      // Submit to API
      const response = await fetch("/api/accounting/journals/entries", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create journal entry");
      }

      toast.success("Journal entry created successfully");
      form.reset();
    } catch (error) {
      console.error("Error creating journal entry:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create journal entry");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add a new entry
  const addEntry = () => {
    append({ accountId: "", isDebit: fields.length % 2 === 0, amount: 0 });
  };

  // Add a balanced pair of entries
  const addBalancedPair = () => {
    const amount = 0;
    append({ accountId: "", isDebit: true, amount });
    append({ accountId: "", isDebit: false, amount });
  };

  // Copy amount from debit to credit
  const copyDebitToCredit = () => {
    const debitAmount = entries.reduce((sum, entry) => sum + (entry.isDebit ? Number(entry.amount) || 0 : 0), 0);
    const creditAmount = entries.reduce((sum, entry) => sum + (!entry.isDebit ? Number(entry.amount) || 0 : 0), 0);

    if (debitAmount > creditAmount) {
      const difference = debitAmount - creditAmount;
      // Find the first credit entry with zero amount
      const creditIndex = entries.findIndex(entry => !entry.isDebit && (Number(entry.amount) === 0));
      if (creditIndex >= 0) {
        form.setValue(`entries.${creditIndex}.amount`, difference);
      } else {
        // Add a new credit entry with the difference
        append({ accountId: "", isDebit: false, amount: difference });
      }
    }
  };

  // Copy amount from credit to debit
  const copyDebitFromCredit = () => {
    const debitAmount = entries.reduce((sum, entry) => sum + (entry.isDebit ? Number(entry.amount) || 0 : 0), 0);
    const creditAmount = entries.reduce((sum, entry) => sum + (!entry.isDebit ? Number(entry.amount) || 0 : 0), 0);

    if (creditAmount > debitAmount) {
      const difference = creditAmount - debitAmount;
      // Find the first debit entry with zero amount
      const debitIndex = entries.findIndex(entry => entry.isDebit && (Number(entry.amount) === 0));
      if (debitIndex >= 0) {
        form.setValue(`entries.${debitIndex}.amount`, difference);
      } else {
        // Add a new debit entry with the difference
        append({ accountId: "", isDebit: true, amount: difference });
      }
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-green-50 border-b">
        <CardTitle className="flex items-center justify-between">
          <span>Create Journal Entry / إنشاء قيد محاسبي</span>
          <div className="flex items-center space-x-2 text-sm font-normal">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
              <span>Debit / مدين</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
              <span>Credit / دائن</span>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                <span className="ml-2 text-gray-500">Loading...</span>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Journal */}
                  <FormField
                    control={form.control}
                    name="journalId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Journal</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a journal" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {journals.map((journal) => (
                              <SelectItem key={journal.id} value={journal.id}>
                                {journal.name} ({journal.code})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Date */}
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date</FormLabel>
                        <FormControl>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact */}
                <FormField
                  control={form.control}
                  name="contactId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact (Optional)</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a contact" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {contacts.map((contact) => (
                            <SelectItem key={contact.id} value={contact.id}>
                              {contact.name} ({contact.phone})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Reference */}
                <FormField
                  control={form.control}
                  name="reference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference (Optional)</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Entries */}
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Entries / القيود</h3>
                    <div className="flex space-x-2">
                      <Button type="button" variant="outline" size="sm" onClick={copyDebitToCredit}>
                        مدين ← دائن
                      </Button>
                      <Button type="button" variant="outline" size="sm" onClick={copyDebitFromCredit}>
                        مدين → دائن
                      </Button>
                      <Button type="button" variant="outline" size="sm" onClick={addBalancedPair}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Pair
                      </Button>
                      <Button type="button" variant="outline" size="sm" onClick={addEntry}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Entry
                      </Button>
                    </div>
                  </div>

                  {fields.map((field, index) => {
                    const isDebit = form.watch(`entries.${index}.isDebit`);
                    return (
                    <div
                      key={field.id}
                      className={`grid grid-cols-12 gap-2 items-end border p-3 rounded-md ${
                        isDebit ? 'border-l-4 border-l-blue-500' : 'border-l-4 border-l-green-500'
                      }`}
                    >
                      {/* Account */}
                      <div className="col-span-5">
                        <FormField
                          control={form.control}
                          name={`entries.${index}.accountId`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex justify-between">
                                <span>Account / الحساب</span>
                                <span className={`text-xs font-bold px-2 py-1 rounded ${
                                  isDebit ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                                }`}>
                                  {isDebit ? 'DEBIT / مدين' : 'CREDIT / دائن'}
                                </span>
                              </FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select an account" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {accounts.map((account) => (
                                    <SelectItem key={account.id} value={account.id}>
                                      {account.name} ({account.code})
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Debit/Credit */}
                      <div className="col-span-3">
                        <FormField
                          control={form.control}
                          name={`entries.${index}.isDebit`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Type / النوع</FormLabel>
                              <Select
                                onValueChange={(value) => field.onChange(value === "debit")}
                                defaultValue={field.value ? "debit" : "credit"}
                              >
                                <FormControl>
                                  <SelectTrigger className={
                                    field.value ? 'bg-blue-50 border-blue-200' : 'bg-green-50 border-green-200'
                                  }>
                                    <SelectValue />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="debit">Debit / مدين</SelectItem>
                                  <SelectItem value="credit">Credit / دائن</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Amount */}
                      <div className="col-span-3">
                        <FormField
                          control={form.control}
                          name={`entries.${index}.amount`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Amount / المبلغ</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className={
                                    isDebit ? 'bg-blue-50 border-blue-200' : 'bg-green-50 border-green-200'
                                  }
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Remove button */}
                      <div className="col-span-1">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => fields.length > 2 && remove(index)}
                          disabled={fields.length <= 2}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  )})}

                  {/* Totals */}
                  <div className="mt-4 p-4 bg-gray-50 rounded-md space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                        <span className="font-medium">Total Debits / إجمالي المدين:</span>
                        <span className="ml-2 font-bold text-blue-700">{new Intl.NumberFormat('en-EG', {
                          style: 'currency',
                          currency: 'EGP',
                        }).format(totalDebits)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="font-medium">Total Credits / إجمالي الدائن:</span>
                        <span className="ml-2 font-bold text-green-700">{new Intl.NumberFormat('en-EG', {
                          style: 'currency',
                          currency: 'EGP',
                        }).format(totalCredits)}</span>
                      </div>
                    </div>

                    {/* Balance progress bar */}
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Balance / التوازن</span>
                        <span className={isBalanced ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
                          {isBalanced ? "Balanced / متوازن" : "Not Balanced / غير متوازن"}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        {totalDebits > 0 || totalCredits > 0 ? (
                          <>
                            <div
                              className="bg-blue-500 h-2.5 rounded-l-full"
                              style={{
                                width: `${Math.min(100, (totalDebits / Math.max(totalDebits, totalCredits)) * 100)}%`,
                                float: 'left'
                              }}
                            ></div>
                            <div
                              className="bg-green-500 h-2.5 rounded-r-full"
                              style={{
                                width: `${Math.min(100, (totalCredits / Math.max(totalDebits, totalCredits)) * 100)}%`,
                                float: 'right'
                              }}
                            ></div>
                          </>
                        ) : (
                          <div className="bg-gray-400 h-2.5 rounded-full w-0"></div>
                        )}
                      </div>
                    </div>

                    {/* Balance warning */}
                    {!isBalanced && entries.some(e => e.amount > 0) && (
                      <div className="text-red-500 text-sm p-2 bg-red-50 rounded border border-red-200">
                        <span className="font-bold">Warning:</span> Journal entry is not balanced. Debits must equal credits.
                        <br />
                        <span className="font-bold">تحذير:</span> القيد المحاسبي غير متوازن. يجب أن يتساوى المدين مع الدائن.
                        <br />
                        <span className="font-bold">Difference / الفرق:</span> {Math.abs(totalDebits - totalCredits).toFixed(2)} EGP
                      </div>
                    )}

                    {/* Balance success */}
                    {isBalanced && entries.some(e => e.amount > 0) && (
                      <div className="text-green-500 text-sm p-2 bg-green-50 rounded border border-green-200">
                        <span className="font-bold">Success:</span> Journal entry is balanced. Debits equal credits.
                        <br />
                        <span className="font-bold">نجاح:</span> القيد المحاسبي متوازن. المدين يساوي الدائن.
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </CardContent>
          <CardFooter className="flex justify-between space-x-2">
            <div className="text-sm text-gray-500">
              * All fields marked with an asterisk are required.
              <br />
              * القيود المحاسبية يجب أن تكون متوازنة (المدين = الدائن).
            </div>
            <Button
              type="submit"
              disabled={isSubmitting || isLoading || !isBalanced}
              className="bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600"
              size="lg"
            >
              {isSubmitting && <Loader2 className="h-5 w-5 mr-2 animate-spin" />}
              {isBalanced ? 'Create Balanced Entry / إنشاء قيد متوازن' : 'Balance Entry First / وازن القيد أولاً'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
