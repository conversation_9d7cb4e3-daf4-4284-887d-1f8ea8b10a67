/**
 * <PERSON><PERSON><PERSON> to apply the accounting module migration
 * 
 * This script will:
 * 1. Apply the accounting module migration SQL
 * 2. Initialize the accounting module with default accounts and journals
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

// Initialize Prisma client
const prisma = new PrismaClient();

// Path to migration file
const migrationFilePath = path.join(__dirname, '..', 'prisma', 'migrations', 'accounting_module_update.sql');

// Function to execute SQL file
async function executeSqlFile(filePath) {
  try {
    console.log(`Reading SQL file: ${filePath}`);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Get database URL from environment
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable is not set');
    }
    
    // Parse database URL to get connection details
    const dbUrlRegex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
    const match = databaseUrl.match(dbUrlRegex);
    
    if (!match) {
      throw new Error('Invalid DATABASE_URL format');
    }
    
    const [, user, password, host, port, database] = match;
    
    // Create a temporary SQL file with the migration
    const tempSqlFile = path.join(__dirname, 'temp_migration.sql');
    fs.writeFileSync(tempSqlFile, sql);
    
    // Execute the SQL file using psql
    console.log('Executing SQL migration...');
    execSync(`psql -U ${user} -h ${host} -p ${port} -d ${database} -f ${tempSqlFile}`, {
      env: { ...process.env, PGPASSWORD: password },
    });
    
    // Remove the temporary file
    fs.unlinkSync(tempSqlFile);
    
    console.log('SQL migration executed successfully');
  } catch (error) {
    console.error('Error executing SQL file:', error);
    throw error;
  }
}

// Default accounts to create
const defaultAccounts = [
  { code: "1000", name: "Cash", type: "ASSET" },
  { code: "1010", name: "Vodafone Cash", type: "ASSET" },
  { code: "1020", name: "Bank Account", type: "ASSET" },
  { code: "1030", name: "Credit Card Payments", type: "ASSET" },
  { code: "1200", name: "Accounts Receivable", type: "ASSET" },
  { code: "1300", name: "Inventory", type: "ASSET" },
  { code: "2000", name: "Accounts Payable", type: "LIABILITY" },
  { code: "2100", name: "Accrued Expenses", type: "LIABILITY" },
  { code: "3000", name: "Owner's Equity", type: "EQUITY" },
  { code: "3100", name: "Retained Earnings", type: "EQUITY" },
  { code: "4000", name: "Sales Revenue", type: "REVENUE" },
  { code: "4900", name: "Sales Returns", type: "REVENUE" },
  { code: "5000", name: "Cost of Goods Sold", type: "EXPENSE" },
  { code: "5100", name: "Salaries Expense", type: "EXPENSE" },
  { code: "5200", name: "Rent Expense", type: "EXPENSE" },
  { code: "5300", name: "Utilities Expense", type: "EXPENSE" },
];

// Default journals to create
const defaultJournals = [
  { code: "CASH-001", name: "Cash Journal", type: "CASH", paymentMethod: "CASH" },
  { code: "VODA-001", name: "Vodafone Cash Journal", type: "VODAFONE_CASH", paymentMethod: "VODAFONE_CASH" },
  { code: "BANK-001", name: "Bank Journal", type: "BANK_TRANSFER", paymentMethod: "BANK_TRANSFER" },
  { code: "VISA-001", name: "Credit Card Journal", type: "VISA", paymentMethod: "CREDIT_CARD" },
  { code: "CUST-001", name: "Customer Accounts Journal", type: "CUSTOMER_ACCOUNT", paymentMethod: "CUSTOMER_ACCOUNT" },
  { code: "GEN-001", name: "General Journal", type: "GENERAL" },
];

// Function to initialize accounting module
async function initializeAccountingModule() {
  try {
    console.log('Initializing accounting module...');
    
    // Create default accounts
    for (const account of defaultAccounts) {
      // Check if account already exists
      const existingAccount = await prisma.account.findFirst({
        where: {
          OR: [
            { code: account.code },
            { name: account.name, type: account.type },
          ],
        },
      });
      
      if (!existingAccount) {
        // Create the account
        await prisma.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            isActive: true,
          },
        });
        console.log(`Created account: ${account.name} (${account.code})`);
      } else {
        console.log(`Account already exists: ${account.name} (${account.code})`);
      }
    }
    
    // Create default journals
    for (const journal of defaultJournals) {
      // Check if journal already exists
      const existingJournal = await prisma.journal.findFirst({
        where: {
          OR: [
            { code: journal.code },
            { name: journal.name, type: journal.type },
          ],
        },
      });
      
      if (!existingJournal) {
        // Create the journal
        await prisma.journal.create({
          data: {
            code: journal.code,
            name: journal.name,
            type: journal.type,
            paymentMethod: journal.paymentMethod,
            isActive: true,
          },
        });
        console.log(`Created journal: ${journal.name} (${journal.code})`);
      } else {
        console.log(`Journal already exists: ${journal.name} (${journal.code})`);
      }
    }
    
    // Create current fiscal year and period if they don't exist
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1); // January 1st
    const endDate = new Date(currentYear, 11, 31); // December 31st
    
    // Check if fiscal year already exists
    let fiscalYear = await prisma.fiscalYear.findFirst({
      where: {
        startDate: {
          gte: new Date(currentYear, 0, 1),
          lt: new Date(currentYear + 1, 0, 1),
        },
      },
    });
    
    if (!fiscalYear) {
      // Create fiscal year
      fiscalYear = await prisma.fiscalYear.create({
        data: {
          name: `Fiscal Year ${currentYear}`,
          startDate,
          endDate,
          isClosed: false,
        },
      });
      console.log(`Created fiscal year: ${fiscalYear.name}`);
      
      // Create fiscal periods (quarters)
      for (let i = 0; i < 4; i++) {
        const periodStartDate = new Date(currentYear, i * 3, 1);
        const periodEndDate = new Date(currentYear, (i + 1) * 3, 0);
        
        await prisma.fiscalPeriod.create({
          data: {
            fiscalYearId: fiscalYear.id,
            name: `Q${i + 1} ${currentYear}`,
            startDate: periodStartDate,
            endDate: periodEndDate,
            isClosed: false,
          },
        });
        console.log(`Created fiscal period: Q${i + 1} ${currentYear}`);
      }
    } else {
      console.log(`Fiscal year already exists: ${fiscalYear.name}`);
    }
    
    console.log('Accounting module initialized successfully');
  } catch (error) {
    console.error('Error initializing accounting module:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Starting accounting module migration...');
    
    // Apply SQL migration
    await executeSqlFile(migrationFilePath);
    
    // Initialize accounting module
    await initializeAccountingModule();
    
    console.log('Accounting module migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
