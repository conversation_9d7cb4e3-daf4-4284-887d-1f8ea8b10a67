"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  Loader2,
  Plus,
  Search,
  Filter,
  AlertTriangle,
  Clock,
  CheckCircle2,
  Truck,
  XCircle,
  Settings,
  ArrowUpDown
} from "lucide-react";
import {
  getStatusLabel,
  getStatusColor,
  calculateServiceDays,
  isServiceOverdue
} from "@/lib/maintenance";
import { formatDistanceToNow, format } from "date-fns";

interface MaintenanceService {
  id: string;
  serviceNumber: string;
  deviceType: string;
  brand: string;
  model?: string;
  status: string;
  receivedDate: string;
  estimatedCompletionDate?: string;
  contact: {
    id: string;
    name: string;
    phone: string;
  };
  _count: {
    parts: number;
    statusHistory: number;
  };
}

export default function MaintenancePage() {
  const router = useRouter();
  const [services, setServices] = useState<MaintenanceService[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showOverdueOnly, setShowOverdueOnly] = useState(false);
  const [overdueCount, setOverdueCount] = useState(0);

  // Fetch maintenance services
  const fetchServices = async () => {
    setIsLoading(true);
    try {
      let url = "/api/maintenance";
      const params = new URLSearchParams();

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      if (showOverdueOnly) {
        params.append("overdue", "true");
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch maintenance services");
      }

      const data = await response.json();
      setServices(data.data || []);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch overdue count
  const fetchOverdueCount = async () => {
    try {
      // Use a more robust approach with timeout and credentials
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch("/api/maintenance/overdue", {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.warn("Overdue services API returned status:", response.status);
        // Don't throw, just set a default value
        setOverdueCount(0);
        return;
      }

      const data = await response.json();

      // The API now returns a consistent format with count
      if (data && typeof data === 'object' && typeof data.count === 'number') {
        setOverdueCount(data.count);
      } else if (data && typeof data === 'object' && Array.isArray(data.data)) {
        setOverdueCount(data.data.length || 0);
      } else if (Array.isArray(data)) {
        setOverdueCount(data.length || 0);
      } else {
        console.warn("Unexpected response format from overdue API:", data);
        setOverdueCount(0);
      }
    } catch (error) {
      // Handle abort errors differently
      if (error.name === 'AbortError') {
        console.warn("Overdue count request timed out");
      } else {
        console.error("Error fetching overdue count:", error);
      }

      // Always set a default value on error
      setOverdueCount(0);
    }
  };

  useEffect(() => {
    fetchServices();
    fetchOverdueCount();
  }, [statusFilter, showOverdueOnly]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchServices();
  };

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
  };

  const handleToggleOverdue = () => {
    setShowOverdueOnly(!showOverdueOnly);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Maintenance Services</h1>
        <Link
          href="/dashboard/maintenance/new"
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 font-medium flex items-center"
        >
          <Plus className="h-5 w-5 mr-1" />
          New Service
        </Link>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Status filters */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => handleStatusFilterChange("all")}
            className={`px-3 py-1 rounded-md font-medium ${
              statusFilter === "all"
                ? "bg-indigo-100 text-indigo-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            All
          </button>
          <button
            onClick={() => handleStatusFilterChange("RECEIVED")}
            className={`px-3 py-1 rounded-md font-medium flex items-center ${
              statusFilter === "RECEIVED"
                ? "bg-blue-100 text-blue-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <Clock className="h-4 w-4 mr-1" />
            Received
          </button>
          <button
            onClick={() => handleStatusFilterChange("IN_PROGRESS")}
            className={`px-3 py-1 rounded-md font-medium flex items-center ${
              statusFilter === "IN_PROGRESS"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <Settings className="h-4 w-4 mr-1" />
            In Progress
          </button>
          <button
            onClick={() => handleStatusFilterChange("WAITING_FOR_PARTS")}
            className={`px-3 py-1 rounded-md font-medium flex items-center ${
              statusFilter === "WAITING_FOR_PARTS"
                ? "bg-purple-100 text-purple-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <Truck className="h-4 w-4 mr-1" />
            Waiting for Parts
          </button>
          <button
            onClick={() => handleStatusFilterChange("COMPLETED")}
            className={`px-3 py-1 rounded-md font-medium flex items-center ${
              statusFilter === "COMPLETED"
                ? "bg-green-100 text-green-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <CheckCircle2 className="h-4 w-4 mr-1" />
            Completed
          </button>
          <button
            onClick={() => handleStatusFilterChange("DELIVERED")}
            className={`px-3 py-1 rounded-md font-medium flex items-center ${
              statusFilter === "DELIVERED"
                ? "bg-gray-100 text-gray-800 border border-gray-300"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <Truck className="h-4 w-4 mr-1" />
            Delivered
          </button>
          <button
            onClick={() => handleStatusFilterChange("CANCELLED")}
            className={`px-3 py-1 rounded-md font-medium flex items-center ${
              statusFilter === "CANCELLED"
                ? "bg-red-100 text-red-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <XCircle className="h-4 w-4 mr-1" />
            Cancelled
          </button>
          <button
            onClick={handleToggleOverdue}
            className={`px-3 py-1 rounded-md font-medium flex items-center ${
              showOverdueOnly
                ? "bg-red-100 text-red-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            <AlertTriangle className="h-4 w-4 mr-1" />
            Overdue ({overdueCount})
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex w-full sm:w-96">
          <div className="relative w-full">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by service #, device, customer..."
              className="border border-gray-300 rounded-l-md px-4 py-2 w-full text-gray-900 font-medium"
            />
            {searchTerm && (
              <button
                type="button"
                onClick={() => {
                  setSearchTerm("");
                  fetchServices();
                }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>
          <button
            type="submit"
            className="bg-indigo-600 text-white px-4 py-2 rounded-r-md hover:bg-indigo-700 font-medium"
          >
            <Search className="h-5 w-5" />
          </button>
        </form>
      </div>

      {/* Services table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Service #
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Customer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Device
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Received
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Days
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-5 w-5 animate-spin mr-2" />
                      Loading...
                    </div>
                  </td>
                </tr>
              ) : services.length > 0 ? (
                services.map((service) => {
                  const days = calculateServiceDays(new Date(service.receivedDate));
                  const isOverdue = isServiceOverdue(new Date(service.receivedDate)) &&
                                   !["DELIVERED", "CANCELLED"].includes(service.status);

                  return (
                    <tr key={service.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{service.serviceNumber}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{service.contact.name}</div>
                        <div className="text-sm text-gray-500">{service.contact.phone}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{service.deviceType}</div>
                        <div className="text-sm text-gray-500">{service.brand} {service.model}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(service.status)}`}>
                          {getStatusLabel(service.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {format(new Date(service.receivedDate), "dd/MM/yyyy")}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(service.receivedDate), { addSuffix: true })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          isOverdue ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"
                        }`}>
                          {days} days
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link
                          href={`/dashboard/maintenance/${service.id}`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          View
                        </Link>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    No maintenance services found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
