import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";

// Define fallback static component types in case no components are found in the database
const FALLBACK_COMPONENT_TYPES = [
  {
    id: "ram",
    name: "RAM",
    description: "Random Access Memory",
  },
  {
    id: "hdd",
    name: "HDD",
    description: "Hard Disk Drive",
  },
  {
    id: "ssd",
    name: "SSD",
    description: "Solid State Drive",
  },
  {
    id: "nvme",
    name: "NVMe",
    description: "NVMe Solid State Drive",
  },
  {
    id: "cpu",
    name: "CPU",
    description: "Central Processing Unit",
  },
  {
    id: "gpu",
    name: "GPU",
    description: "Graphics Processing Unit",
  },
];

// Schema for validating component type data
const componentTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
});

export async function GET() {
  try {
    // Get unique component types from products in the database
    const products = await db.product.findMany({
      where: {
        isComponent: true,
        componentType: {
          not: null,
        },
      },
      select: {
        componentType: true,
      },
      distinct: ['componentType'],
    });

    console.log("Found component types in database:", products);

    // Extract unique component types
    const componentTypes = products
      .filter(product => product.componentType) // Filter out null values
      .map(product => ({
        id: product.componentType!.toLowerCase().replace(/\s+/g, "-"),
        name: product.componentType!,
        description: `${product.componentType} components`,
      }));

    // If no component types found in database, use fallback types
    if (componentTypes.length === 0) {
      console.log("No component types found in database, using fallback types");
      return NextResponse.json(FALLBACK_COMPONENT_TYPES);
    }

    console.log("Returning component types:", componentTypes);
    return NextResponse.json(componentTypes);
  } catch (error) {
    console.error("Error fetching component types:", error);
    // If there's an error, return fallback types
    return NextResponse.json(FALLBACK_COMPONENT_TYPES);
  }
}

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = componentTypeSchema.safeParse(body);
    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors
        .map((err) => err.message)
        .join(", ");
      return NextResponse.json(
        { error: `Validation error: ${errorMessage}` },
        { status: 400 }
      );
    }

    const { name, description } = validationResult.data;

    // Check if component type with the same name already exists
    const existingComponentType = COMPONENT_TYPES.find(
      (type) => type.name.toLowerCase() === name.toLowerCase()
    );

    if (existingComponentType) {
      return NextResponse.json(
        { error: "A component type with this name already exists" },
        { status: 400 }
      );
    }

    // Create a new component type (in a real app, this would be saved to the database)
    const newComponentType = {
      id: name.toLowerCase().replace(/\s+/g, "-"),
      name,
      description: description || "",
    };

    // In a real app, you would save this to the database
    // For now, we'll just return it
    return NextResponse.json(newComponentType, { status: 201 });
  } catch (error) {
    console.error("Error creating component type:", error);
    return NextResponse.json(
      { error: "Failed to create component type" },
      { status: 500 }
    );
  }
}
