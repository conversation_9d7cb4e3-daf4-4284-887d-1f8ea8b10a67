"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Calendar, Plus, Search, Tag, Percent, DollarSign, ShoppingBag, Users, Calendar as CalendarIcon } from "lucide-react";
import { format, addDays, addMonths, startOfMonth, endOf<PERSON>onth, startOfYear, endOfYear, isAfter, isBefore, isEqual } from "date-fns";
import DiscountBadge from "../../../discounts/components/DiscountBadge";

// Define types
interface Discount {
  id: string;
  name: string;
  description: string | null;
  type: "PERCENTAGE" | "FIXED_AMOUNT";
  value: number;
  scope: "ITEM" | "INVOICE" | "CUSTOMER";
  minAmount: number | null;
  maxAmount: number | null;
  startDate: string | null;
  endDate: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  categoryId: string | null;
  productId: string | null;
  category?: {
    id: string;
    name: string;
  } | null;
  product?: {
    id: string;
    name: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  categoryId: string;
}

interface SeasonalCampaign {
  id: string;
  name: string;
  description: string | null;
  startDate: string;
  endDate: string;
  discounts: Discount[];
}

export default function SeasonalDiscountsPage() {
  // State for discounts
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [filteredDiscounts, setFilteredDiscounts] = useState<Discount[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // State for campaigns
  const [campaigns, setCampaigns] = useState<SeasonalCampaign[]>([]);
  const [activeCampaigns, setActiveCampaigns] = useState<SeasonalCampaign[]>([]);
  const [upcomingCampaigns, setUpcomingCampaigns] = useState<SeasonalCampaign[]>([]);
  const [pastCampaigns, setPastCampaigns] = useState<SeasonalCampaign[]>([]);

  // State for new campaign form
  const [showNewCampaignForm, setShowNewCampaignForm] = useState(false);
  const [campaignName, setCampaignName] = useState("");
  const [campaignDescription, setCampaignDescription] = useState("");
  const [campaignStartDate, setCampaignStartDate] = useState<Date | undefined>(undefined);
  const [campaignEndDate, setCampaignEndDate] = useState<Date | undefined>(undefined);
  const [selectedDiscounts, setSelectedDiscounts] = useState<string[]>([]);

  // State for filters
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("active");
  const [filterScope, setFilterScope] = useState("all");
  const [filterType, setFilterType] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");

  // Router
  const router = useRouter();

  // Fetch discounts
  useEffect(() => {
    const fetchDiscounts = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/discounts");
        if (response.ok) {
          const data = await response.json();
          setDiscounts(data);
        } else {
          console.error("Failed to fetch discounts");
        }
      } catch (error) {
        console.error("Error fetching discounts:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDiscounts();
  }, []);

  // Fetch campaigns
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        const response = await fetch("/api/discounts/campaigns");
        if (response.ok) {
          const data = await response.json();
          setCampaigns(data);
        } else {
          console.error("Failed to fetch campaigns");
        }
      } catch (error) {
        console.error("Error fetching campaigns:", error);
      }
    };

    fetchCampaigns();
  }, []);

  // Filter and categorize campaigns
  useEffect(() => {
    const now = new Date();

    const active = campaigns.filter(campaign => {
      const startDate = new Date(campaign.startDate);
      const endDate = new Date(campaign.endDate);
      return (isEqual(startDate, now) || isBefore(startDate, now)) &&
             (isEqual(endDate, now) || isAfter(endDate, now));
    });

    const upcoming = campaigns.filter(campaign => {
      const startDate = new Date(campaign.startDate);
      return isAfter(startDate, now);
    });

    const past = campaigns.filter(campaign => {
      const endDate = new Date(campaign.endDate);
      return isBefore(endDate, now);
    });

    setActiveCampaigns(active);
    setUpcomingCampaigns(upcoming);
    setPastCampaigns(past);
  }, [campaigns]);

  // Filter discounts
  useEffect(() => {
    let result = [...discounts];

    // Apply search filter
    if (searchTerm) {
      result = result.filter(discount =>
        discount.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (discount.description && discount.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply scope filter
    if (filterScope !== "all") {
      result = result.filter(discount => discount.scope === filterScope);
    }

    // Apply type filter
    if (filterType !== "all") {
      result = result.filter(discount => discount.type === filterType);
    }

    // Apply status filter
    if (filterStatus !== "all") {
      if (filterStatus === "active") {
        result = result.filter(discount => discount.isActive);
      } else {
        result = result.filter(discount => !discount.isActive);
      }
    }

    // Apply tab filter
    if (activeTab === 'active') {
      result = result.filter(discount => discount.isActive);
    } else if (activeTab === 'inactive') {
      result = result.filter(discount => !discount.isActive);
    } else if (activeTab === 'expired') {
      const now = new Date();
      result = result.filter(discount =>
        discount.endDate && new Date(discount.endDate) < now
      );
    } else if (activeTab === 'upcoming') {
      const now = new Date();
      result = result.filter(discount =>
        discount.startDate && new Date(discount.startDate) > now
      );
    }

    setFilteredDiscounts(result);
  }, [discounts, searchTerm, filterScope, filterStatus, filterType, activeTab]);

  // Toggle discount selection
  const toggleDiscountSelection = (id: string) => {
    if (selectedDiscounts.includes(id)) {
      setSelectedDiscounts(selectedDiscounts.filter(discountId => discountId !== id));
    } else {
      setSelectedDiscounts([...selectedDiscounts, id]);
    }
  };

  // Create campaign
  const createCampaign = async () => {
    if (!campaignName || !campaignStartDate || !campaignEndDate || selectedDiscounts.length === 0) {
      alert("Please fill in all required fields and select at least one discount");
      return;
    }

    try {
      const response = await fetch("/api/discounts/campaigns", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: campaignName,
          description: campaignDescription,
          startDate: campaignStartDate.toISOString(),
          endDate: campaignEndDate.toISOString(),
          discountIds: selectedDiscounts,
        }),
      });

      if (response.ok) {
        // Reset form
        setCampaignName("");
        setCampaignDescription("");
        setCampaignStartDate(undefined);
        setCampaignEndDate(undefined);
        setSelectedDiscounts([]);
        setShowNewCampaignForm(false);

        // Refresh campaigns
        const campaignsResponse = await fetch("/api/discounts/campaigns");
        if (campaignsResponse.ok) {
          const data = await campaignsResponse.json();
          setCampaigns(data);
        }
      } else {
        const error = await response.json();
        alert(`Failed to create campaign: ${error.error}`);
      }
    } catch (error) {
      console.error("Error creating campaign:", error);
      alert("An error occurred while creating the campaign");
    }
  };

  // Create predefined campaign
  const createPredefinedCampaign = async (type: string) => {
    const now = new Date();
    let name = "";
    let description = "";
    let startDate = new Date();
    let endDate = new Date();

    switch (type) {
      case "summer":
        name = "Summer Sale";
        description = "Special discounts for summer season";
        startDate = new Date(now.getFullYear(), 5, 1); // June 1
        endDate = new Date(now.getFullYear(), 7, 31); // August 31
        break;
      case "winter":
        name = "Winter Sale";
        description = "Special discounts for winter season";
        startDate = new Date(now.getFullYear(), 11, 1); // December 1
        endDate = new Date(now.getFullYear() + 1, 1, 28); // February 28
        break;
      case "backToSchool":
        name = "Back to School";
        description = "Discounts for back to school season";
        startDate = new Date(now.getFullYear(), 7, 15); // August 15
        endDate = new Date(now.getFullYear(), 8, 30); // September 30
        break;
      case "holiday":
        name = "Holiday Season";
        description = "Special discounts for the holiday season";
        startDate = new Date(now.getFullYear(), 10, 15); // November 15
        endDate = new Date(now.getFullYear(), 11, 31); // December 31
        break;
      default:
        return;
    }

    setCampaignName(name);
    setCampaignDescription(description);
    setCampaignStartDate(startDate);
    setCampaignEndDate(endDate);
    setShowNewCampaignForm(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Seasonal Discounts</h2>
          <p className="text-muted-foreground">
            Manage seasonal discount campaigns and promotions
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => createPredefinedCampaign("summer")}>
            <CalendarIcon className="mr-2 h-4 w-4" />
            Summer Sale
          </Button>
          <Button variant="outline" onClick={() => createPredefinedCampaign("winter")}>
            <CalendarIcon className="mr-2 h-4 w-4" />
            Winter Sale
          </Button>
          <Button onClick={() => setShowNewCampaignForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Campaign
          </Button>
        </div>
      </div>

      {/* Campaign Creation Form */}
      {showNewCampaignForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Seasonal Campaign</CardTitle>
            <CardDescription>
              Set up a new seasonal discount campaign with multiple discounts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="campaign-name">Campaign Name</Label>
                  <Input
                    id="campaign-name"
                    placeholder="Summer Sale 2024"
                    value={campaignName}
                    onChange={(e) => setCampaignName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="campaign-description">Description (Optional)</Label>
                  <Input
                    id="campaign-description"
                    placeholder="Special discounts for summer season"
                    value={campaignDescription}
                    onChange={(e) => setCampaignDescription(e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Start Date</Label>
                  <DatePicker
                    date={campaignStartDate}
                    setDate={setCampaignStartDate}
                    placeholder="Select start date"
                  />
                </div>
                <div className="space-y-2">
                  <Label>End Date</Label>
                  <DatePicker
                    date={campaignEndDate}
                    setDate={setCampaignEndDate}
                    placeholder="Select end date"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Select Discounts for this Campaign</Label>
                <div className="border rounded-md p-4">
                  <div className="flex items-center space-x-2 mb-4">
                    <Search className="h-4 w-4 text-gray-500" />
                    <Input
                      placeholder="Search discounts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="flex-1"
                    />
                    <Select value={filterScope} onValueChange={setFilterScope}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by scope" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Scopes</SelectItem>
                        <SelectItem value="INVOICE">Invoice</SelectItem>
                        <SelectItem value="ITEM">Item</SelectItem>
                        <SelectItem value="CUSTOMER">Customer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-[300px] overflow-y-auto">
                    {filteredDiscounts.map((discount) => (
                      <div
                        key={discount.id}
                        className={`border rounded-md p-3 cursor-pointer transition-all ${
                          selectedDiscounts.includes(discount.id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => toggleDiscountSelection(discount.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <Checkbox checked={selectedDiscounts.includes(discount.id)} />
                          <div>
                            <h4 className="font-medium">{discount.name}</h4>
                            <div className="flex items-center mt-1 space-x-2">
                              <DiscountBadge
                                type={discount.type}
                                scope={discount.scope}
                                value={discount.value}
                                size="sm"
                              />
                              {discount.startDate && discount.endDate && (
                                <Badge variant="outline" className="text-xs">
                                  {format(new Date(discount.startDate), 'MMM d')} - {format(new Date(discount.endDate), 'MMM d, yyyy')}
                                </Badge>
                              )}
                            </div>
                            {discount.description && (
                              <p className="text-xs text-gray-500 mt-1">{discount.description}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {filteredDiscounts.length === 0 && (
                    <div className="text-center py-4 text-gray-500">
                      No discounts found matching your filters
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowNewCampaignForm(false)}>
                  Cancel
                </Button>
                <Button onClick={createCampaign} disabled={selectedDiscounts.length === 0}>
                  Create Campaign
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Campaigns */}
      <Card>
        <CardHeader>
          <CardTitle>Active Campaigns</CardTitle>
          <CardDescription>
            Currently running seasonal discount campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          {activeCampaigns.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No active campaigns at the moment
            </div>
          ) : (
            <div className="grid gap-4">
              {activeCampaigns.map((campaign) => (
                <div key={campaign.id} className="border rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-medium text-lg">{campaign.name}</h3>
                      {campaign.description && (
                        <p className="text-sm text-gray-500 mt-1">{campaign.description}</p>
                      )}
                      <div className="flex items-center mt-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>
                          {format(new Date(campaign.startDate), 'MMM d')} - {format(new Date(campaign.endDate), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Active</Badge>
                  </div>

                  <Separator className="my-3" />

                  <div className="flex flex-wrap gap-2 mt-2">
                    {campaign.discounts.map((discount) => (
                      <DiscountBadge
                        key={discount.id}
                        type={discount.type}
                        scope={discount.scope}
                        value={discount.value}
                        size="sm"
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upcoming Campaigns */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Campaigns</CardTitle>
          <CardDescription>
            Scheduled seasonal discount campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          {upcomingCampaigns.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No upcoming campaigns scheduled
            </div>
          ) : (
            <div className="grid gap-4">
              {upcomingCampaigns.map((campaign) => (
                <div key={campaign.id} className="border rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-medium text-lg">{campaign.name}</h3>
                      {campaign.description && (
                        <p className="text-sm text-gray-500 mt-1">{campaign.description}</p>
                      )}
                      <div className="flex items-center mt-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>
                          {format(new Date(campaign.startDate), 'MMM d')} - {format(new Date(campaign.endDate), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800">Upcoming</Badge>
                  </div>

                  <Separator className="my-3" />

                  <div className="flex flex-wrap gap-2 mt-2">
                    {campaign.discounts.map((discount) => (
                      <DiscountBadge
                        key={discount.id}
                        type={discount.type}
                        scope={discount.scope}
                        value={discount.value}
                        size="sm"
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
