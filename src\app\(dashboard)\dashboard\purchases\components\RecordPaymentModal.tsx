"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Textarea } from "@/components/ui/textarea";
import { format } from "date-fns";

interface RecordPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  purchaseId: string;
  invoiceNumber: string;
  totalAmount: number;
  paidAmount: number;
  onPaymentRecorded: () => void;
}

export default function RecordPaymentModal({
  isOpen,
  onClose,
  purchaseId,
  invoiceNumber,
  totalAmount,
  paidAmount,
  onPaymentRecorded
}: RecordPaymentModalProps) {
  const [amount, setAmount] = useState<string>((totalAmount - paidAmount).toFixed(2));
  const [paymentMethod, setPaymentMethod] = useState<string>("CASH");
  const [paymentDate, setPaymentDate] = useState<Date>(new Date());
  const [notes, setNotes] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const remainingAmount = totalAmount - paidAmount;
  
  const handleSubmit = async () => {
    setError(null);
    
    // Validate amount
    const paymentAmount = parseFloat(amount);
    if (isNaN(paymentAmount) || paymentAmount <= 0) {
      setError("Please enter a valid payment amount");
      return;
    }
    
    if (paymentAmount > remainingAmount) {
      setError(`Payment amount cannot exceed the remaining amount (${remainingAmount.toFixed(2)})`);
      return;
    }
    
    // Validate payment method
    if (!paymentMethod) {
      setError("Please select a payment method");
      return;
    }
    
    // Validate payment date
    if (!paymentDate) {
      setError("Please select a payment date");
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/purchases/${purchaseId}/payments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: paymentAmount,
          paymentMethod,
          paymentDate: paymentDate.toISOString(),
          notes
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to record payment");
      }
      
      // Reset form
      setAmount("");
      setPaymentMethod("CASH");
      setPaymentDate(new Date());
      setNotes("");
      
      // Notify parent component
      onPaymentRecorded();
      
      // Close modal
      onClose();
    } catch (error) {
      console.error("Error recording payment:", error);
      setError(error instanceof Error ? error.message : "An error occurred while recording the payment");
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Record Payment</DialogTitle>
          <DialogDescription>
            Record a payment for invoice #{invoiceNumber}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Total Amount</Label>
              <div className="mt-1 p-2 bg-gray-50 rounded border border-gray-200">
                {formatCurrency(totalAmount)}
              </div>
            </div>
            <div>
              <Label>Remaining Amount</Label>
              <div className="mt-1 p-2 bg-gray-50 rounded border border-gray-200">
                {formatCurrency(remainingAmount)}
              </div>
            </div>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="amount">Payment Amount</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="Enter payment amount"
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="payment-method">Payment Method</Label>
            <Select value={paymentMethod} onValueChange={setPaymentMethod}>
              <SelectTrigger id="payment-method">
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CASH">Cash</SelectItem>
                <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                <SelectItem value="VODAFONE_CASH">Vodafone Cash</SelectItem>
                <SelectItem value="VISA">Visa</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label>Payment Date</Label>
            <DatePicker date={paymentDate} setDate={setPaymentDate} />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>
          
          {error && (
            <div className="text-sm text-red-500 mt-2">
              {error}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Recording..." : "Record Payment"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
