"use client";

import { useState, useEffect } from "react";
import { Database, Save, Upload, Download, Trash2 } from "lucide-react";

interface BackupFile {
  name: string;
  size: string;
  date: string;
}

interface DatabaseBackupFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

export default function DatabaseBackupForm({ onClose, onSuccess }: DatabaseBackupFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [backups, setBackups] = useState<BackupFile[]>([]);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [isRestoring, setIsRestoring] = useState(false);
  const [restoreConfirm, setRestoreConfirm] = useState("");
  const [selectedBackup, setSelectedBackup] = useState<string | null>(null);

  useEffect(() => {
    fetchBackups();
  }, []);

  const fetchBackups = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/system/database/backups");

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (response.ok) {
        if (contentType && contentType.indexOf("application/json") !== -1) {
          try {
            const data = await response.json();
            setBackups(data.backups || []);
          } catch (jsonError) {
            console.error("Error parsing JSON:", jsonError);
            setError("Error parsing server response");
            setBackups([]);
          }
        } else {
          console.error("Non-JSON response received");
          setError("Server returned an invalid response format");
          setBackups([]);
        }
      } else {
        if (contentType && contentType.indexOf("application/json") !== -1) {
          try {
            const errorData = await response.json();
            setError(errorData.error || "Failed to fetch backups");
          } catch (jsonError) {
            console.error("Error parsing JSON error:", jsonError);
            setError("Error parsing server error response");
          }
        } else {
          const textResponse = await response.text();
          console.error("Non-JSON error response:", textResponse);
          setError("Server returned an invalid error response format");
        }
        setBackups([]);
      }
    } catch (err) {
      console.error("Error fetching backups:", err);
      setError("Failed to connect to server");
      setBackups([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackup = async () => {
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch("/api/system/database/backup", {
        method: "POST",
      });

      if (response.ok) {
        setSuccess("Database backup created successfully!");
        fetchBackups();
      } else {
        const data = await response.json();
        setError(data.error || "Failed to create backup");
      }
    } catch (err) {
      console.error("Error creating backup:", err);
      setError("Failed to connect to server");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestore = async () => {
    if (!selectedBackup) return;
    if (restoreConfirm !== "RESTORE") {
      setError("Please type RESTORE to confirm");
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch(`/api/system/database/restore`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ filename: selectedBackup }),
      });

      if (response.ok) {
        setSuccess("Database restored successfully!");
        setIsRestoring(false);
        setRestoreConfirm("");
        setSelectedBackup(null);
        setTimeout(() => {
          onSuccess();
        }, 2000);
      } else {
        const data = await response.json();
        setError(data.error || "Failed to restore backup");
      }
    } catch (err) {
      console.error("Error restoring backup:", err);
      setError("Failed to connect to server");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (filename: string) => {
    if (!confirm(`Are you sure you want to delete the backup: ${filename}?`)) {
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch(`/api/system/database/backup`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ filename }),
      });

      if (response.ok) {
        setSuccess("Backup deleted successfully!");
        fetchBackups();
      } else {
        const data = await response.json();
        setError(data.error || "Failed to delete backup");
      }
    } catch (err) {
      console.error("Error deleting backup:", err);
      setError("Failed to connect to server");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (filename: string) => {
    try {
      const response = await fetch(`/api/system/database/download?filename=${encodeURIComponent(filename)}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Failed to download backup");
      }
    } catch (err) {
      console.error("Error downloading backup:", err);
      setError("Failed to connect to server");
    }
  };

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!uploadFile) return;

    setIsLoading(true);
    setError("");
    setSuccess("");

    const formData = new FormData();
    formData.append("backupFile", uploadFile);

    try {
      const response = await fetch("/api/system/database/upload", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        setSuccess("Backup file uploaded successfully!");
        setUploadFile(null);
        fetchBackups();
      } else {
        const data = await response.json();
        setError(data.error || "Failed to upload backup");
      }
    } catch (err) {
      console.error("Error uploading backup:", err);
      setError("Failed to connect to server");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
      <div className="sm:flex sm:items-start">
        <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
          <Database className="h-6 w-6 text-blue-600" />
        </div>
        <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Database Backup & Restore</h3>

          {isRestoring ? (
            <div className="mt-4">
              <div className="rounded-md bg-yellow-50 p-4 mb-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Warning</h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        Restoring a backup will overwrite your current database. This action cannot be undone.
                        Make sure you have a backup of your current data before proceeding.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700">Selected Backup</label>
                <div className="mt-1 text-sm text-gray-900">{selectedBackup}</div>
              </div>

              <div className="mb-4">
                <label htmlFor="restore-confirm" className="block text-sm font-medium text-gray-700">
                  Type RESTORE to confirm
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    id="restore-confirm"
                    value={restoreConfirm}
                    onChange={(e) => setRestoreConfirm(e.target.value)}
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleRestore}
                  disabled={isLoading || restoreConfirm !== "RESTORE"}
                  className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                    isLoading || restoreConfirm !== "RESTORE"
                      ? "bg-gray-300 cursor-not-allowed"
                      : "bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  }`}
                >
                  {isLoading ? "Processing..." : "Restore Database"}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setIsRestoring(false);
                    setSelectedBackup(null);
                    setRestoreConfirm("");
                  }}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="mt-4">
              <div className="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0 md:space-x-4 mb-6">
                <button
                  type="button"
                  onClick={handleBackup}
                  disabled={isLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Create Backup
                </button>

                <form onSubmit={handleUpload} className="flex items-center space-x-2">
                  <input
                    type="file"
                    id="backup-upload"
                    accept=".sql,.gz,.zip"
                    onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                    className="sr-only"
                  />
                  <label
                    htmlFor="backup-upload"
                    className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Backup
                  </label>
                  {uploadFile && (
                    <>
                      <span className="text-sm text-gray-600 truncate max-w-xs">
                        {uploadFile.name}
                      </span>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        Upload
                      </button>
                    </>
                  )}
                </form>
              </div>

              {error && (
                <div className="rounded-md bg-red-50 p-4 mb-4">
                  <div className="flex">
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Error</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {success && (
                <div className="rounded-md bg-green-50 p-4 mb-4">
                  <div className="flex">
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">Success</h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>{success}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="mt-4">
                <h4 className="text-md font-medium text-gray-900 mb-2">Available Backups</h4>
                {isLoading ? (
                  <p className="text-sm text-gray-500">Loading backups...</p>
                ) : backups.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Filename
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Size
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {backups.map((backup) => (
                          <tr key={backup.name}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {backup.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {backup.size}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {backup.date}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex space-x-2">
                                <button
                                  type="button"
                                  onClick={() => {
                                    setSelectedBackup(backup.name);
                                    setIsRestoring(true);
                                  }}
                                  className="text-indigo-600 hover:text-indigo-900"
                                  title="Restore"
                                >
                                  <Upload className="h-5 w-5" />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => handleDownload(backup.name)}
                                  className="text-blue-600 hover:text-blue-900"
                                  title="Download"
                                >
                                  <Download className="h-5 w-5" />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => handleDelete(backup.name)}
                                  className="text-red-600 hover:text-red-900"
                                  title="Delete"
                                >
                                  <Trash2 className="h-5 w-5" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No backups available</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
        <button
          type="button"
          onClick={onClose}
          className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
        >
          Close
        </button>
      </div>
    </div>
  );
}
