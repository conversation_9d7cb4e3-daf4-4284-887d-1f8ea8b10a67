import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

// Define types
interface DiscountItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface DiscountRequest {
  items: DiscountItem[];
  subtotal: number;
  contactId?: string;
  branchId?: string;
  applyTax: boolean;
  taxRate: number;
}

interface DiscountResult {
  invoiceDiscounts: any[];
  itemDiscounts: Record<string, any[]>;
  manualDiscountAmount: number;
  automaticDiscountAmount: number;
  itemDiscountsTotal: number;
  totalDiscountAmount: number;
  calculationTime: number;
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const data: DiscountRequest = await req.json();
    
    // Start timing the calculation
    const startTime = performance.now();
    
    // Validate request
    if (!data.items || !Array.isArray(data.items)) {
      return NextResponse.json(
        { error: "Invalid request: items array is required" },
        { status: 400 }
      );
    }
    
    // Initialize result
    const result: DiscountResult = {
      invoiceDiscounts: [],
      itemDiscounts: {},
      manualDiscountAmount: 0,
      automaticDiscountAmount: 0,
      itemDiscountsTotal: 0,
      totalDiscountAmount: 0,
      calculationTime: 0
    };
    
    // Calculate tax amount if applicable
    const taxAmount = data.applyTax ? data.subtotal * data.taxRate : 0;
    const subtotalWithTax = data.subtotal + taxAmount;
    
    // Get all active discounts
    const activeDiscounts = await prisma.discount.findMany({
      where: {
        isActive: true,
        OR: [
          { startDate: null },
          { startDate: { lte: new Date() } }
        ],
        AND: [
          { OR: [
            { endDate: null },
            { endDate: { gte: new Date() } }
          ] }
        ]
      },
      include: {
        customerDiscounts: true
      }
    });
    
    // Filter discounts by minimum amount
    const eligibleDiscounts = activeDiscounts.filter(discount => 
      !discount.minAmount || subtotalWithTax >= discount.minAmount
    );
    
    // Process invoice-level discounts
    const invoiceDiscounts = eligibleDiscounts.filter(discount => 
      discount.scope === "INVOICE"
    );
    
    // Calculate invoice discounts
    for (const discount of invoiceDiscounts) {
      let discountAmount = 0;
      
      if (discount.type === "PERCENTAGE") {
        discountAmount = subtotalWithTax * (discount.value / 100);
        
        // Apply maximum discount if specified
        if (discount.maxAmount && discountAmount > discount.maxAmount) {
          discountAmount = discount.maxAmount;
        }
      } else {
        // Fixed amount discount
        discountAmount = Math.min(discount.value, subtotalWithTax);
      }
      
      result.invoiceDiscounts.push({
        id: discount.id,
        name: discount.name,
        type: discount.type,
        value: discount.value,
        scope: discount.scope,
        amount: discountAmount
      });
      
      result.automaticDiscountAmount += discountAmount;
    }
    
    // Process customer-specific discounts if contactId is provided
    if (data.contactId) {
      const customerDiscounts = eligibleDiscounts.filter(discount => 
        discount.scope === "CUSTOMER" && 
        discount.customerDiscounts.some(cd => cd.contactId === data.contactId)
      );
      
      // Calculate customer discounts
      for (const discount of customerDiscounts) {
        let discountAmount = 0;
        
        if (discount.type === "PERCENTAGE") {
          discountAmount = subtotalWithTax * (discount.value / 100);
          
          // Apply maximum discount if specified
          if (discount.maxAmount && discountAmount > discount.maxAmount) {
            discountAmount = discount.maxAmount;
          }
        } else {
          // Fixed amount discount
          discountAmount = Math.min(discount.value, subtotalWithTax);
        }
        
        result.invoiceDiscounts.push({
          id: discount.id,
          name: discount.name,
          type: discount.type,
          value: discount.value,
          scope: discount.scope,
          amount: discountAmount
        });
        
        result.automaticDiscountAmount += discountAmount;
      }
    }
    
    // Process item-level discounts
    const itemDiscounts = eligibleDiscounts.filter(discount => 
      discount.scope === "ITEM"
    );
    
    // Calculate item discounts for each item
    for (const item of data.items) {
      const applicableItemDiscounts = itemDiscounts.filter(discount => 
        !discount.productId || discount.productId === item.productId
      );
      
      if (applicableItemDiscounts.length > 0) {
        result.itemDiscounts[item.id] = [];
        
        for (const discount of applicableItemDiscounts) {
          let discountAmount = 0;
          
          if (discount.type === "PERCENTAGE") {
            discountAmount = item.total * (discount.value / 100);
            
            // Apply maximum discount if specified
            if (discount.maxAmount && discountAmount > discount.maxAmount) {
              discountAmount = discount.maxAmount;
            }
          } else {
            // Fixed amount discount
            discountAmount = Math.min(discount.value, item.total);
          }
          
          result.itemDiscounts[item.id].push({
            id: discount.id,
            name: discount.name,
            type: discount.type,
            value: discount.value,
            scope: discount.scope,
            amount: discountAmount
          });
          
          result.itemDiscountsTotal += discountAmount;
        }
      }
    }
    
    // Calculate total discount amount
    result.totalDiscountAmount = result.manualDiscountAmount + result.automaticDiscountAmount + result.itemDiscountsTotal;
    
    // Ensure total discount doesn't exceed subtotal with tax
    if (result.totalDiscountAmount > subtotalWithTax) {
      const reductionFactor = subtotalWithTax / result.totalDiscountAmount;
      
      // Reduce all discount amounts proportionally
      result.invoiceDiscounts.forEach(discount => {
        discount.amount *= reductionFactor;
      });
      
      Object.keys(result.itemDiscounts).forEach(itemId => {
        result.itemDiscounts[itemId].forEach(discount => {
          discount.amount *= reductionFactor;
        });
      });
      
      result.automaticDiscountAmount *= reductionFactor;
      result.itemDiscountsTotal *= reductionFactor;
      result.totalDiscountAmount = subtotalWithTax;
    }
    
    // Calculate performance time
    const endTime = performance.now();
    result.calculationTime = endTime - startTime;
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error calculating discounts:", error);
    return NextResponse.json(
      { error: "Failed to calculate discounts" },
      { status: 500 }
    );
  }
}
