import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  serverExternalPackages: ['bcrypt'],
};

// This function is used to set a fixed port for the development server
// It will be used when running `npm run dev`
module.exports = {
  ...nextConfig,
  async rewrites() {
    return [];
  },
};

// Export the config for production builds
export default nextConfig;
