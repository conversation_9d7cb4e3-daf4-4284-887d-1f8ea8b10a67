const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking for existing branches...');
    
    // Check if any branch already exists
    const existingBranches = await prisma.branch.findMany();

    if (existingBranches.length > 0) {
      console.log('Branches already exist. Skipping creation.');
      return;
    }

    console.log('Creating default branch...');
    
    // Create the default branch
    const defaultBranch = await prisma.branch.create({
      data: {
        id: uuidv4(),
        name: 'Main Branch',
        code: 'MAIN',
        address: 'Main Street, Cairo, Egypt',
        phone: '+************',
        isActive: true,
      },
    });
    
    console.log('Default branch created successfully:', defaultBranch.name);
    
    // Create default warehouse for the branch
    console.log('Creating default warehouse...');
    
    const defaultWarehouse = await prisma.warehouse.create({
      data: {
        id: uuidv4(),
        name: 'Main Warehouse',
        branchId: defaultBranch.id,
        isActive: true,
      },
    });
    
    console.log('Default warehouse created successfully:', defaultWarehouse.name);
    
    // Assign the branch to the admin user
    console.log('Assigning branch to admin user...');
    
    const adminUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
    });
    
    if (adminUser) {
      await prisma.user.update({
        where: {
          id: adminUser.id,
        },
        data: {
          branchId: defaultBranch.id,
        },
      });
      
      // Assign warehouse to admin user
      await prisma.userWarehouse.create({
        data: {
          userId: adminUser.id,
          warehouseId: defaultWarehouse.id,
        },
      });
      
      console.log('Branch and warehouse assigned to admin user successfully');
    } else {
      console.log('Admin user not found. Branch not assigned.');
    }
    
    console.log('Default branch setup completed successfully');
  } catch (error) {
    console.error('Error creating default branch:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
