import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/journals/entries - Get journal entries
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const journalId = url.searchParams.get("journalId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const contactId = url.searchParams.get("contactId");
    const reference = url.searchParams.get("reference");
    const referenceType = url.searchParams.get("referenceType");
    const accountId = url.searchParams.get("accountId");
    const search = url.searchParams.get("search");
    const limit = url.searchParams.get("limit") ? parseInt(url.searchParams.get("limit")!) : 100;
    const page = url.searchParams.get("page") ? parseInt(url.searchParams.get("page")!) : 1;
    const skip = (page - 1) * limit;

    // Build query filters
    const filters: any = {};
    if (journalId) filters.journalId = journalId;
    if (contactId) filters.contactId = contactId;
    if (reference) filters.reference = { contains: reference, mode: "insensitive" };
    if (referenceType) filters.referenceType = referenceType;

    // Filter by account ID (either debit or credit account)
    if (accountId) {
      filters.OR = [
        { debitAccountId: accountId },
        { creditAccountId: accountId },
      ];
    }

    // Search in description, reference, or contact name
    if (search) {
      const searchFilters = [
        { description: { contains: search, mode: 'insensitive' } },
        { reference: { contains: search, mode: 'insensitive' } },
      ];

      if (filters.OR) {
        // If we already have OR conditions for accountId, we need to combine them
        filters.AND = [
          { OR: filters.OR },
          { OR: searchFilters }
        ];
        delete filters.OR;
      } else {
        filters.OR = searchFilters;
      }
    }

    if (startDate && endDate) {
      filters.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      filters.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      filters.date = {
        lte: new Date(endDate),
      };
    }

    // Get journal entries
    const entries = await db.journalEntry.findMany({
      where: filters,
      include: {
        journal: true,
        debitAccount: true,
        creditAccount: true,
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        fiscalPeriod: {
          include: {
            fiscalYear: true,
          },
        },
      },
      orderBy: [
        { date: "desc" },
        { createdAt: "desc" },
      ],
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await db.journalEntry.count({
      where: filters,
    });

    return NextResponse.json({
      data: entries,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error: any) {
    console.error("Error fetching journal entries:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch journal entries" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/journals/entries - Create a new journal entry
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to create journal entries
    const hasCreatePermission = await hasPermission("manage_accounts");
    if (!hasCreatePermission) {
      return NextResponse.json(
        { error: "You don't have permission to create journal entries" },
        { status: 403 }
      );
    }

    // Parse request body
    const data = await req.json();

    // Validate required fields
    if (!data.journalId) {
      return NextResponse.json({ error: "Journal is required" }, { status: 400 });
    }

    if (!data.description) {
      return NextResponse.json({ error: "Description is required" }, { status: 400 });
    }

    if (!data.entries || !Array.isArray(data.entries) || data.entries.length < 2) {
      return NextResponse.json({ error: "At least two entries are required" }, { status: 400 });
    }

    // Validate that debits equal credits
    const totalDebits = data.entries
      .filter((entry: any) => entry.type === "DEBIT")
      .reduce((sum: number, entry: any) => sum + Number(entry.amount), 0);

    const totalCredits = data.entries
      .filter((entry: any) => entry.type === "CREDIT")
      .reduce((sum: number, entry: any) => sum + Number(entry.amount), 0);

    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      return NextResponse.json(
        { error: "Journal entry must be balanced (debits must equal credits)" },
        { status: 400 }
      );
    }

    // Create journal entry in a transaction
    const result = await db.$transaction(async (tx) => {
      // Get the journal
      const journal = await tx.journal.findUnique({
        where: { id: data.journalId },
      });

      if (!journal) {
        throw new Error("Journal not found");
      }

      // Get the last entry number for this journal
      const lastEntry = await tx.journalEntry.findFirst({
        where: {
          journalId: data.journalId,
        },
        orderBy: {
          entryNumber: 'desc',
        },
      });

      // Generate entry number
      let entryNumber = "1";
      if (lastEntry) {
        const lastEntryNumber = parseInt(lastEntry.entryNumber);
        entryNumber = (lastEntryNumber + 1).toString();
      }

      // Get current fiscal period
      const currentDate = data.date ? new Date(data.date) : new Date();
      const fiscalPeriod = await tx.fiscalPeriod.findFirst({
        where: {
          startDate: { lte: currentDate },
          endDate: { gte: currentDate },
          isClosed: false,
        },
      });

      // Create entries
      const journalEntries = [];

      // Group entries by type (debit/credit)
      const debitEntries = data.entries.filter((entry: any) => entry.type === "DEBIT");
      const creditEntries = data.entries.filter((entry: any) => entry.type === "CREDIT");

      // Create journal entries (one debit and one credit per entry)
      for (let i = 0; i < Math.max(debitEntries.length, creditEntries.length); i++) {
        const debitEntry = debitEntries[i];
        const creditEntry = creditEntries[i];

        if (!debitEntry || !creditEntry) {
          continue; // Skip if we don't have both a debit and credit entry
        }

        // Create the journal entry
        const journalEntry = await tx.journalEntry.create({
          data: {
            journalId: data.journalId,
            entryNumber: `${entryNumber}-${i + 1}`,
            date: currentDate,
            description: data.description,
            debitAccountId: debitEntry.accountId,
            creditAccountId: creditEntry.accountId,
            amount: debitEntry.amount,
            contactId: data.contactId || null,
            reference: data.reference || null,
            referenceType: data.referenceType || null,
            fiscalPeriodId: fiscalPeriod?.id || null,
            isPosted: true,
          },
        });

        journalEntries.push(journalEntry);

        // Update account balances
        await tx.account.update({
          where: { id: debitEntry.accountId },
          data: { balance: { increment: debitEntry.amount } },
        });

        await tx.account.update({
          where: { id: creditEntry.accountId },
          data: { balance: { increment: creditEntry.amount } },
        });
      }

      return journalEntries;
    });

    return NextResponse.json({
      success: true,
      data: result,
    }, { status: 201 });
  } catch (error: any) {
    console.error("Error creating journal entry:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create journal entry" },
      { status: 500 }
    );
  }
}
