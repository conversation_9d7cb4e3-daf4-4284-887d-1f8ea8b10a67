-- Drop existing accounting tables to rebuild from scratch
DROP TABLE IF EXISTS "AccountReconciliation" CASCADE;
DROP TABLE IF EXISTS "ReconciliationItem" CASCADE;
DROP TABLE IF EXISTS "FinancialReport" CASCADE;
DROP TABLE IF EXISTS "AccountingSettings" CASCADE;
DROP TABLE IF EXISTS "GeneralLedgerEntry" CASCADE;
DROP TABLE IF EXISTS "JournalEntry" CASCADE;
DROP TABLE IF EXISTS "Journal" CASCADE;
DROP TABLE IF EXISTS "FiscalPeriod" CASCADE;
DROP TABLE IF EXISTS "FiscalYear" CASCADE;
DROP TABLE IF EXISTS "Transaction" CASCADE;
DROP TABLE IF EXISTS "Account" CASCADE;

-- Drop existing accounting enums
DROP TYPE IF EXISTS "AccountType" CASCADE;
DROP TYPE IF EXISTS "TransactionType" CASCADE;
DROP TYPE IF EXISTS "EntryType" CASCADE;
DROP TYPE IF EXISTS "ReportType" CASCADE;
DROP TYPE IF EXISTS "JournalType" CASCADE;

-- Create new accounting enums
CREATE TYPE "AccountType" AS ENUM (
  'ASSET',
  'LIABILITY',
  'EQUITY',
  'REVENUE',
  'EXPENSE'
);

CREATE TYPE "JournalType" AS ENUM (
  'CASH',
  'VODAFONE_CASH',
  'BANK_TRANSFER',
  'VISA',
  'CUSTOMER_ACCOUNT',
  'GENERAL'
);

-- Create new accounting tables
CREATE TABLE "Account" (
  "id" TEXT NOT NULL,
  "code" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "type" "AccountType" NOT NULL,
  "parentId" TEXT,
  "balance" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "branchId" TEXT,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Journal" (
  "id" TEXT NOT NULL,
  "code" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "type" "JournalType" NOT NULL,
  "branchId" TEXT,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "Journal_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "JournalEntry" (
  "id" TEXT NOT NULL,
  "journalId" TEXT NOT NULL,
  "entryNumber" TEXT NOT NULL,
  "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "description" TEXT NOT NULL,
  "debitAccountId" TEXT NOT NULL,
  "creditAccountId" TEXT NOT NULL,
  "amount" DOUBLE PRECISION NOT NULL,
  "reference" TEXT,
  "referenceType" TEXT,
  "isPosted" BOOLEAN NOT NULL DEFAULT false,
  "fiscalPeriodId" TEXT,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "JournalEntry_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "FiscalYear" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "startDate" TIMESTAMP(3) NOT NULL,
  "endDate" TIMESTAMP(3) NOT NULL,
  "isClosed" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "FiscalYear_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "FiscalPeriod" (
  "id" TEXT NOT NULL,
  "fiscalYearId" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "startDate" TIMESTAMP(3) NOT NULL,
  "endDate" TIMESTAMP(3) NOT NULL,
  "isClosed" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "FiscalPeriod_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints
ALTER TABLE "Account" ADD CONSTRAINT "Account_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Account" ADD CONSTRAINT "Account_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Journal" ADD CONSTRAINT "Journal_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_journalId_fkey" FOREIGN KEY ("journalId") REFERENCES "Journal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_debitAccountId_fkey" FOREIGN KEY ("debitAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_creditAccountId_fkey" FOREIGN KEY ("creditAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_fiscalPeriodId_fkey" FOREIGN KEY ("fiscalPeriodId") REFERENCES "FiscalPeriod"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "FiscalPeriod" ADD CONSTRAINT "FiscalPeriod_fiscalYearId_fkey" FOREIGN KEY ("fiscalYearId") REFERENCES "FiscalYear"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX "Account_code_idx" ON "Account"("code");
CREATE INDEX "Account_type_idx" ON "Account"("type");
CREATE INDEX "Account_parentId_idx" ON "Account"("parentId");
CREATE INDEX "Account_isActive_idx" ON "Account"("isActive");

CREATE INDEX "Journal_code_idx" ON "Journal"("code");
CREATE INDEX "Journal_type_idx" ON "Journal"("type");
CREATE INDEX "Journal_isActive_idx" ON "Journal"("isActive");

CREATE INDEX "JournalEntry_journalId_idx" ON "JournalEntry"("journalId");
CREATE INDEX "JournalEntry_date_idx" ON "JournalEntry"("date");
CREATE INDEX "JournalEntry_debitAccountId_idx" ON "JournalEntry"("debitAccountId");
CREATE INDEX "JournalEntry_creditAccountId_idx" ON "JournalEntry"("creditAccountId");
CREATE INDEX "JournalEntry_isPosted_idx" ON "JournalEntry"("isPosted");

CREATE INDEX "FiscalYear_isClosed_idx" ON "FiscalYear"("isClosed");
CREATE INDEX "FiscalPeriod_fiscalYearId_idx" ON "FiscalPeriod"("fiscalYearId");
CREATE INDEX "FiscalPeriod_isClosed_idx" ON "FiscalPeriod"("isClosed");
CREATE INDEX "FiscalPeriod_startDate_endDate_idx" ON "FiscalPeriod"("startDate", "endDate");

-- Create unique constraints
CREATE UNIQUE INDEX "Account_code_key" ON "Account"("code");
CREATE UNIQUE INDEX "Journal_code_key" ON "Journal"("code");
CREATE UNIQUE INDEX "JournalEntry_entryNumber_journalId_key" ON "JournalEntry"("entryNumber", "journalId");
