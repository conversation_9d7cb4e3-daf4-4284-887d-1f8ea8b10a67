-- CreateTable
CREATE TABLE "PaymentMethodSettings" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "accountId" TEXT,
    "journalId" TEXT,
    "sequence" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PaymentMethodSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PaymentMethodSettings_code_key" ON "PaymentMethodSettings"("code");

-- AddForeignKey
ALTER TABLE "PaymentMethodSettings" ADD CONSTRAINT "PaymentMethodSettings_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentMethodSettings" ADD CONSTRAINT "PaymentMethodSettings_journalId_fkey" FOREIGN KEY ("journalId") REFERENCES "Journal"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Insert default payment methods
INSERT INTO "PaymentMethodSettings" ("id", "code", "name", "isActive", "sequence", "updatedAt")
VALUES 
('1', 'CASH', 'Cash', true, 1, CURRENT_TIMESTAMP),
('2', 'VODAFONE_CASH', 'Vodafone Cash', true, 2, CURRENT_TIMESTAMP),
('3', 'BANK_TRANSFER', 'Bank Transfer', true, 3, CURRENT_TIMESTAMP),
('4', 'CREDIT_CARD', 'Credit Card', true, 4, CURRENT_TIMESTAMP),
('5', 'CUSTOMER_ACCOUNT', 'Customer Account', true, 5, CURRENT_TIMESTAMP);
