"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import React from "react";
import {
  Loader2,
  ArrowLeft,
  Printer,
  Phone,
  Calendar,
  Clock,
  CheckCircle2,
  Setting<PERSON>,
  AlertTriangle
} from "lucide-react";
import { getStatusLabel, getStatusColor, calculateServiceDays } from "@/lib/maintenance";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { PrintButton } from "@/components/shared/PrintButton";

interface MaintenanceService {
  id: string;
  serviceNumber: string;
  deviceType: string;
  brand: string;
  model?: string;
  serialNumber?: string;
  problemDescription: string;
  receivedDate: string;
  estimatedCompletionDate?: string;
  completionDate?: string;
  deliveryDate?: string;
  status: string;
  priority: string;
  initialDiagnosis?: string;
  technicalNotes?: string;
  estimatedCost?: number;
  finalCost?: number;
  isPaid: boolean;
  isWarranty: boolean;
  warrantyDetails?: string;
  estimatedHours?: number;
  actualHours?: number;
  customerSignature: boolean;
  customerRating?: number;
  customerFeedback?: string;
  notificationMethod?: string;
  paymentMethod?: string;
  paymentStatus?: string;
  invoiceId?: string;
  invoiceNumber?: string;
  contact: {
    id: string;
    name: string;
    phone: string;
    address?: string;
    email?: string;
  };
  user: {
    id: string;
    name: string;
    email: string;
  };
  branch: {
    id: string;
    name: string;
  };
  parts: MaintenancePart[];
  statusHistory: StatusHistory[];
  payments?: MaintenancePayment[];
}

interface MaintenancePart {
  id: string;
  partName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  isFromInventory: boolean;
  product?: {
    id: string;
    name: string;
    basePrice: number;
    costPrice: number;
  };
}

interface StatusHistory {
  id: string;
  status: string;
  notes?: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
  };
}

interface MaintenancePayment {
  id: string;
  method: string;
  amount: number;
  date: string;
  notes?: string;
}

export default function PrintMaintenancePage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const id = unwrappedParams.id;

  const router = useRouter();
  const [service, setService] = useState<MaintenanceService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [companyInfo, setCompanyInfo] = useState({
    name: "VERO ERP",
    address: "123 Main Street, Cairo, Egypt",
    phone: "+20 ************",
    email: "<EMAIL>",
    logo: "/logo.png",
  });

  // Fetch maintenance service details
  useEffect(() => {
    const fetchServiceDetails = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/maintenance/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch maintenance service details");
        }

        const data = await response.json();
        setService(data);

        // Fetch company info
        try {
          const settingsResponse = await fetch("/api/settings/company");
          if (settingsResponse.ok) {
            const settingsData = await settingsResponse.json();
            if (settingsData) {
              setCompanyInfo(settingsData);
            }
          }
        } catch (error) {
          // Use default company info if settings can't be fetched
          console.error("Error fetching company info:", error);
        }
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceDetails();
  }, [id]);

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          {error}
        </div>
        <div className="mt-4">
          <Link
            href="/dashboard/maintenance"
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance
          </Link>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 text-yellow-700 p-4 rounded-md">
          Maintenance service not found
        </div>
        <div className="mt-4">
          <Link
            href="/dashboard/maintenance"
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance
          </Link>
        </div>
      </div>
    );
  }

  const days = calculateServiceDays(new Date(service.receivedDate));
  const isOverdue = days > 15 && !["DELIVERED", "CANCELLED"].includes(service.status);
  const totalPartsPrice = service.parts.reduce((sum, part) => sum + part.totalPrice, 0);

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6 print:hidden">
        <Link
          href={`/dashboard/maintenance/${id}`}
          className="flex items-center text-indigo-600 hover:text-indigo-900"
        >
          <ArrowLeft className="h-5 w-5 mr-1" />
          Back to Service Details
        </Link>
        <div className="flex space-x-2">
          <PrintButton
            documentType="maintenance_receipt"
            documentData={{
              id: service.id,
              company_name: companyInfo.name,
              receipt_number: service.serviceNumber,
              date: format(new Date(service.receivedDate), "dd/MM/yyyy"),
              customer_details: `${service.contact.name}<br>${service.contact.phone || ''}<br>${service.contact.address || ''}`,
              device_details: `
                <div>
                  <p><strong>Device Type:</strong> ${service.deviceType || 'Not specified'}</p>
                  <p><strong>Brand:</strong> ${service.brand || 'Not specified'}</p>
                  <p><strong>Model:</strong> ${service.model || 'Not specified'}</p>
                  <p><strong>Serial Number:</strong> ${service.serialNumber || 'Not specified'}</p>
                </div>
              `,
              issue_description: service.problemDescription || 'No description provided',
              diagnosis: service.initialDiagnosis || 'No diagnosis yet',
              resolution: service.technicalNotes || 'No resolution yet',
              status: getStatusLabel(service.status),
              technician: service.user?.name || 'Not assigned',
              cost: service.finalCost ? formatCurrency(service.finalCost) : 'Not specified',
              barcode: service.serviceNumber
            }}
          />
          <button
            onClick={handlePrint}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium flex items-center"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print Detailed Report
          </button>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden p-6 print:shadow-none print:p-0">
        {/* Header - More compact */}
        <div className="flex justify-between items-start border-b border-gray-200 pb-3 mb-3">
          <div>
            <h1 className="text-xl font-bold text-gray-900">Maintenance Service Report</h1>
            <p className="text-sm text-gray-500">Service #: {service.serviceNumber}</p>
          </div>
          <div className="text-right">
            <img src={companyInfo.logo} alt="Company Logo" className="h-10 mb-1 inline-block" />
            <p className="font-bold text-sm">{companyInfo.name}</p>
            <p className="text-xs text-gray-500">{companyInfo.address}</p>
            <p className="text-xs text-gray-500">{companyInfo.phone}</p>
          </div>
        </div>

        {/* Main content in 3 columns */}
        <div className="grid grid-cols-3 gap-4 mb-3">
          {/* Column 1: Customer & Device */}
          <div>
            <div className="mb-3">
              <h2 className="text-sm font-semibold text-gray-900 mb-1 border-b border-gray-200 pb-1">Customer Information</h2>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Name:</span> {service.contact.name}</p>
                <p className="flex items-center">
                  <Phone className="h-3 w-3 mr-1 text-gray-400" />
                  {service.contact.phone}
                </p>
                {service.contact.address && (
                  <p className="text-xs"><span className="font-medium">Address:</span> {service.contact.address}</p>
                )}
              </div>
            </div>

            <div>
              <h2 className="text-sm font-semibold text-gray-900 mb-1 border-b border-gray-200 pb-1">Device Information</h2>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Type:</span> {service.deviceType}</p>
                <p><span className="font-medium">Brand:</span> {service.brand}</p>
                {service.model && (
                  <p><span className="font-medium">Model:</span> {service.model}</p>
                )}
                {service.serialNumber && (
                  <p className="text-xs"><span className="font-medium">S/N:</span> {service.serialNumber}</p>
                )}
              </div>
            </div>
          </div>

          {/* Column 2: Service Info & Problem */}
          <div>
            <div className="mb-3">
              <h2 className="text-sm font-semibold text-gray-900 mb-1 border-b border-gray-200 pb-1">Service Information</h2>
              <div className="space-y-1 text-sm">
                <p className="flex items-center">
                  <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                  <span className="font-medium">Received:</span> {format(new Date(service.receivedDate), "dd/MM/yyyy")}
                </p>
                {service.completionDate && (
                  <p><span className="font-medium">Completed:</span> {format(new Date(service.completionDate), "dd/MM/yyyy")}</p>
                )}
                <p>
                  <span className="font-medium">Status:</span>
                  <span className={`ml-1 px-1 py-0.5 rounded-full text-xs ${getStatusColor(service.status)}`}>
                    {getStatusLabel(service.status)}
                  </span>
                </p>
                <p>
                  <span className="font-medium">Payment:</span>
                  <span className={`ml-1 px-1 py-0.5 rounded-full text-xs ${
                    service.paymentStatus === "PAID"
                      ? "bg-green-100 text-green-800"
                      : service.paymentStatus === "PARTIALLY_PAID"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                  }`}>
                    {service.paymentStatus === "PAID"
                      ? "Paid"
                      : service.paymentStatus === "PARTIALLY_PAID"
                        ? "Partially Paid"
                        : "Unpaid"}
                  </span>
                </p>
              </div>
            </div>

            <div>
              <h2 className="text-sm font-semibold text-gray-900 mb-1 border-b border-gray-200 pb-1">Problem Description</h2>
              <p className="text-sm">{service.problemDescription}</p>
            </div>
          </div>

          {/* Column 3: Diagnosis & Notes */}
          <div>
            {service.initialDiagnosis && (
              <div className="mb-3">
                <h2 className="text-sm font-semibold text-gray-900 mb-1 border-b border-gray-200 pb-1">Initial Diagnosis</h2>
                <p className="text-sm">{service.initialDiagnosis}</p>
              </div>
            )}

            {service.technicalNotes && (
              <div>
                <h2 className="text-sm font-semibold text-gray-900 mb-1 border-b border-gray-200 pb-1">Technical Notes</h2>
                <p className="text-sm">{service.technicalNotes}</p>
              </div>
            )}
          </div>
        </div>

        {/* Parts Used - More compact */}
        <div className="mb-3">
          <h2 className="text-sm font-semibold text-gray-900 mb-1 border-b border-gray-200 pb-1">Parts & Services</h2>
          {service.parts.length > 0 ? (
            <div>
              <table className="min-w-full divide-y divide-gray-200 text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-2 py-1 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Part Name
                    </th>
                    <th scope="col" className="px-2 py-1 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Qty
                    </th>
                    <th scope="col" className="px-2 py-1 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th scope="col" className="px-2 py-1 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {service.parts.map((part) => (
                    <tr key={part.id}>
                      <td className="px-2 py-1 text-xs font-medium text-gray-900">
                        {part.partName}
                      </td>
                      <td className="px-2 py-1 text-xs text-gray-500 text-center">
                        {part.quantity}
                      </td>
                      <td className="px-2 py-1 text-xs text-gray-500 text-right">
                        {formatCurrency(part.unitPrice)}
                      </td>
                      <td className="px-2 py-1 text-xs font-medium text-gray-900 text-right">
                        {formatCurrency(part.totalPrice)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td colSpan={3} className="px-2 py-1 text-xs font-semibold text-gray-900 text-right">
                      Total Parts:
                    </td>
                    <td className="px-2 py-1 text-xs font-bold text-gray-900 text-right">
                      {formatCurrency(totalPartsPrice)}
                    </td>
                  </tr>
                  {service.finalCost !== null && service.finalCost !== undefined && (
                    <tr>
                      <td colSpan={3} className="px-2 py-1 text-xs font-semibold text-gray-900 text-right">
                        Final Cost:
                      </td>
                      <td className="px-2 py-1 text-xs font-bold text-gray-900 text-right">
                        {formatCurrency(service.finalCost)}
                      </td>
                    </tr>
                  )}
                </tfoot>
              </table>

              {/* Payment Information */}
              {service.payments && service.payments.length > 0 && (
                <div className="mt-2">
                  <h3 className="text-xs font-semibold text-gray-900 mb-1">Payment History</h3>
                  <table className="min-w-full divide-y divide-gray-200 text-xs">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-2 py-1 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-2 py-1 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Method
                        </th>
                        <th scope="col" className="px-2 py-1 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {service.payments.map((payment) => (
                        <tr key={payment.id}>
                          <td className="px-2 py-1 text-xs text-gray-500">
                            {format(new Date(payment.date), "dd/MM/yyyy")}
                          </td>
                          <td className="px-2 py-1 text-xs text-gray-500">
                            {payment.method}
                          </td>
                          <td className="px-2 py-1 text-xs font-medium text-gray-900 text-right">
                            {formatCurrency(payment.amount)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              <div className="mt-2 text-xs text-gray-500">
                {service.isWarranty && (
                  <p className="font-medium text-green-600">
                    This service is covered under warranty.
                    {service.warrantyDetails && ` ${service.warrantyDetails}`}
                  </p>
                )}
              </div>
            </div>
          ) : (
            <p className="text-xs text-gray-500">No parts used for this service.</p>
          )}
        </div>

        {/* Signatures - More compact */}
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs font-medium mb-4">Customer Signature:</p>
            <div className="border-t border-gray-300 pt-1">
              <p className="text-xs text-gray-500">Name: {service.contact.name}</p>
              <p className="text-xs text-gray-500">Date: {format(new Date(), "dd/MM/yyyy")}</p>
            </div>
          </div>
          <div>
            <p className="text-xs font-medium mb-4">Technician Signature:</p>
            <div className="border-t border-gray-300 pt-1">
              <p className="text-xs text-gray-500">Name: {service.user.name}</p>
              <p className="text-xs text-gray-500">Date: {format(new Date(), "dd/MM/yyyy")}</p>
            </div>
          </div>
        </div>

        {/* Footer - More compact */}
        <div className="mt-4 pt-2 border-t border-gray-200 text-center text-xs text-gray-500">
          <p>Thank you for choosing {companyInfo.name} for your maintenance needs.</p>
          <p>For any inquiries, please contact us at {companyInfo.phone} or {companyInfo.email}</p>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          body {
            font-size: 10pt;
            color: #000;
            background: #fff;
          }
          @page {
            size: A4;
            margin: 1cm;
          }
          .print\\:shadow-none {
            box-shadow: none !important;
          }
          .print\\:p-0 {
            padding: 0 !important;
          }
          .print\\:hidden {
            display: none !important;
          }
          table {
            page-break-inside: avoid;
          }
          tr {
            page-break-inside: avoid;
            page-break-after: auto;
          }
          td, th {
            padding: 1px 2px !important;
          }
          h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
          }
          p {
            margin-bottom: 2px !important;
          }
          .space-y-1 > * + * {
            margin-top: 2px !important;
          }
          .mb-3 {
            margin-bottom: 6px !important;
          }
          .mb-4 {
            margin-bottom: 8px !important;
          }
          .mt-4 {
            margin-top: 8px !important;
          }
          .pb-3 {
            padding-bottom: 6px !important;
          }
          .pt-2 {
            padding-top: 4px !important;
          }
        }
      `}</style>
    </div>
  );
}
