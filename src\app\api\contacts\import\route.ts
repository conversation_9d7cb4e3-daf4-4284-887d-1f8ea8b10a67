import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import * as XLSX from 'xlsx';

// POST /api/contacts/import - Import contacts from Excel
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add contacts
    const canAddCustomer = await hasPermission("add_customers");
    const canAddSupplier = await hasPermission("add_suppliers");
    const canAddContacts = canAddCustomer || canAddSupplier || session.user.role === "ADMIN";

    if (!canAddContacts) {
      return NextResponse.json(
        { error: "You don't have permission to import contacts" },
        { status: 403 }
      );
    }

    let file: File;
    let data: any[];

    try {
      console.log("Parsing form data...");
      // Parse form data
      const formData = await req.formData();
      file = formData.get("file") as File;

      if (!file) {
        console.error("No file found in form data");
        return NextResponse.json(
          { error: "No file uploaded. Please select a file before submitting." },
          { status: 400 }
        );
      }

      console.log(`File received: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);

      // Validate file type
      const fileName = file.name;
      if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
        console.error(`Invalid file type: ${file.type}`);
        return NextResponse.json(
          { error: "Invalid file type. Only Excel files (.xlsx, .xls) are allowed." },
          { status: 400 }
        );
      }

      // Validate file size
      if (file.size === 0) {
        console.error("File is empty");
        return NextResponse.json(
          { error: "The uploaded file is empty. Please select a valid Excel file." },
          { status: 400 }
        );
      }

      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        console.error(`File too large: ${file.size} bytes`);
        return NextResponse.json(
          { error: "The file is too large. Maximum file size is 10MB." },
          { status: 400 }
        );
      }

      console.log("Reading file content...");
      // Read the Excel file
      try {
        const fileBuffer = await file.arrayBuffer();
        console.log(`File buffer size: ${fileBuffer.byteLength} bytes`);
        
        const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
        console.log(`Workbook sheets: ${workbook.SheetNames.join(', ')}`);

        if (workbook.SheetNames.length === 0) {
          console.error("No sheets found in workbook");
          return NextResponse.json(
            { error: "The Excel file does not contain any sheets." },
            { status: 400 }
          );
        }

        // Get the first worksheet
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        
        // Convert worksheet to JSON
        data = XLSX.utils.sheet_to_json(worksheet);
        console.log(`Parsed ${data.length} rows from Excel file`);
      } catch (xlsxError) {
        console.error("Error parsing Excel file:", xlsxError);
        return NextResponse.json(
          { 
            error: "Error processing Excel file. Please make sure the file is a valid Excel file.",
            details: xlsxError instanceof Error ? xlsxError.message : "Unknown error"
          },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error("Error processing form data:", error);
      return NextResponse.json(
        { 
          error: "Error processing the uploaded file. Please try again.",
          details: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 400 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: "The Excel file is empty or has no valid data" },
        { status: 400 }
      );
    }

    // Validate the data structure
    const requiredFields = ['Name', 'Phone'];
    const firstRow = data[0] as any;
    const missingFields = [];

    for (const field of requiredFields) {
      if (!(field in firstRow)) {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Process each contact
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const row of data) {
      const contact = row as any;

      try {
        // Validate required fields
        if (!contact.Name || !contact.Phone) {
          throw new Error("Name and Phone are required");
        }

        // Validate contact type
        const isCustomer = contact.IsCustomer === 'Yes' || contact.IsCustomer === true;
        const isSupplier = contact.IsSupplier === 'Yes' || contact.IsSupplier === true;

        if (!isCustomer && !isSupplier) {
          throw new Error("Contact must be either a customer or a supplier");
        }

        // Check if phone is already used
        const existingContact = await db.contact.findFirst({
          where: {
            phone: contact.Phone,
          },
        });

        if (existingContact) {
          throw new Error(`Phone number ${contact.Phone} already exists`);
        }

        // Create the contact
        await db.contact.create({
          data: {
            name: contact.Name,
            phone: contact.Phone,
            address: contact.Address || null,
            email: contact.Email || null,
            isCustomer: isCustomer,
            isSupplier: isSupplier,
            balance: 0, // Balance is calculated from transactions
            creditLimit: isCustomer ? (parseFloat(contact.CreditLimit) || 0) : 0,
            creditPeriod: isCustomer ? (parseInt(contact.CreditPeriod) || 30) : 0,
            openingBalance: parseFloat(contact.OpeningBalance) || 0,
            openingBalanceDate: contact.OpeningBalanceDate ? new Date(contact.OpeningBalanceDate) : new Date(),
            isActive: contact.IsActive === 'Yes' || contact.IsActive === true || contact.IsActive === undefined,
          },
        });

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Error importing ${contact.Name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      message: `Import completed. ${results.success} contacts imported successfully, ${results.failed} failed.`,
      results
    });
  } catch (error) {
    console.error("Error importing contacts:", error);
    return NextResponse.json(
      { error: "Failed to import contacts. Please try again or contact your system administrator." },
      { status: 500 }
    );
  }
}
