// This script checks if the admin user exists and creates it if it doesn't
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking for admin user...');

    // Check if admin user exists
    const adminUser = await prisma.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    });

    if (adminUser) {
      console.log("Admin user already exists");
      
      // Check if admin user has permissions
      const adminPermissions = await prisma.permission.findMany({
        where: {
          users: {
            some: {
              id: adminUser.id
            }
          }
        }
      });
      
      if (adminPermissions.length === 0) {
        console.log("Admin user has no permissions. Adding permissions...");
        await addPermissionsToAdmin(adminUser.id);
      } else {
        console.log(`Admin user has ${adminPermissions.length} permissions`);
      }
      
      return;
    }

    console.log("Admin user not found. Creating a new admin user...");

    // Find a branch or create one
    let branch = await prisma.branch.findFirst();

    if (!branch) {
      console.log("No branch found. Creating a new branch...");
      branch = await prisma.branch.create({
        data: {
          name: "Main Branch",
          code: "A",
          address: "Cairo, Egypt",
          phone: "01000000000",
        },
      });
      console.log(`Created new branch: ${branch.name}`);
    }

    // Create default permissions
    const defaultPermissions = [
      { name: "view_users", description: "View users" },
      { name: "add_users", description: "Add new users" },
      { name: "edit_users", description: "Edit existing users" },
      { name: "delete_users", description: "Delete users" },
      { name: "view_branches", description: "View branches" },
      { name: "add_branches", description: "Add new branches" },
      { name: "edit_branches", description: "Edit existing branches" },
      { name: "delete_branches", description: "Delete branches" },
      { name: "view_warehouses", description: "View warehouses" },
      { name: "add_warehouses", description: "Add new warehouses" },
      { name: "edit_warehouses", description: "Edit existing warehouses" },
      { name: "delete_warehouses", description: "Delete warehouses" },
      { name: "view_products", description: "View products" },
      { name: "add_products", description: "Add new products" },
      { name: "edit_products", description: "Edit existing products" },
      { name: "delete_products", description: "Delete products" },
      { name: "view_inventory", description: "View inventory" },
      { name: "add_inventory", description: "Add inventory" },
      { name: "edit_inventory", description: "Edit inventory" },
      { name: "delete_inventory", description: "Delete inventory" },
      { name: "view_sales", description: "View sales" },
      { name: "add_sales", description: "Add new sales" },
      { name: "edit_sales", description: "Edit existing sales" },
      { name: "delete_sales", description: "Delete sales" },
      { name: "view_purchases", description: "View purchases" },
      { name: "add_purchases", description: "Add new purchases" },
      { name: "edit_purchases", description: "Edit existing purchases" },
      { name: "delete_purchases", description: "Delete purchases" },
      { name: "view_contacts", description: "View contacts" },
      { name: "add_contacts", description: "Add new contacts" },
      { name: "edit_contacts", description: "Edit existing contacts" },
      { name: "delete_contacts", description: "Delete contacts" },
      { name: "view_reports", description: "View reports" },
      { name: "view_settings", description: "View settings" },
      { name: "edit_settings", description: "Edit settings" },
    ];

    // Create permissions
    const createdPermissions = [];
    for (const permission of defaultPermissions) {
      // Check if permission already exists
      const existingPermission = await prisma.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        const newPermission = await prisma.permission.create({
          data: permission,
        });
        createdPermissions.push(newPermission);
        console.log(`Created permission: ${permission.name}`);
      } else {
        createdPermissions.push(existingPermission);
        console.log(`Permission already exists: ${permission.name}`);
      }
    }

    // Create a new admin user
    const hashedPassword = await bcrypt.hash("admin123", 10);
    const newAdmin = await prisma.user.create({
      data: {
        name: "Admin User",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "ADMIN",
        branchId: branch.id,
        isActive: true,
        permissions: {
          connect: createdPermissions.map(p => ({ id: p.id })),
        },
      },
    });

    // Create a warehouse if none exists
    const warehouse = await prisma.warehouse.findFirst({
      where: {
        branchId: branch.id,
      },
    });

    if (!warehouse) {
      const newWarehouse = await prisma.warehouse.create({
        data: {
          name: "Main Warehouse",
          branchId: branch.id,
          isActive: true,
        },
      });

      // Connect admin to warehouse
      await prisma.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: newWarehouse.id,
        },
      });
    } else {
      // Connect admin to existing warehouse
      await prisma.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: warehouse.id,
        },
      });
    }

    console.log("Admin user created successfully");
    console.log("Email: <EMAIL>");
    console.log("Password: admin123");
  } catch (error) {
    console.error("Error creating admin user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

async function addPermissionsToAdmin(adminId) {
  try {
    // Get all permissions
    const allPermissions = await prisma.permission.findMany();
    
    // Connect all permissions to admin user
    await prisma.user.update({
      where: {
        id: adminId,
      },
      data: {
        permissions: {
          connect: allPermissions.map(p => ({ id: p.id })),
        },
      },
    });
    
    console.log(`Added ${allPermissions.length} permissions to admin user`);
  } catch (error) {
    console.error("Error adding permissions to admin user:", error);
  }
}

main();
