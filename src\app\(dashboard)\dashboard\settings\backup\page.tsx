"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Download, Upload, Calendar, Clock, Database, Save } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";

interface BackupSettings {
  enabled: boolean;
  frequency: "daily" | "weekly" | "monthly";
  dayOfWeek?: number; // 0-6 (Sunday-Saturday)
  dayOfMonth?: number; // 1-31
  time: string; // HH:MM format
  keepCount: number;
  storageType: "local" | "cloud";
  cloudProvider?: string;
  cloudSettings?: {
    apiKey?: string;
    bucketName?: string;
    region?: string;
  };
}

export default function BackupSettingsPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<BackupSettings>({
    enabled: false,
    frequency: "daily",
    time: "00:00",
    keepCount: 7,
    storageType: "local",
  });
  
  // Fetch backup settings
  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would fetch from an API
        // For now, we'll simulate a delay and use default values
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Simulated response
        setSettings({
          enabled: false,
          frequency: "daily",
          time: "00:00",
          keepCount: 7,
          storageType: "local",
        });
      } catch (error) {
        console.error("Error fetching backup settings:", error);
        toast({
          title: "Error",
          description: "Failed to load backup settings",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchSettings();
  }, [toast]);
  
  // Save backup settings
  const saveSettings = async () => {
    setSaving(true);
    try {
      // In a real implementation, this would send to an API
      // For now, we'll simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success",
        description: "Backup settings saved successfully",
      });
    } catch (error) {
      console.error("Error saving backup settings:", error);
      toast({
        title: "Error",
        description: "Failed to save backup settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };
  
  // Create manual backup
  const createBackup = async () => {
    try {
      toast({
        title: "Creating Backup",
        description: "Starting backup process...",
      });
      
      // In a real implementation, this would call an API endpoint
      // For now, we'll simulate a delay
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast({
        title: "Success",
        description: "Backup created successfully",
      });
    } catch (error) {
      console.error("Error creating backup:", error);
      toast({
        title: "Error",
        description: "Failed to create backup",
        variant: "destructive",
      });
    }
  };
  
  // Restore from backup
  const restoreBackup = async () => {
    try {
      toast({
        title: "Restoring Backup",
        description: "Starting restore process...",
      });
      
      // In a real implementation, this would call an API endpoint
      // For now, we'll simulate a delay
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast({
        title: "Success",
        description: "Backup restored successfully",
      });
    } catch (error) {
      console.error("Error restoring backup:", error);
      toast({
        title: "Error",
        description: "Failed to restore backup",
        variant: "destructive",
      });
    }
  };
  
  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Backup Settings</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={createBackup}>
            <Download className="h-4 w-4 mr-2" />
            Create Backup
          </Button>
          <Button variant="outline" onClick={restoreBackup}>
            <Upload className="h-4 w-4 mr-2" />
            Restore Backup
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="schedule">
        <TabsList>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="history">Backup History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="schedule" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Backup Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-backup">Enable Automatic Backup</Label>
                    <Switch
                      id="enable-backup"
                      checked={settings.enabled}
                      onCheckedChange={(checked) => setSettings({ ...settings, enabled: checked })}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="frequency">Backup Frequency</Label>
                      <Select
                        value={settings.frequency}
                        onValueChange={(value: "daily" | "weekly" | "monthly") => 
                          setSettings({ ...settings, frequency: value })
                        }
                        disabled={!settings.enabled}
                      >
                        <SelectTrigger id="frequency">
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {settings.frequency === "weekly" && (
                      <div>
                        <Label htmlFor="day-of-week">Day of Week</Label>
                        <Select
                          value={settings.dayOfWeek?.toString() || "0"}
                          onValueChange={(value) => 
                            setSettings({ ...settings, dayOfWeek: parseInt(value) })
                          }
                          disabled={!settings.enabled}
                        >
                          <SelectTrigger id="day-of-week">
                            <SelectValue placeholder="Select day" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">Sunday</SelectItem>
                            <SelectItem value="1">Monday</SelectItem>
                            <SelectItem value="2">Tuesday</SelectItem>
                            <SelectItem value="3">Wednesday</SelectItem>
                            <SelectItem value="4">Thursday</SelectItem>
                            <SelectItem value="5">Friday</SelectItem>
                            <SelectItem value="6">Saturday</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                    
                    {settings.frequency === "monthly" && (
                      <div>
                        <Label htmlFor="day-of-month">Day of Month</Label>
                        <Select
                          value={settings.dayOfMonth?.toString() || "1"}
                          onValueChange={(value) => 
                            setSettings({ ...settings, dayOfMonth: parseInt(value) })
                          }
                          disabled={!settings.enabled}
                        >
                          <SelectTrigger id="day-of-month">
                            <SelectValue placeholder="Select day" />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: 31 }, (_, i) => (
                              <SelectItem key={i + 1} value={(i + 1).toString()}>
                                {i + 1}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                    
                    <div>
                      <Label htmlFor="time">Time</Label>
                      <Input
                        id="time"
                        type="time"
                        value={settings.time}
                        onChange={(e) => setSettings({ ...settings, time: e.target.value })}
                        disabled={!settings.enabled}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="keep-count">Keep Last N Backups</Label>
                      <Input
                        id="keep-count"
                        type="number"
                        min="1"
                        max="30"
                        value={settings.keepCount}
                        onChange={(e) => setSettings({ ...settings, keepCount: parseInt(e.target.value) || 7 })}
                        disabled={!settings.enabled}
                      />
                    </div>
                  </div>
                  
                  <Button onClick={saveSettings} disabled={saving}>
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Settings
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="storage" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Storage Settings</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="storage-type">Storage Type</Label>
                    <Select
                      value={settings.storageType}
                      onValueChange={(value: "local" | "cloud") => 
                        setSettings({ ...settings, storageType: value })
                      }
                      disabled={!settings.enabled}
                    >
                      <SelectTrigger id="storage-type">
                        <SelectValue placeholder="Select storage type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="local">Local Storage</SelectItem>
                        <SelectItem value="cloud">Cloud Storage</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {settings.storageType === "cloud" && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="cloud-provider">Cloud Provider</Label>
                        <Select
                          value={settings.cloudProvider || "s3"}
                          onValueChange={(value) => 
                            setSettings({ 
                              ...settings, 
                              cloudProvider: value,
                              cloudSettings: { ...(settings.cloudSettings || {}) }
                            })
                          }
                          disabled={!settings.enabled}
                        >
                          <SelectTrigger id="cloud-provider">
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="s3">Amazon S3</SelectItem>
                            <SelectItem value="gcs">Google Cloud Storage</SelectItem>
                            <SelectItem value="azure">Azure Blob Storage</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="api-key">API Key / Access Key</Label>
                        <Input
                          id="api-key"
                          type="password"
                          value={settings.cloudSettings?.apiKey || ""}
                          onChange={(e) => setSettings({
                            ...settings,
                            cloudSettings: {
                              ...(settings.cloudSettings || {}),
                              apiKey: e.target.value
                            }
                          })}
                          disabled={!settings.enabled}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="bucket-name">Bucket Name</Label>
                        <Input
                          id="bucket-name"
                          type="text"
                          value={settings.cloudSettings?.bucketName || ""}
                          onChange={(e) => setSettings({
                            ...settings,
                            cloudSettings: {
                              ...(settings.cloudSettings || {}),
                              bucketName: e.target.value
                            }
                          })}
                          disabled={!settings.enabled}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="region">Region</Label>
                        <Input
                          id="region"
                          type="text"
                          value={settings.cloudSettings?.region || ""}
                          onChange={(e) => setSettings({
                            ...settings,
                            cloudSettings: {
                              ...(settings.cloudSettings || {}),
                              region: e.target.value
                            }
                          })}
                          disabled={!settings.enabled}
                        />
                      </div>
                    </div>
                  )}
                  
                  <Button onClick={saveSettings} disabled={saving}>
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Settings
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="history" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Backup History</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No backups found</AlertTitle>
                <AlertDescription>
                  No backup history is available. Create your first backup to see it here.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
