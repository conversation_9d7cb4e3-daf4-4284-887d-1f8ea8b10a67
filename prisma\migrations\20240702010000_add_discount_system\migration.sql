-- Create DiscountType enum
CREATE TYPE "DiscountType" AS ENUM ('PERCENTAGE', 'FIXED_AMOUNT');

-- Create DiscountScope enum
CREATE TYPE "DiscountScope" AS ENUM ('ITEM', 'INVOICE', 'CUSTOMER');

-- Create Discount table
CREATE TABLE "Discount" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "DiscountType" NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "scope" "DiscountScope" NOT NULL,
    "minAmount" DOUBLE PRECISION,
    "maxAmount" DOUBLE PRECISION,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "categoryId" TEXT,
    "productId" TEXT,

    CONSTRAINT "Discount_pkey" PRIMARY KEY ("id")
);

-- Create CustomerDiscount table for customer-specific discounts
CREATE TABLE "CustomerDiscount" (
    "id" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "contactId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CustomerDiscount_pkey" PRIMARY KEY ("id")
);

-- Create SaleDiscount table for tracking discounts applied to sales
CREATE TABLE "SaleDiscount" (
    "id" TEXT NOT NULL,
    "saleId" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "type" "DiscountType" NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SaleDiscount_pkey" PRIMARY KEY ("id")
);

-- Create SaleItemDiscount table for tracking discounts applied to sale items
CREATE TABLE "SaleItemDiscount" (
    "id" TEXT NOT NULL,
    "saleItemId" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "type" "DiscountType" NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SaleItemDiscount_pkey" PRIMARY KEY ("id")
);

-- Add loyalty fields to Contact table
ALTER TABLE "Contact" 
    ADD COLUMN "isVIP" BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN "loyaltyPoints" INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN "loyaltyTier" TEXT,
    ADD COLUMN "defaultDiscountId" TEXT;

-- Add foreign key constraints
ALTER TABLE "Discount" ADD CONSTRAINT "Discount_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Discount" ADD CONSTRAINT "Discount_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "CustomerDiscount" ADD CONSTRAINT "CustomerDiscount_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "CustomerDiscount" ADD CONSTRAINT "CustomerDiscount_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "CustomerDiscount" ADD CONSTRAINT "CustomerDiscount_discountId_contactId_key" UNIQUE ("discountId", "contactId");

ALTER TABLE "SaleDiscount" ADD CONSTRAINT "SaleDiscount_saleId_fkey" FOREIGN KEY ("saleId") REFERENCES "Sale"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "SaleDiscount" ADD CONSTRAINT "SaleDiscount_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "SaleItemDiscount" ADD CONSTRAINT "SaleItemDiscount_saleItemId_fkey" FOREIGN KEY ("saleItemId") REFERENCES "SaleItem"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "SaleItemDiscount" ADD CONSTRAINT "SaleItemDiscount_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "Contact" ADD CONSTRAINT "Contact_defaultDiscountId_fkey" FOREIGN KEY ("defaultDiscountId") REFERENCES "Discount"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX "Discount_isActive_idx" ON "Discount"("isActive");
CREATE INDEX "Discount_startDate_endDate_idx" ON "Discount"("startDate", "endDate");
CREATE INDEX "Discount_scope_idx" ON "Discount"("scope");
CREATE INDEX "CustomerDiscount_contactId_idx" ON "CustomerDiscount"("contactId");
CREATE INDEX "SaleDiscount_saleId_idx" ON "SaleDiscount"("saleId");
CREATE INDEX "SaleItemDiscount_saleItemId_idx" ON "SaleItemDiscount"("saleItemId");
CREATE INDEX "Contact_isVIP_idx" ON "Contact"("isVIP");
CREATE INDEX "Contact_loyaltyTier_idx" ON "Contact"("loyaltyTier");
