"use client";

import { useState, useRef } from "react";
import { motion } from "framer-motion";
import {
  Cpu,
  HardDrive,
  Save,
  DollarSign,
  Plus,
  X
} from "lucide-react";

// Component types
type ComponentType = string;

// Component interface
interface Component {
  id: string;
  name: string;
  type: ComponentType;
  price: number;
  stock: number;
  capacity?: string;
  speed?: string;
  description?: string;
  imageUrl?: string;
}

interface DraggableComponentSelectorProps {
  componentType: ComponentType;
  components: Component[];
  selectedComponent?: Component;
  onSelect: (component: Component | undefined) => void;
  className?: string;
}

export default function DraggableComponentSelector({
  componentType,
  components,
  selectedComponent,
  onSelect,
  className = ""
}: DraggableComponentSelectorProps) {
  // State for drag
  const [isDragging, setIsDragging] = useState(false);
  const [draggedComponent, setDraggedComponent] = useState<Component | null>(null);

  // Ref for drop zone
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // Get icon based on component type
  const getComponentIcon = (type: ComponentType) => {
    switch (type) {
      case "CPU":
        return <Cpu className="h-5 w-5 text-blue-500" />;
      case "RAM":
        return <Save className="h-5 w-5 text-green-500" />;
      case "SSD":
      case "HDD":
      case "NVMe":
        return <HardDrive className="h-5 w-5 text-purple-500" />;
      default:
        return <DollarSign className="h-5 w-5 text-gray-500" />;
    }
  };

  // Handle drag start
  const handleDragStart = (component: Component) => {
    setIsDragging(true);
    setDraggedComponent(component);
  };

  // Handle drag end
  const handleDragEnd = (component: Component, dropSuccess: boolean) => {
    setIsDragging(false);
    setDraggedComponent(null);

    if (dropSuccess) {
      onSelect(component);
    }
  };

  // Type guard for component
  const isValidComponent = (component: unknown): component is Component => {
    return (
      typeof component === 'object' &&
      component !== null &&
      'id' in component &&
      'name' in component &&
      'type' in component &&
      'price' in component
    );
  };

  // Handle drop
  const handleDrop = (component: Component) => {
    if (dropZoneRef.current) {
      onSelect(component);
      return true;
    }
    return false;
  };

  // Handle remove selected component
  const handleRemove = () => {
    onSelect(undefined);
  };

  return (
    <div className={`${className}`}>
      <div className="mb-2 flex justify-between items-center">
        <h3 className="text-sm font-medium text-gray-700 flex items-center">
          {getComponentIcon(componentType)}
          <span className="ml-2">{componentType}</span>
        </h3>

        {selectedComponent && (
          <button
            type="button"
            className="text-xs text-red-500 hover:text-red-700 flex items-center"
            onClick={handleRemove}
          >
            <X className="h-3 w-3 mr-1" />
            Remove
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4">
        {/* Drop Zone */}
        <div
          ref={dropZoneRef}
          className={`border-2 ${
            selectedComponent
              ? 'border-blue-500 bg-blue-50'
              : isDragging
                ? 'border-dashed border-blue-300 bg-blue-50'
                : 'border-dashed border-gray-300'
          } rounded-lg p-4 min-h-[100px] flex items-center justify-center transition-colors`}
        >
          {selectedComponent ? (
            <div className="w-full">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium text-sm">{selectedComponent.name}</h4>
                  {selectedComponent.capacity && (
                    <p className="text-xs text-gray-500 mt-1">Capacity: {selectedComponent.capacity}</p>
                  )}
                  {selectedComponent.speed && (
                    <p className="text-xs text-gray-500">Speed: {selectedComponent.speed}</p>
                  )}
                </div>
                <span className="text-sm font-medium">{selectedComponent.price.toFixed(2)} ج.م</span>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500">
              <Plus className="h-8 w-8 mx-auto text-gray-300" />
              <p className="text-sm mt-2">Drag a {componentType} here</p>
            </div>
          )}
        </div>

        {/* Available Components */}
        <div className="mt-4">
          <h4 className="text-xs font-medium text-gray-500 mb-2">Available Components</h4>
          <div className="space-y-2 max-h-[200px] overflow-y-auto pr-2">
            {components.map((component) => (
              <motion.div
                key={component.id}
                drag
                dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                whileDrag={{ scale: 1.05, zIndex: 10 }}
                onDragStart={() => handleDragStart(component)}
                onDragEnd={(_, info) => {
                  const dropSuccess = handleDrop(component);
                  handleDragEnd(component, dropSuccess);
                }}
                onClick={() => onSelect(component)}
                className={`border rounded-lg p-3 cursor-grab active:cursor-grabbing ${
                  component.stock <= 0 ? 'opacity-50' : ''
                } hover:border-blue-300 transition-colors`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="font-medium text-sm">{component.name}</h5>
                    {component.capacity && (
                      <p className="text-xs text-gray-500 mt-1">Capacity: {component.capacity}</p>
                    )}
                    {component.speed && (
                      <p className="text-xs text-gray-500">Speed: {component.speed}</p>
                    )}
                  </div>
                  <span className="text-sm font-medium">{component.price.toFixed(2)} ج.م</span>
                </div>
                {component.stock <= 0 && (
                  <p className="text-xs text-red-500 mt-1">Out of stock</p>
                )}
              </motion.div>
            ))}

            {components.length === 0 && (
              <p className="text-sm text-gray-500 text-center py-4">
                No {componentType} components available
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
