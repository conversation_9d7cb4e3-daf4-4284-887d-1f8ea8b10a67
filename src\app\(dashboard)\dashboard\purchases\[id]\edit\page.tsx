'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Save, Trash } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

interface Product {
  id: string;
  name: string;
  sku: string;
  basePrice: number;
  specifications: any;
}

interface Warehouse {
  id: string;
  name: string;
}

interface Contact {
  id: string;
  name: string;
  type: string;
}

interface Branch {
  id: string;
  name: string;
  code: string;
}

interface PurchaseItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
  warehouseId: string;
  warehouseName: string;
  specifications?: any;
}

interface Purchase {
  id: string;
  invoiceNumber: string;
  date: string;
  contactId: string;
  branchId: string;
  status: string;
  paymentMethod: string;
  paymentStatus: string;
  subtotalAmount: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  notes: string | null;
  currency: string;
  items: PurchaseItem[];
}

export default function EditPurchasePage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form data
  const [purchase, setPurchase] = useState<Purchase | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);

  // Form fields
  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [selectedContact, setSelectedContact] = useState('');
  const [selectedBranch, setSelectedBranch] = useState('');
  const [status, setStatus] = useState<'DRAFT' | 'COMPLETED'>('COMPLETED');
  const [paymentMethod, setPaymentMethod] = useState<string>('CASH');
  const [paymentStatus, setPaymentStatus] = useState<'PAID' | 'PARTIALLY_PAID' | 'UNPAID'>('PAID');

  // Payment methods
  interface PaymentMethodSetting {
    id: string;
    name: string;
    code: string;
    isActive: boolean;
  }
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState<PaymentMethodSetting[]>([]);
  const [paymentMethodsLoading, setPaymentMethodsLoading] = useState(false);
  const [notes, setNotes] = useState('');
  const [items, setItems] = useState<PurchaseItem[]>([]);

  // Item form
  const [selectedProduct, setSelectedProduct] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [unitPrice, setUnitPrice] = useState(1);

  // Calculations
  const [subtotal, setSubtotal] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [taxRate, setTaxRate] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [total, setTotal] = useState(0);

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      setPaymentMethodsLoading(true);
      const response = await fetch('/api/settings/payment-methods');

      if (response.ok) {
        const data = await response.json();
        // Filter only active payment methods
        const activeMethods = data.filter((method: PaymentMethodSetting) =>
          method.isActive && method.code !== 'CUSTOMER_ACCOUNT' && method.code !== 'SUPPLIER_ACCOUNT'
        );

        setAvailablePaymentMethods(activeMethods);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    } finally {
      setPaymentMethodsLoading(false);
    }
  };

  // Fetch purchase and reference data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch purchase
        const purchaseResponse = await fetch(`/api/purchases/${params.id}`);
        if (!purchaseResponse.ok) {
          throw new Error('Failed to fetch purchase');
        }
        const purchaseData = await purchaseResponse.json();

        // Fetch products
        const productsResponse = await fetch('/api/products');
        const productsData = await productsResponse.json();

        // Fetch contacts (suppliers)
        const contactsResponse = await fetch('/api/contacts?type=supplier');
        const contactsData = await contactsResponse.json();

        // Fetch branches
        const branchesResponse = await fetch('/api/branches');
        const branchesData = await branchesResponse.json();

        // Fetch warehouses
        const warehousesResponse = await fetch('/api/warehouses');
        const warehousesData = await warehousesResponse.json();

        // Fetch payment methods
        await fetchPaymentMethods();

        // Set data
        setPurchase(purchaseData);
        setProducts(productsData);
        setContacts(contactsData);
        setBranches(branchesData);
        setWarehouses(warehousesData);

        // Set form fields
        setInvoiceNumber(purchaseData.invoiceNumber);
        setDate(format(new Date(purchaseData.date), 'yyyy-MM-dd'));
        setSelectedContact(purchaseData.contactId);
        setSelectedBranch(purchaseData.branchId);
        setStatus(purchaseData.status);
        setPaymentMethod(purchaseData.paymentMethod);
        setPaymentStatus(purchaseData.paymentStatus);
        setNotes(purchaseData.notes || '');

        // Transform items
        const transformedItems = purchaseData.items.map((item: any) => ({
          id: item.id,
          productId: item.productId,
          productName: item.product.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.totalPrice,
          warehouseId: item.warehouseId || warehouses[0]?.id || '',
          warehouseName: item.warehouseName || warehouses[0]?.name || '',
          specifications: item.product.specifications,
        }));

        setItems(transformedItems);

        // Set calculations
        setSubtotal(purchaseData.subtotalAmount);
        setDiscount(purchaseData.discountAmount);
        const calculatedTaxRate = purchaseData.subtotalAmount > 0
          ? (purchaseData.taxAmount / (purchaseData.subtotalAmount - purchaseData.discountAmount)) * 100
          : 0;
        setTaxRate(calculatedTaxRate);
        setTaxAmount(purchaseData.taxAmount);
        setTotal(purchaseData.totalAmount);

      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load purchase data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  // Update calculations when items, discount or tax rate change
  useEffect(() => {
    const newSubtotal = items.reduce((sum, item) => sum + item.total, 0);
    setSubtotal(newSubtotal);

    const newTaxAmount = (newSubtotal - discount) * (taxRate / 100);
    setTaxAmount(newTaxAmount);

    const newTotal = newSubtotal - discount + newTaxAmount;
    setTotal(newTotal);
  }, [items, discount, taxRate]);

  // Add item to purchase
  const addItem = () => {
    if (!selectedProduct || !selectedWarehouse) return;

    // Ensure quantity and unit price are valid
    let finalQuantity = quantity;
    let finalUnitPrice = unitPrice;

    if (finalQuantity <= 0) {
      finalQuantity = 1;
      setQuantity(1);
    }

    if (finalUnitPrice <= 0) {
      finalUnitPrice = 1;
      setUnitPrice(1);
    }

    const product = products.find(p => p.id === selectedProduct);
    if (!product) return;

    // Create a new item
    const newItem = {
      id: `item-${Date.now()}`,
      productId: product.id,
      productName: product.name,
      unitPrice: finalUnitPrice,
      quantity: finalQuantity,
      total: finalUnitPrice * finalQuantity,
      warehouseId: selectedWarehouse,
      warehouseName: warehouses.find(w => w.id === selectedWarehouse)?.name || "Unknown",
      specifications: product.specifications,
    };

    setItems([...items, newItem]);
    setSelectedProduct("");
    setQuantity(1);
    setUnitPrice(1);
  };

  // Remove item from purchase
  const removeItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  // Update unit price when product changes
  useEffect(() => {
    if (selectedProduct) {
      const product = products.find(p => p.id === selectedProduct);
      if (product) {
        // Set a default price of 1 if the product's base price is 0 or negative
        const price = product.basePrice > 0 ? product.basePrice : 1;
        setUnitPrice(price);
      }
    } else {
      setUnitPrice(1); // Default to 1 instead of 0
    }
  }, [selectedProduct, products]);

  // Save purchase
  const savePurchase = async () => {
    if (!selectedContact || !selectedBranch || items.length === 0) {
      alert('Please fill in all required fields and add at least one item');
      return;
    }

    try {
      setSaving(true);

      const purchaseData = {
        invoiceNumber,
        date: new Date(date).toISOString(),
        contactId: selectedContact,
        branchId: selectedBranch,
        status,
        paymentMethod,
        paymentStatus,
        subtotal,
        discount,
        taxAmount,
        total,
        notes: notes || null,
        currency: 'EGP',
        items: items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total,
          warehouseId: item.warehouseId,
        })),
      };

      const response = await fetch(`/api/purchases/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(purchaseData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update purchase');
      }

      router.push(`/dashboard/purchases/${params.id}`);
    } catch (err: any) {
      console.error('Error updating purchase:', err);
      alert(`Error updating purchase: ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-700">Loading purchase data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !purchase) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          <p>{error || 'Purchase not found'}</p>
          <Link href="/dashboard/purchases" className="text-red-600 underline mt-2 inline-block">
            Return to purchases
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link href={`/dashboard/purchases/${params.id}`} className="mr-4">
            <ArrowLeft className="h-5 w-5 text-black" />
          </Link>
          <h1 className="text-2xl font-bold text-black">Edit Purchase</h1>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={savePurchase}
            disabled={saving}
            className="flex items-center px-4 py-2 bg-blue-600 rounded-md text-white hover:bg-blue-700 disabled:bg-blue-300"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-black mb-1">Invoice Number</label>
            <input
              type="text"
              value={invoiceNumber}
              onChange={(e) => setInvoiceNumber(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Date</label>
            <input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Supplier</label>
            <select
              value={selectedContact}
              onChange={(e) => setSelectedContact(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            >
              <option value="" className="text-black">Select Supplier</option>
              {contacts.map((contact) => (
                <option key={contact.id} value={contact.id} className="text-black">
                  {contact.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Branch</label>
            <select
              value={selectedBranch}
              onChange={(e) => setSelectedBranch(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            >
              <option value="" className="text-black">Select Branch</option>
              {branches.map((branch) => (
                <option key={branch.id} value={branch.id} className="text-black">
                  {branch.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Status</label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value as 'DRAFT' | 'COMPLETED')}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
            >
              <option value="DRAFT" className="text-black">Draft</option>
              <option value="COMPLETED" className="text-black">Completed</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Payment Status</label>
            <select
              value={paymentStatus}
              onChange={(e) => setPaymentStatus(e.target.value as 'PAID' | 'PARTIALLY_PAID' | 'UNPAID')}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
            >
              <option value="PAID" className="text-black">Paid</option>
              <option value="PARTIALLY_PAID" className="text-black">Partially Paid</option>
              <option value="UNPAID" className="text-black">Unpaid</option>
            </select>
          </div>
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4 text-black">Payment Method</h2>
          {paymentMethodsLoading ? (
            <div className="flex justify-center items-center py-4">
              <svg className="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="ml-2 text-gray-600">Loading payment methods...</span>
            </div>
          ) : availablePaymentMethods.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              No payment methods available. Please add payment methods in settings.
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {availablePaymentMethods.map((method) => (
                <div
                  key={method.id}
                  className={`p-2 border rounded-md cursor-pointer flex items-center ${paymentMethod === method.code ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                  onClick={() => setPaymentMethod(method.code)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-black" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 2h8a2 2 0 012 2v4a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2z" clipRule="evenodd" />
                  </svg>
                  <span className="font-medium text-black">{method.name}</span>
                </div>
              ))}
              <div
                className={`p-2 border rounded-md cursor-pointer flex items-center ${paymentMethod === 'SUPPLIER_ACCOUNT' ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                onClick={() => setPaymentMethod('SUPPLIER_ACCOUNT')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-black" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
                <span className="font-medium text-black">Supplier Account</span>
              </div>
            </div>
          )}
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4 text-black">Items</h2>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-black mb-1">Product</label>
              <select
                value={selectedProduct}
                onChange={(e) => setSelectedProduct(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-black"
              >
                <option value="" className="text-black">Select Product</option>
                {products.map((product) => (
                  <option key={product.id} value={product.id} className="text-black">
                    {product.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-black mb-1">Warehouse</label>
              <select
                value={selectedWarehouse}
                onChange={(e) => setSelectedWarehouse(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-black"
              >
                <option value="" className="text-black">Select Warehouse</option>
                {warehouses.map((warehouse) => (
                  <option key={warehouse.id} value={warehouse.id} className="text-black">
                    {warehouse.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-black mb-1">Unit Price</label>
              <input
                type="number"
                value={unitPrice}
                onChange={(e) => setUnitPrice(parseFloat(e.target.value) || 0)}
                className="w-full p-2 border border-gray-300 rounded-md text-black"
                min="0"
                step="0.01"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-black mb-1">Quantity</label>
              <div className="flex">
                <input
                  type="number"
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
                  className="w-full p-2 border border-gray-300 rounded-l-md text-black"
                  min="1"
                />
                <button
                  onClick={addItem}
                  disabled={!selectedProduct || !selectedWarehouse}
                  className="bg-blue-600 text-white px-4 rounded-r-md hover:bg-blue-700 disabled:bg-blue-300"
                >
                  Add
                </button>
              </div>
            </div>
          </div>

          {items.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Product</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Warehouse</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Unit Price</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Quantity</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Total</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-black">{item.productName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-black">{item.warehouseName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-black">{item.unitPrice.toFixed(2)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-black">{item.quantity}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-black">{item.total.toFixed(2)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-4 bg-gray-50 rounded-md">
              <p className="text-gray-500">No items added yet</p>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-black mb-1">Notes</label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              rows={4}
            ></textarea>
          </div>

          <div>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex justify-between py-2">
                <span className="text-black font-medium">Subtotal:</span>
                <span className="text-black">{subtotal.toFixed(2)} EGP</span>
              </div>
              <div className="flex justify-between py-2">
                <span className="text-black font-medium">Discount:</span>
                <div className="flex items-center">
                  <input
                    type="number"
                    value={discount}
                    onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
                    className="w-20 p-1 border border-gray-300 rounded-md text-right text-black"
                    min="0"
                    step="0.01"
                  />
                  <span className="ml-2 text-black">EGP</span>
                </div>
              </div>
              <div className="flex justify-between py-2">
                <span className="text-black font-medium">Tax Rate:</span>
                <div className="flex items-center">
                  <input
                    type="number"
                    value={taxRate}
                    onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
                    className="w-20 p-1 border border-gray-300 rounded-md text-right text-black"
                    min="0"
                    max="100"
                    step="0.01"
                  />
                  <span className="ml-2 text-black">%</span>
                </div>
              </div>
              <div className="flex justify-between py-2">
                <span className="text-black font-medium">Tax Amount:</span>
                <span className="text-black">{taxAmount.toFixed(2)} EGP</span>
              </div>
              <div className="flex justify-between py-2 font-bold border-t border-gray-200 mt-2 pt-2">
                <span className="text-black">Total:</span>
                <span className="text-black">{total.toFixed(2)} EGP</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
