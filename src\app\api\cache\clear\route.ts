import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import cache from "@/lib/cache";

// POST /api/cache/clear - Clear cache
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only allow admins to clear cache
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only administrators can clear cache" },
        { status: 403 }
      );
    }

    console.log("Clearing cache...");

    // Get the pattern from the request body
    const data = await req.json();
    const pattern = data.pattern;

    if (pattern) {
      // Clear cache for specific pattern
      try {
        const regex = new RegExp(pattern);
        cache.deletePattern(regex);
        console.log(`Cleared cache for pattern: ${pattern}`);
      } catch (error) {
        console.error("Invalid regex pattern:", error);
        return NextResponse.json(
          { error: "Invalid regex pattern" },
          { status: 400 }
        );
      }
    } else {
      // Clear all cache
      cache.clear();
      console.log("Cleared all cache");
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error clearing cache:", error);
    return NextResponse.json(
      { error: "Failed to clear cache" },
      { status: 500 }
    );
  }
}
