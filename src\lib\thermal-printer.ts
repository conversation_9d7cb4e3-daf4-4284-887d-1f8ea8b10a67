"use client";

import { PrintService } from './print-service';

interface ThermalPrinterOptions {
  printerName?: string;
  paperWidth?: number;
  paperHeight?: number;
  characterSet?: string;
  encoding?: string;
  copies?: number;
}

export class ThermalPrinter {
  private printService: PrintService;
  private options: ThermalPrinterOptions;
  
  constructor(options: ThermalPrinterOptions = {}) {
    this.printService = new PrintService();
    this.options = {
      printerName: options.printerName || 'Default Printer',
      paperWidth: options.paperWidth || 80, // mm
      paperHeight: options.paperHeight || 297, // mm (A4 height by default)
      characterSet: options.characterSet || 'UTF-8',
      encoding: options.encoding || 'windows-1256',
      copies: options.copies || 1
    };
  }
  
  /**
   * Print a receipt
   */
  async printReceipt(data: any, options: {
    template?: string;
    language?: string;
    copies?: number;
  } = {}) {
    try {
      // Initialize print service
      await this.printService.initialize();
      
      // Get settings
      const settings = await this.printService.getSettings();
      const thermalSettings = settings.thermalPrinterSettings || {};
      
      // Prepare thermal printer specific options
      const printerOptions = {
        printerName: options.copies || this.options.copies || thermalSettings.copies || 1,
        paperWidth: this.options.paperWidth || thermalSettings.paperWidth || 80,
        paperHeight: this.options.paperHeight || thermalSettings.paperHeight || 297,
        copies: options.copies || this.options.copies || thermalSettings.copies || 1
      };
      
      // Generate HTML for thermal printer
      const html = await this.generateThermalHTML(data, options.template, options.language);
      
      // Print using the print service
      await this.printService.print('receipt', {
        ...data,
        thermal_html: html
      }, {
        printerName: printerOptions.printerName,
        copies: printerOptions.copies,
        language: options.language || 'ar'
      });
      
      return true;
    } catch (error) {
      console.error('Failed to print receipt:', error);
      throw error;
    }
  }
  
  /**
   * Generate HTML for thermal printer
   */
  private async generateThermalHTML(data: any, template?: string, language: string = 'ar'): Promise<string> {
    // If a custom template is provided, use it
    if (template) {
      return this.applyDataToTemplate(template, data);
    }
    
    // Otherwise, use a default thermal receipt template
    return `
      <div style="font-family: 'Courier New', monospace; font-size: 10pt; width: ${this.options.paperWidth}mm; max-width: ${this.options.paperWidth}mm; text-align: center;">
        <div style="text-align: center; margin-bottom: 10px;">
          <h1 style="font-size: 12pt; margin: 0;">${data.company_name || 'VERO Company'}</h1>
          ${data.company_address ? `<p style="margin: 0;">${data.company_address}</p>` : ''}
          ${data.company_phone ? `<p style="margin: 0;">Tel: ${data.company_phone}</p>` : ''}
        </div>
        
        <div style="border-top: 1px dashed #000; border-bottom: 1px dashed #000; padding: 5px 0; margin: 5px 0;">
          <p style="margin: 0;">Receipt #: ${data.receipt_number || data.invoice_number || ''}</p>
          <p style="margin: 0;">Date: ${data.date || new Date().toLocaleDateString()}</p>
          ${data.cashier ? `<p style="margin: 0;">Cashier: ${data.cashier}</p>` : ''}
        </div>
        
        <div style="text-align: left; margin: 10px 0;">
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr>
                <th style="text-align: left; padding: 2px;">Item</th>
                <th style="text-align: right; padding: 2px;">Qty</th>
                <th style="text-align: right; padding: 2px;">Price</th>
                <th style="text-align: right; padding: 2px;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${this.generateItemsHTML(data.items || [])}
            </tbody>
          </table>
        </div>
        
        <div style="border-top: 1px dashed #000; padding-top: 5px; margin-top: 5px; text-align: right;">
          <p style="margin: 0;"><strong>Subtotal:</strong> ${data.subtotal || '0.00'}</p>
          ${data.tax ? `<p style="margin: 0;"><strong>Tax:</strong> ${data.tax}</p>` : ''}
          ${data.discount ? `<p style="margin: 0;"><strong>Discount:</strong> ${data.discount}</p>` : ''}
          <p style="margin: 5px 0; font-size: 12pt;"><strong>Total:</strong> ${data.total || '0.00'}</p>
          ${data.payment_method ? `<p style="margin: 0;"><strong>Payment:</strong> ${data.payment_method}</p>` : ''}
        </div>
        
        <div style="text-align: center; margin-top: 10px;">
          <p style="margin: 0;">${data.footer_text || 'Thank you for your business!'}</p>
          ${data.barcode ? `<div style="margin: 10px 0;"><img src="${data.barcode_image || ''}" alt="Barcode" style="max-width: 100%;"></div>` : ''}
        </div>
      </div>
    `;
  }
  
  /**
   * Generate HTML for items
   */
  private generateItemsHTML(items: any[]): string {
    if (!items || !items.length) {
      return '<tr><td colspan="4" style="text-align: center; padding: 5px;">No items</td></tr>';
    }
    
    return items.map(item => `
      <tr>
        <td style="text-align: left; padding: 2px;">${item.name || 'Unknown Item'}</td>
        <td style="text-align: right; padding: 2px;">${item.quantity || 1}</td>
        <td style="text-align: right; padding: 2px;">${item.price || '0.00'}</td>
        <td style="text-align: right; padding: 2px;">${item.total || '0.00'}</td>
      </tr>
    `).join('');
  }
  
  /**
   * Apply data to template
   */
  private applyDataToTemplate(template: string, data: any): string {
    let result = template;
    
    // Replace variables with actual data
    Object.keys(data).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, data[key] !== undefined ? data[key] : '');
    });
    
    return result;
  }
  
  /**
   * Get available printers
   */
  async getAvailablePrinters(): Promise<string[]> {
    // This is a browser-based implementation, so we can't get the actual list of printers
    // In a real implementation, this would use a backend API or Electron to get the list
    return ['Default Printer'];
  }
  
  /**
   * Test printer connection
   */
  async testPrinter(printerName: string): Promise<boolean> {
    // This is a browser-based implementation, so we can't test the actual printer
    // In a real implementation, this would use a backend API or Electron to test the printer
    return true;
  }
}
