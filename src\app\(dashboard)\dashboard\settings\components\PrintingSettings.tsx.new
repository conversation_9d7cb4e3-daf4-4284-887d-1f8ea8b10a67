"use client";

import { useState, useEffect } from "react";
import axios from "axios";
import { BarcodeComponent } from "@/components/shared/Barcodes";

// Define types
type TemplateType = 'invoice' | 'receipt' | 'report' | 'label' | 'purchase_order' | 'delivery_note' | 'maintenance_receipt' | 'maintenance_report' | 'customer_statement';

interface Template {
  id: string;
  name: string;
  type: TemplateType;
  content: string;
  isDefault: boolean;
  language: string;
  paperSize: string;
  orientation: string;
  branchId?: string;
  variables?: any;
  previewImage?: string;
}

interface PrintSettings {
  id?: string;
  pageSize: string;
  orientation: string;
  marginTop: number;
  marginRight: number;
  marginBottom: number;
  marginLeft: number;
  headerHeight: number;
  footerHeight: number;
  showLogo: boolean;
  logoPosition: string;
  showBranch: boolean;
  showQRCode: boolean;
  showBarcode: boolean;
  fontSize: number;
  fontFamily: string;
  primaryColor: string;
  secondaryColor: string;
  footerText: string;
  termsText: string;
  customWidth?: number;
  customHeight?: number;
  companyLogo?: string;
  defaultTemplateSettings?: any;
  thermalPrinterSettings?: any;
  signatureSettings?: any;
  stampSettings?: any;
  multiLanguageSettings?: any;
  branchId?: string;
}

export default function PrintingSettings() {
  const [activeTab, setActiveTab] = useState("general");
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Settings state
  const [settings, setSettings] = useState<PrintSettings>({
    pageSize: 'A4',
    orientation: 'portrait',
    marginTop: 10,
    marginRight: 10,
    marginBottom: 10,
    marginLeft: 10,
    headerHeight: 30,
    footerHeight: 20,
    showLogo: true,
    logoPosition: 'left',
    showBranch: true,
    showQRCode: true,
    showBarcode: true,
    fontSize: 12,
    fontFamily: 'Arial',
    primaryColor: '#3895e7',
    secondaryColor: '#f3f4f6',
    footerText: 'Thank you for your business!',
    termsText: 'Terms and conditions apply.'
  });

  // Logo preview state
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  // Templates state
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  // Preview mode state
  const [previewMode, setPreviewMode] = useState(false);

  // Fetch settings and templates on component mount
  useEffect(() => {
    async function fetchData() {
      try {
        setIsInitialLoading(true);

        // Fetch print settings
        const settingsResponse = await axios.get('/api/print-settings');
        const settingsData = settingsResponse.data;

        // Map API response to component state
        setSettings({
          id: settingsData.id,
          pageSize: settingsData.pageSize,
          orientation: settingsData.orientation,
          marginTop: settingsData.marginTop,
          marginRight: settingsData.marginRight,
          marginBottom: settingsData.marginBottom,
          marginLeft: settingsData.marginLeft,
          headerHeight: settingsData.headerHeight,
          footerHeight: settingsData.footerHeight,
          showLogo: settingsData.showLogo,
          logoPosition: settingsData.logoPosition,
          showBranch: settingsData.showBranch,
          showQRCode: settingsData.showQRCode,
          showBarcode: settingsData.showBarcode,
          fontSize: settingsData.fontSize,
          fontFamily: settingsData.fontFamily,
          primaryColor: settingsData.primaryColor,
          secondaryColor: settingsData.secondaryColor,
          footerText: settingsData.footerText,
          termsText: settingsData.termsText,
          customWidth: settingsData.customWidth,
          customHeight: settingsData.customHeight,
          companyLogo: settingsData.companyLogo,
          defaultTemplateSettings: settingsData.defaultTemplateSettings,
          thermalPrinterSettings: settingsData.thermalPrinterSettings,
          signatureSettings: settingsData.signatureSettings,
          stampSettings: settingsData.stampSettings,
          multiLanguageSettings: settingsData.multiLanguageSettings,
          branchId: settingsData.branchId
        });

        // Set logo preview if available
        if (settingsData.companyLogo) {
          setLogoPreview(settingsData.companyLogo);
        }

        // Fetch templates
        const templatesResponse = await axios.get('/api/print-templates');
        const templatesData = templatesResponse.data;

        if (templatesData.length > 0) {
          setTemplates(templatesData);

          // Find default template for the active tab
          const defaultTemplate = templatesData.find((t: Template) => t.isDefault && t.type === 'invoice');
          if (defaultTemplate) {
            setSelectedTemplate(defaultTemplate);
          } else {
            setSelectedTemplate(templatesData[0]);
          }
        }

        setIsInitialLoading(false);
      } catch (err: any) {
        setError('Failed to load settings: ' + err.message);
        setIsInitialLoading(false);
      }
    }

    fetchData();
  }, []);

  // Handle logo upload
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64String = reader.result as string;
          setLogoPreview(base64String);
        };
        reader.readAsDataURL(file);
      } catch (err: any) {
        setError('Failed to upload logo: ' + err.message);
      }
    }
  };

  // Template functions
  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId) || null;
    setSelectedTemplate(template);
  };

  const handleTemplateNameChange = (name: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, name});
    }
  };

  const handleTemplateTypeChange = (type: TemplateType) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, type});
    }
  };

  const handleTemplateContentChange = (content: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, content});
    }
  };

  const handleTemplateDefaultChange = (isDefault: boolean) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, isDefault});
    }
  };

  const handleTemplatePaperSizeChange = (paperSize: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, paperSize});
    }
  };

  const handleTemplateOrientationChange = (orientation: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, orientation});
    }
  };

  const createNewTemplate = () => {
    const newTemplate: Template = {
      id: 'temp_' + Date.now().toString(),
      name: 'New Template',
      type: 'invoice',
      content: '<h1>{{company_name}}</h1><p>New Template</p>',
      isDefault: false,
      language: 'ar',
      paperSize: 'A4',
      orientation: 'portrait'
    };
    setTemplates([...templates, newTemplate]);
    setSelectedTemplate(newTemplate);
  };

  const saveTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      setIsLoading(true);

      // Check if this is a new template (temporary ID) or existing one
      const isNewTemplate = selectedTemplate.id.startsWith('temp_');

      const response = isNewTemplate
        ? await axios.post('/api/print-templates', {
            name: selectedTemplate.name,
            type: selectedTemplate.type,
            content: selectedTemplate.content,
            isDefault: selectedTemplate.isDefault,
            language: selectedTemplate.language,
            paperSize: selectedTemplate.paperSize,
            orientation: selectedTemplate.orientation
          })
        : await axios.put(`/api/print-templates/${selectedTemplate.id}`, {
            name: selectedTemplate.name,
            type: selectedTemplate.type,
            content: selectedTemplate.content,
            isDefault: selectedTemplate.isDefault,
            language: selectedTemplate.language,
            paperSize: selectedTemplate.paperSize,
            orientation: selectedTemplate.orientation
          });

      const savedTemplate = response.data;

      // Update templates list
      setTemplates(templates.map(t =>
        t.id === selectedTemplate.id ? savedTemplate : t
      ));

      // Update selected template
      setSelectedTemplate(savedTemplate);

      setSuccess('Template saved successfully');
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      setIsLoading(false);
    } catch (err: any) {
      setError('Failed to save template: ' + err.message);
      setIsLoading(false);
    }
  };

  const deleteTemplate = async (templateId: string) => {
    try {
      // Only call API if it's not a temporary template
      if (!templateId.startsWith('temp_')) {
        await axios.delete(`/api/print-templates/${templateId}`);
      }

      // Update templates list
      setTemplates(templates.filter(t => t.id !== templateId));

      // Update selected template if needed
      if (selectedTemplate && selectedTemplate.id === templateId) {
        setSelectedTemplate(templates.length > 1 ? templates[0] : null);
      }

      setSuccess('Template deleted successfully');
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      setError('Failed to delete template: ' + err.message);
    }
  };

  // Save settings function
  const saveSettings = async () => {
    try {
      setIsLoading(true);

      // Prepare data for API
      const settingsData = {
        pageSize: settings.pageSize,
        orientation: settings.orientation,
        marginTop: settings.marginTop,
        marginRight: settings.marginRight,
        marginBottom: settings.marginBottom,
        marginLeft: settings.marginLeft,
        headerHeight: settings.headerHeight,
        footerHeight: settings.footerHeight,
        showLogo: settings.showLogo,
        logoPosition: settings.logoPosition,
        showBranch: settings.showBranch,
        showQRCode: settings.showQRCode,
        showBarcode: settings.showBarcode,
        fontSize: settings.fontSize,
        fontFamily: settings.fontFamily,
        primaryColor: settings.primaryColor,
        secondaryColor: settings.secondaryColor,
        footerText: settings.footerText,
        termsText: settings.termsText,
        customWidth: settings.customWidth,
        customHeight: settings.customHeight,
        companyLogo: logoPreview,
        defaultTemplateSettings: settings.defaultTemplateSettings,
        thermalPrinterSettings: settings.thermalPrinterSettings,
        signatureSettings: settings.signatureSettings,
        stampSettings: settings.stampSettings,
        multiLanguageSettings: settings.multiLanguageSettings,
        branchId: settings.branchId
      };

      // Save settings to API
      await axios.post('/api/print-settings', settingsData);

      setSuccess('Settings saved successfully');
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      setIsLoading(false);
    } catch (err: any) {
      setError('Failed to save settings: ' + err.message);
      setIsLoading(false);
    }
  };

  // Generate preview HTML
  const generatePreviewHTML = () => {
    if (!selectedTemplate) return '';

    // Replace variables with sample data
    let html = selectedTemplate.content;
    html = html.replace(/{{company_name}}/g, 'VERO Company');
    html = html.replace(/{{date}}/g, new Date().toLocaleDateString());
    html = html.replace(/{{invoice_number}}/g, 'INV-12345');
    html = html.replace(/{{receipt_number}}/g, 'REC-12345');
    html = html.replace(/{{po_number}}/g, 'PO-12345');
    html = html.replace(/{{delivery_number}}/g, 'DEL-12345');
    html = html.replace(/{{customer_details}}/g, 'Customer Name<br>123 Street<br>City, Country');
    html = html.replace(/{{supplier_details}}/g, 'Supplier Name<br>456 Avenue<br>City, Country');

    // Sample items table
    const itemsTable = `
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: ${settings.secondaryColor};">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Item</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Qty</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Price</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Product 1</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">2</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$50.00</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$100.00</td>
          </tr>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Product 2</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">1</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$75.00</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$75.00</td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Subtotal:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$175.00</td>
          </tr>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Tax:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$17.50</td>
          </tr>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Total:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$192.50</td>
          </tr>
        </tfoot>
      </table>
    `;

    html = html.replace(/{{items}}/g, itemsTable);
    html = html.replace(/{{invoice_items}}/g, itemsTable);
    html = html.replace(/{{invoice_items_detailed}}/g, itemsTable);
    html = html.replace(/{{subtotal}}/g, '$175.00');
    html = html.replace(/{{tax}}/g, '$17.50');
    html = html.replace(/{{total}}/g, '$192.50');
    html = html.replace(/{{barcode}}/g, '[Barcode: 12345678]');
    html = html.replace(/{{period}}/g, 'Jan 1, 2023 - Jan 31, 2023');

    // Sample sales data
    const salesData = `
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: ${settings.secondaryColor};">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Date</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Sales</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Jan 1, 2023</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$1,250.00</td>
          </tr>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Jan 2, 2023</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$1,875.00</td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Total:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$3,125.00</td>
          </tr>
        </tfoot>
      </table>
    `;

    html = html.replace(/{{sales_data}}/g, salesData);
    html = html.replace(/{{total_sales}}/g, '$3,125.00');

    return html;
  };

  // Render loading state
  if (isInitialLoading) {
    return (
      <div className="bg-white shadow-md rounded-lg border border-gray-200 w-full p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-6"></div>
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="h-10 bg-gray-200 rounded"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
          <div className="h-40 bg-gray-200 rounded mb-6"></div>
          <div className="h-10 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg border border-gray-200 w-full">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Print Settings</h3>
        <p className="text-sm text-gray-500">
          Customize your printing templates and settings
        </p>
      </div>

      <div className="p-6">
        {error && (
          <div className="mb-4 p-4 border border-red-200 bg-red-50 rounded-md text-red-800">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <h4 className="font-medium">Error</h4>
            </div>
            <p className="mt-1 ml-7 text-sm">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 border border-green-200 bg-green-50 rounded-md text-green-800">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <h4 className="font-medium">Success</h4>
            </div>
            <p className="mt-1 ml-7 text-sm">{success}</p>
          </div>
        )}

        <div className="w-full">
          <div className="mb-4 overflow-x-auto border-b border-gray-200">
            <div className="inline-flex w-full md:w-auto">
              <button
                onClick={() => setActiveTab("general")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "general" ? "border-b-2 border-indigo-500 text-indigo-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                General Settings
              </button>
              <button
                onClick={() => setActiveTab("templates")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "templates" ? "border-b-2 border-indigo-500 text-indigo-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                Templates
              </button>
              <button
                onClick={() => setActiveTab("preview")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "preview" ? "border-b-2 border-indigo-500 text-indigo-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                Preview
              </button>
              <button
                onClick={() => setActiveTab("advanced")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "advanced" ? "border-b-2 border-indigo-500 text-indigo-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                Advanced
              </button>
            </div>
          </div>

          {/* Tab content goes here - similar to the original component but with updated API calls */}
          {/* ... */}
        </div>
      </div>

      <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
        <button
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          onClick={() => {
            // Reset to defaults
            setSettings({
              pageSize: 'A4',
              orientation: 'portrait',
              marginTop: 10,
              marginRight: 10,
              marginBottom: 10,
              marginLeft: 10,
              headerHeight: 30,
              footerHeight: 20,
              showLogo: true,
              logoPosition: 'left',
              showBranch: true,
              showQRCode: true,
              showBarcode: true,
              fontSize: 12,
              fontFamily: 'Arial',
              primaryColor: '#3895e7',
              secondaryColor: '#f3f4f6',
              footerText: 'Thank you for your business!',
              termsText: 'Terms and conditions apply.'
            });
            setSuccess('Settings reset to defaults');
            setTimeout(() => {
              setSuccess(null);
            }, 3000);
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Reset to Defaults
        </button>

        <button
          onClick={saveSettings}
          disabled={isLoading}
          className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Save Settings
            </>
          )}
        </button>
      </div>
    </div>
  );