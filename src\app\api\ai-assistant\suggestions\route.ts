import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { generateContextualSuggestions } from "@/lib/ai-assistant";

// GET /api/ai-assistant/suggestions - Get contextual suggestions based on current path
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get the current path from query params
    const url = new URL(req.url);
    const path = url.searchParams.get("path") || "";
    
    // Check user settings to see if auto-suggestions are enabled
    const userSettings = await prisma.aIAssistantSettings.findUnique({
      where: {
        userId: session.user.id,
      },
    });
    
    // If auto-suggestions are turned off, return empty array
    if (userSettings && !userSettings.autoSuggest) {
      return NextResponse.json({ suggestions: [] });
    }
    
    // Generate contextual suggestions based on the current path
    const suggestions = await generateContextualSuggestions(path, session.user.id);
    
    return NextResponse.json({ suggestions });
  } catch (error) {
    console.error("Error fetching suggestions:", error);
    return NextResponse.json(
      { error: "Failed to fetch suggestions" },
      { status: 500 }
    );
  }
}
