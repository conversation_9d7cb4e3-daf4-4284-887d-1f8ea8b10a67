const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearAccountingData() {
  console.log('Iniciando eliminación de datos contables...');

  try {
    // Desactivar restricciones de clave foránea temporalmente
    await prisma.$executeRaw`SET session_replication_role = 'replica'`;

    console.log('Eliminando datos de ReconciliationItem...');
    await prisma.reconciliationItem.deleteMany({});

    console.log('Eliminando datos de AccountReconciliation...');
    await prisma.accountReconciliation.deleteMany({});

    console.log('Eliminando datos de FinancialReport...');
    await prisma.financialReport.deleteMany({});

    console.log('Eliminando datos de GeneralLedgerEntry...');
    await prisma.generalLedgerEntry.deleteMany({});

    console.log('Eliminando datos de JournalEntry...');
    await prisma.journalEntry.deleteMany({});

    console.log('Eliminando datos de Journal...');
    await prisma.journal.deleteMany({});

    console.log('Eliminando datos de Transaction...');
    await prisma.transaction.deleteMany({});

    console.log('Eliminando datos de FiscalPeriod...');
    await prisma.fiscalPeriod.deleteMany({});

    console.log('Eliminando datos de FiscalYear...');
    await prisma.fiscalYear.deleteMany({});

    console.log('Eliminando datos de AccountingSettings...');
    await prisma.accountingSettings.deleteMany({});

    console.log('Eliminando datos de Account...');
    await prisma.account.deleteMany({});

    // Restaurar restricciones de clave foránea
    await prisma.$executeRaw`SET session_replication_role = 'origin'`;

    console.log('Todos los datos contables han sido eliminados correctamente.');
  } catch (error) {
    console.error('Error al eliminar datos contables:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearAccountingData();
