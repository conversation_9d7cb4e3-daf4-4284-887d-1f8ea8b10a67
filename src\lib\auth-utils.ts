import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

/**
 * Check if the current user is an admin
 */
export async function isAdmin(req?: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return false;
    }
    
    // Get user from database
    const user = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
    });
    
    // Check if user has admin role
    return user?.role === "ADMIN";
  } catch (error) {
    console.error("Error checking admin status:", error);
    return false;
  }
}
