import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import fs from "fs";
import path from "path";

// Function to check if user is admin
async function isAdmin(req: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return false;
  }
  
  // Check if user has admin role
  return session.user.role === "ADMIN";
}

// GET /api/system/database/download - Download a backup file
export async function GET(req: NextRequest) {
  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }

    // Get filename from query parameters
    const url = new URL(req.url);
    const filename = url.searchParams.get("filename");
    
    if (!filename) {
      return NextResponse.json(
        { error: "Filename is required" },
        { status: 400 }
      );
    }
    
    // Validate filename to prevent directory traversal
    if (filename.includes("..") || filename.includes("/") || filename.includes("\\")) {
      return NextResponse.json(
        { error: "Invalid filename" },
        { status: 400 }
      );
    }
    
    const backupFile = path.join(process.cwd(), "backups", filename);
    
    // Check if file exists
    if (!fs.existsSync(backupFile)) {
      return NextResponse.json(
        { error: "Backup file not found" },
        { status: 404 }
      );
    }
    
    // Read file
    const fileBuffer = fs.readFileSync(backupFile);
    
    // Determine content type based on file extension
    let contentType = "application/octet-stream";
    if (filename.endsWith(".sql")) {
      contentType = "application/sql";
    } else if (filename.endsWith(".gz")) {
      contentType = "application/gzip";
    } else if (filename.endsWith(".zip")) {
      contentType = "application/zip";
    }
    
    // Create response with file content
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
    
    return response;
  } catch (error) {
    console.error("Error in database download GET:", error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
