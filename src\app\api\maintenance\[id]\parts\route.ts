import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/maintenance/[id]/parts - Get parts for a maintenance service
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view maintenance services
    const canViewMaintenance = await hasPermission("view_maintenance") || session.user.role === "ADMIN";

    if (!canViewMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to view maintenance parts" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Get parts for the maintenance service
    const parts = await db.maintenancePart.findMany({
      where: { maintenanceServiceId: id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            basePrice: true,
            costPrice: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(parts);
  } catch (error) {
    console.error("Error fetching maintenance parts:", error);
    return NextResponse.json(
      { error: "Failed to fetch maintenance parts" },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/[id]/parts - Add a part to a maintenance service
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit maintenance services
    const canEditMaintenance = await hasPermission("edit_maintenance") || session.user.role === "ADMIN";

    if (!canEditMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to add maintenance parts" },
        { status: 403 }
      );
    }

    const id = params.id;
    const data = await req.json();

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!data.partName) {
      return NextResponse.json(
        { error: "Part name is required" },
        { status: 400 }
      );
    }

    // If using a product from inventory, check if it exists
    let product = null;
    if (data.isFromInventory && data.productId) {
      product = await db.product.findUnique({
        where: { id: data.productId },
      });

      if (!product) {
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        );
      }
    }

    // Calculate total price
    const quantity = data.quantity || 1;
    const unitPrice = data.unitPrice || (product ? product.basePrice : 0);
    const totalPrice = quantity * unitPrice;

    // Create the maintenance part
    const part = await db.maintenancePart.create({
      data: {
        maintenanceServiceId: id,
        productId: data.isFromInventory ? data.productId : null,
        partName: data.partName,
        quantity: quantity,
        unitPrice: unitPrice,
        totalPrice: totalPrice,
        isFromInventory: data.isFromInventory || false,
        // warehouseId field is not available in the schema yet
      },
    });

    // If using inventory, update the inventory quantity
    if (data.isFromInventory && data.productId && data.warehouseId) {
      // Check if inventory exists for this product in the warehouse
      const inventory = await db.inventory.findFirst({
        where: {
          productId: data.productId,
          warehouseId: data.warehouseId,
        },
      });

      if (inventory) {
        // Update inventory quantity
        await db.inventory.update({
          where: { id: inventory.id },
          data: {
            quantity: {
              decrement: quantity,
            },
          },
        });
      }
    }

    // Update the estimated cost of the maintenance service
    const allParts = await db.maintenancePart.findMany({
      where: { maintenanceServiceId: id },
    });

    const totalCost = allParts.reduce((sum, part) => sum + part.totalPrice, 0);

    await db.maintenanceService.update({
      where: { id },
      data: {
        estimatedCost: totalCost,
      },
    });

    return NextResponse.json(part);
  } catch (error) {
    console.error("Error adding maintenance part:", error);
    return NextResponse.json(
      { error: "Failed to add maintenance part" },
      { status: 500 }
    );
  }
}
