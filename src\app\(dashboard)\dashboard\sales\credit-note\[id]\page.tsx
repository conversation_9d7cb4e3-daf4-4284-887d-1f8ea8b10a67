"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";

export default function CreditNoteDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const creditNoteId = params.id as string;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [creditNote, setCreditNote] = useState<any>(null);

  // Fetch credit note data from API
  useEffect(() => {
    const fetchCreditNote = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/credit-notes/${creditNoteId}`);

        if (response.ok) {
          const data = await response.json();
          setCreditNote(data);
          setError(null);
        } else {
          const errorData = await response.json();
          setError(errorData.error || "Failed to fetch credit note");
          setCreditNote(null);
        }
      } catch (err: any) {
        console.error("Error fetching credit note:", err);
        setError(err.message || "An error occurred while fetching the credit note");
        setCreditNote(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCreditNote();
  }, [creditNoteId]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-700">Loading credit note data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !creditNote) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6">
        <h2 className="text-xl font-semibold text-gray-900">Credit Note not found</h2>
        <p className="mt-2 text-gray-600">{error || `The credit note with ID ${creditNoteId} could not be found.`}</p>
        <div className="mt-4">
          <Link
            href="/dashboard/sales/credit-note"
            className="text-indigo-600 hover:text-indigo-900"
          >
            Back to Credit Notes
          </Link>
        </div>
      </div>
    );
  }

  // Format date
  const formattedDate = new Date(creditNote.date).toLocaleDateString();

  return (
    <div className="space-y-6">
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Credit Note #{creditNote.creditNoteNumber}
          </h2>
          <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
            <div className="mt-2 flex items-center text-sm text-gray-700">
              <span className="mr-1.5 font-medium">Status:</span>
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  creditNote.status === "COMPLETED"
                    ? "bg-green-100 text-green-800"
                    : creditNote.status === "PENDING"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {creditNote.status}
              </span>
            </div>
            <div className="mt-2 flex items-center text-sm text-gray-700">
              <span className="mr-1.5 font-medium">Payment Status:</span>
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  creditNote.paymentStatus === "PAID"
                    ? "bg-green-100 text-green-800"
                    : creditNote.paymentStatus === "PARTIALLY_PAID"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {creditNote.paymentStatus}
              </span>
            </div>
            <div className="mt-2 flex items-center text-sm text-gray-700">
              <span className="mr-1.5 font-medium">Date:</span>
              <span>{formattedDate}</span>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Link
            href="/dashboard/sales/credit-note"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Credit Notes
          </Link>
          <button
            type="button"
            onClick={() => window.print()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Print
          </button>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Customer Information
            </h3>
            <div className="mt-2 text-sm text-gray-700">
              <p className="font-medium text-gray-900">{creditNote.contact?.name || "Unknown Customer"}</p>
              <p>{creditNote.contact?.phone || "No phone"}</p>
              <p>{creditNote.contact?.address || "No address"}</p>
            </div>
          </div>
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Credit Note Information
            </h3>
            <div className="mt-2 text-sm text-gray-700">
              <p><span className="font-medium">Branch:</span> {creditNote.branch?.name || "Unknown Branch"}</p>
              <p><span className="font-medium">Created By:</span> {creditNote.user?.name || "Unknown User"}</p>
              {creditNote.originalInvoiceNumber && (
                <p><span className="font-medium">Original Invoice:</span> #{creditNote.originalInvoiceNumber}</p>
              )}
              {creditNote.notes && (
                <p><span className="font-medium">Notes:</span> {creditNote.notes}</p>
              )}
            </div>
          </div>

          {/* Payment Methods */}
          {creditNote.payments && creditNote.payments.length > 0 && (
            <div className="mt-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Payment Methods
              </h3>
              <div className="mt-2 overflow-hidden border border-gray-200 rounded-md">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Method
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {creditNote.payments.map((payment: any) => (
                      <tr key={payment.id}>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                          {payment.method === 'CASH' && 'Cash'}
                          {payment.method === 'VODAFONE_CASH' && 'Vodafone Cash'}
                          {payment.method === 'BANK_TRANSFER' && 'Bank Transfer'}
                          {payment.method === 'VISA' && 'Visa'}
                          {payment.method === 'CUSTOMER_ACCOUNT' && 'Customer Account'}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                          {payment.amount.toFixed(2)} ج.م
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>

        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Items
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Warehouse
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Reason
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Unit Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {creditNote.items.map((item: any) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.product?.name || "Unknown Product"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.warehouse?.name || "Unknown Warehouse"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.reason || "No reason provided"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.unitPrice?.toFixed(2) || "0.00"} ج.م
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.totalPrice?.toFixed(2) || (item.unitPrice * item.quantity).toFixed(2) || "0.00"} ج.م
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div className="sm:flex sm:justify-end">
            <div className="mt-4 sm:mt-0 sm:w-1/3">
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="flex justify-between py-2 text-sm">
                  <span className="font-medium text-gray-700">Subtotal:</span>
                  <span className="text-gray-900">{creditNote.subtotalAmount?.toFixed(2) || "0.00"} ج.م</span>
                </div>
                {creditNote.taxAmount > 0 && (
                  <div className="flex justify-between py-2 text-sm">
                    <span className="font-medium text-gray-700">Tax:</span>
                    <span className="text-gray-900">{creditNote.taxAmount?.toFixed(2) || "0.00"} ج.م</span>
                  </div>
                )}
                {creditNote.discountAmount > 0 && (
                  <div className="flex justify-between py-2 text-sm">
                    <span className="font-medium text-gray-700">Discount:</span>
                    <span className="text-gray-900">{creditNote.discountAmount?.toFixed(2) || "0.00"} ج.م</span>
                  </div>
                )}
                <div className="flex justify-between py-2 text-sm font-bold border-t border-gray-200 mt-2 pt-2">
                  <span className="text-gray-900">Total:</span>
                  <span className="text-gray-900">{creditNote.totalAmount?.toFixed(2) || "0.00"} ج.م</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
