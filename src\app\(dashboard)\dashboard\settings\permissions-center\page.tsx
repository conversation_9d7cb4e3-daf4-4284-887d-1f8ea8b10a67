"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import PermissionsManager from "../components/PermissionsManager";

export default function PermissionsCenterPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'system' | 'fix'>('users');
  const [isLoading, setIsLoading] = useState(false);
  const [permissionsStatus, setPermissionsStatus] = useState<any>(null);
  const [adminPermissionsStatus, setAdminPermissionsStatus] = useState<any>(null);
  const [isFixing, setIsFixing] = useState(false);
  const [isEnsuringAdmin, setIsEnsuringAdmin] = useState(false);

  // Fetch permissions status
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch current user permissions status
        const userResponse = await fetch("/api/system/fix-permissions");
        if (userResponse.ok) {
          const userData = await userResponse.json();
          setPermissionsStatus(userData);
        }

        // Fetch admin user permissions status
        const adminResponse = await fetch("/api/system/ensure-admin-permissions");
        if (adminResponse.ok) {
          const adminData = await adminResponse.json();
          setAdminPermissionsStatus(adminData);
        }
      } catch (error) {
        console.error("Error fetching permissions status:", error);
      }
    };

    fetchData();
  }, []);

  // Fix permissions
  const handleFixPermissions = async () => {
    setIsFixing(true);

    try {
      const response = await fetch("/api/system/fix-permissions", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message || "Permissions fixed successfully");

        // Refresh permissions status
        const statusResponse = await fetch("/api/system/fix-permissions");
        if (statusResponse.ok) {
          const statusData = await statusResponse.json();
          setPermissionsStatus(statusData);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to fix permissions");
      }
    } catch (error) {
      console.error("Error fixing permissions:", error);
      toast.error("An error occurred while fixing permissions");
    } finally {
      setIsFixing(false);
    }
  };

  // Ensure all system permissions
  const handleEnsurePermissions = async () => {
    if (confirm("Are you sure you want to ensure all system permissions? This will add any missing permissions.")) {
      setIsLoading(true);
      try {
        // First, try to fix permissions to ensure admin status
        await fetch("/api/system/fix-permissions", {
          method: "POST",
        });

        // Then ensure all permissions
        const response = await fetch('/api/permissions/ensure-all', {
          method: 'POST',
        });

        if (response.ok) {
          toast.success("All system permissions have been ensured!");

          // Refresh permissions status
          const statusResponse = await fetch("/api/system/fix-permissions");
          if (statusResponse.ok) {
            const statusData = await statusResponse.json();
            setPermissionsStatus(statusData);
          }

          router.refresh();
        } else {
          const data = await response.json();
          toast.error(data.error || "Failed to ensure permissions");
        }
      } catch (error) {
        console.error("Error ensuring permissions:", error);
        toast.error("Failed to ensure permissions");
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Restore admin user
  const handleRestoreAdmin = async () => {
    if (confirm("Are you sure you want to restore the admin user? This will create a new admin user if it doesn't exist or restore permissions if it does.")) {
      setIsLoading(true);
      try {
        const response = await fetch('/api/system/restore-admin', {
          method: 'POST',
        });

        if (response.ok) {
          toast.success("Admin user restored successfully!");

          // Refresh data after restoring admin
          const adminResponse = await fetch("/api/system/ensure-admin-permissions");
          if (adminResponse.ok) {
            const adminData = await adminResponse.json();
            setAdminPermissionsStatus(adminData);
          }

          router.refresh();
        } else {
          const data = await response.json();
          toast.error(data.error || "Failed to restore admin user");
        }
      } catch (error) {
        console.error("Error restoring admin user:", error);
        toast.error("Failed to restore admin user");
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Ensure admin permissions
  const handleEnsureAdminPermissions = async () => {
    if (confirm("Are you sure you want to ensure all permissions for the admin user? This will grant all system <NAME_EMAIL>.")) {
      setIsEnsuringAdmin(true);
      try {
        const response = await fetch('/api/system/ensure-admin-permissions', {
          method: 'POST',
        });

        if (response.ok) {
          const data = await response.json();
          setAdminPermissionsStatus(data);
          toast.success(`Admin user now has ${data.adminUser.permissionsCount} permissions!`);
        } else {
          const data = await response.json();
          toast.error(data.error || "Failed to ensure admin permissions");
        }
      } catch (error) {
        console.error("Error ensuring admin permissions:", error);
        toast.error("Failed to ensure admin permissions");
      } finally {
        setIsEnsuringAdmin(false);
      }
    }
  };

  return (
    <div className="p-6">
      <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-blue-500 to-teal-500 p-2 rounded-lg mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                  Permissions Center
                </h2>
                <p className="mt-1 text-sm text-gray-500">
                  Manage all permissions-related settings in one place
                </p>
              </div>
            </div>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <Link
              href="/dashboard/settings"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to Settings
            </Link>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('users')}
              className={`
                ${activeTab === 'users'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center
              `}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              User Permissions
            </button>
            <button
              onClick={() => setActiveTab('system')}
              className={`
                ${activeTab === 'system'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center
              `}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              System Permissions
            </button>
            <button
              onClick={() => setActiveTab('fix')}
              className={`
                ${activeTab === 'fix'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center
              `}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
              </svg>
              Fix Permissions
            </button>
          </nav>
        </div>

        <div className="px-4 py-5 sm:p-6">
          {activeTab === 'users' && (
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">User Permissions Management</h3>
              <PermissionsManager />
            </div>
          )}

          {activeTab === 'system' && (
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">System Permissions</h3>
              <div className="space-y-6">
                <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Ensure All Permissions</h3>
                    <div className="mt-2 max-w-xl text-sm text-gray-500">
                      <p>Ensure all required permissions exist in the system. This will add any missing permissions and assign them to admin users.</p>
                    </div>
                    <div className="mt-5">
                      <button
                        type="button"
                        onClick={handleEnsurePermissions}
                        disabled={isLoading}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-blue-500 to-teal-500 hover:from-blue-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        {isLoading ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                            Ensure All Permissions
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Restore Admin User</h3>
                    <div className="mt-2 max-w-xl text-sm text-gray-500">
                      <p>If the admin user has been deleted or is missing permissions, use this tool to restore it.</p>
                    </div>
                    <div className="mt-5">
                      <button
                        type="button"
                        onClick={handleRestoreAdmin}
                        disabled={isLoading}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                      >
                        {isLoading ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Restore Admin User
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 mt-6">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Admin User Permissions</h3>
                    <div className="mt-2 max-w-xl text-sm text-gray-500">
                      <p>Ensure the admin user (<EMAIL>) has all system permissions.</p>
                    </div>

                    {adminPermissionsStatus && (
                      <div className="mt-4 bg-gray-50 p-4 rounded-md">
                        <h4 className="text-md font-medium text-gray-900 mb-2">Admin User Status</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Name</p>
                            <p className="text-md font-medium">{adminPermissionsStatus.adminUser?.name || "Not found"}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Email</p>
                            <p className="text-md font-medium">{adminPermissionsStatus.adminUser?.email || "Not found"}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Role</p>
                            <p className="text-md font-medium">
                              {adminPermissionsStatus.adminUser?.role ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                  {adminPermissionsStatus.adminUser.role}
                                </span>
                              ) : "Not found"}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Permissions</p>
                            <p className="text-md font-medium">
                              {adminPermissionsStatus.permissions ? (
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  adminPermissionsStatus.permissions.assigned === adminPermissionsStatus.permissions.total
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {adminPermissionsStatus.permissions.assigned} / {adminPermissionsStatus.permissions.total}
                                </span>
                              ) : "Unknown"}
                            </p>
                          </div>
                        </div>

                        {adminPermissionsStatus.permissions && adminPermissionsStatus.permissions.missing > 0 && (
                          <div className="mt-3">
                            <p className="text-sm text-red-500">Missing {adminPermissionsStatus.permissions.missing} permissions</p>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="mt-5">
                      <button
                        type="button"
                        onClick={handleEnsureAdminPermissions}
                        disabled={isEnsuringAdmin}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                      >
                        {isEnsuringAdmin ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                            Ensure Admin Permissions
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'fix' && (
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Fix Permissions Issues</h3>

              {permissionsStatus && (
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Current User Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="text-md font-medium">{permissionsStatus.user.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="text-md font-medium">{permissionsStatus.user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Role</p>
                      <p className="text-md font-medium">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          permissionsStatus.user.role === 'ADMIN'
                            ? 'bg-purple-100 text-purple-800'
                            : permissionsStatus.user.role === 'MANAGER'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-green-100 text-green-800'
                        }`}>
                          {permissionsStatus.user.role}
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Permissions</p>
                      <p className="text-md font-medium">{permissionsStatus.permissions.assigned} / {permissionsStatus.permissions.total}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <button
                  onClick={handleFixPermissions}
                  disabled={isFixing}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-500 to-teal-500 hover:from-blue-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {isFixing ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Fixing Permissions...
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      Fix Current User Permissions
                    </>
                  )}
                </button>

                <Link
                  href="/dashboard/products"
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  Go to Products Page
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
