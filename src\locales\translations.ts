// Translations for the application
// This file contains all the translations for the application
// It will be used to support multiple languages in the future

export type Language = 'en' | 'ar';

export interface Translations {
  // Common
  appName: string;
  save: string;
  cancel: string;
  edit: string;
  delete: string;
  add: string;
  search: string;
  filter: string;
  actions: string;

  // Navigation
  dashboard: string;
  sales: string;
  purchases: string;
  inventory: string;
  customers: string;
  suppliers: string;
  contacts: string;
  reports: string;
  settings: string;

  // Sales
  newSale: string;
  saleDetails: string;
  customer: string;
  branch: string;
  date: string;
  invoiceNumber: string;
  product: string;
  warehouse: string;
  quantity: string;
  unitPrice: string;
  total: string;
  subtotal: string;
  tax: string;
  discount: string;
  notes: string;
  selectCustomer: string;
  selectBranch: string;
  selectProduct: string;
  selectWarehouse: string;

  // Product Customization
  customize: string;
  basePrice: string;
  costPrice: string;
  totalPrice: string;
  setCustomPrice: string;
  customPrice: string;
  selectedComponents: string;
  component: string;
  specification: string;
  price: string;
  confirm: string;

  // Components
  ram: string;
  ssd: string;
  hdd: string;
  nvme: string;
  gpu: string;
  selectRAM: string;
  selectSSD: string;
  selectHDD: string;
  selectNVMe: string;
  selectGPU: string;
}

// English translations
export const enTranslations: Translations = {
  // Common
  appName: 'VERO ERP',
  save: 'Save',
  cancel: 'Cancel',
  edit: 'Edit',
  delete: 'Delete',
  add: 'Add',
  search: 'Search',
  filter: 'Filter',
  actions: 'Actions',

  // Navigation
  dashboard: 'Dashboard',
  sales: 'Sales',
  purchases: 'Purchases',
  inventory: 'Inventory',
  customers: 'Customers',
  suppliers: 'Suppliers',
  contacts: 'Contacts',
  reports: 'Reports',
  settings: 'Settings',

  // Sales
  newSale: 'New Sale',
  saleDetails: 'Sale Details',
  customer: 'Customer',
  branch: 'Branch',
  date: 'Date',
  invoiceNumber: 'Invoice Number',
  product: 'Product',
  warehouse: 'Warehouse',
  quantity: 'Quantity',
  unitPrice: 'Unit Price',
  total: 'Total',
  subtotal: 'Subtotal',
  tax: 'Tax',
  discount: 'Discount',
  notes: 'Notes',
  selectCustomer: 'Select Customer',
  selectBranch: 'Select Branch',
  selectProduct: 'Select Product',
  selectWarehouse: 'Select Warehouse',

  // Product Customization
  customize: 'Customize',
  basePrice: 'Base Price',
  costPrice: 'Cost Price',
  totalPrice: 'Total Price',
  setCustomPrice: 'Set custom price',
  customPrice: 'Custom Price',
  selectedComponents: 'Selected Components',
  component: 'Component',
  specification: 'Specification',
  price: 'Price',
  confirm: 'Confirm',

  // Components
  ram: 'RAM',
  ssd: 'SATA SSD',
  hdd: 'HDD',
  nvme: 'NVMe SSD',
  gpu: 'Graphics Card',
  selectRAM: 'Select RAM',
  selectSSD: 'Select SATA SSD',
  selectHDD: 'Select HDD',
  selectNVMe: 'Select NVMe SSD',
  selectGPU: 'Select Graphics Card',
};

// Arabic translations
export const arTranslations: Translations = {
  // Common
  appName: 'فيرو لإدارة الموارد',
  save: 'حفظ',
  cancel: 'إلغاء',
  edit: 'تعديل',
  delete: 'حذف',
  add: 'إضافة',
  search: 'بحث',
  filter: 'تصفية',
  actions: 'إجراءات',

  // Navigation
  dashboard: 'لوحة التحكم',
  sales: 'المبيعات',
  purchases: 'المشتريات',
  inventory: 'المخزون',
  customers: 'العملاء',
  suppliers: 'الموردين',
  contacts: 'جهات الاتصال',
  reports: 'التقارير',
  settings: 'الإعدادات',

  // Sales
  newSale: 'مبيعات جديدة',
  saleDetails: 'تفاصيل المبيعات',
  customer: 'العميل',
  branch: 'الفرع',
  date: 'التاريخ',
  invoiceNumber: 'رقم الفاتورة',
  product: 'المنتج',
  warehouse: 'المستودع',
  quantity: 'الكمية',
  unitPrice: 'سعر الوحدة',
  total: 'الإجمالي',
  subtotal: 'المجموع الفرعي',
  tax: 'الضريبة',
  discount: 'الخصم',
  notes: 'ملاحظات',
  selectCustomer: 'اختر العميل',
  selectBranch: 'اختر الفرع',
  selectProduct: 'اختر المنتج',
  selectWarehouse: 'اختر المستودع',

  // Product Customization
  customize: 'تخصيص',
  basePrice: 'السعر الأساسي',
  costPrice: 'سعر التكلفة',
  totalPrice: 'السعر الإجمالي',
  setCustomPrice: 'تعيين سعر مخصص',
  customPrice: 'سعر مخصص',
  selectedComponents: 'المكونات المختارة',
  component: 'المكون',
  specification: 'المواصفات',
  price: 'السعر',
  confirm: 'تأكيد',

  // Components
  ram: 'الذاكرة',
  ssd: 'قرص SSD',
  hdd: 'القرص الصلب',
  nvme: 'قرص NVMe',
  gpu: 'كرت الشاشة',
  selectRAM: 'اختر الذاكرة',
  selectSSD: 'اختر قرص SSD',
  selectHDD: 'اختر القرص الصلب',
  selectNVMe: 'اختر قرص NVMe',
  selectGPU: 'اختر كرت الشاشة',
};

// Default language
export const defaultLanguage: Language = 'en';

// Get translations for a specific language
export function getTranslations(language: Language): Translations {
  return language === 'ar' ? arTranslations : enTranslations;
}

// Current translations (will be used throughout the application)
export const translations = getTranslations(defaultLanguage);
