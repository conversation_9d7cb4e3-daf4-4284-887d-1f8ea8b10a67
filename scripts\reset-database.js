// Este script reinicia la base de datos y crea datos iniciales coherentes
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Iniciando reinicio de la base de datos...');

    // Eliminar todos los datos existentes
    console.log('Eliminando datos existentes...');
    
    // Eliminar en orden para respetar las restricciones de clave foránea
    await prisma.creditNotePayment.deleteMany({});
    await prisma.creditNoteItem.deleteMany({});
    await prisma.creditNote.deleteMany({});
    await prisma.saleItemComponent.deleteMany({});
    await prisma.saleItem.deleteMany({});
    await prisma.sale.deleteMany({});
    await prisma.purchaseItem.deleteMany({});
    await prisma.purchase.deleteMany({});
    await prisma.inventory.deleteMany({});
    await prisma.specification.deleteMany({});
    await prisma.product.deleteMany({});
    await prisma.category.deleteMany({});
    await prisma.userWarehouse.deleteMany({});
    await prisma.warehouse.deleteMany({});
    await prisma.contact.deleteMany({});
    await prisma.permission.deleteMany({});
    await prisma.user.deleteMany({});
    await prisma.branch.deleteMany({});

    console.log('Datos existentes eliminados correctamente.');

    // Crear datos iniciales
    console.log('Creando datos iniciales...');

    // 1. Crear sucursales (branches)
    console.log('Creando sucursales...');
    const mainBranch = await prisma.branch.create({
      data: {
        name: 'Main Branch',
        address: 'Cairo, Egypt',
        phone: '+201234567890',
        code: 'A',
        isActive: true,
      },
    });

    const secondBranch = await prisma.branch.create({
      data: {
        name: 'Alexandria Branch',
        address: 'Alexandria, Egypt',
        phone: '+201234567891',
        code: 'B',
        isActive: true,
      },
    });

    // 2. Crear permisos
    console.log('Creando permisos...');
    const permissions = [
      { name: 'view_dashboard', description: 'Permission to view dashboard' },
      { name: 'view_products', description: 'Permission to view products' },
      { name: 'add_products', description: 'Permission to add products' },
      { name: 'edit_products', description: 'Permission to edit products' },
      { name: 'delete_products', description: 'Permission to delete products' },
      { name: 'view_inventory', description: 'Permission to view inventory' },
      { name: 'add_inventory', description: 'Permission to add inventory' },
      { name: 'edit_inventory', description: 'Permission to edit inventory' },
      { name: 'view_customers', description: 'Permission to view customers' },
      { name: 'add_customers', description: 'Permission to add customers' },
      { name: 'edit_customers', description: 'Permission to edit customers' },
      { name: 'delete_customers', description: 'Permission to delete customers' },
      { name: 'view_suppliers', description: 'Permission to view suppliers' },
      { name: 'add_suppliers', description: 'Permission to add suppliers' },
      { name: 'edit_suppliers', description: 'Permission to edit suppliers' },
      { name: 'delete_suppliers', description: 'Permission to delete suppliers' },
      { name: 'view_sales', description: 'Permission to view sales' },
      { name: 'add_sales', description: 'Permission to add sales' },
      { name: 'edit_sales', description: 'Permission to edit sales' },
      { name: 'delete_sales', description: 'Permission to delete sales' },
      { name: 'view_purchases', description: 'Permission to view purchases' },
      { name: 'add_purchases', description: 'Permission to add purchases' },
      { name: 'edit_purchases', description: 'Permission to edit purchases' },
      { name: 'delete_purchases', description: 'Permission to delete purchases' },
      { name: 'view_warehouses', description: 'Permission to view warehouses' },
      { name: 'add_warehouses', description: 'Permission to add warehouses' },
      { name: 'edit_warehouses', description: 'Permission to edit warehouses' },
      { name: 'delete_warehouses', description: 'Permission to delete warehouses' },
      { name: 'view_branches', description: 'Permission to view branches' },
      { name: 'add_branches', description: 'Permission to add branches' },
      { name: 'edit_branches', description: 'Permission to edit branches' },
      { name: 'delete_branches', description: 'Permission to delete branches' },
      { name: 'view_users', description: 'Permission to view users' },
      { name: 'add_users', description: 'Permission to add users' },
      { name: 'edit_users', description: 'Permission to edit users' },
      { name: 'delete_users', description: 'Permission to delete users' },
      { name: 'view_roles', description: 'Permission to view roles' },
      { name: 'add_roles', description: 'Permission to add roles' },
      { name: 'edit_roles', description: 'Permission to edit roles' },
      { name: 'delete_roles', description: 'Permission to delete roles' },
      { name: 'view_settings', description: 'Permission to view settings' },
      { name: 'edit_settings', description: 'Permission to edit settings' },
      { name: 'view_finance', description: 'Permission to view finance' },
      { name: 'edit_finance', description: 'Permission to edit finance' },
    ];

    const createdPermissions = await Promise.all(
      permissions.map(permission => 
        prisma.permission.create({
          data: permission
        })
      )
    );

    // 3. Crear usuarios
    console.log('Creando usuarios...');
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const adminUser = await prisma.user.create({
      data: {
        name: 'Administrator',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        branchId: mainBranch.id,
        isActive: true,
        permissions: {
          connect: createdPermissions.map(p => ({ id: p.id }))
        }
      },
    });

    const managerUser = await prisma.user.create({
      data: {
        name: 'Branch Manager',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'MANAGER',
        branchId: secondBranch.id,
        isActive: true,
        permissions: {
          connect: createdPermissions.filter(p => !p.name.startsWith('delete_')).map(p => ({ id: p.id }))
        }
      },
    });

    // 4. Crear almacenes
    console.log('Creando almacenes...');
    const mainWarehouse = await prisma.warehouse.create({
      data: {
        name: 'Main Warehouse',
        branchId: mainBranch.id,
        isActive: true,
      },
    });

    const secondWarehouse = await prisma.warehouse.create({
      data: {
        name: 'Alexandria Warehouse',
        branchId: secondBranch.id,
        isActive: true,
      },
    });

    // 5. Asignar usuarios a almacenes
    await prisma.userWarehouse.create({
      data: {
        userId: adminUser.id,
        warehouseId: mainWarehouse.id,
      },
    });

    await prisma.userWarehouse.create({
      data: {
        userId: managerUser.id,
        warehouseId: secondWarehouse.id,
      },
    });

    // 6. Crear categorías
    console.log('Creando categorías...');
    const categories = [
      { name: 'Computers', description: 'Desktop computers and laptops' },
      { name: 'Components', description: 'Computer components like RAM, CPU, etc.' },
      { name: 'Peripherals', description: 'Keyboards, mice, monitors, etc.' },
      { name: 'Networking', description: 'Routers, switches, cables, etc.' },
      { name: 'Software', description: 'Operating systems, applications, etc.' },
    ];

    const createdCategories = await Promise.all(
      categories.map(category => 
        prisma.category.create({
          data: category
        })
      )
    );

    // 7. Crear productos
    console.log('Creando productos...');
    
    // Productos normales
    const products = [
      { 
        name: 'Dell XPS 15', 
        description: 'High-performance laptop with 15" display', 
        basePrice: 25000, 
        costPrice: 20000, 
        categoryId: createdCategories[0].id,
        isCustomizable: true
      },
      { 
        name: 'HP Pavilion', 
        description: 'Mid-range laptop for everyday use', 
        basePrice: 15000, 
        costPrice: 12000, 
        categoryId: createdCategories[0].id,
        isCustomizable: true
      },
      { 
        name: 'Lenovo ThinkPad', 
        description: 'Business laptop with excellent keyboard', 
        basePrice: 18000, 
        costPrice: 14000, 
        categoryId: createdCategories[0].id,
        isCustomizable: true
      },
      { 
        name: 'Logitech MX Master', 
        description: 'Wireless mouse with advanced features', 
        basePrice: 1500, 
        costPrice: 1000, 
        categoryId: createdCategories[2].id 
      },
      { 
        name: 'Mechanical Keyboard', 
        description: 'Mechanical keyboard with RGB lighting', 
        basePrice: 2000, 
        costPrice: 1500, 
        categoryId: createdCategories[2].id 
      },
    ];

    const createdProducts = await Promise.all(
      products.map(product => 
        prisma.product.create({
          data: product
        })
      )
    );

    // Componentes
    const components = [
      { 
        name: 'RAM 8GB DDR4', 
        description: '8GB DDR4 RAM module', 
        basePrice: 800, 
        costPrice: 600, 
        categoryId: createdCategories[1].id,
        isComponent: true,
        componentType: 'RAM'
      },
      { 
        name: 'RAM 16GB DDR4', 
        description: '16GB DDR4 RAM module', 
        basePrice: 1500, 
        costPrice: 1200, 
        categoryId: createdCategories[1].id,
        isComponent: true,
        componentType: 'RAM'
      },
      { 
        name: 'SSD 256GB', 
        description: '256GB Solid State Drive', 
        basePrice: 1000, 
        costPrice: 800, 
        categoryId: createdCategories[1].id,
        isComponent: true,
        componentType: 'STORAGE'
      },
      { 
        name: 'SSD 512GB', 
        description: '512GB Solid State Drive', 
        basePrice: 1800, 
        costPrice: 1400, 
        categoryId: createdCategories[1].id,
        isComponent: true,
        componentType: 'STORAGE'
      },
      { 
        name: 'HDD 1TB', 
        description: '1TB Hard Disk Drive', 
        basePrice: 900, 
        costPrice: 700, 
        categoryId: createdCategories[1].id,
        isComponent: true,
        componentType: 'STORAGE'
      },
    ];

    const createdComponents = await Promise.all(
      components.map(component => 
        prisma.product.create({
          data: component
        })
      )
    );

    // 8. Crear especificaciones para productos personalizables
    console.log('Creando especificaciones para productos...');
    
    // Especificaciones para Dell XPS 15
    await prisma.specification.create({
      data: {
        name: 'Processor',
        value: 'Intel Core i7',
        productId: createdProducts[0].id,
      },
    });

    await prisma.specification.create({
      data: {
        name: 'Graphics',
        value: 'NVIDIA GeForce RTX 3050',
        productId: createdProducts[0].id,
      },
    });

    // Especificaciones para HP Pavilion
    await prisma.specification.create({
      data: {
        name: 'Processor',
        value: 'Intel Core i5',
        productId: createdProducts[1].id,
      },
    });

    await prisma.specification.create({
      data: {
        name: 'Graphics',
        value: 'Intel Iris Xe',
        productId: createdProducts[1].id,
      },
    });

    // Especificaciones para Lenovo ThinkPad
    await prisma.specification.create({
      data: {
        name: 'Processor',
        value: 'AMD Ryzen 7',
        productId: createdProducts[2].id,
      },
    });

    await prisma.specification.create({
      data: {
        name: 'Graphics',
        value: 'AMD Radeon Graphics',
        productId: createdProducts[2].id,
      },
    });

    // 9. Crear inventario
    console.log('Creando inventario...');
    
    // Inventario para productos en el almacén principal
    for (const product of [...createdProducts, ...createdComponents]) {
      await prisma.inventory.create({
        data: {
          productId: product.id,
          warehouseId: mainWarehouse.id,
          quantity: Math.floor(Math.random() * 20) + 10, // 10-30 unidades
          costPrice: product.costPrice,
        },
      });
    }

    // Inventario para productos en el segundo almacén
    for (const product of [...createdProducts, ...createdComponents]) {
      await prisma.inventory.create({
        data: {
          productId: product.id,
          warehouseId: secondWarehouse.id,
          quantity: Math.floor(Math.random() * 10) + 5, // 5-15 unidades
          costPrice: product.costPrice,
        },
      });
    }

    // 10. Crear contactos (clientes y proveedores)
    console.log('Creando contactos...');
    
    // Clientes
    const customers = [
      { 
        name: 'Ahmed Mohamed', 
        phone: '+201012345678', 
        address: 'Cairo, Egypt', 
        isCustomer: true,
        creditLimit: 10000,
      },
      { 
        name: 'Sara Ahmed', 
        phone: '+201123456789', 
        address: 'Alexandria, Egypt', 
        isCustomer: true,
        creditLimit: 5000,
      },
      { 
        name: 'Mohamed Ali', 
        phone: '+201234567890', 
        address: 'Giza, Egypt', 
        isCustomer: true,
        creditLimit: 8000,
      },
    ];

    const createdCustomers = await Promise.all(
      customers.map(customer => 
        prisma.contact.create({
          data: customer
        })
      )
    );

    // Proveedores
    const suppliers = [
      { 
        name: 'Tech Supplies Inc.', 
        phone: '+201987654321', 
        address: 'Cairo, Egypt', 
        isSupplier: true,
      },
      { 
        name: 'Computer Parts Ltd.', 
        phone: '+201876543210', 
        address: 'Alexandria, Egypt', 
        isSupplier: true,
      },
      { 
        name: 'Global Electronics', 
        phone: '+201765432109', 
        address: 'Sharm El-Sheikh, Egypt', 
        isSupplier: true,
      },
    ];

    const createdSuppliers = await Promise.all(
      suppliers.map(supplier => 
        prisma.contact.create({
          data: supplier
        })
      )
    );

    // Contacto que es tanto cliente como proveedor
    const dualContact = await prisma.contact.create({
      data: {
        name: 'Dual Tech Solutions',
        phone: '+201654321098',
        address: 'Hurghada, Egypt',
        isCustomer: true,
        isSupplier: true,
        creditLimit: 15000,
      },
    });

    console.log('Base de datos reiniciada y datos iniciales creados correctamente.');
    console.log('Credenciales de administrador:');
    console.log('Email: <EMAIL>');
    console.log('Contraseña: admin123');

  } catch (error) {
    console.error('Error al reiniciar la base de datos:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
