import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// Define all permissions with descriptions
const defaultPermissions = [
  // Sales permissions
  { name: 'view_sales', description: 'View sales invoices and orders' },
  { name: 'create_sales', description: 'Create new sales invoices' },
  { name: 'edit_sales', description: 'Edit existing sales invoices' },
  { name: 'delete_sales', description: 'Delete sales invoices' },
  { name: 'view_credit_notes', description: 'View credit notes' },
  { name: 'create_credit_notes', description: 'Create new credit notes' },
  { name: 'edit_credit_notes', description: 'Edit existing credit notes' },
  { name: 'delete_credit_notes', description: 'Delete credit notes' },
  { name: 'print_sales', description: 'Print sales invoices and reports' },
  { name: 'apply_discount', description: 'Apply discounts to sales' },
  { name: 'modify_price', description: 'Modify product prices in sales' },

  // Purchase permissions
  { name: 'view_purchases', description: 'View purchase orders' },
  { name: 'create_purchases', description: 'Create new purchase orders' },
  { name: 'edit_purchases', description: 'Edit existing purchase orders' },
  { name: 'delete_purchases', description: 'Delete purchase orders' },
  { name: 'print_purchases', description: 'Print purchase orders and reports' },

  // Inventory permissions
  { name: 'view_inventory', description: 'View inventory and stock levels' },
  { name: 'create_inventory', description: 'Add new items to inventory' },
  { name: 'edit_inventory', description: 'Edit existing inventory items' },
  { name: 'delete_inventory', description: 'Delete inventory items' },
  { name: 'transfer_inventory', description: 'Transfer inventory between warehouses' },
  { name: 'view_products', description: 'View product catalog' },
  { name: 'create_products', description: 'Create new products' },
  { name: 'edit_products', description: 'Edit existing products' },
  { name: 'delete_products', description: 'Delete products' },

  // Contact permissions
  { name: 'view_contacts', description: 'View customers and suppliers' },
  { name: 'create_contacts', description: 'Create new customers and suppliers' },
  { name: 'edit_contacts', description: 'Edit existing customers and suppliers' },
  { name: 'delete_contacts', description: 'Delete customers and suppliers' },

  // Maintenance permissions
  { name: 'view_maintenance', description: 'View maintenance services' },
  { name: 'create_maintenance', description: 'Create new maintenance services' },
  { name: 'edit_maintenance', description: 'Edit existing maintenance services' },
  { name: 'delete_maintenance', description: 'Delete maintenance services' },
  { name: 'change_maintenance_status', description: 'Change status of maintenance services' },
  { name: 'add_maintenance_parts', description: 'Add parts to maintenance services' },
  { name: 'process_maintenance_payment', description: 'Process payments for maintenance services' },
  { name: 'print_maintenance', description: 'Print maintenance service documents' },
  { name: 'notify_customers', description: 'Send notifications to customers about maintenance' },

  // Accounting permissions
  { name: 'view_accounting', description: 'View accounting information' },
  { name: 'create_journal_entries', description: 'Create new journal entries' },
  { name: 'edit_journal_entries', description: 'Edit existing journal entries' },
  { name: 'delete_journal_entries', description: 'Delete journal entries' },
  { name: 'view_accounts', description: 'View chart of accounts' },
  { name: 'create_accounts', description: 'Create new accounts' },
  { name: 'edit_accounts', description: 'Edit existing accounts' },
  { name: 'delete_accounts', description: 'Delete accounts' },
  { name: 'view_financial_reports', description: 'View financial reports' },
  { name: 'print_financial_reports', description: 'Print financial reports' },

  // Payment permissions
  { name: 'process_payments', description: 'Process payments for sales and purchases' },
  { name: 'view_payment_methods', description: 'View payment methods' },
  { name: 'edit_payment_methods', description: 'Edit payment methods' },
  { name: 'view_payment_history', description: 'View payment history' },

  // User management permissions
  { name: 'view_users', description: 'View system users' },
  { name: 'create_users', description: 'Create new system users' },
  { name: 'edit_users', description: 'Edit existing system users' },
  { name: 'delete_users', description: 'Delete system users' },
  { name: 'assign_permissions', description: 'Assign permissions to users' },

  // Branch and warehouse permissions
  { name: 'view_branches', description: 'View branches' },
  { name: 'create_branches', description: 'Create new branches' },
  { name: 'edit_branches', description: 'Edit existing branches' },
  { name: 'delete_branches', description: 'Delete branches' },
  { name: 'view_warehouses', description: 'View warehouses' },
  { name: 'create_warehouses', description: 'Create new warehouses' },
  { name: 'edit_warehouses', description: 'Edit existing warehouses' },
  { name: 'delete_warehouses', description: 'Delete warehouses' },

  // System permissions
  { name: 'view_settings', description: 'View system settings' },
  { name: 'edit_settings', description: 'Edit system settings' },
  { name: 'backup_database', description: 'Create database backups' },
  { name: 'restore_database', description: 'Restore database from backups' },
  { name: 'view_logs', description: 'View system logs' },
  { name: 'clear_data', description: 'Clear system data' },
];

// POST /api/permissions/create-defaults - Create default permissions
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admins can create default permissions
    const isAdmin = await db.user.findFirst({
      where: {
        email: session.user?.email,
        role: "ADMIN",
      },
    });

    if (!isAdmin) {
      return NextResponse.json(
        { error: "Only administrators can create default permissions" },
        { status: 403 }
      );
    }

    // Create or update permissions
    const results = [];
    for (const permission of defaultPermissions) {
      const existingPermission = await db.permission.findFirst({
        where: { name: permission.name },
      });

      if (existingPermission) {
        // Update description
        const updated = await db.permission.update({
          where: { id: existingPermission.id },
          data: { description: permission.description },
        });
        results.push({ action: 'updated', permission: updated });
      } else {
        // Create new permission
        const created = await db.permission.create({
          data: permission,
        });
        results.push({ action: 'created', permission: created });
      }
    }

    return NextResponse.json({
      message: "Default permissions created successfully",
      results
    });
  } catch (error) {
    console.error("Error creating default permissions:", error);
    return NextResponse.json(
      { error: "Failed to create default permissions" },
      { status: 500 }
    );
  }
}
