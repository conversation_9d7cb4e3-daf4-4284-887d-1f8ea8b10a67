const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateReferenceType() {
  try {
    console.log('Starting reference type update...');

    // Add new values to the ReferenceType enum
    // Note: We need to execute each command separately
    const newValues = [
      'SALES_INVOICE',
      'PURCHASE_INVOICE',
      'SALES_RETURN',
      'PURCHASE_RETURN',
      'EXPENSE',
      'INCOME',
      'TRANSFER',
      'ADJUSTMENT',
      'MANUAL'
    ];

    for (const value of newValues) {
      try {
        await prisma.$executeRaw`ALTER TYPE "ReferenceType" ADD VALUE IF NOT EXISTS ${value}`;
        console.log(`Added ${value} to ReferenceType enum`);
      } catch (error) {
        console.error(`Error adding ${value} to ReferenceType enum:`, error.message);
      }
    }

    console.log('Reference type update completed successfully!');
  } catch (error) {
    console.error('Error updating reference type:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateReferenceType();
