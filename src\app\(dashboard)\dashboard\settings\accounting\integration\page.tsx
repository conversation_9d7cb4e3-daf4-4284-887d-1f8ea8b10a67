"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Loader2,
  RefreshCw,
  CheckCircle2,
  XCircle,
  AlertCircle,
  ArrowRight,
  Settings,
  Database,
  Link as LinkIcon,
  Unlink,
  Save
} from "lucide-react";
import Link from "next/link";

interface AccountingStatus {
  success: boolean;
  isInitialized: boolean;
  isFullySynchronized: boolean;
  counts: {
    accounts: number;
    journals: number;
    paymentMethods: number;
    paymentMethodsWithAccounts: number;
    paymentMethodsWithJournals: number;
    fiscalYears: number;
    fiscalPeriods: number;
    journalEntries: number;
    paymentVouchers: number;
    receiptVouchers: number;
  };
  settings: {
    defaultCurrency: string;
    defaultTaxRate: number;
    fiscalYearStart: string;
    fiscalYearEnd: string;
    autoCreateAccounts: boolean;
    requireApproval: boolean;
    allowNegativeInventory: boolean;
    integrateWithSales: boolean;
    integrateWithPurchases: boolean;
    integrateWithInventory: boolean;
    integrateWithContacts: boolean;
  };
}

export default function AccountingIntegrationPage() {
  const [status, setStatus] = useState<AccountingStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSynchronizing, setIsSynchronizing] = useState(false);
  const [activeTab, setActiveTab] = useState("status");
  const [settings, setSettings] = useState({
    integrateWithSales: true,
    integrateWithPurchases: true,
    integrateWithInventory: true,
    integrateWithContacts: true,
  });

  // Fetch accounting status
  const fetchStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/accounting/sync");
      if (response.ok) {
        const data = await response.json();
        setStatus(data);

        // Update settings state
        if (data.settings) {
          setSettings({
            integrateWithSales: data.settings.integrateWithSales,
            integrateWithPurchases: data.settings.integrateWithPurchases,
            integrateWithInventory: data.settings.integrateWithInventory,
            integrateWithContacts: data.settings.integrateWithContacts,
          });
        }
      } else {
        toast.error("Failed to fetch accounting status");
      }
    } catch (error) {
      console.error("Error fetching accounting status:", error);
      toast.error("Failed to fetch accounting status");
    } finally {
      setIsLoading(false);
    }
  };

  // Synchronize accounting module
  const synchronizeAccounting = async () => {
    setIsSynchronizing(true);
    try {
      const response = await fetch("/api/accounting/sync", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("Accounting module synchronized successfully");
        fetchStatus();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to synchronize accounting module");
      }
    } catch (error) {
      console.error("Error synchronizing accounting module:", error);
      toast.error("Failed to synchronize accounting module");
    } finally {
      setIsSynchronizing(false);
    }
  };

  // Save integration settings
  const saveSettings = async () => {
    try {
      const response = await fetch("/api/settings/accounting", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        toast.success("Integration settings saved successfully");
        fetchStatus();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to save integration settings");
      }
    } catch (error) {
      console.error("Error saving integration settings:", error);
      toast.error("Failed to save integration settings");
    }
  };

  // Handle setting change
  const handleSettingChange = (setting: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value,
    }));
  };

  // Fetch status on mount
  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Accounting Integration</h1>
        <Button onClick={fetchStatus} variant="outline" disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="status">Status</TabsTrigger>
          <TabsTrigger value="integration">Integration Settings</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>

        {/* Status Tab */}
        <TabsContent value="status" className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center p-6">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
              <span className="ml-2 text-gray-500">Loading accounting status...</span>
            </div>
          ) : status ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Accounting Module Status</CardTitle>
                  <CardDescription>Current status of the accounting module and its integration</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Accounting Module Initialized:</span>
                      <div className="flex items-center">
                        {status.isInitialized ? (
                          <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500 mr-2" />
                        )}
                        <span>{status.isInitialized ? "Yes" : "No"}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Fully Synchronized:</span>
                      <div className="flex items-center">
                        {status.isFullySynchronized ? (
                          <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
                        )}
                        <span>{status.isFullySynchronized ? "Yes" : "Partial"}</span>
                      </div>
                    </div>
                    <Separator />
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="font-medium mb-2">Accounts & Journals</h3>
                        <ul className="space-y-2 text-sm">
                          <li className="flex justify-between">
                            <span>Accounts:</span>
                            <span className="font-medium">{status.counts.accounts}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>Journals:</span>
                            <span className="font-medium">{status.counts.journals}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>Journal Entries:</span>
                            <span className="font-medium">{status.counts.journalEntries}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>Fiscal Years:</span>
                            <span className="font-medium">{status.counts.fiscalYears}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>Fiscal Periods:</span>
                            <span className="font-medium">{status.counts.fiscalPeriods}</span>
                          </li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="font-medium mb-2">Payment Methods & Vouchers</h3>
                        <ul className="space-y-2 text-sm">
                          <li className="flex justify-between">
                            <span>Payment Methods:</span>
                            <span className="font-medium">{status.counts.paymentMethods}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>With Accounts:</span>
                            <span className="font-medium">{status.counts.paymentMethodsWithAccounts} / {status.counts.paymentMethods}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>With Journals:</span>
                            <span className="font-medium">{status.counts.paymentMethodsWithJournals} / {status.counts.paymentMethods}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>Payment Vouchers:</span>
                            <span className="font-medium">{status.counts.paymentVouchers}</span>
                          </li>
                          <li className="flex justify-between">
                            <span>Receipt Vouchers:</span>
                            <span className="font-medium">{status.counts.receiptVouchers}</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button onClick={synchronizeAccounting} disabled={isSynchronizing}>
                    {isSynchronizing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Synchronizing...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Synchronize
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Integration Status</CardTitle>
                    <CardDescription>Status of integration with other modules</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Sales Integration:</span>
                        <div className="flex items-center">
                          {status.settings?.integrateWithSales ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          )}
                          <span>{status.settings?.integrateWithSales ? "Enabled" : "Disabled"}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Purchases Integration:</span>
                        <div className="flex items-center">
                          {status.settings?.integrateWithPurchases ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          )}
                          <span>{status.settings?.integrateWithPurchases ? "Enabled" : "Disabled"}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Inventory Integration:</span>
                        <div className="flex items-center">
                          {status.settings?.integrateWithInventory ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          )}
                          <span>{status.settings?.integrateWithInventory ? "Enabled" : "Disabled"}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">Contacts Integration:</span>
                        <div className="flex items-center">
                          {status.settings?.integrateWithContacts ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          )}
                          <span>{status.settings?.integrateWithContacts ? "Enabled" : "Disabled"}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Links</CardTitle>
                    <CardDescription>Navigate to related accounting pages</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Link href="/dashboard/accounting/payment-methods" className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                        <span>Payment Methods</span>
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                      <Link href="/dashboard/accounting/journals" className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                        <span>Journals</span>
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                      <Link href="/dashboard/accounting/payment-vouchers" className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                        <span>Payment Vouchers</span>
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                      <Link href="/dashboard/accounting/receipt-vouchers" className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                        <span>Receipt Vouchers</span>
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                      <Link href="/dashboard/settings/accounting" className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md">
                        <span>Accounting Settings</span>
                        <Settings className="h-4 w-4" />
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Failed to load accounting status</h3>
                  <p className="text-gray-500 mb-4">There was an error loading the accounting module status.</p>
                  <Button onClick={fetchStatus}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Integration Settings Tab */}
        <TabsContent value="integration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>Configure how accounting integrates with other modules</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="integrate-sales" className="text-base">Sales Integration</Label>
                    <p className="text-sm text-gray-500">Automatically create journal entries for sales transactions</p>
                  </div>
                  <Switch
                    id="integrate-sales"
                    checked={settings.integrateWithSales}
                    onCheckedChange={(checked) => handleSettingChange("integrateWithSales", checked)}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="integrate-purchases" className="text-base">Purchases Integration</Label>
                    <p className="text-sm text-gray-500">Automatically create journal entries for purchase transactions</p>
                  </div>
                  <Switch
                    id="integrate-purchases"
                    checked={settings.integrateWithPurchases}
                    onCheckedChange={(checked) => handleSettingChange("integrateWithPurchases", checked)}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="integrate-inventory" className="text-base">Inventory Integration</Label>
                    <p className="text-sm text-gray-500">Automatically create journal entries for inventory movements</p>
                  </div>
                  <Switch
                    id="integrate-inventory"
                    checked={settings.integrateWithInventory}
                    onCheckedChange={(checked) => handleSettingChange("integrateWithInventory", checked)}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="integrate-contacts" className="text-base">Contacts Integration</Label>
                    <p className="text-sm text-gray-500">Automatically update customer and supplier balances</p>
                  </div>
                  <Switch
                    id="integrate-contacts"
                    checked={settings.integrateWithContacts}
                    onCheckedChange={(checked) => handleSettingChange("integrateWithContacts", checked)}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={saveSettings}>
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Actions Tab */}
        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Accounting Actions</CardTitle>
              <CardDescription>Perform actions to manage the accounting module</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Synchronize Accounting</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-gray-500 mb-4">
                        Initialize and synchronize the accounting module with other modules. This will create missing accounts, journals, and link payment methods.
                      </p>
                      <Button onClick={synchronizeAccounting} disabled={isSynchronizing} className="w-full">
                        {isSynchronizing ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Synchronizing...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Synchronize Accounting
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Link Payment Methods</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-gray-500 mb-4">
                        Link payment methods to their corresponding accounts and journals. This ensures proper accounting for transactions.
                      </p>
                      <Link href="/dashboard/accounting/payment-methods">
                        <Button variant="outline" className="w-full">
                          <Link className="h-4 w-4 mr-2" />
                          Manage Payment Methods
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Manage Accounts</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-gray-500 mb-4">
                        View and manage accounting accounts. Create new accounts, edit existing ones, or view account balances.
                      </p>
                      <Link href="/dashboard/accounting/accounts">
                        <Button variant="outline" className="w-full">
                          <Database className="h-4 w-4 mr-2" />
                          Manage Accounts
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Accounting Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-gray-500 mb-4">
                        Configure general accounting settings such as fiscal year, default currency, and tax rates.
                      </p>
                      <Link href="/dashboard/settings/accounting">
                        <Button variant="outline" className="w-full">
                          <Settings className="h-4 w-4 mr-2" />
                          Accounting Settings
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
