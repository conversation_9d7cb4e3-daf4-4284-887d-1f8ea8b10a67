import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// POST /api/system/fix-permissions - Fix permissions for the current user
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    console.log("Fixing permissions for user:", session.user.email);
    console.log("User role:", session.user.role);

    // Get the current user
    const user = await db.user.findUnique({
      where: {
        email: session.user.email,
      },
      include: {
        permissions: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // If user is admin, ensure they have all permissions
    if (user.role === "ADMIN") {
      console.log("User is admin, ensuring all permissions");
      
      // Get all permissions
      const allPermissions = await db.permission.findMany();
      
      // Get user's current permissions
      const userPermissionIds = user.permissions.map(p => p.id);
      
      // Find permissions that the user doesn't have
      const missingPermissions = allPermissions.filter(
        p => !userPermissionIds.includes(p.id)
      );
      
      if (missingPermissions.length > 0) {
        console.log(`Adding ${missingPermissions.length} missing permissions to admin user`);
        
        // Connect missing permissions to user
        await db.user.update({
          where: {
            id: user.id,
          },
          data: {
            permissions: {
              connect: missingPermissions.map(p => ({ id: p.id })),
            },
          },
        });
        
        return NextResponse.json({
          success: true,
          message: `Added ${missingPermissions.length} missing permissions to admin user`,
          permissionsAdded: missingPermissions.map(p => p.name),
        });
      } else {
        console.log("Admin user already has all permissions");
        return NextResponse.json({
          success: true,
          message: "Admin user already has all permissions",
          permissionsAdded: [],
        });
      }
    }
    
    // For non-admin users, return their current permissions
    return NextResponse.json({
      success: true,
      message: "User is not an admin, no permissions added",
      currentPermissions: user.permissions.map(p => p.name),
    });
  } catch (error) {
    console.error("Error fixing permissions:", error);
    return NextResponse.json(
      { error: "Failed to fix permissions" },
      { status: 500 }
    );
  }
}

// GET /api/system/fix-permissions - Get current user permissions status
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the current user
    const user = await db.user.findUnique({
      where: {
        email: session.user.email,
      },
      include: {
        permissions: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Get all permissions
    const allPermissions = await db.permission.findMany();
    
    // Get user's current permissions
    const userPermissions = user.permissions;
    
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      permissions: {
        total: allPermissions.length,
        assigned: userPermissions.length,
        isAdmin: user.role === "ADMIN",
        list: userPermissions.map(p => p.name),
      },
    });
  } catch (error) {
    console.error("Error getting permissions status:", error);
    return NextResponse.json(
      { error: "Failed to get permissions status" },
      { status: 500 }
    );
  }
}
