{"name": "vero", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "next build", "start": "NODE_ENV=production node server.js", "start:windows": "set NODE_ENV=production && node server.js", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "ts-node --compiler-options {\\\"module\\\":\\\"CommonJS\\\"} prisma/seed.ts", "accounting:setup": "node scripts/accounting-migration.js", "admin:restore": "node scripts/restore-admin-user.js", "data:add-real": "node scripts/add-real-data.js", "permissions:add-dashboard": "node scripts/add-dashboard-permission.js", "permissions:ensure-all": "node scripts/ensure-all-permissions.js", "ai-assistant:setup": "node scripts/ai-assistant-migration.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "framer-motion": "^12.10.5", "html2canvas": "^1.4.1", "jsbarcode": "^3.11.6", "jspdf": "^3.0.1", "lucide-react": "^0.507.0", "next": "15.3.1", "next-auth": "^4.24.11", "prisma": "^6.7.0", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}