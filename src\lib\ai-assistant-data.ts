import { db } from "@/lib/db";

/**
 * Get customer information by name or phone
 */
export async function getCustomerInfo(query: string) {
  try {
    // Search for customer by name or phone
    const customer = await db.contact.findFirst({
      where: {
        OR: [
          { name: { contains: query, mode: "insensitive" } },
          { phone: { contains: query, mode: "insensitive" } },
        ],
      },
      include: {
        sales: {
          orderBy: {
            date: "desc",
          },
          take: 5,
        },
      },
    });

    if (!customer) {
      return { success: false, message: "لم يتم العثور على العميل" };
    }

    // Get customer balance
    const balance = await getCustomerBalance(customer.id);

    return {
      success: true,
      customer: {
        id: customer.id,
        name: customer.name,
        phone: customer.phone,
        address: customer.address,
        balance,
        recentSales: customer.sales.map(sale => ({
          id: sale.id,
          date: sale.date,
          total: sale.total,
          paymentStatus: sale.paymentStatus,
        })),
      },
    };
  } catch (error) {
    console.error("Error getting customer info:", error);
    return { success: false, message: "حدث خطأ أثناء البحث عن العميل" };
  }
}

/**
 * Get customer balance
 */
async function getCustomerBalance(customerId: string) {
  try {
    // Get total sales amount
    const salesResult = await db.sale.aggregate({
      where: {
        contactId: customerId,
      },
      _sum: {
        total: true,
      },
    });

    // Get total payments amount
    const paymentsResult = await db.receiptVoucher.aggregate({
      where: {
        contactId: customerId,
      },
      _sum: {
        amount: true,
      },
    });

    const totalSales = salesResult._sum.total || 0;
    const totalPayments = paymentsResult._sum.amount || 0;

    return totalSales - totalPayments;
  } catch (error) {
    console.error("Error calculating customer balance:", error);
    return 0;
  }
}

/**
 * Get product information by name or barcode
 */
export async function getProductInfo(query: string) {
  try {
    // Search for product by name or barcode
    const product = await db.product.findFirst({
      where: {
        OR: [
          { name: { contains: query, mode: "insensitive" } },
          { barcode: { contains: query, mode: "insensitive" } },
        ],
      },
      include: {
        category: true,
        inventory: {
          include: {
            warehouse: true,
          },
        },
      },
    });

    if (!product) {
      return { success: false, message: "لم يتم العثور على المنتج" };
    }

    // Calculate total inventory
    const totalInventory = product.inventory.reduce(
      (sum, item) => sum + item.quantity,
      0
    );

    return {
      success: true,
      product: {
        id: product.id,
        name: product.name,
        barcode: product.barcode,
        description: product.description,
        price: product.price,
        cost: product.cost,
        category: product.category?.name || "بدون تصنيف",
        totalInventory,
        inventoryByWarehouse: product.inventory.map(item => ({
          warehouse: item.warehouse.name,
          quantity: item.quantity,
        })),
      },
    };
  } catch (error) {
    console.error("Error getting product info:", error);
    return { success: false, message: "حدث خطأ أثناء البحث عن المنتج" };
  }
}

/**
 * Get sales summary for a specific period
 */
export async function getSalesSummary(period: "day" | "week" | "month" | "year") {
  try {
    let startDate = new Date();
    
    // Calculate start date based on period
    switch (period) {
      case "day":
        startDate.setHours(0, 0, 0, 0);
        break;
      case "week":
        startDate.setDate(startDate.getDate() - 7);
        break;
      case "month":
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case "year":
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }
    
    // Get sales data
    const sales = await db.sale.findMany({
      where: {
        date: {
          gte: startDate,
        },
      },
      include: {
        contact: true,
      },
    });
    
    // Calculate totals
    const totalSales = sales.length;
    const totalAmount = sales.reduce((sum, sale) => sum + sale.total, 0);
    const paidSales = sales.filter(sale => sale.paymentStatus === "PAID");
    const paidAmount = paidSales.reduce((sum, sale) => sum + sale.total, 0);
    const unpaidSales = sales.filter(sale => sale.paymentStatus === "UNPAID");
    const unpaidAmount = unpaidSales.reduce((sum, sale) => sum + sale.total, 0);
    
    return {
      success: true,
      summary: {
        period,
        totalSales,
        totalAmount,
        paidSales: paidSales.length,
        paidAmount,
        unpaidSales: unpaidSales.length,
        unpaidAmount,
      },
    };
  } catch (error) {
    console.error("Error getting sales summary:", error);
    return { success: false, message: "حدث خطأ أثناء جلب ملخص المبيعات" };
  }
}

/**
 * Get payment method balances
 */
export async function getPaymentMethodBalances() {
  try {
    // Get all payment methods
    const paymentMethods = await db.paymentMethodSettings.findMany();
    
    const balances = [];
    
    // Calculate balance for each payment method
    for (const method of paymentMethods) {
      // Get receipts (incoming money)
      const receiptsResult = await db.receiptVoucher.aggregate({
        where: {
          paymentMethodId: method.id,
        },
        _sum: {
          amount: true,
        },
      });
      
      // Get payments (outgoing money)
      const paymentsResult = await db.paymentVoucher.aggregate({
        where: {
          paymentMethodId: method.id,
        },
        _sum: {
          amount: true,
        },
      });
      
      const receipts = receiptsResult._sum.amount || 0;
      const payments = paymentsResult._sum.amount || 0;
      const balance = receipts - payments;
      
      balances.push({
        id: method.id,
        name: method.name,
        balance,
      });
    }
    
    return {
      success: true,
      balances,
    };
  } catch (error) {
    console.error("Error getting payment method balances:", error);
    return { success: false, message: "حدث خطأ أثناء جلب أرصدة طرق الدفع" };
  }
}
