/**
 * Accounting Integration Library
 * 
 * This library provides functions to integrate the accounting module with other modules
 * such as sales, purchases, payment vouchers, receipt vouchers, etc.
 */

import { db } from "@/lib/db";
import { createJournalEntry } from "@/lib/accounting";

// Transaction types
export enum TransactionType {
  SALE = "SALE",
  PURCHASE = "PURCHASE",
  PAYMENT_VOUCHER = "PAYMENT_VOUCHER",
  RECEIPT_VOUCHER = "RECEIPT_VOUCHER",
  CREDIT_NOTE = "CREDIT_NOTE",
  INVENTORY_ADJUSTMENT = "INVENTORY_ADJUSTMENT",
  GENERAL = "GENERAL",
}

// Payment information interface
export interface PaymentInfo {
  method: string;
  amount: number;
  date?: Date;
  reference?: string;
  journalId?: string;
}

// Transaction information interface
export interface TransactionInfo {
  transactionType: TransactionType;
  referenceId: string;
  referenceNumber: string;
  description: string;
  contactId?: string;
  contactName?: string;
  branchId?: string;
  date: Date;
}

/**
 * Ensure that all required accounts exist in the system
 * This function checks for essential accounts and creates them if they don't exist
 */
export async function ensureAccountsExist() {
  console.log("Ensuring required accounts exist...");

  // Define essential accounts
  const essentialAccounts = [
    // Asset accounts
    { code: "1000", name: "Assets", type: "ASSET", isParent: true },
    { code: "1100", name: "Cash", type: "ASSET", parentCode: "1000" },
    { code: "1110", name: "Cash on Hand", type: "ASSET", parentCode: "1100" },
    { code: "1120", name: "Vodafone Cash", type: "ASSET", parentCode: "1100" },
    { code: "1130", name: "Bank Account", type: "ASSET", parentCode: "1100" },
    { code: "1140", name: "Credit Card", type: "ASSET", parentCode: "1100" },
    { code: "1200", name: "Accounts Receivable", type: "ASSET", parentCode: "1000" },
    { code: "1210", name: "Customer Receivables", type: "ASSET", parentCode: "1200" },
    { code: "1300", name: "Inventory", type: "ASSET", parentCode: "1000" },
    { code: "1310", name: "Merchandise Inventory", type: "ASSET", parentCode: "1300" },
    
    // Liability accounts
    { code: "2000", name: "Liabilities", type: "LIABILITY", isParent: true },
    { code: "2100", name: "Accounts Payable", type: "LIABILITY", parentCode: "2000" },
    { code: "2110", name: "Supplier Payables", type: "LIABILITY", parentCode: "2100" },
    
    // Equity accounts
    { code: "3000", name: "Equity", type: "EQUITY", isParent: true },
    { code: "3100", name: "Owner's Equity", type: "EQUITY", parentCode: "3000" },
    
    // Revenue accounts
    { code: "4000", name: "Revenue", type: "REVENUE", isParent: true },
    { code: "4100", name: "Sales Revenue", type: "REVENUE", parentCode: "4000" },
    { code: "4200", name: "Other Revenue", type: "REVENUE", parentCode: "4000" },
    
    // Expense accounts
    { code: "5000", name: "Expenses", type: "EXPENSE", isParent: true },
    { code: "5100", name: "Cost of Goods Sold", type: "EXPENSE", parentCode: "5000" },
    { code: "5200", name: "Operating Expenses", type: "EXPENSE", parentCode: "5000" },
    { code: "5250", name: "Miscellaneous Expense", type: "EXPENSE", parentCode: "5200" },
  ];

  // Create accounts that don't exist
  for (const account of essentialAccounts) {
    const existingAccount = await db.account.findFirst({
      where: { code: account.code },
    });

    if (!existingAccount) {
      console.log(`Creating account: ${account.name} (${account.code})`);
      
      // Find parent account if needed
      let parentId = null;
      if (account.parentCode && !account.isParent) {
        const parentAccount = await db.account.findFirst({
          where: { code: account.parentCode },
        });
        
        if (parentAccount) {
          parentId = parentAccount.id;
        }
      }
      
      // Create the account
      await db.account.create({
        data: {
          code: account.code,
          name: account.name,
          type: account.type as any,
          parentId,
          balance: 0,
          isActive: true,
        },
      });
    }
  }
}

/**
 * Ensure that all required journals exist in the system
 * This function checks for essential journals and creates them if they don't exist
 */
export async function ensureJournalsExist() {
  console.log("Ensuring required journals exist...");

  // Define essential journals
  const essentialJournals = [
    { code: "CASH", name: "Cash Journal", type: "CASH" },
    { code: "VFCASH", name: "Vodafone Cash Journal", type: "VODAFONE_CASH" },
    { code: "BANK", name: "Bank Journal", type: "BANK_TRANSFER" },
    { code: "VISA", name: "Credit Card Journal", type: "VISA" },
    { code: "CUST", name: "Customer Accounts Journal", type: "CUSTOMER_ACCOUNT" },
    { code: "GEN", name: "General Journal", type: "GENERAL" },
    { code: "PAYMENT", name: "Payment Vouchers Journal", type: "PAYMENT_VOUCHER" },
    { code: "RECEIPT", name: "Receipt Vouchers Journal", type: "RECEIPT_VOUCHER" },
  ];

  // Create journals that don't exist
  for (const journal of essentialJournals) {
    const existingJournal = await db.journal.findFirst({
      where: { code: journal.code },
    });

    if (!existingJournal) {
      console.log(`Creating journal: ${journal.name} (${journal.code})`);
      
      // Create the journal
      await db.journal.create({
        data: {
          code: journal.code,
          name: journal.name,
          type: journal.type as any,
          isActive: true,
        },
      });
    }
  }
}

/**
 * Get the account associated with a payment method
 * @param paymentMethodCode The payment method code
 * @returns The account associated with the payment method
 */
export async function getPaymentMethodAccount(paymentMethodCode: string) {
  // First, try to find the payment method settings
  const paymentMethod = await db.paymentMethodSettings.findFirst({
    where: {
      code: paymentMethodCode,
      isActive: true,
    },
    include: {
      account: true,
    },
  });

  if (paymentMethod?.account) {
    return paymentMethod.account;
  }

  // If no account is linked to the payment method, use a default account based on the method
  let accountCode: string;
  
  switch (paymentMethodCode) {
    case "CASH":
      accountCode = "1110"; // Cash on Hand
      break;
    case "VODAFONE_CASH":
      accountCode = "1120"; // Vodafone Cash
      break;
    case "BANK_TRANSFER":
      accountCode = "1130"; // Bank Account
      break;
    case "VISA":
      accountCode = "1140"; // Credit Card
      break;
    default:
      accountCode = "1110"; // Default to Cash on Hand
  }

  // Find the account by code
  const account = await db.account.findFirst({
    where: {
      code: accountCode,
      isActive: true,
    },
  });

  return account;
}

/**
 * Get the journal associated with a payment method
 * @param paymentMethodCode The payment method code
 * @returns The journal associated with the payment method
 */
export async function getPaymentMethodJournal(paymentMethodCode: string) {
  // First, try to find the payment method settings
  const paymentMethod = await db.paymentMethodSettings.findFirst({
    where: {
      code: paymentMethodCode,
      isActive: true,
    },
    include: {
      journal: true,
    },
  });

  if (paymentMethod?.journal) {
    return paymentMethod.journal;
  }

  // If no journal is linked to the payment method, use a default journal based on the method
  let journalCode: string;
  
  switch (paymentMethodCode) {
    case "CASH":
      journalCode = "CASH"; // Cash Journal
      break;
    case "VODAFONE_CASH":
      journalCode = "VFCASH"; // Vodafone Cash Journal
      break;
    case "BANK_TRANSFER":
      journalCode = "BANK"; // Bank Journal
      break;
    case "VISA":
      journalCode = "VISA"; // Credit Card Journal
      break;
    default:
      journalCode = "GEN"; // Default to General Journal
  }

  // Find the journal by code
  const journal = await db.journal.findFirst({
    where: {
      code: journalCode,
      isActive: true,
    },
  });

  return journal;
}

/**
 * Initialize the accounting module
 * This function ensures that all required accounts and journals exist
 */
export async function initializeAccountingModule() {
  console.log("Initializing accounting module...");
  
  // Ensure accounts exist
  await ensureAccountsExist();
  
  // Ensure journals exist
  await ensureJournalsExist();
  
  // Link payment methods to accounts and journals
  await linkPaymentMethodsToAccounts();
  
  console.log("Accounting module initialized successfully");
}

/**
 * Link payment methods to accounts and journals
 */
export async function linkPaymentMethodsToAccounts() {
  console.log("Linking payment methods to accounts and journals...");
  
  // Define payment method mappings
  const paymentMethodMappings = [
    { code: "CASH", accountCode: "1110", journalCode: "CASH" },
    { code: "VODAFONE_CASH", accountCode: "1120", journalCode: "VFCASH" },
    { code: "BANK_TRANSFER", accountCode: "1130", journalCode: "BANK" },
    { code: "VISA", accountCode: "1140", journalCode: "VISA" },
  ];
  
  // Link each payment method
  for (const mapping of paymentMethodMappings) {
    // Find the payment method
    const paymentMethod = await db.paymentMethodSettings.findFirst({
      where: { code: mapping.code },
    });
    
    if (!paymentMethod) {
      console.log(`Payment method ${mapping.code} not found, skipping`);
      continue;
    }
    
    // Find the account
    const account = await db.account.findFirst({
      where: { code: mapping.accountCode },
    });
    
    if (!account) {
      console.log(`Account ${mapping.accountCode} not found, skipping`);
      continue;
    }
    
    // Find the journal
    const journal = await db.journal.findFirst({
      where: { code: mapping.journalCode },
    });
    
    if (!journal) {
      console.log(`Journal ${mapping.journalCode} not found, skipping`);
      continue;
    }
    
    // Update the payment method
    await db.paymentMethodSettings.update({
      where: { id: paymentMethod.id },
      data: {
        accountId: account.id,
        journalId: journal.id,
      },
    });
    
    console.log(`Linked payment method ${mapping.code} to account ${mapping.accountCode} and journal ${mapping.journalCode}`);
  }
}
