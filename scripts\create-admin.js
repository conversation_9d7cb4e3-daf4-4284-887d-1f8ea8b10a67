// This script creates an admin user in the database
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
      },
    });

    if (existingAdmin) {
      console.log('Admin user already exists. Updating password...');

      // Hash the password
      const hashedPassword = await bcrypt.hash('admin123', 10);

      // Update the admin user
      const updatedAdmin = await prisma.user.update({
        where: {
          id: existingAdmin.id,
        },
        data: {
          password: hashedPassword,
          role: 'ADMIN',
          isActive: true,
        },
      });

      console.log('Admin user updated successfully:', updatedAdmin.email);
    } else {
      console.log('Creating new admin user...');

      // Hash the password
      const hashedPassword = await bcrypt.hash('admin123', 10);

      // Create the admin user
      const admin = await prisma.user.create({
        data: {
          name: 'Administrator',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'ADMIN',
          isActive: true,
        },
      });

      console.log('Admin user created successfully:', admin.email);
    }

    // Create default permissions for admin
    const permissions = [
      'view_dashboard',
      'view_products',
      'add_products',
      'edit_products',
      'delete_products',
      'view_inventory',
      'add_inventory',
      'edit_inventory',
      'view_customers',
      'add_customers',
      'edit_customers',
      'delete_customers',
      'view_suppliers',
      'add_suppliers',
      'edit_suppliers',
      'delete_suppliers',
      'view_sales',
      'add_sales',
      'edit_sales',
      'delete_sales',
      'view_purchases',
      'add_purchases',
      'edit_purchases',
      'delete_purchases',
      'view_warehouses',
      'add_warehouses',
      'edit_warehouses',
      'delete_warehouses',
      'view_branches',
      'add_branches',
      'edit_branches',
      'delete_branches',
      'view_users',
      'add_users',
      'edit_users',
      'delete_users',
      'view_roles',
      'add_roles',
      'edit_roles',
      'delete_roles',
      'view_settings',
      'edit_settings',
    ];

    // Get the admin user
    const adminUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
      },
    });

    if (!adminUser) {
      throw new Error('Failed to find admin user after creation');
    }

    // Check existing permissions for this user
    const existingPermissions = await prisma.user.findUnique({
      where: {
        id: adminUser.id,
      },
      include: {
        permissions: true,
      },
    });

    const existingPermissionNames = existingPermissions?.permissions?.map(p => p.name) || [];

    // Add missing permissions
    for (const permission of permissions) {
      if (!existingPermissionNames.includes(permission)) {
        // Check if permission already exists in the system
        let permissionRecord = await prisma.permission.findFirst({
          where: {
            name: permission,
          },
        });

        // If permission doesn't exist, create it
        if (!permissionRecord) {
          permissionRecord = await prisma.permission.create({
            data: {
              name: permission,
              description: `Permission to ${permission.replace('_', ' ')}`,
            },
          });
          console.log(`Created permission: ${permission}`);
        }

        // Connect permission to user
        await prisma.user.update({
          where: {
            id: adminUser.id,
          },
          data: {
            permissions: {
              connect: {
                id: permissionRecord.id,
              },
            },
          },
        });

        console.log(`Added permission to user: ${permission}`);
      }
    }

    console.log('Admin user setup completed successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
