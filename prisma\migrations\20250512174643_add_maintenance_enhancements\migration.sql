-- AlterEnum
ALTER TYPE "MaintenanceStatus" ADD VALUE 'REJECTED_BY_CUSTOMER';

-- AlterTable
ALTER TABLE "Contact" ADD COLUMN     "email" TEXT;

-- AlterTable
ALTER TABLE "MaintenanceService" ADD COLUMN     "actualHours" DOUBLE PRECISION,
ADD COLUMN     "customerFeedback" TEXT,
ADD COLUMN     "customerRating" INTEGER,
ADD COLUMN     "customerSignature" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "estimatedHours" DOUBLE PRECISION,
ADD COLUMN     "technicianId" TEXT;
