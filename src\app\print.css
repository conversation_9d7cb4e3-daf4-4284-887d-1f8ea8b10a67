@media print {
  /* Hide all navigation, buttons, and other UI elements */
  nav, 
  header, 
  footer, 
  button, 
  .sidebar, 
  .navbar,
  .print-hidden {
    display: none !important;
  }

  /* Set page size to A4 */
  @page {
    size: A4;
    margin: 1cm;
  }

  /* Basic styling for the printed page */
  body {
    font-family: Arial, sans-serif;
    font-size: 12pt;
    line-height: 1.3;
    background: white !important;
    color: black !important;
  }

  /* Remove shadows, borders, and other decorative elements */
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }

  /* Ensure links are not underlined and show their full URL */
  a {
    text-decoration: none !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  /* Exceptions for internal links */
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }

  /* Ensure tables are properly displayed */
  table {
    border-collapse: collapse;
    width: 100%;
  }

  table, th, td {
    border: 1px solid #ddd;
  }

  th, td {
    padding: 8px;
    text-align: left;
  }

  /* Force page breaks */
  .page-break {
    page-break-after: always;
  }

  /* Prevent elements from breaking across pages */
  .no-break {
    page-break-inside: avoid;
  }
}
