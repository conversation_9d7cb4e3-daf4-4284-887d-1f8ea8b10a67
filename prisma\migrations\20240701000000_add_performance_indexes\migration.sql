-- Add indexes to improve query performance

-- Sales indexes
CREATE INDEX IF NOT EXISTS "Sale_date_idx" ON "Sale"("date");
CREATE INDEX IF NOT EXISTS "Sale_contactId_idx" ON "Sale"("contactId");
CREATE INDEX IF NOT EXISTS "Sale_branchId_idx" ON "Sale"("branchId");
CREATE INDEX IF NOT EXISTS "Sale_userId_idx" ON "Sale"("userId");
CREATE INDEX IF NOT EXISTS "Sale_paymentStatus_idx" ON "Sale"("paymentStatus");
CREATE INDEX IF NOT EXISTS "Sale_status_idx" ON "Sale"("status");

-- Purchase indexes
CREATE INDEX IF NOT EXISTS "Purchase_date_idx" ON "Purchase"("date");
CREATE INDEX IF NOT EXISTS "Purchase_contactId_idx" ON "Purchase"("contactId");
CREATE INDEX IF NOT EXISTS "Purchase_branchId_idx" ON "Purchase"("branchId");
CREATE INDEX IF NOT EXISTS "Purchase_userId_idx" ON "Purchase"("userId");
CREATE INDEX IF NOT EXISTS "Purchase_paymentStatus_idx" ON "Purchase"("paymentStatus");
CREATE INDEX IF NOT EXISTS "Purchase_status_idx" ON "Purchase"("status");

-- Inventory indexes
CREATE INDEX IF NOT EXISTS "Inventory_productId_idx" ON "Inventory"("productId");
CREATE INDEX IF NOT EXISTS "Inventory_warehouseId_idx" ON "Inventory"("warehouseId");
CREATE INDEX IF NOT EXISTS "Inventory_quantity_idx" ON "Inventory"("quantity");

-- Product indexes
CREATE INDEX IF NOT EXISTS "Product_categoryId_idx" ON "Product"("categoryId");
CREATE INDEX IF NOT EXISTS "Product_name_idx" ON "Product"("name");
CREATE INDEX IF NOT EXISTS "Product_isComponent_idx" ON "Product"("isComponent");
CREATE INDEX IF NOT EXISTS "Product_isCustomizable_idx" ON "Product"("isCustomizable");

-- Contact indexes
CREATE INDEX IF NOT EXISTS "Contact_isCustomer_idx" ON "Contact"("isCustomer");
CREATE INDEX IF NOT EXISTS "Contact_isSupplier_idx" ON "Contact"("isSupplier");
CREATE INDEX IF NOT EXISTS "Contact_name_idx" ON "Contact"("name");
CREATE INDEX IF NOT EXISTS "Contact_phone_idx" ON "Contact"("phone");

-- Maintenance indexes
CREATE INDEX IF NOT EXISTS "MaintenanceService_status_idx" ON "MaintenanceService"("status");
CREATE INDEX IF NOT EXISTS "MaintenanceService_contactId_idx" ON "MaintenanceService"("contactId");
CREATE INDEX IF NOT EXISTS "MaintenanceService_receivedDate_idx" ON "MaintenanceService"("receivedDate");
CREATE INDEX IF NOT EXISTS "MaintenanceService_priority_idx" ON "MaintenanceService"("priority");
