import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

// POST /api/notifications/test - Create a test notification
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Create a test notification
    const notification = await prisma.notification.create({
      data: {
        userId: session.user.id,
        type: "SYSTEM",
        title: "Test Notification",
        message: "This is a test notification created at " + new Date().toLocaleString(),
        link: "/dashboard/notifications",
        read: false
      }
    });
    
    return NextResponse.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error("Error creating test notification:", error);
    return NextResponse.json(
      { error: "Failed to create test notification" },
      { status: 500 }
    );
  }
}
