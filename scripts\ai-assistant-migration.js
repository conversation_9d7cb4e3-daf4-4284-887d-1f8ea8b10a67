// AI Assistant Migration Script
// This script adds the AI assistant tables to the database

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Starting AI Assistant migration...');

  try {
    // Check if the migration has already been applied
    const aiAssistantSettingsExists = await checkTableExists('AIAssistantSettings');
    const aiAssistantConversationExists = await checkTableExists('AIAssistantConversation');
    const aiAssistantMessageExists = await checkTableExists('AIAssistantMessage');
    const aiAssistantNotificationExists = await checkTableExists('AIAssistantNotification');

    if (aiAssistantSettingsExists && aiAssistantConversationExists && aiAssistantMessageExists && aiAssistantNotificationExists) {
      console.log('AI Assistant tables already exist. Migration not needed.');
      return;
    }

    // Run the migration
    console.log('Running migration...');

    // Execute the Prisma migration
    const { execSync } = require('child_process');
    execSync('npx prisma migrate dev --name add_ai_assistant', { stdio: 'inherit' });

    console.log('Migration completed successfully.');

    // Create default settings for admin user
    const adminUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
      },
    });

    if (adminUser) {
      console.log('Creating default AI Assistant settings for admin user...');

      await prisma.aIAssistantSettings.upsert({
        where: {
          userId: adminUser.id,
        },
        update: {},
        create: {
          userId: adminUser.id,
          isEnabled: true,
          autoSuggest: true,
          voiceEnabled: false,
          notificationsOn: true,
        },
      });

      console.log('Default settings created successfully.');
    }

    console.log('AI Assistant migration completed successfully!');
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function checkTableExists(tableName) {
  try {
    // Query the database to check if the table exists
    const result = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = ${tableName.toLowerCase()}
      );
    `;

    return result[0].exists;
  } catch (error) {
    console.error(`Error checking if table ${tableName} exists:`, error);
    return false;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
