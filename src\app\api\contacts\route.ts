import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import { updateContactBalance, TransactionType } from "@/lib/accounting";

// GET /api/contacts - Get all contacts
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view contacts
    // We'll check for various permissions that might allow viewing contacts
    const hasViewCustomersPermission = await hasPermission("view_customers");
    const hasViewSuppliersPermission = await hasPermission("view_suppliers");
    const hasViewSalesPermission = await hasPermission("view_sales");
    const hasViewPurchasesPermission = await hasPermission("view_purchases");
    const isAdmin = session.user.role === "ADMIN";

    // If user has any of these permissions, they can view contacts
    const canViewContacts = hasViewCustomersPermission ||
                           hasViewSuppliersPermission ||
                           hasViewSalesPermission ||
                           hasViewPurchasesPermission ||
                           isAdmin;

    if (!canViewContacts) {
      console.error("User without proper permissions is trying to access contacts:", session.user.email);
      return NextResponse.json(
        { error: "You don't have permission to view contacts. Please contact your administrator." },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const type = url.searchParams.get("type"); // customer, supplier, or both
    const search = url.searchParams.get("search");

    // Build the query
    const query: any = {
      where: {},
      orderBy: {
        name: "asc",
      },
    };

    // Filter by type if provided
    if (type === "customer") {
      // For customers, check if user has any customer-related permissions
      const canViewCustomers = hasViewCustomersPermission ||
                              hasViewSalesPermission ||
                              isAdmin;

      if (!canViewCustomers) {
        console.warn("User without customer permissions is trying to filter by customer type:", session.user.email);
        // Instead of blocking, we'll return an empty array
        return NextResponse.json([]);
      }
      query.where.isCustomer = true;
    } else if (type === "supplier") {
      // For suppliers, check if user has any supplier-related permissions
      const canViewSuppliers = hasViewSuppliersPermission ||
                              hasViewPurchasesPermission ||
                              isAdmin;

      if (!canViewSuppliers) {
        console.warn("User without supplier permissions is trying to filter by supplier type:", session.user.email);
        // Instead of blocking, we'll return an empty array
        return NextResponse.json([]);
      }
      query.where.isSupplier = true;
    } else {
      // If no type filter, apply permission-based filtering
      // This ensures users only see contacts they have permission to view
      const conditions = [];

      if (hasViewCustomersPermission || hasViewSalesPermission || isAdmin) {
        conditions.push({ isCustomer: true });
      }

      if (hasViewSuppliersPermission || hasViewPurchasesPermission || isAdmin) {
        conditions.push({ isSupplier: true });
      }

      // If user has permissions for both types, we need to use OR
      if (conditions.length > 1) {
        query.where.OR = conditions;
      }
      // If user has permission for only one type, use that condition directly
      else if (conditions.length === 1) {
        Object.assign(query.where, conditions[0]);
      }
      // If somehow user has no specific permissions but passed the initial check,
      // they'll get an empty result
    }

    // Add search filter if provided
    if (search) {
      query.where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { phone: { contains: search } },
      ];
    }

    // Get balance filter from query params
    const balanceFilter = url.searchParams.get("balance");

    // Filter by balance if requested
    if (balanceFilter === "non-zero") {
      // Add condition to show only contacts with non-zero balance
      if (!query.where.OR) {
        query.where.OR = [];
      } else if (!Array.isArray(query.where.OR)) {
        // If OR is not an array (e.g., from search filter), convert it to array
        query.where.OR = [query.where.OR];
      }

      // Add balance conditions to OR array
      query.where.OR.push(
        { balance: { gt: 0 } },  // Debit balance (customer owes money)
        { balance: { lt: 0 } }   // Credit balance (we owe money to supplier)
      );
    }

    const contacts = await db.contact.findMany(query);

    return NextResponse.json(contacts);
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Failed to fetch contacts" },
      { status: 500 }
    );
  }
}

// POST /api/contacts - Create a new contact
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await req.json();

    // Check permissions based on contact type
    const hasAddCustomersPermission = await hasPermission("add_customers");
    const hasAddSuppliersPermission = await hasPermission("add_suppliers");
    const isAdmin = session.user.role === "ADMIN";

    // Check if user can add the requested contact type
    const canAddCustomer = hasAddCustomersPermission || isAdmin;
    const canAddSupplier = hasAddSuppliersPermission || isAdmin;

    // Validate permissions based on what the user is trying to add
    if (data.isCustomer && !canAddCustomer) {
      console.error("User without add_customers permission is trying to add a customer:", session.user.email);
      return NextResponse.json(
        { error: "You don't have permission to add customers. Please contact your administrator." },
        { status: 403 }
      );
    }

    if (data.isSupplier && !canAddSupplier) {
      console.error("User without add_suppliers permission is trying to add a supplier:", session.user.email);
      return NextResponse.json(
        { error: "You don't have permission to add suppliers. Please contact your administrator." },
        { status: 403 }
      );
    }

    // If user is trying to add a contact without specifying a type, default to what they have permission for
    if (!data.isCustomer && !data.isSupplier) {
      if (canAddCustomer) {
        data.isCustomer = true;
      } else if (canAddSupplier) {
        data.isSupplier = true;
      } else {
        return NextResponse.json(
          { error: "You must specify a valid contact type (customer or supplier) that you have permission to add." },
          { status: 400 }
        );
      }
    }

    // Validate required fields
    if (!data.name || !data.phone) {
      return NextResponse.json(
        { error: "Name and phone are required" },
        { status: 400 }
      );
    }

    // Check if phone is already used
    const existingContact = await db.contact.findFirst({
      where: {
        phone: data.phone,
      },
    });

    if (existingContact) {
      return NextResponse.json(
        { error: "Phone number already exists" },
        { status: 400 }
      );
    }

    // Create the contact
    const contact = await db.contact.create({
      data: {
        name: data.name,
        phone: data.phone,
        address: data.address || null,
        isCustomer: data.isCustomer || false,
        isSupplier: data.isSupplier || false,
        balance: data.balance || 0,
        creditLimit: data.isCustomer ? (data.creditLimit || 0) : 0,
        creditPeriod: data.isCustomer ? (data.creditPeriod || 30) : 0,
        openingBalance: data.openingBalance || 0,
        openingBalanceDate: data.openingBalanceDate ? new Date(data.openingBalanceDate) : new Date(),
      },
    });

    // If there's an opening balance, create accounting entries
    if (data.openingBalance && data.openingBalance !== 0) {
      try {
        // Update contact balance in accounting system
        await updateContactBalance(contact.id);
      } catch (accountingError) {
        console.error("Error updating contact balance in accounting system:", accountingError);
        // Don't fail the contact creation if accounting update fails
      }
    }

    return NextResponse.json(contact);
  } catch (error) {
    console.error("Error creating contact:", error);
    return NextResponse.json(
      { error: "Failed to create contact" },
      { status: 500 }
    );
  }
}
