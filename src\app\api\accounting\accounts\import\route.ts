import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // For now, allow any authenticated user to import accounts
    // Later, we can add proper permission checks
    // const hasPermission = await hasPermission("manage_accounts");
    // if (!hasPermission) {
    //   return NextResponse.json(
    //     { error: "You don't have permission to import accounts" },
    //     { status: 403 }
    //   );
    // }

    const body = await request.json();
    const { accounts } = body;

    if (!accounts || !Array.isArray(accounts) || accounts.length === 0) {
      return NextResponse.json(
        { error: "No accounts provided for import" },
        { status: 400 }
      );
    }

    // Validate account data
    for (const account of accounts) {
      if (!account.code || !account.name || !account.type) {
        return NextResponse.json(
          { error: "Each account must have a code, name, and type" },
          { status: 400 }
        );
      }
    }

    // Process accounts in a transaction
    const result = await prisma.$transaction(async (tx) => {
      const importedAccounts = [];
      const errors = [];

      // First pass: Create accounts without parent relationships
      for (const account of accounts) {
        try {
          // Check if account already exists
          const existingAccount = await tx.account.findFirst({
            where: {
              code: account.code,
            },
          });

          if (existingAccount) {
            // Update existing account
            const updatedAccount = await tx.account.update({
              where: {
                id: existingAccount.id,
              },
              data: {
                name: account.name,
                type: account.type,
                isActive: account.isActive,
                balance: account.balance || 0,
              },
            });
            importedAccounts.push(updatedAccount);
          } else {
            // Create new account (without parent for now)
            const newAccount = await tx.account.create({
              data: {
                code: account.code,
                name: account.name,
                type: account.type,
                isActive: account.isActive,
                balance: account.balance || 0,
              },
            });
            importedAccounts.push(newAccount);
          }
        } catch (error: any) {
          errors.push({
            code: account.code,
            error: error.message || "Failed to import account",
          });
        }
      }

      // Second pass: Update parent relationships
      for (const account of accounts) {
        if (account.parentCode) {
          try {
            // Find the imported account
            const importedAccount = importedAccounts.find(
              (a) => a.code === account.code
            );

            if (!importedAccount) continue;

            // Find the parent account
            const parentAccount = importedAccounts.find(
              (a) => a.code === account.parentCode
            ) || await tx.account.findFirst({
              where: {
                code: account.parentCode,
              },
            });

            if (!parentAccount) {
              errors.push({
                code: account.code,
                error: `Parent account with code ${account.parentCode} not found`,
              });
              continue;
            }

            // Update the account with the parent ID
            await tx.account.update({
              where: {
                id: importedAccount.id,
              },
              data: {
                parentId: parentAccount.id,
              },
            });
          } catch (error: any) {
            errors.push({
              code: account.code,
              error: error.message || "Failed to update parent relationship",
            });
          }
        }
      }

      return {
        importedAccounts,
        errors,
      };
    });

    return NextResponse.json({
      success: true,
      imported: result.importedAccounts.length,
      errors: result.errors,
    });
  } catch (error: any) {
    console.error("Error importing accounts:", error);
    return NextResponse.json(
      { error: error.message || "Failed to import accounts" },
      { status: 500 }
    );
  }
}
