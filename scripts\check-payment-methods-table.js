const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // Get table information
    const tableInfo = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'PaymentMethodSettings'
      ORDER BY ordinal_position;
    `;
    
    console.log('PaymentMethodSettings Table Structure:');
    console.table(tableInfo);
    
  } catch (error) {
    console.error('Error checking table structure:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
