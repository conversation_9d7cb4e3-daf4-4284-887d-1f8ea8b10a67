"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  Plus,
  Search,
  Filter,
  Tag,
  Percent,
  Calendar,
  Users,
  ShoppingBag,
  Layers,
  Edit,
  Trash2,
  AlertCircle,
  Check,
  X
} from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { Di<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON>rigger } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";

// Define types
interface Discount {
  id: string;
  name: string;
  description: string | null;
  type: "PERCENTAGE" | "FIXED_AMOUNT";
  value: number;
  scope: "ITEM" | "INVOICE" | "CUSTOMER";
  minAmount: number | null;
  maxAmount: number | null;
  startDate: string | null;
  endDate: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  categoryId: string | null;
  productId: string | null;
  category?: {
    id: string;
    name: string;
  } | null;
  product?: {
    id: string;
    name: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  categoryId: string;
}

export default function DiscountsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [filteredDiscounts, setFilteredDiscounts] = useState<Discount[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterScope, setFilterScope] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterType, setFilterType] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("all");

  // Fetch discounts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch discounts with cache-busting and admin header
        const discountsResponse = await fetch(`/api/discounts?nocache=${Date.now()}`, {
          headers: {
            'X-Admin-Request': 'true',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        });
        if (!discountsResponse.ok) {
          throw new Error('Failed to fetch discounts');
        }
        const discountsData = await discountsResponse.json();
        setDiscounts(discountsData);
        setFilteredDiscounts(discountsData);

        // Fetch categories with cache-busting and admin header
        const categoriesResponse = await fetch(`/api/categories?nocache=${Date.now()}`, {
          headers: {
            'X-Admin-Request': 'true',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        });
        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories');
        }
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData);

        // Fetch products with cache-busting and admin header
        const productsResponse = await fetch(`/api/products?nocache=${Date.now()}`, {
          headers: {
            'X-Admin-Request': 'true',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        });
        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products');
        }
        const productsData = await productsResponse.json();
        setProducts(productsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: "Error",
          description: "Failed to load discounts",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Apply filters
  useEffect(() => {
    let result = [...discounts];

    // Apply search filter
    if (searchTerm) {
      result = result.filter(discount =>
        discount.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (discount.description && discount.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply scope filter
    if (filterScope !== 'all') {
      result = result.filter(discount => discount.scope === filterScope);
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      result = result.filter(discount => discount.isActive === (filterStatus === 'active'));
    }

    // Apply type filter
    if (filterType !== 'all') {
      result = result.filter(discount => discount.type === filterType);
    }

    // Apply tab filter
    if (activeTab === 'active') {
      result = result.filter(discount => discount.isActive);
    } else if (activeTab === 'inactive') {
      result = result.filter(discount => !discount.isActive);
    } else if (activeTab === 'expired') {
      const now = new Date();
      result = result.filter(discount =>
        discount.endDate && new Date(discount.endDate) < now
      );
    } else if (activeTab === 'upcoming') {
      const now = new Date();
      result = result.filter(discount =>
        discount.startDate && new Date(discount.startDate) > now
      );
    }

    setFilteredDiscounts(result);
  }, [discounts, searchTerm, filterScope, filterStatus, filterType, activeTab]);

  // Toggle discount status
  const toggleDiscountStatus = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/discounts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-Admin-Request': 'true',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
        body: JSON.stringify({ isActive: !currentStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update discount status');
      }

      // Update local state
      setDiscounts(discounts.map(discount =>
        discount.id === id
          ? { ...discount, isActive: !currentStatus }
          : discount
      ));

      toast({
        title: "Success",
        description: `Discount ${currentStatus ? 'deactivated' : 'activated'} successfully`,
      });
    } catch (error) {
      console.error('Error updating discount status:', error);
      toast({
        title: "Error",
        description: "Failed to update discount status",
        variant: "destructive",
      });
    }
  };

  // Delete discount
  const deleteDiscount = async (id: string) => {
    try {
      const response = await fetch(`/api/discounts/${id}`, {
        method: 'DELETE',
        headers: {
          'X-Admin-Request': 'true',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete discount');
      }

      // Update local state
      setDiscounts(discounts.filter(discount => discount.id !== id));

      toast({
        title: "Success",
        description: "Discount deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting discount:', error);
      toast({
        title: "Error",
        description: "Failed to delete discount",
        variant: "destructive",
      });
    }
  };

  // Format discount value
  const formatDiscountValue = (discount: Discount) => {
    if (discount.type === 'PERCENTAGE') {
      return `${discount.value}%`;
    } else {
      return `${discount.value.toFixed(2)} EGP`;
    }
  };

  // Get scope icon
  const getScopeIcon = (scope: string) => {
    switch (scope) {
      case 'ITEM':
        return <Tag className="h-4 w-4" />;
      case 'INVOICE':
        return <ShoppingBag className="h-4 w-4" />;
      case 'CUSTOMER':
        return <Users className="h-4 w-4" />;
      default:
        return <Tag className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Discounts</h1>
        <Button onClick={() => router.push('/dashboard/sales/discounts/new')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Discount
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="w-full md:w-2/3">
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input
                  placeholder="Search discounts..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <Select value={filterScope} onValueChange={setFilterScope}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Filter by scope" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Scopes</SelectItem>
                  <SelectItem value="ITEM">Item</SelectItem>
                  <SelectItem value="INVOICE">Invoice</SelectItem>
                  <SelectItem value="CUSTOMER">Customer</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                  <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
                <TabsTrigger value="inactive">Inactive</TabsTrigger>
                <TabsTrigger value="expired">Expired</TabsTrigger>
                <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-0">
                {loading ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <Card key={i}>
                        <CardHeader className="pb-2">
                          <Skeleton className="h-6 w-1/3" />
                          <Skeleton className="h-4 w-1/2" />
                        </CardHeader>
                        <CardContent>
                          <Skeleton className="h-4 w-full mb-2" />
                          <Skeleton className="h-4 w-2/3" />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : filteredDiscounts.length === 0 ? (
                  <div className="text-center py-8">
                    <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-lg font-medium">No discounts found</h3>
                    <p className="mt-1 text-gray-500">Try adjusting your search or filters</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredDiscounts.map((discount) => (
                      <Card key={discount.id} className={!discount.isActive ? "opacity-70" : ""}>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle className="flex items-center">
                                {discount.name}
                                {!discount.isActive && (
                                  <Badge variant="outline" className="ml-2 text-gray-500">Inactive</Badge>
                                )}
                              </CardTitle>
                              <CardDescription className="flex items-center mt-1">
                                {getScopeIcon(discount.scope)}
                                <span className="ml-1">
                                  {discount.scope === 'ITEM' ? 'Item Discount' :
                                   discount.scope === 'INVOICE' ? 'Invoice Discount' : 'Customer Discount'}
                                </span>
                                <span className="mx-2">•</span>
                                <Percent className="h-3 w-3" />
                                <span className="ml-1">{formatDiscountValue(discount)}</span>
                              </CardDescription>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => router.push(`/dashboard/sales/discounts/${discount.id}/edit`)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Delete Discount</DialogTitle>
                                    <DialogDescription>
                                      Are you sure you want to delete this discount? This action cannot be undone.
                                    </DialogDescription>
                                  </DialogHeader>
                                  <DialogFooter>
                                    <Button variant="outline" onClick={() => {}}>Cancel</Button>
                                    <Button variant="destructive" onClick={() => deleteDiscount(discount.id)}>
                                      Delete
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {discount.description && (
                            <p className="text-sm text-gray-500 mb-2">{discount.description}</p>
                          )}
                          <div className="flex flex-wrap gap-2 mt-2">
                            {discount.startDate && (
                              <Badge variant="outline" className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                From: {format(new Date(discount.startDate), 'MMM d, yyyy')}
                              </Badge>
                            )}
                            {discount.endDate && (
                              <Badge variant="outline" className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                Until: {format(new Date(discount.endDate), 'MMM d, yyyy')}
                              </Badge>
                            )}
                            {discount.minAmount && (
                              <Badge variant="outline" className="flex items-center">
                                Min: {discount.minAmount.toFixed(2)} EGP
                              </Badge>
                            )}
                            {discount.maxAmount && (
                              <Badge variant="outline" className="flex items-center">
                                Max: {discount.maxAmount.toFixed(2)} EGP
                              </Badge>
                            )}
                            {discount.category && (
                              <Badge variant="outline" className="flex items-center">
                                <Layers className="h-3 w-3 mr-1" />
                                {discount.category.name}
                              </Badge>
                            )}
                            {discount.product && (
                              <Badge variant="outline" className="flex items-center">
                                <ShoppingBag className="h-3 w-3 mr-1" />
                                {discount.product.name}
                              </Badge>
                            )}
                          </div>
                        </CardContent>
                        <CardFooter className="pt-0 flex justify-between">
                          <div className="text-xs text-gray-500">
                            Created: {format(new Date(discount.createdAt), 'MMM d, yyyy')}
                          </div>
                          <div className="flex items-center">
                            <Label htmlFor={`status-${discount.id}`} className="mr-2">
                              {discount.isActive ? 'Active' : 'Inactive'}
                            </Label>
                            <Switch
                              id={`status-${discount.id}`}
                              checked={discount.isActive}
                              onCheckedChange={() => toggleDiscountStatus(discount.id, discount.isActive)}
                            />
                          </div>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <div className="w-full md:w-1/3">
          <Card>
            <CardHeader>
              <CardTitle>Discount Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Discounts</span>
                  <span className="font-bold">{discounts.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Active Discounts</span>
                  <span className="font-bold">{discounts.filter(d => d.isActive).length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Item Discounts</span>
                  <span className="font-bold">{discounts.filter(d => d.scope === 'ITEM').length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Invoice Discounts</span>
                  <span className="font-bold">{discounts.filter(d => d.scope === 'INVOICE').length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Customer Discounts</span>
                  <span className="font-bold">{discounts.filter(d => d.scope === 'CUSTOMER').length}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => router.push('/dashboard/sales/discounts/new')}>
                <Plus className="h-4 w-4 mr-2" />
                Create New Discount
              </Button>
            </CardFooter>
          </Card>

          <Card className="mt-4">
            <CardHeader>
              <CardTitle>Quick Tips</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-blue-100 p-2 rounded-full mr-3">
                    <Tag className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Item Discounts</h4>
                    <p className="text-xs text-gray-500">Apply to specific products or categories</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-green-100 p-2 rounded-full mr-3">
                    <ShoppingBag className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Invoice Discounts</h4>
                    <p className="text-xs text-gray-500">Apply to the entire invoice total</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-purple-100 p-2 rounded-full mr-3">
                    <Users className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Customer Discounts</h4>
                    <p className="text-xs text-gray-500">Apply to specific customers or VIPs</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
