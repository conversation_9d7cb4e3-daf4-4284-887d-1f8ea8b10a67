-- Update AccountType enum
ALTER TYPE "AccountType" ADD VALUE 'COST_OF_GOODS_SOLD';
ALTER TYPE "AccountType" ADD VALUE 'OTHER_INCOME';
ALTER TYPE "AccountType" ADD VALUE 'OTHER_EXPENSE';

-- Add account code and parent account relationship
ALTER TABLE "Account" ADD COLUMN "code" TEXT;
ALTER TABLE "Account" ADD COLUMN "parentId" TEXT;
ALTER TABLE "Account" ADD COLUMN "level" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "Account" ADD COLUMN "isGroup" BOOLEAN NOT NULL DEFAULT false;

-- Add foreign key constraint for parent account
ALTER TABLE "Account" ADD CONSTRAINT "Account_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add account category for better organization
ALTER TABLE "Account" ADD COLUMN "category" TEXT;

-- Create GeneralLedgerEntry model for detailed ledger entries
CREATE TABLE "GeneralLedgerEntry" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "journalEntryId" TEXT,
    "transactionId" TEXT,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "description" TEXT NOT NULL,
    "debitAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "creditAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "runningBalance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "reference" TEXT,
    "referenceType" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "GeneralLedgerEntry_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints
ALTER TABLE "GeneralLedgerEntry" ADD CONSTRAINT "GeneralLedgerEntry_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "GeneralLedgerEntry" ADD CONSTRAINT "GeneralLedgerEntry_journalEntryId_fkey" FOREIGN KEY ("journalEntryId") REFERENCES "JournalEntry"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "GeneralLedgerEntry" ADD CONSTRAINT "GeneralLedgerEntry_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Update JournalEntry to support double-entry accounting
ALTER TABLE "JournalEntry" ADD COLUMN "debitAccountId" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "creditAccountId" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "voucherNumber" TEXT;
ALTER TABLE "JournalEntry" ADD COLUMN "voucherType" TEXT;

-- Add foreign key constraints
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_debitAccountId_fkey" FOREIGN KEY ("debitAccountId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_creditAccountId_fkey" FOREIGN KEY ("creditAccountId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create FiscalYear model
CREATE TABLE "FiscalYear" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "isClosed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FiscalYear_pkey" PRIMARY KEY ("id")
);

-- Create FiscalPeriod model
CREATE TABLE "FiscalPeriod" (
    "id" TEXT NOT NULL,
    "fiscalYearId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "isClosed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FiscalPeriod_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraint
ALTER TABLE "FiscalPeriod" ADD CONSTRAINT "FiscalPeriod_fiscalYearId_fkey" FOREIGN KEY ("fiscalYearId") REFERENCES "FiscalYear"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add fiscal period to journal entries and transactions
ALTER TABLE "JournalEntry" ADD COLUMN "fiscalPeriodId" TEXT;
ALTER TABLE "Transaction" ADD COLUMN "fiscalPeriodId" TEXT;

-- Add foreign key constraints
ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_fiscalPeriodId_fkey" FOREIGN KEY ("fiscalPeriodId") REFERENCES "FiscalPeriod"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_fiscalPeriodId_fkey" FOREIGN KEY ("fiscalPeriodId") REFERENCES "FiscalPeriod"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create index for better performance
CREATE INDEX "GeneralLedgerEntry_accountId_date_idx" ON "GeneralLedgerEntry"("accountId", "date");
CREATE INDEX "JournalEntry_date_idx" ON "JournalEntry"("date");
CREATE INDEX "Transaction_date_idx" ON "Transaction"("date");
CREATE INDEX "Account_code_idx" ON "Account"("code");
CREATE INDEX "Account_parentId_idx" ON "Account"("parentId");
