"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Use<PERSON>, <PERSON><PERSON>, Check } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ar } from "date-fns/locale";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  createdAt: string;
}

interface AIMessageProps {
  message: Message;
  isLoading?: boolean;
}

export function AIMessage({ message, isLoading = false }: AIMessageProps) {
  const [copied, setCopied] = useState(false);

  const isUser = message.role === "user";
  const isAssistant = message.role === "assistant";

  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Format the message content with proper line breaks
  const formattedContent = message.content.split("\n").map((line, i) => (
    <span key={i}>
      {line}
      {i < message.content.split("\n").length - 1 && <br />}
    </span>
  ));

  return (
    <div
      className={`flex ${
        isUser ? "justify-end" : "justify-start"
      } items-start gap-2 flex-row-reverse`}
      dir="rtl"
    >
      {isAssistant && (
        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <Bot className="h-5 w-5 text-blue-600" />
        </div>
      )}

      <div
        className={`relative group max-w-[80%] p-3 rounded-lg animate-fadeIn ${
          isUser
            ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md"
            : "bg-gradient-to-r from-gray-50 to-gray-100 text-gray-800 shadow-sm"
        }`}
        dir="rtl"
      >
        {isLoading ? (
          <div className="flex items-center space-x-reverse space-x-2">
            <span>{message.content}</span>
            <div className="flex space-x-reverse space-x-1">
              <span className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></span>
              <span className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></span>
              <span className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></span>
            </div>
          </div>
        ) : (
          <>
            <div className="text-sm">{formattedContent}</div>
            <div className={`text-xs mt-1 ${isUser ? "text-blue-200" : "text-gray-500"}`}>
              {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true, locale: ar })}
            </div>

            {isAssistant && (
              <button
                onClick={copyToClipboard}
                className="absolute top-2 left-2 p-1 rounded-md opacity-0 group-hover:opacity-100 transition-opacity"
                title="نسخ النص"
              >
                {copied ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4 text-gray-500 hover:text-gray-700" />
                )}
              </button>
            )}
          </>
        )}
      </div>

      {isUser && (
        <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
          <User className="h-5 w-5 text-white" />
        </div>
      )}
    </div>
  );
}
