-- Script para eliminar todos los datos contables
-- <PERSON><PERSON> script elimina todos los datos de las tablas relacionadas con el módulo contable

-- Desactivar restricciones de clave foránea temporalmente
SET session_replication_role = 'replica';

-- Eliminar datos de ReconciliationItem
TRUNCATE TABLE "ReconciliationItem" CASCADE;

-- Eliminar datos de AccountReconciliation
TRUNCATE TABLE "AccountReconciliation" CASCADE;

-- Eliminar datos de FinancialReport
TRUNCATE TABLE "FinancialReport" CASCADE;

-- Eliminar datos de GeneralLedgerEntry
TRUNCATE TABLE "GeneralLedgerEntry" CASCADE;

-- Eliminar datos de JournalEntry
TRUNCATE TABLE "JournalEntry" CASCADE;

-- Eliminar datos de Journal
TRUNCATE TABLE "Journal" CASCADE;

-- Eliminar datos de Transaction
TRUNCATE TABLE "Transaction" CASCADE;

-- Eliminar datos de FiscalPeriod
TRUNCATE TABLE "FiscalPeriod" CASCADE;

-- Eliminar datos de FiscalYear
TRUNCATE TABLE "FiscalYear" CASCADE;

-- Eliminar datos de AccountingSettings
TRUNCATE TABLE "AccountingSettings" CASCADE;

-- Eliminar datos de Account
TRUNCATE TABLE "Account" CASCADE;

-- Restaurar restricciones de clave foránea
SET session_replication_role = 'origin';

-- Mensaje de confirmación
SELECT 'Todos los datos contables han sido eliminados correctamente.' AS "Mensaje";
