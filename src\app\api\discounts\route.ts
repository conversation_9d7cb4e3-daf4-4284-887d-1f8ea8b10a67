import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/discounts - Get all discounts
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view discounts
    const hasViewPermission = await hasPermission("view_discounts") || session.user.role === "ADMIN";

    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view discounts" },
        { status: 403 }
      );
    }

    // Log user information
    console.log("User accessing discounts:", {
      email: session.user.email,
      role: session.user.role
    });

    // Get query parameters
    const url = new URL(req.url);
    const scope = url.searchParams.get("scope");
    const isActive = url.searchParams.get("isActive");
    const type = url.searchParams.get("type");
    const categoryId = url.searchParams.get("categoryId");
    const productId = url.searchParams.get("productId");

    // Build filter object
    const filter: any = {};

    if (scope) {
      filter.scope = scope;
    }

    if (isActive !== null) {
      filter.isActive = isActive === "true";
    }

    if (type) {
      filter.type = type;
    }

    if (categoryId) {
      filter.categoryId = categoryId;
    }

    if (productId) {
      filter.productId = productId;
    }

    // Get discounts from database
    const discounts = await db.discount.findMany({
      where: filter,
      include: {
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
          },
        },
        customerDiscounts: {
          include: {
            contact: {
              select: {
                id: true,
                name: true,
                phone: true,
                isVIP: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(discounts);
  } catch (error) {
    console.error("Error fetching discounts:", error);
    return NextResponse.json(
      { error: "Failed to fetch discounts" },
      { status: 500 }
    );
  }
}

// POST /api/discounts - Create a new discount
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add discounts
    const hasAddPermission = await hasPermission("add_discounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add discounts" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.type || !data.value || !data.scope) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Validate discount value
    if (data.value <= 0) {
      return NextResponse.json(
        { error: "Discount value must be greater than zero" },
        { status: 400 }
      );
    }

    if (data.type === "PERCENTAGE" && data.value > 100) {
      return NextResponse.json(
        { error: "Percentage discount cannot exceed 100%" },
        { status: 400 }
      );
    }

    // Start a transaction
    const result = await db.$transaction(async (tx) => {
      // Create the discount
      const discount = await tx.discount.create({
        data: {
          name: data.name,
          description: data.description,
          type: data.type,
          value: data.value,
          scope: data.scope,
          minAmount: data.minAmount,
          maxAmount: data.maxAmount,
          startDate: data.startDate,
          endDate: data.endDate,
          isActive: data.isActive !== undefined ? data.isActive : true,
          categoryId: data.categoryId,
          productId: data.productId,
        },
      });

      // If it's a customer discount, create customer discount relationships
      if (data.scope === "CUSTOMER") {
        // Handle customer-specific discounts
        if (data.customerIds && data.customerIds.length > 0) {
          for (const customerId of data.customerIds) {
            await tx.customerDiscount.create({
              data: {
                discountId: discount.id,
                contactId: customerId,
              },
            });
          }
        }

        // If applying to all VIP customers, we'll handle this during discount application
        // We don't need to store this in the database as we'll check the isVIP flag on contacts
      }

      return discount;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Error creating discount:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create discount" },
      { status: 500 }
    );
  }
}
