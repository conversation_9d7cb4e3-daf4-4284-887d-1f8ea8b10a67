import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { hasPermission } from "@/lib/permissions";
import cache from "@/lib/cache";

// GET /api/discounts/campaigns - Get all discount campaigns
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to view discounts
    const hasViewPermission = await hasPermission("view_discounts");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view discount campaigns" },
        { status: 403 }
      );
    }
    
    // Get URL parameters
    const url = new URL(req.url);
    const search = url.searchParams.get("search") || "";
    const status = url.searchParams.get("status") || "all";
    
    // Build filter
    const filter: any = {};
    
    if (search) {
      filter.OR = [
        {
          name: {
            contains: search,
            mode: "insensitive"
          }
        },
        {
          description: {
            contains: search,
            mode: "insensitive"
          }
        }
      ];
    }
    
    // Apply status filter
    const now = new Date();
    if (status === "active") {
      filter.AND = [
        {
          startDate: {
            lte: now
          }
        },
        {
          endDate: {
            gte: now
          }
        }
      ];
    } else if (status === "upcoming") {
      filter.startDate = {
        gt: now
      };
    } else if (status === "expired") {
      filter.endDate = {
        lt: now
      };
    }
    
    // Get campaigns from database
    const campaigns = await prisma.discountCampaign.findMany({
      where: filter,
      include: {
        campaignDiscounts: {
          include: {
            discount: true
          }
        }
      },
      orderBy: {
        startDate: "asc"
      }
    });
    
    // Format response
    const formattedCampaigns = campaigns.map(campaign => ({
      id: campaign.id,
      name: campaign.name,
      description: campaign.description,
      startDate: campaign.startDate.toISOString(),
      endDate: campaign.endDate.toISOString(),
      createdAt: campaign.createdAt.toISOString(),
      updatedAt: campaign.updatedAt.toISOString(),
      discounts: campaign.campaignDiscounts.map(cd => cd.discount)
    }));
    
    return NextResponse.json(formattedCampaigns);
  } catch (error) {
    console.error("Error fetching discount campaigns:", error);
    return NextResponse.json(
      { error: "Failed to fetch discount campaigns" },
      { status: 500 }
    );
  }
}

// POST /api/discounts/campaigns - Create a new discount campaign
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to add discounts
    const hasAddPermission = await hasPermission("add_discounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add discount campaigns" },
        { status: 403 }
      );
    }
    
    const data = await req.json();
    
    // Validate required fields
    if (!data.name || !data.startDate || !data.endDate || !data.discountIds || !Array.isArray(data.discountIds)) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Validate dates
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format" },
        { status: 400 }
      );
    }
    
    if (startDate > endDate) {
      return NextResponse.json(
        { error: "Start date must be before end date" },
        { status: 400 }
      );
    }
    
    // Verify all discounts exist
    const discounts = await prisma.discount.findMany({
      where: {
        id: {
          in: data.discountIds
        }
      }
    });
    
    if (discounts.length !== data.discountIds.length) {
      return NextResponse.json(
        { error: "One or more discount IDs are invalid" },
        { status: 400 }
      );
    }
    
    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the campaign
      const campaign = await tx.discountCampaign.create({
        data: {
          name: data.name,
          description: data.description,
          startDate,
          endDate
        }
      });
      
      // Create campaign-discount relationships
      for (const discountId of data.discountIds) {
        await tx.campaignDiscount.create({
          data: {
            campaignId: campaign.id,
            discountId
          }
        });
        
        // Update discount dates if needed
        const discount = discounts.find(d => d.id === discountId);
        if (discount) {
          const updateData: any = {};
          
          // If discount has no start date or its start date is after campaign start date
          if (!discount.startDate || discount.startDate > startDate) {
            updateData.startDate = startDate;
          }
          
          // If discount has no end date or its end date is before campaign end date
          if (!discount.endDate || discount.endDate < endDate) {
            updateData.endDate = endDate;
          }
          
          // Only update if there are changes
          if (Object.keys(updateData).length > 0) {
            await tx.discount.update({
              where: { id: discountId },
              data: updateData
            });
          }
        }
      }
      
      return campaign;
    });
    
    // Clear cache
    cache.clear(/^discounts_/);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error creating discount campaign:", error);
    return NextResponse.json(
      { error: "Failed to create discount campaign" },
      { status: 500 }
    );
  }
}
