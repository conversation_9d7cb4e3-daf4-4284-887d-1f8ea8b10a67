import { render, screen, fireEvent } from '@testing-library/react';
import ProductPreview from './ProductPreview';
import { act } from 'react-dom/test-utils';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => {
  const actual = jest.requireActual('framer-motion');
  return {
    ...actual,
    motion: {
      div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    },
  };
});

// Mock product data
const mockProduct = {
  id: 'product-1',
  name: 'Test Computer',
  basePrice: 1000,
  imageUrl: '/images/computer.jpg'
};

// Mock selected components
const mockSelectedComponents = {
  CPU: {
    id: 'cpu-1',
    name: 'Intel i7',
    type: 'CPU',
    price: 300,
    speed: '4.0 GHz'
  },
  RAM: {
    id: 'ram-1',
    name: 'Kingston 16GB',
    type: 'RAM',
    price: 150,
    capacity: '16GB',
    count: 2
  },
  SSD: {
    id: 'ssd-1',
    name: 'Samsung 500GB',
    type: 'SSD',
    price: 120,
    capacity: '500GB'
  }
};

describe('ProductPreview', () => {
  it('renders the product name and price', () => {
    render(
      <ProductPreview
        product={mockProduct}
        selectedComponents={mockSelectedComponents}
        totalPrice={1570}
      />
    );

    // Check if product name is displayed
    expect(screen.getByText(mockProduct.name)).toBeInTheDocument();
    
    // Check if total price is displayed
    expect(screen.getByText('1570.00 ج.م')).toBeInTheDocument();
  });

  it('displays the product image if available', () => {
    render(
      <ProductPreview
        product={mockProduct}
        selectedComponents={mockSelectedComponents}
        totalPrice={1570}
      />
    );

    // Check if product image is displayed
    const image = screen.getByAltText(mockProduct.name);
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', mockProduct.imageUrl);
  });

  it('displays a placeholder icon if no image is available', () => {
    const productWithoutImage = { ...mockProduct, imageUrl: undefined };
    
    render(
      <ProductPreview
        product={productWithoutImage}
        selectedComponents={mockSelectedComponents}
        totalPrice={1570}
      />
    );

    // Check if placeholder icon is displayed (we can't easily check for SVG content)
    // But we can check that the image is not present
    expect(screen.queryByAltText(mockProduct.name)).not.toBeInTheDocument();
  });

  it('displays all selected components', () => {
    render(
      <ProductPreview
        product={mockProduct}
        selectedComponents={mockSelectedComponents}
        totalPrice={1570}
      />
    );

    // Check if all component names are displayed
    expect(screen.getByText('Intel i7')).toBeInTheDocument();
    expect(screen.getByText(/Kingston 16GB/)).toBeInTheDocument();
    expect(screen.getByText(/Samsung 500GB/)).toBeInTheDocument();
    
    // Check if RAM count is displayed
    expect(screen.getByText(/2 sticks, 32GB/)).toBeInTheDocument();
    
    // Check if storage capacity is displayed
    expect(screen.getByText(/SSD: 500GB/)).toBeInTheDocument();
  });

  it('calculates total RAM capacity correctly', () => {
    render(
      <ProductPreview
        product={mockProduct}
        selectedComponents={mockSelectedComponents}
        totalPrice={1570}
      />
    );

    // Check if total RAM capacity is calculated and displayed correctly
    // 16GB × 2 sticks = 32GB
    expect(screen.getByText(/2 sticks, 32GB/)).toBeInTheDocument();
  });

  it('handles mouse movement for 3D effect', async () => {
    render(
      <ProductPreview
        product={mockProduct}
        selectedComponents={mockSelectedComponents}
        totalPrice={1570}
      />
    );

    // Find the container that handles mouse events
    const container = screen.getByText(mockProduct.name).closest('div');
    if (!container) return;

    // Simulate mouse movement
    await act(async () => {
      fireEvent.mouseMove(container, { clientX: 100, clientY: 100 });
    });

    // We can't easily test the 3D effect visually, but we can ensure it doesn't crash
    
    // Simulate mouse leave
    await act(async () => {
      fireEvent.mouseLeave(container);
    });
  });

  it('renders correctly with minimal components', () => {
    render(
      <ProductPreview
        product={mockProduct}
        selectedComponents={{
          CPU: mockSelectedComponents.CPU
        }}
        totalPrice={1300}
      />
    );

    // Check if only CPU is displayed
    expect(screen.getByText('Intel i7')).toBeInTheDocument();
    expect(screen.queryByText(/Kingston 16GB/)).not.toBeInTheDocument();
    expect(screen.queryByText(/Samsung 500GB/)).not.toBeInTheDocument();
    
    // Check if total price is correct
    expect(screen.getByText('1300.00 ج.م')).toBeInTheDocument();
  });

  it('renders correctly with no components', () => {
    render(
      <ProductPreview
        product={mockProduct}
        selectedComponents={{}}
        totalPrice={1000}
      />
    );

    // Check if product name is still displayed
    expect(screen.getByText(mockProduct.name)).toBeInTheDocument();
    
    // Check if total price is correct (just base price)
    expect(screen.getByText('1000.00 ج.م')).toBeInTheDocument();
  });
});
