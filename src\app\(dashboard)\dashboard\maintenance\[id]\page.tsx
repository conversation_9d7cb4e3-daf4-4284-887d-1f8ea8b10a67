"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import React from "react";
import {
  Loader2,
  ArrowLeft,
  Edit,
  Trash2,
  Plus,
  Clock,
  Calendar,
  User,
  Phone,
  AlertTriangle,
  CheckCircle2,
  Truck,
  XCircle,
  Settings,
  Tag,
  DollarSign,
  FileText,
  MoreVertical,
  Printer,
  MessageSquare,
  CreditCard,
  Receipt,
  Banknote,
  Smartphone,
  Building
} from "lucide-react";
import {
  getStatusLabel,
  getStatusColor,
  getPriorityLabel,
  getPriorityColor,
  calculateServiceDays,
  isServiceOverdue
} from "@/lib/maintenance";
import { formatCurrency } from "@/lib/utils";
import { format, formatDistanceToNow } from "date-fns";

interface MaintenanceService {
  id: string;
  serviceNumber: string;
  deviceType: string;
  brand: string;
  model?: string;
  serialNumber?: string;
  problemDescription: string;
  receivedDate: string;
  estimatedCompletionDate?: string;
  completionDate?: string;
  deliveryDate?: string;
  status: string;
  priority: string;
  initialDiagnosis?: string;
  technicalNotes?: string;
  estimatedCost?: number;
  finalCost?: number;
  isPaid: boolean;
  isWarranty: boolean;
  warrantyDetails?: string;
  estimatedHours?: number;
  actualHours?: number;
  technicianId?: string;
  customerSignature: boolean;
  customerRating?: number;
  customerFeedback?: string;
  notificationMethod?: string;
  paymentMethod?: string;
  paymentStatus?: string;
  invoiceId?: string;
  invoiceNumber?: string;
  contact: {
    id: string;
    name: string;
    phone: string;
    address?: string;
  };
  user: {
    id: string;
    name: string;
    email: string;
  };
  branch: {
    id: string;
    name: string;
  };
  parts: MaintenancePart[];
  statusHistory: StatusHistory[];
  payments?: MaintenancePayment[];
}

interface MaintenancePart {
  id: string;
  partName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  isFromInventory: boolean;
  product?: {
    id: string;
    name: string;
    basePrice: number;
    costPrice: number;
  };
}

interface StatusHistory {
  id: string;
  status: string;
  notes?: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
  };
}

interface MaintenancePayment {
  id: string;
  method: string;
  amount: number;
  date: string;
  notes?: string;
}

export default function MaintenanceDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const id = unwrappedParams.id;

  const router = useRouter();
  const [service, setService] = useState<MaintenanceService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"details" | "parts" | "history" | "feedback" | "payment">("details");
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [statusNotes, setStatusNotes] = useState("");
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("");
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentNotes, setPaymentNotes] = useState("");
  const [isAddingPayment, setIsAddingPayment] = useState(false);
  const [isGeneratingInvoice, setIsGeneratingInvoice] = useState(false);

  // Fetch maintenance service details
  const fetchServiceDetails = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/maintenance/${id}`);

      if (!response.ok) {
        throw new Error("Failed to fetch maintenance service details");
      }

      const data = await response.json();
      setService(data);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchServiceDetails();
  }, [id]);

  // Handle status change
  const handleStatusChange = async (newStatus: string) => {
    if (!service || service.status === newStatus) {
      setShowStatusDropdown(false);
      return;
    }

    setIsUpdatingStatus(true);
    try {
      const response = await fetch(`/api/maintenance/${id}/status-history`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          notes: statusNotes,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update status");
      }

      // Refresh service details
      await fetchServiceDetails();
      setStatusNotes("");
      setShowStatusDropdown(false);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Handle payment addition
  const handleAddPayment = async () => {
    if (!service || !paymentMethod || !paymentAmount || parseFloat(paymentAmount) <= 0) {
      return;
    }

    setIsAddingPayment(true);
    try {
      const response = await fetch(`/api/maintenance/${id}/payments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          method: paymentMethod,
          amount: parseFloat(paymentAmount),
          notes: paymentNotes,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to add payment");
      }

      // Refresh service details
      await fetchServiceDetails();
      setPaymentMethod("");
      setPaymentAmount("");
      setPaymentNotes("");
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsAddingPayment(false);
    }
  };

  // Handle invoice generation
  const handleGenerateInvoice = async () => {
    if (!service || !service.parts || service.parts.length === 0) {
      setError("Cannot generate invoice: No parts added to this service");
      return;
    }

    // We'll allow custom parts without productId, they will be handled on the server

    setIsGeneratingInvoice(true);
    setError(null);

    try {
      const response = await fetch(`/api/maintenance/${id}/invoice`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Invoice generation error:", errorData);
        throw new Error(errorData.error || "Failed to generate invoice");
      }

      // Refresh service details
      await fetchServiceDetails();
    } catch (error: any) {
      console.error("Invoice generation error:", error);
      setError(error.message);
    } finally {
      setIsGeneratingInvoice(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          {error}
        </div>
        <div className="mt-4">
          <Link
            href="/dashboard/maintenance"
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance
          </Link>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 text-yellow-700 p-4 rounded-md">
          Maintenance service not found
        </div>
        <div className="mt-4">
          <Link
            href="/dashboard/maintenance"
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Maintenance
          </Link>
        </div>
      </div>
    );
  }

  const days = calculateServiceDays(new Date(service.receivedDate));
  const isOverdue = isServiceOverdue(new Date(service.receivedDate)) &&
                   !["DELIVERED", "CANCELLED"].includes(service.status);

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            href="/dashboard/maintenance"
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              {service.serviceNumber}
              {isOverdue && (
                <span className="ml-2 bg-red-100 text-red-800 text-xs font-semibold px-2.5 py-0.5 rounded flex items-center">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Overdue ({days} days)
                </span>
              )}
            </h1>
            <p className="text-sm text-gray-500">
              {service.deviceType} - {service.brand} {service.model}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <button
              onClick={() => setShowStatusDropdown(!showStatusDropdown)}
              className={`px-4 py-2 rounded-md font-medium flex items-center ${getStatusColor(service.status)}`}
            >
              {service.status === "RECEIVED" && <Clock className="h-4 w-4 mr-1" />}
              {service.status === "IN_PROGRESS" && <Settings className="h-4 w-4 mr-1" />}
              {service.status === "WAITING_FOR_PARTS" && <Truck className="h-4 w-4 mr-1" />}
              {service.status === "COMPLETED" && <CheckCircle2 className="h-4 w-4 mr-1" />}
              {service.status === "DELIVERED" && <Truck className="h-4 w-4 mr-1" />}
              {service.status === "CANCELLED" && <XCircle className="h-4 w-4 mr-1" />}
              {service.status === "REJECTED_BY_CUSTOMER" && <AlertTriangle className="h-4 w-4 mr-1" />}
              {getStatusLabel(service.status)}
            </button>

            {showStatusDropdown && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                <div className="p-2">
                  <div className="mb-2">
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Change Status
                    </label>
                    <textarea
                      value={statusNotes}
                      onChange={(e) => setStatusNotes(e.target.value)}
                      placeholder="Add notes about this status change (optional)"
                      className="w-full text-sm border border-gray-300 rounded-md px-3 py-2"
                      rows={2}
                    ></textarea>
                  </div>
                  <div className="space-y-1">
                    <button
                      onClick={() => handleStatusChange("RECEIVED")}
                      className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-blue-50 flex items-center"
                    >
                      <Clock className="h-4 w-4 mr-2 text-blue-600" />
                      Received
                    </button>
                    <button
                      onClick={() => handleStatusChange("IN_PROGRESS")}
                      className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-yellow-50 flex items-center"
                    >
                      <Settings className="h-4 w-4 mr-2 text-yellow-600" />
                      In Progress
                    </button>
                    <button
                      onClick={() => handleStatusChange("WAITING_FOR_PARTS")}
                      className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-purple-50 flex items-center"
                    >
                      <Truck className="h-4 w-4 mr-2 text-purple-600" />
                      Waiting for Parts
                    </button>
                    <button
                      onClick={() => handleStatusChange("COMPLETED")}
                      className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-green-50 flex items-center"
                    >
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-600" />
                      Completed
                    </button>
                    <button
                      onClick={() => handleStatusChange("DELIVERED")}
                      className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-50 flex items-center"
                    >
                      <Truck className="h-4 w-4 mr-2 text-gray-600" />
                      Delivered
                    </button>
                    <button
                      onClick={() => handleStatusChange("REJECTED_BY_CUSTOMER")}
                      className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-orange-50 flex items-center"
                    >
                      <AlertTriangle className="h-4 w-4 mr-2 text-orange-600" />
                      Rejected by Customer
                    </button>
                    <button
                      onClick={() => handleStatusChange("CANCELLED")}
                      className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-red-50 flex items-center"
                    >
                      <XCircle className="h-4 w-4 mr-2 text-red-600" />
                      Cancelled
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Link
            href={`/dashboard/maintenance/${service.id}/edit`}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700"
            title="Edit Service"
          >
            <Edit className="h-5 w-5" />
          </Link>

          <Link
            href={`/dashboard/maintenance/${service.id}/print`}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700"
            title="Print Service Report"
          >
            <Printer className="h-5 w-5" />
          </Link>

          <Link
            href={`/dashboard/maintenance/${service.id}/notify`}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700"
            title="Notify Customer"
          >
            <MessageSquare className="h-5 w-5" />
          </Link>

          <Link
            href={`/dashboard/maintenance/${service.id}/labels`}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700"
            title="Print Labels"
          >
            <Tag className="h-5 w-5" />
          </Link>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("details")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "details"
                ? "border-indigo-500 text-indigo-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Details
          </button>
          <button
            onClick={() => setActiveTab("workflow")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "workflow"
                ? "border-indigo-500 text-indigo-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Workflow
          </button>
          <button
            onClick={() => setActiveTab("parts")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "parts"
                ? "border-indigo-500 text-indigo-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Parts & Costs
          </button>
          <button
            onClick={() => setActiveTab("payment")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "payment"
                ? "border-indigo-500 text-indigo-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Payment & Invoice
          </button>
          <button
            onClick={() => setActiveTab("history")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "history"
                ? "border-indigo-500 text-indigo-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Status History
          </button>
          <button
            onClick={() => setActiveTab("feedback")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "feedback"
                ? "border-indigo-500 text-indigo-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Time & Feedback
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        {activeTab === "details" && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Customer Information */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h2>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Name</p>
                    <p className="text-base font-medium text-gray-900">{service.contact.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Phone</p>
                    <p className="text-base font-medium text-gray-900">{service.contact.phone}</p>
                  </div>
                  {service.contact.address && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Address</p>
                      <p className="text-base font-medium text-gray-900">{service.contact.address}</p>
                    </div>
                  )}
                  <div>
                    <Link
                      href={`/dashboard/contacts/${service.contact.id}`}
                      className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                    >
                      View Customer Profile
                    </Link>
                  </div>
                </div>
              </div>

              {/* Device Information */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Device Information</h2>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Device Type</p>
                    <p className="text-base font-medium text-gray-900">{service.deviceType}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Brand</p>
                    <p className="text-base font-medium text-gray-900">{service.brand}</p>
                  </div>
                  {service.model && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Model</p>
                      <p className="text-base font-medium text-gray-900">{service.model}</p>
                    </div>
                  )}
                  {service.serialNumber && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Serial Number</p>
                      <p className="text-base font-medium text-gray-900">{service.serialNumber}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Service Information */}
              <div className="md:col-span-2">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Service Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Received Date</p>
                    <p className="text-base font-medium text-gray-900">
                      {format(new Date(service.receivedDate), "dd/MM/yyyy")}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(service.receivedDate), { addSuffix: true })}
                    </p>
                  </div>
                  {service.estimatedCompletionDate && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Estimated Completion</p>
                      <p className="text-base font-medium text-gray-900">
                        {format(new Date(service.estimatedCompletionDate), "dd/MM/yyyy")}
                      </p>
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-500">Priority</p>
                    <p className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(service.priority)}`}>
                      {getPriorityLabel(service.priority)}
                    </p>
                  </div>
                  {service.completionDate && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Completion Date</p>
                      <p className="text-base font-medium text-gray-900">
                        {format(new Date(service.completionDate), "dd/MM/yyyy")}
                      </p>
                    </div>
                  )}
                  {service.deliveryDate && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Delivery Date</p>
                      <p className="text-base font-medium text-gray-900">
                        {format(new Date(service.deliveryDate), "dd/MM/yyyy")}
                      </p>
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-500">Warranty</p>
                    <p className="text-base font-medium text-gray-900">
                      {service.isWarranty ? "Yes" : "No"}
                    </p>
                    {service.isWarranty && service.warrantyDetails && (
                      <p className="text-xs text-gray-500">{service.warrantyDetails}</p>
                    )}
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Problem Description</p>
                    <p className="text-base text-gray-900 mt-1 whitespace-pre-line">
                      {service.problemDescription}
                    </p>
                  </div>

                  {service.initialDiagnosis && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Initial Diagnosis</p>
                      <p className="text-base text-gray-900 mt-1 whitespace-pre-line">
                        {service.initialDiagnosis}
                      </p>
                    </div>
                  )}

                  {service.technicalNotes && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Technical Notes</p>
                      <p className="text-base text-gray-900 mt-1 whitespace-pre-line">
                        {service.technicalNotes}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "workflow" && (
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Maintenance Workflow</h2>

            <div className="relative">
              {/* Timeline */}
              <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200"></div>

              {/* Step 1: Device Reception */}
              <div className="relative mb-8 pl-10">
                <div className="absolute left-0 rounded-full bg-blue-500 text-white w-10 h-10 flex items-center justify-center">
                  1
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-md font-semibold text-gray-900 mb-2">Device Reception</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Customer:</span> {service.contact.name}</p>
                    <p><span className="font-medium">Branch:</span> {service.branch.name}</p>
                    <p><span className="font-medium">Device:</span> {service.deviceType} {service.brand} {service.model}</p>
                    <p><span className="font-medium">Problem:</span> {service.problemDescription}</p>
                    <p><span className="font-medium">Initial Diagnosis:</span> {service.initialDiagnosis || "Not provided"}</p>
                    <p><span className="font-medium">Estimated Cost:</span> {service.estimatedCost ? formatCurrency(service.estimatedCost) : "Not provided"}</p>
                    <p><span className="font-medium">Receipt Status:</span> {service.customerSignature ? "Signed by customer" : "Not signed"}</p>
                  </div>
                  <div className="mt-3 flex space-x-2">
                    <button
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm hover:bg-blue-200"
                      onClick={() => {
                        // Print receipt logic
                        alert("Print receipt functionality will be implemented");
                      }}
                    >
                      Print Receipt
                    </button>
                  </div>
                </div>
              </div>

              {/* Step 2: Final Diagnosis */}
              <div className="relative mb-8 pl-10">
                <div className={`absolute left-0 rounded-full w-10 h-10 flex items-center justify-center ${
                  service.status === "IN_PROGRESS" || service.status === "COMPLETED" || service.status === "DELIVERED"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-500"
                }`}>
                  2
                </div>
                <div className={`bg-white p-4 rounded-lg shadow-sm border ${
                  service.status === "IN_PROGRESS" || service.status === "COMPLETED" || service.status === "DELIVERED"
                    ? "border-blue-200"
                    : "border-gray-200"
                }`}>
                  <h3 className="text-md font-semibold text-gray-900 mb-2">Final Diagnosis</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Status:</span> {
                      service.status === "IN_PROGRESS" || service.status === "COMPLETED" || service.status === "DELIVERED"
                        ? "Completed"
                        : "Pending"
                    }</p>
                    {service.technicalNotes && (
                      <p><span className="font-medium">Technical Notes:</span> {service.technicalNotes}</p>
                    )}
                  </div>
                  {service.status === "RECEIVED" && (
                    <div className="mt-3 flex space-x-2">
                      <button
                        className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                        onClick={() => handleStatusChange("IN_PROGRESS")}
                      >
                        Start Diagnosis
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Step 3: Customer Notification */}
              <div className="relative mb-8 pl-10">
                <div className={`absolute left-0 rounded-full w-10 h-10 flex items-center justify-center ${
                  service.notificationMethod
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-500"
                }`}>
                  3
                </div>
                <div className={`bg-white p-4 rounded-lg shadow-sm border ${
                  service.notificationMethod
                    ? "border-blue-200"
                    : "border-gray-200"
                }`}>
                  <h3 className="text-md font-semibold text-gray-900 mb-2">Customer Notification</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Status:</span> {
                      service.notificationMethod
                        ? `Notified via ${service.notificationMethod}`
                        : "Not notified yet"
                    }</p>
                    <p><span className="font-medium">Customer Phone:</span> {service.contact.phone}</p>
                  </div>
                  {!service.notificationMethod && service.status === "IN_PROGRESS" && (
                    <div className="mt-3 flex space-x-2">
                      <button
                        className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                        onClick={() => {
                          // WhatsApp notification
                          window.open(`https://wa.me/${service.contact.phone.replace(/\D/g, '')}`, '_blank');
                        }}
                      >
                        Notify via WhatsApp
                      </button>
                      <button
                        className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                        onClick={() => {
                          // Phone call notification
                          alert(`Call customer at ${service.contact.phone}`);
                        }}
                      >
                        Call Customer
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Step 4: Customer Decision */}
              <div className="relative mb-8 pl-10">
                <div className={`absolute left-0 rounded-full w-10 h-10 flex items-center justify-center ${
                  service.status === "COMPLETED" || service.status === "DELIVERED" || service.status === "CANCELLED" || service.status === "REJECTED_BY_CUSTOMER"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-500"
                }`}>
                  4
                </div>
                <div className={`bg-white p-4 rounded-lg shadow-sm border ${
                  service.status === "COMPLETED" || service.status === "DELIVERED" || service.status === "CANCELLED" || service.status === "REJECTED_BY_CUSTOMER"
                    ? "border-blue-200"
                    : "border-gray-200"
                }`}>
                  <h3 className="text-md font-semibold text-gray-900 mb-2">Customer Decision</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Status:</span> {
                      service.status === "COMPLETED" || service.status === "DELIVERED"
                        ? "Approved Repair"
                        : service.status === "CANCELLED" || service.status === "REJECTED_BY_CUSTOMER"
                          ? "Rejected Repair"
                          : "Awaiting Decision"
                    }</p>
                    <p><span className="font-medium">Pickup Deadline:</span> Within 15 days of notification</p>
                  </div>
                  {service.status === "IN_PROGRESS" && (
                    <div className="mt-3 flex space-x-2">
                      <button
                        className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                        onClick={() => handleStatusChange("COMPLETED")}
                      >
                        Customer Approved
                      </button>
                      <button
                        className="px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700"
                        onClick={() => handleStatusChange("REJECTED_BY_CUSTOMER")}
                      >
                        Customer Rejected
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Step 5: Service Completion */}
              <div className="relative mb-8 pl-10">
                <div className={`absolute left-0 rounded-full w-10 h-10 flex items-center justify-center ${
                  service.status === "COMPLETED" || service.status === "DELIVERED"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-500"
                }`}>
                  5
                </div>
                <div className={`bg-white p-4 rounded-lg shadow-sm border ${
                  service.status === "COMPLETED" || service.status === "DELIVERED"
                    ? "border-blue-200"
                    : "border-gray-200"
                }`}>
                  <h3 className="text-md font-semibold text-gray-900 mb-2">Service Completion</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Status:</span> {
                      service.status === "COMPLETED"
                        ? "Ready for Pickup"
                        : service.status === "DELIVERED"
                          ? "Delivered to Customer"
                          : "Pending"
                    }</p>
                    <p><span className="font-medium">Final Cost:</span> {service.finalCost ? formatCurrency(service.finalCost) : "Not set"}</p>
                    <p><span className="font-medium">Payment Status:</span> {service.isPaid ? "Paid" : "Unpaid"}</p>
                  </div>
                  {service.status === "COMPLETED" && !service.isPaid && (
                    <div className="mt-3 flex space-x-2">
                      <button
                        className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                        onClick={() => setActiveTab("payment")}
                      >
                        Process Payment
                      </button>
                    </div>
                  )}
                  {service.status === "COMPLETED" && service.isPaid && (
                    <div className="mt-3 flex space-x-2">
                      <button
                        className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                        onClick={() => handleStatusChange("DELIVERED")}
                      >
                        Mark as Delivered
                      </button>
                      <button
                        className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                        onClick={() => {
                          // Print delivery receipt
                          alert("Print delivery receipt functionality will be implemented");
                        }}
                      >
                        Print Delivery Receipt
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "parts" && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Parts & Costs</h2>
              <Link
                href={`/dashboard/maintenance/${service.id}/parts/add`}
                className="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm font-medium flex items-center"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Part
              </Link>
            </div>

            {service.parts.length > 0 ? (
              <div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Part Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Total
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {service.parts.map((part) => (
                        <tr key={part.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{part.partName}</div>
                            {part.product && (
                              <div className="text-xs text-gray-500">From inventory: {part.product.name}</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {part.quantity}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                            {formatCurrency(part.unitPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                            {formatCurrency(part.totalPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link
                              href={`/dashboard/maintenance/${service.id}/parts/${part.id}/edit`}
                              className="text-indigo-600 hover:text-indigo-900 mr-3"
                            >
                              Edit
                            </Link>
                            <button
                              className="text-red-600 hover:text-red-900"
                              onClick={() => {
                                if (confirm("Are you sure you want to delete this part?")) {
                                  // Delete part
                                }
                              }}
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-gray-50">
                      <tr>
                        <td colSpan={3} className="px-6 py-4 text-sm font-semibold text-gray-900 text-right">
                          Total Cost:
                        </td>
                        <td className="px-6 py-4 text-sm font-bold text-gray-900 text-right">
                          {formatCurrency(service.parts.reduce((sum, part) => sum + part.totalPrice, 0))}
                        </td>
                        <td></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-sm font-semibold text-gray-700 mb-2">Estimated Cost</h3>
                    <p className="text-2xl font-bold text-gray-900">
                      {service.estimatedCost ? formatCurrency(service.estimatedCost) : "Not set"}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-sm font-semibold text-gray-700 mb-2">Final Cost</h3>
                    <p className="text-2xl font-bold text-gray-900">
                      {service.finalCost ? formatCurrency(service.finalCost) : "Not set"}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Payment Status: {service.isPaid ? "Paid" : "Unpaid"}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 p-6 text-center rounded-md">
                <p className="text-gray-500">No parts added yet</p>
                <Link
                  href={`/dashboard/maintenance/${service.id}/parts/add`}
                  className="mt-2 inline-flex items-center text-indigo-600 hover:text-indigo-900"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add the first part
                </Link>
              </div>
            )}
          </div>
        )}

        {activeTab === "history" && (
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Status History</h2>

            {service.statusHistory.length > 0 ? (
              <div className="flow-root">
                <ul className="-mb-8">
                  {service.statusHistory.map((historyItem, index) => (
                    <li key={historyItem.id}>
                      <div className="relative pb-8">
                        {index !== service.statusHistory.length - 1 && (
                          <span
                            className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                            aria-hidden="true"
                          ></span>
                        )}
                        <div className="relative flex space-x-3">
                          <div>
                            <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getStatusColor(historyItem.status)}`}>
                              {historyItem.status === "RECEIVED" && <Clock className="h-4 w-4" />}
                              {historyItem.status === "IN_PROGRESS" && <Settings className="h-4 w-4" />}
                              {historyItem.status === "WAITING_FOR_PARTS" && <Truck className="h-4 w-4" />}
                              {historyItem.status === "COMPLETED" && <CheckCircle2 className="h-4 w-4" />}
                              {historyItem.status === "DELIVERED" && <Truck className="h-4 w-4" />}
                              {historyItem.status === "CANCELLED" && <XCircle className="h-4 w-4" />}
                            </span>
                          </div>
                          <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                              <p className="text-sm text-gray-900">
                                Status changed to <span className="font-medium">{getStatusLabel(historyItem.status)}</span>
                              </p>
                              {historyItem.notes && (
                                <p className="mt-1 text-sm text-gray-500">{historyItem.notes}</p>
                              )}
                            </div>
                            <div className="text-right text-sm whitespace-nowrap text-gray-500">
                              <div>{format(new Date(historyItem.createdAt), "dd/MM/yyyy HH:mm")}</div>
                              <div className="text-xs">By {historyItem.user.name}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <div className="bg-gray-50 p-6 text-center rounded-md">
                <p className="text-gray-500">No status history available</p>
              </div>
            )}
          </div>
        )}

        {activeTab === "payment" && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Payment Information */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Final Cost</p>
                    <p className="text-base font-medium text-gray-900">
                      {service.finalCost !== null && service.finalCost !== undefined
                        ? `${service.finalCost.toFixed(2)} EGP`
                        : "Not specified"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Payment Status</p>
                    <p className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      service.paymentStatus === "PAID"
                        ? "bg-green-100 text-green-800"
                        : service.paymentStatus === "PARTIALLY_PAID"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                    }`}>
                      {service.paymentStatus === "PAID"
                        ? "Paid"
                        : service.paymentStatus === "PARTIALLY_PAID"
                          ? "Partially Paid"
                          : "Unpaid"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Payment Method</p>
                    <p className="text-base font-medium text-gray-900 flex items-center">
                      {service.paymentMethod === "CASH" && <Banknote className="h-4 w-4 mr-1" />}
                      {service.paymentMethod === "VODAFONE_CASH" && <Smartphone className="h-4 w-4 mr-1" />}
                      {service.paymentMethod === "BANK_TRANSFER" && <Building className="h-4 w-4 mr-1" />}
                      {service.paymentMethod === "VISA" && <CreditCard className="h-4 w-4 mr-1" />}
                      {service.paymentMethod || "Not specified"}
                    </p>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-md font-semibold text-gray-900 mb-2">Add Payment</h3>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Method
                      </label>
                      <select
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                        value={paymentMethod}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        disabled={isAddingPayment}
                      >
                        <option value="">Select method</option>
                        <option value="CASH">Cash</option>
                        <option value="VODAFONE_CASH">Vodafone Cash</option>
                        <option value="BANK_TRANSFER">Bank Transfer</option>
                        <option value="VISA">Visa</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Amount
                      </label>
                      <input
                        type="number"
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                        placeholder="0.00"
                        value={paymentAmount}
                        onChange={(e) => setPaymentAmount(e.target.value)}
                        disabled={isAddingPayment}
                      />
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes
                    </label>
                    <textarea
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                      rows={2}
                      placeholder="Payment notes (optional)"
                      value={paymentNotes}
                      onChange={(e) => setPaymentNotes(e.target.value)}
                      disabled={isAddingPayment}
                    ></textarea>
                  </div>
                  <button
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm font-medium"
                    onClick={handleAddPayment}
                    disabled={isAddingPayment || !paymentMethod || !paymentAmount || parseFloat(paymentAmount) <= 0}
                  >
                    {isAddingPayment ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-1 animate-spin inline" />
                        Processing...
                      </>
                    ) : (
                      "Add Payment"
                    )}
                  </button>
                </div>
              </div>

              {/* Invoice Information */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Invoice Information</h2>
                {service.invoiceId ? (
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Invoice Number</p>
                      <p className="text-base font-medium text-gray-900">{service.invoiceNumber}</p>
                    </div>
                    <div>
                      <Link
                        href={`/dashboard/sales/${service.invoiceId}`}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm font-medium inline-flex items-center"
                      >
                        <Receipt className="h-4 w-4 mr-1" />
                        View Invoice
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <p className="text-gray-500">No invoice has been created for this service.</p>
                    <button
                      className={`px-4 py-2 text-white rounded-md text-sm font-medium inline-flex items-center ${
                        !service.parts || service.parts.length === 0 ||
                        isGeneratingInvoice
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-indigo-600 hover:bg-indigo-700"
                      }`}
                      onClick={handleGenerateInvoice}
                      disabled={
                        !service.parts ||
                        service.parts.length === 0 ||
                        isGeneratingInvoice
                      }
                      title={
                        !service.parts || service.parts.length === 0
                          ? "Add parts before generating an invoice"
                          : "Generate invoice for this service"
                      }
                    >
                      {isGeneratingInvoice ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Receipt className="h-4 w-4 mr-1" />
                          {!service.parts || service.parts.length === 0
                            ? "Add Parts First"
                            : "Generate Invoice"}
                        </>
                      )}
                    </button>
                  </div>
                )}

                <div className="mt-8">
                  <h3 className="text-md font-semibold text-gray-900 mb-4">Payment History</h3>
                  {service.payments && service.payments.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                              Method
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">
                              Amount
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {service.payments.map((payment) => (
                            <tr key={payment.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {format(new Date(payment.date), "dd/MM/yyyy")}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {payment.method}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {payment.amount.toFixed(2)} EGP
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="bg-gray-50 p-6 text-center rounded-md">
                      <p className="text-gray-500">No payment history available</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "feedback" && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Time Tracking */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Time Tracking</h2>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Estimated Hours</p>
                    <p className="text-base font-medium text-gray-900">
                      {service.estimatedHours !== null && service.estimatedHours !== undefined
                        ? `${service.estimatedHours} hours`
                        : "Not specified"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Actual Hours</p>
                    <p className="text-base font-medium text-gray-900">
                      {service.actualHours !== null && service.actualHours !== undefined
                        ? `${service.actualHours} hours`
                        : "Not recorded"}
                    </p>
                  </div>
                  {service.estimatedHours && service.actualHours && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Variance</p>
                      <p className={`text-base font-medium ${
                        service.actualHours > service.estimatedHours
                          ? "text-red-600"
                          : service.actualHours < service.estimatedHours
                            ? "text-green-600"
                            : "text-gray-900"
                      }`}>
                        {service.actualHours > service.estimatedHours
                          ? `+${(service.actualHours - service.estimatedHours).toFixed(1)} hours (over estimate)`
                          : service.actualHours < service.estimatedHours
                            ? `-${(service.estimatedHours - service.actualHours).toFixed(1)} hours (under estimate)`
                            : "On target"}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Customer Feedback */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Feedback</h2>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Customer Rating</p>
                    {service.customerRating ? (
                      <div className="flex items-center mt-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            className={`h-5 w-5 ${
                              star <= service.customerRating!
                                ? "text-yellow-400"
                                : "text-gray-300"
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                        <span className="ml-2 text-sm text-gray-600">
                          {service.customerRating}/5
                        </span>
                      </div>
                    ) : (
                      <p className="text-base font-medium text-gray-900">Not rated</p>
                    )}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Customer Feedback</p>
                    {service.customerFeedback ? (
                      <p className="text-base font-medium text-gray-900 p-3 bg-gray-50 rounded-md mt-1">
                        "{service.customerFeedback}"
                      </p>
                    ) : (
                      <p className="text-base font-medium text-gray-900">No feedback provided</p>
                    )}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Customer Signature</p>
                    <p className="text-base font-medium text-gray-900 flex items-center">
                      {service.customerSignature ? (
                        <>
                          <CheckCircle2 className="h-5 w-5 text-green-500 mr-1" />
                          Signature received
                        </>
                      ) : (
                        <>
                          <XCircle className="h-5 w-5 text-red-500 mr-1" />
                          No signature
                        </>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
