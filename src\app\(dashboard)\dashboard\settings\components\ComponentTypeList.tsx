"use client";

import { useState } from "react";

interface ComponentType {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

interface ComponentTypeListProps {
  componentTypes: ComponentType[];
  onEdit: (componentType: ComponentType) => void;
  onDelete: (componentTypeId: string) => void;
  isLoading: boolean;
}

export default function ComponentTypeList({
  componentTypes,
  onEdit,
  onDelete,
  isLoading,
}: ComponentTypeListProps) {
  const [deleteConfirmation, setDeleteConfirmation] = useState<string | null>(null);

  const handleDeleteClick = (id: string) => {
    setDeleteConfirmation(id);
  };

  const confirmDelete = (id: string) => {
    onDelete(id);
    setDeleteConfirmation(null);
  };

  const cancelDelete = () => {
    setDeleteConfirmation(null);
  };

  if (isLoading) {
    return (
      <div className="p-4 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (componentTypes.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No component types found. Add your first component type.
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Name
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created At
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {componentTypes.map((componentType) => (
            <tr key={componentType.id}>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {componentType.name}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {componentType.description || "-"}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {new Date(componentType.createdAt).toLocaleDateString()}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                {deleteConfirmation === componentType.id ? (
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => confirmDelete(componentType.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Confirm
                    </button>
                    <button
                      onClick={cancelDelete}
                      className="text-gray-600 hover:text-gray-900"
                    >
                      Cancel
                    </button>
                  </div>
                ) : (
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => onEdit(componentType)}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteClick(componentType.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </div>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
