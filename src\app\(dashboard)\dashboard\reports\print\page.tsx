"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Printer } from "lucide-react";
import { PrintableDocument } from "@/components/shared/PrintableDocument";
import { PrintButton } from "@/components/shared/PrintButton";
import { formatCurrency, formatDate } from "@/lib/utils";

export default function ReportPrintPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const reportType = searchParams.get("type") || "sales";
  const startDate = searchParams.get("startDate") || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
  const endDate = searchParams.get("endDate") || new Date().toISOString().split('T')[0];
  const branchId = searchParams.get("branchId") || undefined;
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [companyInfo, setCompanyInfo] = useState<any>({
    name: "VERO Company",
    address: "",
    phone: "",
    email: "",
    website: "",
    taxNumber: ""
  });

  // Fetch report data
  useEffect(() => {
    const fetchReportData = async () => {
      setIsLoading(true);
      try {
        // Fetch report data
        const response = await fetch(
          `/api/reports/${reportType}?startDate=${startDate}&endDate=${endDate}${branchId ? `&branchId=${branchId}` : ''}`
        );
        if (!response.ok) {
          throw new Error(`Failed to fetch ${reportType} report`);
        }
        const data = await response.json();
        setReportData(data);
        
        // Fetch company info
        try {
          const settingsResponse = await fetch("/api/settings/company");
          if (settingsResponse.ok) {
            const settingsData = await settingsResponse.json();
            if (settingsData) {
              setCompanyInfo(settingsData);
            }
          }
        } catch (error) {
          // Use default company info if settings can't be fetched
          console.error("Error fetching company info:", error);
        }
      } catch (error: any) {
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReportData();
  }, [reportType, startDate, endDate, branchId]);

  // Prepare print data
  const preparePrintData = () => {
    if (!reportData) return null;
    
    // Generate report HTML based on report type
    let reportHtml = '';
    let reportTitle = '';
    
    switch (reportType) {
      case 'sales':
        reportTitle = 'Sales Report';
        reportHtml = generateSalesReportHtml(reportData);
        break;
      case 'purchases':
        reportTitle = 'Purchases Report';
        reportHtml = generatePurchasesReportHtml(reportData);
        break;
      case 'inventory':
        reportTitle = 'Inventory Report';
        reportHtml = generateInventoryReportHtml(reportData);
        break;
      case 'finance':
        reportTitle = 'Financial Report';
        reportHtml = generateFinanceReportHtml(reportData);
        break;
      default:
        reportTitle = 'Report';
        reportHtml = '<p>No data available</p>';
    }
    
    // Prepare print data
    return {
      id: `${reportType}-${startDate}-${endDate}`,
      company_name: companyInfo.name,
      report_title: reportTitle,
      date: formatDate(new Date()),
      period: `${formatDate(startDate)} - ${formatDate(endDate)}`,
      report_content: reportHtml,
      branch: reportData.branch?.name || 'All Branches',
    };
  };
  
  // Generate sales report HTML
  const generateSalesReportHtml = (data: any) => {
    // Summary section
    const summaryHtml = `
      <div style="display: flex; justify-content: space-between; margin-bottom: 20px; flex-wrap: wrap;">
        <div style="flex: 1; min-width: 200px; background-color: #f3f4f6; padding: 15px; margin: 5px; border-radius: 5px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
          <h3 style="margin: 0 0 10px 0; font-size: 16px;">Total Sales</h3>
          <p style="margin: 0; font-size: 20px; font-weight: bold;">${formatCurrency(data.totalSales)}</p>
        </div>
        <div style="flex: 1; min-width: 200px; background-color: #f3f4f6; padding: 15px; margin: 5px; border-radius: 5px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
          <h3 style="margin: 0 0 10px 0; font-size: 16px;">Paid Sales</h3>
          <p style="margin: 0; font-size: 20px; font-weight: bold;">${formatCurrency(data.paidSales)}</p>
        </div>
        <div style="flex: 1; min-width: 200px; background-color: #f3f4f6; padding: 15px; margin: 5px; border-radius: 5px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
          <h3 style="margin: 0 0 10px 0; font-size: 16px;">Unpaid Sales</h3>
          <p style="margin: 0; font-size: 20px; font-weight: bold;">${formatCurrency(data.unpaidSales)}</p>
        </div>
      </div>
    `;
    
    // Sales by product table
    const salesByProductHtml = data.salesByProduct?.length ? `
      <h3 style="margin: 20px 0 10px 0;">Sales by Product</h3>
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Product</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Quantity</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${data.salesByProduct.map((item: any) => `
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd;">${item.product?.name || 'Unknown Product'}</td>
              <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.quantity}</td>
              <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(item.total)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    ` : '';
    
    // Sales by customer table
    const salesByCustomerHtml = data.salesByCustomer?.length ? `
      <h3 style="margin: 20px 0 10px 0;">Sales by Customer</h3>
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Customer</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Orders</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${data.salesByCustomer.map((item: any) => `
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd;">${item.customer?.name || 'Unknown Customer'}</td>
              <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.orderCount}</td>
              <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${formatCurrency(item.total)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    ` : '';
    
    // Combine all sections
    return `
      ${summaryHtml}
      ${salesByProductHtml}
      ${salesByCustomerHtml}
    `;
  };
  
  // Generate purchases report HTML
  const generatePurchasesReportHtml = (data: any) => {
    // Similar to sales report but for purchases
    // Implementation details would be similar to the sales report
    return `<p>Purchases report content would go here</p>`;
  };
  
  // Generate inventory report HTML
  const generateInventoryReportHtml = (data: any) => {
    // Implementation for inventory report
    return `<p>Inventory report content would go here</p>`;
  };
  
  // Generate finance report HTML
  const generateFinanceReportHtml = (data: any) => {
    // Implementation for finance report
    return `<p>Finance report content would go here</p>`;
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          {error}
        </div>
        <div className="mt-4">
          <Link
            href={`/dashboard/reports/${reportType}`}
            className="text-indigo-600 hover:text-indigo-900 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Reports
          </Link>
        </div>
      </div>
    );
  }

  const printData = preparePrintData();

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6 print:hidden">
        <Link
          href={`/dashboard/reports/${reportType}`}
          className="flex items-center text-indigo-600 hover:text-indigo-900"
        >
          <ArrowLeft className="h-5 w-5 mr-1" />
          Back to Reports
        </Link>
        
        {printData && (
          <PrintButton
            documentType="report"
            documentData={printData}
          />
        )}
      </div>

      {printData && (
        <PrintableDocument
          documentType="report"
          documentData={printData}
          showControls={true}
        />
      )}
    </div>
  );
}
