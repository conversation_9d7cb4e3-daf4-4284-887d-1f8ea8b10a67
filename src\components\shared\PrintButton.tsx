"use client";

import React, { useState } from 'react';
import { Printer, Download, ChevronDown, Loader2 } from 'lucide-react';
import { PrintService } from '@/lib/print-service';

interface PrintButtonProps {
  documentType: string;
  documentData: any;
  className?: string;
  showSaveAsPDF?: boolean;
  language?: string;
  copies?: number;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function PrintButton({
  documentType,
  documentData,
  className = '',
  showSaveAsPDF = true,
  language = 'ar',
  copies = 1,
  onSuccess,
  onError
}: PrintButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const printService = new PrintService();

  const handlePrint = async () => {
    try {
      setIsLoading(true);
      await printService.print(documentType, documentData, { language, copies });
      setIsLoading(false);
      if (onSuccess) onSuccess();
    } catch (error: any) {
      setIsLoading(false);
      console.error('Print error:', error);
      if (onError) onError(error);
      else alert(`Failed to print: ${error.message}`);
    }
  };

  const handleSaveAsPDF = async () => {
    try {
      setIsLoading(true);
      const pdfBlob = await printService.saveToPDF(documentType, documentData, {
        language,
        fileName: `${documentType}_${documentData.id || new Date().getTime()}.pdf`
      });

      // Create a download link
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${documentType}_${documentData.id || new Date().getTime()}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setIsLoading(false);
      if (onSuccess) onSuccess();
    } catch (error: any) {
      setIsLoading(false);
      console.error('PDF error:', error);
      if (onError) onError(error);
      else alert(`Failed to save as PDF: ${error.message}`);
    }
  };

  return (
    <div className="relative print-hidden">
      {showSaveAsPDF ? (
        <div className="flex">
          <button
            onClick={handlePrint}
            disabled={isLoading}
            className={`flex items-center px-3 py-2 bg-indigo-600 text-white rounded-l-md hover:bg-indigo-700 font-medium ${isLoading ? 'opacity-70 cursor-not-allowed' : ''} ${className}`}
          >
            <Printer className="h-4 w-4 mr-2" />
            {isLoading ? 'Processing...' : 'Print'}
          </button>
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="flex items-center px-2 py-2 bg-indigo-600 text-white rounded-r-md hover:bg-indigo-700 border-l border-indigo-500"
          >
            <ChevronDown className="h-4 w-4" />
          </button>

          {isDropdownOpen && (
            <div className="absolute right-0 mt-10 w-48 bg-white rounded-md shadow-lg z-10">
              <ul className="py-1">
                <li>
                  <button
                    onClick={handleSaveAsPDF}
                    disabled={isLoading}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Save as PDF
                  </button>
                </li>
              </ul>
            </div>
          )}
        </div>
      ) : (
        <button
          onClick={handlePrint}
          disabled={isLoading}
          className={`flex items-center px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 font-medium ${isLoading ? 'opacity-70 cursor-not-allowed' : ''} ${className}`}
        >
          <Printer className="h-4 w-4 mr-2" />
          {isLoading ? 'Processing...' : 'Print'}
        </button>
      )}
    </div>
  );
}
