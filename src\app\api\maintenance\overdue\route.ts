import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/maintenance/overdue - Get overdue maintenance services (more than 15 days)
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      console.warn("Unauthorized access attempt to overdue maintenance API");
      return NextResponse.json(
        { error: "Unauthorized", count: 0, data: [] },
        { status: 401 }
      );
    }

    // Check if user has permission to view maintenance services
    let canViewMaintenance = false;

    try {
      // Check if user is admin first (faster check)
      const isAdmin = session.user.role === "ADMIN";

      if (isAdmin) {
        canViewMaintenance = true;
      } else {
        // Only check permissions if not admin
        const hasViewPermission = await hasPermission("view_maintenance");
        canViewMaintenance = hasViewPermission;
      }
    } catch (permError) {
      console.error("Error checking permissions:", permError);
      // Default to no permission on error
      canViewMaintenance = false;
    }

    if (!canViewMaintenance) {
      console.warn(`User ${session.user.email} attempted to access overdue maintenance without permission`);
      return NextResponse.json(
        { error: "You don't have permission to view maintenance services", count: 0, data: [] },
        { status: 403 }
      );
    }

    // Calculate date 15 days ago
    const fifteenDaysAgo = new Date();
    fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);

    // Get overdue maintenance services
    const overdueServices = await db.maintenanceService.findMany({
      where: {
        receivedDate: {
          lte: fifteenDaysAgo,
        },
        status: {
          notIn: ["DELIVERED", "CANCELLED"],
        },
      },
      include: {
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        receivedDate: "asc",
      },
    });

    // Calculate days overdue for each service
    const servicesWithOverdueDays = overdueServices.map(service => {
      const receivedDate = new Date(service.receivedDate);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - receivedDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return {
        ...service,
        daysOverdue: diffDays,
      };
    });

    // Return data in a consistent format with count
    return NextResponse.json({
      count: servicesWithOverdueDays.length,
      data: servicesWithOverdueDays
    });
  } catch (error) {
    console.error("Error fetching overdue maintenance services:", error);
    // Return empty data with error
    return NextResponse.json(
      {
        error: "Failed to fetch overdue maintenance services",
        count: 0,
        data: []
      },
      { status: 500 }
    );
  }
}
