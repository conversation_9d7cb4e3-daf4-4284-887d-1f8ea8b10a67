import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { 
  veroERPKnowledge, 
  getModuleKnowledge, 
  getTableKnowledge, 
  getWorkflowKnowledge,
  searchKnowledgeBase
} from "@/lib/ai-assistant-knowledge-base";
import { updateKnowledgeBase } from "@/lib/ai-assistant-knowledge-updater";

// GET /api/ai-assistant/knowledge - Get knowledge about the system
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    const action = url.searchParams.get("action") || "all";
    const query = url.searchParams.get("query") || "";
    
    let result;
    
    switch (action) {
      case "all":
        result = veroERPKnowledge;
        break;
      case "module":
        if (!query) {
          return NextResponse.json(
            { error: "Module name is required" },
            { status: 400 }
          );
        }
        result = getModuleKnowledge(query);
        if (!result) {
          return NextResponse.json(
            { error: "Module not found" },
            { status: 404 }
          );
        }
        break;
      case "table":
        if (!query) {
          return NextResponse.json(
            { error: "Table name is required" },
            { status: 400 }
          );
        }
        result = getTableKnowledge(query);
        if (!result) {
          return NextResponse.json(
            { error: "Table not found" },
            { status: 404 }
          );
        }
        break;
      case "workflow":
        if (!query) {
          return NextResponse.json(
            { error: "Workflow name is required" },
            { status: 400 }
          );
        }
        result = getWorkflowKnowledge(query);
        if (!result) {
          return NextResponse.json(
            { error: "Workflow not found" },
            { status: 404 }
          );
        }
        break;
      case "search":
        if (!query) {
          return NextResponse.json(
            { error: "Search term is required" },
            { status: 400 }
          );
        }
        result = searchKnowledgeBase(query);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }
    
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error("Error in knowledge API:", error);
    return NextResponse.json(
      { error: "Failed to retrieve knowledge" },
      { status: 500 }
    );
  }
}

// POST /api/ai-assistant/knowledge/update - Update the knowledge base
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admins to update the knowledge base
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }
    
    // Update the knowledge base
    const updatedKnowledge = await updateKnowledgeBase();
    
    return NextResponse.json({ 
      success: true, 
      message: "Knowledge base updated successfully",
      lastUpdated: updatedKnowledge.lastUpdated
    });
  } catch (error) {
    console.error("Error updating knowledge base:", error);
    return NextResponse.json(
      { error: "Failed to update knowledge base" },
      { status: 500 }
    );
  }
}
