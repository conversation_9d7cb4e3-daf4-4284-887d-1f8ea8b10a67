-- Add notification method to MaintenanceService
ALTER TABLE "MaintenanceService" ADD COLUMN "notificationMethod" TEXT DEFAULT 'PHONE';

-- Add payment fields to MaintenanceService
ALTER TABLE "MaintenanceService" ADD COLUMN "paymentMethod" "PaymentMethod";
ALTER TABLE "MaintenanceService" ADD COLUMN "paymentStatus" "PaymentStatus" DEFAULT 'UNPAID';
ALTER TABLE "MaintenanceService" ADD COLUMN "invoiceId" TEXT;
ALTER TABLE "MaintenanceService" ADD COLUMN "invoiceNumber" TEXT;

-- Create MaintenancePayment model
CREATE TABLE "MaintenancePayment" (
    "id" TEXT NOT NULL,
    "maintenanceServiceId" TEXT NOT NULL,
    "method" "PaymentMethod" NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MaintenancePayment_pkey" PRIMARY KEY ("id")
);

-- Create indexes
CREATE INDEX "MaintenancePayment_maintenanceServiceId_idx" ON "MaintenancePayment"("maintenanceServiceId");
CREATE INDEX "MaintenanceService_invoiceId_idx" ON "MaintenanceService"("invoiceId");

-- Add foreign key constraints
ALTER TABLE "MaintenancePayment" ADD CONSTRAINT "MaintenancePayment_maintenanceServiceId_fkey" FOREIGN KEY ("maintenanceServiceId") REFERENCES "MaintenanceService"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "MaintenanceService" ADD CONSTRAINT "MaintenanceService_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "Sale"("id") ON DELETE SET NULL ON UPDATE CASCADE;
