import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { z } from "zod";

// Schema for validating component type data
const componentTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
});

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const componentTypeId = params.id;

  try {
    // Fetch the component type
    const componentType = await db.componentType.findUnique({
      where: {
        id: componentTypeId,
      },
    });

    if (!componentType) {
      return NextResponse.json(
        { error: "Component type not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(componentType);
  } catch (error) {
    console.error(`Error fetching component type ${componentTypeId}:`, error);
    return NextResponse.json(
      { error: "Failed to fetch component type" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  const componentTypeId = params.id;

  try {
    // Check if the component type exists
    const existingComponentType = await db.componentType.findUnique({
      where: {
        id: componentTypeId,
      },
    });

    if (!existingComponentType) {
      return NextResponse.json(
        { error: "Component type not found" },
        { status: 404 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = componentTypeSchema.safeParse(body);
    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors
        .map((err) => err.message)
        .join(", ");
      return NextResponse.json(
        { error: `Validation error: ${errorMessage}` },
        { status: 400 }
      );
    }

    const { name, description } = validationResult.data;

    // Check if another component type with the same name already exists
    const duplicateComponentType = await db.componentType.findFirst({
      where: {
        name,
        id: {
          not: componentTypeId,
        },
      },
    });

    if (duplicateComponentType) {
      return NextResponse.json(
        { error: "Another component type with this name already exists" },
        { status: 400 }
      );
    }

    // Update the component type
    const updatedComponentType = await db.componentType.update({
      where: { id: componentTypeId },
      data: {
        name,
        description: description || "",
      },
    });

    return NextResponse.json(updatedComponentType);
  } catch (error) {
    console.error(`Error updating component type ${componentTypeId}:`, error);
    return NextResponse.json(
      { error: "Failed to update component type" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const componentTypeId = params.id;

  try {
    // Check if the component type exists
    const existingComponentType = await db.componentType.findUnique({
      where: {
        id: componentTypeId,
      },
    });

    if (!existingComponentType) {
      return NextResponse.json(
        { error: "Component type not found" },
        { status: 404 }
      );
    }

    // Delete the component type
    await db.componentType.delete({
      where: {
        id: componentTypeId,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`Error deleting component type ${componentTypeId}:`, error);
    return NextResponse.json(
      { error: "Failed to delete component type" },
      { status: 500 }
    );
  }
}
