"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Loader2, ArrowRight, TrendingUp, TrendingDown, DollarSign,
  CreditCard, FileText, BarChart3, PieChart, LineChart,
  ReceiptText, Wallet, Building, Users, ShoppingCart,
  ArrowUpRight, ArrowDownLeft
} from "lucide-react";
import { format, subMonths, subDays } from "date-fns";
import Link from "next/link";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, <PERSON>, Pie, Doughnut } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface AccountSummary {
  type: string;
  totalBalance: number;
  accounts: {
    id: string;
    code: string;
    name: string;
    balance: number;
  }[];
}

interface JournalSummary {
  id: string;
  code: string;
  name: string;
  type: string;
  entryCount: number;
  totalAmount: number;
}

export default function AccountingDashboardPage() {
  const [period, setPeriod] = useState<"week" | "month" | "quarter" | "year">("month");
  const [asOfDate, setAsOfDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState(false);
  const [accountSummaries, setAccountSummaries] = useState<Record<string, AccountSummary>>({});
  const [journalSummaries, setJournalSummaries] = useState<JournalSummary[]>([]);
  const [recentEntries, setRecentEntries] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("overview");

  // Calculate start date based on period
  const getStartDate = () => {
    if (!asOfDate) return null;

    switch (period) {
      case "week":
        return subDays(asOfDate, 7);
      case "month":
        return subMonths(asOfDate, 1);
      case "quarter":
        return subMonths(asOfDate, 3);
      case "year":
        return subMonths(asOfDate, 12);
      default:
        return subMonths(asOfDate, 1);
    }
  };

  // Generate chart data for revenue vs expenses
  const generateRevenueExpenseChartData = () => {
    const labels = [];
    const revenueData = [];
    const expenseData = [];

    // Generate labels based on period
    const startDate = getStartDate();
    if (!startDate || !asOfDate) return { labels: [], datasets: [] };

    let currentDate = new Date(startDate);

    // Generate data points based on period
    while (currentDate <= asOfDate) {
      // Format label based on period
      let label = '';
      if (period === 'week') {
        label = format(currentDate, 'EEE');
      } else if (period === 'month') {
        label = format(currentDate, 'd MMM');
      } else if (period === 'quarter') {
        label = format(currentDate, 'MMM');
      } else {
        label = format(currentDate, 'MMM');
      }

      labels.push(label);

      // Generate random data for now - in a real app, this would come from the API
      const revenue = Math.floor(Math.random() * 10000) + 5000;
      const expense = Math.floor(Math.random() * 8000) + 3000;

      revenueData.push(revenue);
      expenseData.push(expense);

      // Increment date based on period
      if (period === 'week') {
        currentDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
      } else if (period === 'month') {
        currentDate = new Date(currentDate.setDate(currentDate.getDate() + 3));
      } else if (period === 'quarter') {
        currentDate = new Date(currentDate.setDate(currentDate.getDate() + 7));
      } else {
        currentDate = new Date(currentDate.setMonth(currentDate.getMonth() + 1));
      }
    }

    return {
      labels,
      datasets: [
        {
          label: 'Revenue',
          data: revenueData,
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          fill: true,
          tension: 0.4,
        },
        {
          label: 'Expenses',
          data: expenseData,
          borderColor: 'rgb(239, 68, 68)',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          fill: true,
          tension: 0.4,
        }
      ]
    };
  };

  // Generate chart data for account balances
  const generateAccountBalanceChartData = () => {
    const labels = [];
    const data = [];
    const backgroundColors = [
      'rgba(54, 162, 235, 0.8)',
      'rgba(255, 99, 132, 0.8)',
      'rgba(75, 192, 192, 0.8)',
      'rgba(255, 206, 86, 0.8)',
      'rgba(153, 102, 255, 0.8)',
      'rgba(255, 159, 64, 0.8)',
    ];

    // Get account types and their balances
    Object.keys(accountSummaries).forEach((type, index) => {
      labels.push(type);
      data.push(accountSummaries[type].totalBalance);
    });

    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(color => color.replace('0.8', '1')),
          borderWidth: 1,
        }
      ]
    };
  };

  // Generate chart data for journal activity
  const generateJournalActivityChartData = () => {
    return {
      labels: journalSummaries.map(journal => journal.name),
      datasets: [
        {
          label: 'Number of Entries',
          data: journalSummaries.map(journal => journal.entryCount),
          backgroundColor: 'rgba(54, 162, 235, 0.8)',
        }
      ]
    };
  };

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    if (!asOfDate) return;

    setIsLoading(true);
    try {
      const startDate = getStartDate();

      // Build query parameters
      const params = new URLSearchParams();
      params.append("asOfDate", asOfDate.toISOString());
      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }
      params.append("period", period);

      const response = await fetch(`/api/accounting/dashboard?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setAccountSummaries(data.accountSummaries || {});
        setJournalSummaries(data.journalSummaries || []);
        setRecentEntries(data.recentEntries || []);
      }
    } catch (error) {
      console.error("Error fetching accounting dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when period or date changes
  useEffect(() => {
    fetchDashboardData();
  }, [period, asOfDate]);

  // Format currency
  const formatCurrency = (amount: number) => {
    // Format with Egyptian Pound symbol
    return `EGP ${amount.toFixed(2)}`;
  };

  // Get total for account type
  const getAccountTypeTotal = (type: string) => {
    return accountSummaries[type]?.totalBalance || 0;
  };

  // Calculate net income (Revenue - Expenses)
  const netIncome = getAccountTypeTotal("REVENUE") - getAccountTypeTotal("EXPENSE");

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Accounting Dashboard</h1>
        <div className="flex items-center space-x-4">
          <DatePicker date={asOfDate} setDate={setAsOfDate} />
          <select
            className="rounded-md border border-gray-300 p-2"
            value={period}
            onChange={(e) => setPeriod(e.target.value as "month" | "quarter" | "year")}
          >
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
            <option value="year">Last Year</option>
          </select>
          <Button onClick={fetchDashboardData} disabled={isLoading}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Refresh"}
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500 text-lg">Loading dashboard data...</span>
        </div>
      ) : (
        <>
          {/* Financial Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Revenue</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(getAccountTypeTotal("REVENUE"))}</h3>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <Link href="/dashboard/accounting/general-ledger" className="text-sm text-blue-600 flex items-center">
                    View Details <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Expenses</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(getAccountTypeTotal("EXPENSE"))}</h3>
                  </div>
                  <div className="p-3 bg-red-100 rounded-full">
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <Link href="/dashboard/accounting/general-ledger" className="text-sm text-blue-600 flex items-center">
                    View Details <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Net Income</p>
                    <h3 className={`text-2xl font-bold mt-1 ${netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(netIncome)}
                    </h3>
                  </div>
                  <div className={`p-3 ${netIncome >= 0 ? 'bg-green-100' : 'bg-red-100'} rounded-full`}>
                    {netIncome >= 0 ? (
                      <DollarSign className="h-6 w-6 text-green-600" />
                    ) : (
                      <DollarSign className="h-6 w-6 text-red-600" />
                    )}
                  </div>
                </div>
                <div className="mt-4">
                  <Link href="/dashboard/accounting/profit-loss" className="text-sm text-blue-600 flex items-center">
                    View Profit & Loss <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Assets</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(getAccountTypeTotal("ASSET"))}</h3>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <Link href="/dashboard/accounting/balance-sheet" className="text-sm text-blue-600 flex items-center">
                    View Balance Sheet <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Journal Summaries */}
          <Card>
            <CardHeader>
              <CardTitle>Journal Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-gray-100 border-b">
                      <th className="px-4 py-3 text-left">Journal</th>
                      <th className="px-4 py-3 text-left">Type</th>
                      <th className="px-4 py-3 text-right">Entries</th>
                      <th className="px-4 py-3 text-right">Total Amount</th>
                      <th className="px-4 py-3 text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {journalSummaries.length > 0 ? (
                      journalSummaries.map(journal => (
                        <tr key={journal.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{journal.code} - {journal.name}</td>
                          <td className="px-4 py-3">{journal.type}</td>
                          <td className="px-4 py-3 text-right">{journal.entryCount}</td>
                          <td className="px-4 py-3 text-right">{formatCurrency(journal.totalAmount)}</td>
                          <td className="px-4 py-3 text-center">
                            <Link href={`/dashboard/accounting/journals?journal=${journal.id}`}>
                              <Button variant="outline" size="sm">View Entries</Button>
                            </Link>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="px-4 py-3 text-center text-gray-500">No journal activity found for the selected period.</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Quick Links */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/dashboard/accounting/payment-vouchers">
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <ArrowUpRight className="h-12 w-12 text-red-600 mb-4" />
                  <h3 className="text-lg font-medium text-center">Payment Vouchers</h3>
                  <p className="text-sm text-gray-500 text-center mt-2">Create and manage payment vouchers (إذن صرف)</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dashboard/accounting/receipt-vouchers">
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <ArrowDownLeft className="h-12 w-12 text-green-600 mb-4" />
                  <h3 className="text-lg font-medium text-center">Receipt Vouchers</h3>
                  <p className="text-sm text-gray-500 text-center mt-2">Create and manage receipt vouchers (إذن استلام)</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dashboard/accounting/payment-journals">
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <Wallet className="h-12 w-12 text-blue-600 mb-4" />
                  <h3 className="text-lg font-medium text-center">Payment Journals</h3>
                  <p className="text-sm text-gray-500 text-center mt-2">View payment method journals and transactions</p>
                </CardContent>
              </Card>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
            <Link href="/dashboard/accounting/journals">
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <FileText className="h-12 w-12 text-indigo-600 mb-4" />
                  <h3 className="text-lg font-medium text-center">Journal Entries</h3>
                  <p className="text-sm text-gray-500 text-center mt-2">Create and manage journal entries</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dashboard/accounting/general-ledger">
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <FileText className="h-12 w-12 text-green-600 mb-4" />
                  <h3 className="text-lg font-medium text-center">General Ledger</h3>
                  <p className="text-sm text-gray-500 text-center mt-2">View account transactions and balances</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dashboard/accounting/trial-balance">
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <CreditCard className="h-12 w-12 text-purple-600 mb-4" />
                  <h3 className="text-lg font-medium text-center">Trial Balance</h3>
                  <p className="text-sm text-gray-500 text-center mt-2">View account balances and verify accounting</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dashboard/accounting/payment-methods">
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <CreditCard className="h-12 w-12 text-orange-600 mb-4" />
                  <h3 className="text-lg font-medium text-center">Payment Methods</h3>
                  <p className="text-sm text-gray-500 text-center mt-2">Manage payment methods and their accounts</p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </>
      )}
    </div>
  );
}
