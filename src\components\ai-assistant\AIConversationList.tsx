"use client";

import { useState } from "react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { MessageSquare, Plus, Trash2, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface Conversation {
  id: string;
  title: string | null;
  createdAt: string;
  isActive: boolean;
}

interface AIConversationListProps {
  conversations: Conversation[];
  activeConversationId: string | null;
  onSelectConversation: (id: string) => void;
  onNewConversation: () => void;
  onClose: () => void;
}

export function AIConversationList({
  conversations,
  activeConversationId,
  onSelectConversation,
  onNewConversation,
  onClose,
}: AIConversationListProps) {
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const handleDeleteConversation = async (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (isDeleting) return;
    
    try {
      setIsDeleting(id);
      const response = await fetch(`/api/ai-assistant/conversations/${id}`, {
        method: "DELETE",
      });
      
      if (response.ok) {
        toast.success("تم حذف المحادثة بنجاح");
        // If the deleted conversation was active, create a new one
        if (id === activeConversationId) {
          onNewConversation();
        }
      } else {
        toast.error("حدث خطأ أثناء حذف المحادثة");
      }
    } catch (error) {
      console.error("Error deleting conversation:", error);
      toast.error("حدث خطأ أثناء حذف المحادثة");
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium text-lg">المحادثات</h3>
        <button
          onClick={onClose}
          className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      <Button
        onClick={onNewConversation}
        className="w-full mb-4 flex items-center justify-center gap-2"
      >
        <Plus className="h-4 w-4" />
        <span>محادثة جديدة</span>
      </Button>

      <div className="space-y-2 max-h-[400px] overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            لا توجد محادثات سابقة
          </div>
        ) : (
          conversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => onSelectConversation(conversation.id)}
              className={`p-3 rounded-md cursor-pointer flex items-center justify-between group ${
                activeConversationId === conversation.id
                  ? "bg-blue-100"
                  : "hover:bg-gray-100"
              }`}
            >
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="font-medium truncate max-w-[180px]">
                    {conversation.title || "محادثة جديدة"}
                  </p>
                  <p className="text-xs text-gray-500">
                    {format(new Date(conversation.createdAt), "d MMMM yyyy", { locale: ar })}
                  </p>
                </div>
              </div>
              
              <button
                onClick={(e) => handleDeleteConversation(conversation.id, e)}
                className={`p-1 rounded-md opacity-0 group-hover:opacity-100 transition-opacity ${
                  isDeleting === conversation.id ? "opacity-100" : ""
                }`}
                disabled={isDeleting === conversation.id}
              >
                <Trash2 className="h-4 w-4 text-red-500" />
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
