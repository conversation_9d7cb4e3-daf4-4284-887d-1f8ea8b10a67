"use client";

import { useState } from "react";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Download, Upload, FileSpreadsheet, AlertCircle, CheckCircle2 } from "lucide-react";

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
  parentId: string | null;
  balance: number;
  isActive: boolean;
}

interface AccountsExportImportProps {
  accounts: Account[];
  onImportComplete: () => void;
}

export default function AccountsExportImport({ accounts, onImportComplete }: AccountsExportImportProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importValidation, setImportValidation] = useState<{
    isValid: boolean;
    message: string;
    data?: any[];
  } | null>(null);

  // Export accounts to Excel
  const exportToExcel = async () => {
    try {
      setIsExporting(true);

      // Prepare data for export
      const exportData = accounts.map(account => ({
        Code: account.code,
        Name: account.name,
        Type: account.type,
        ParentCode: accounts.find(a => a.id === account.parentId)?.code || "",
        Balance: account.balance,
        IsActive: account.isActive ? "Yes" : "No"
      }));

      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      
      // Add column widths
      const columnWidths = [
        { wch: 10 }, // Code
        { wch: 30 }, // Name
        { wch: 15 }, // Type
        { wch: 15 }, // ParentCode
        { wch: 15 }, // Balance
        { wch: 10 }, // IsActive
      ];
      worksheet["!cols"] = columnWidths;

      // Create workbook
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Accounts");

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
      const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      
      // Save file
      saveAs(blob, `accounts_export_${new Date().toISOString().split("T")[0]}.xlsx`);
      
      toast.success("Accounts exported successfully");
    } catch (error) {
      console.error("Error exporting accounts:", error);
      toast.error("Failed to export accounts");
    } finally {
      setIsExporting(false);
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const data = new Uint8Array(event.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        
        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        // Validate data
        validateImportData(jsonData);
      } catch (error) {
        console.error("Error reading Excel file:", error);
        setImportValidation({
          isValid: false,
          message: "Invalid Excel file format"
        });
      }
    };
    reader.readAsArrayBuffer(file);
  };

  // Validate import data
  const validateImportData = (data: any[]) => {
    if (!data || data.length === 0) {
      setImportValidation({
        isValid: false,
        message: "No data found in the Excel file"
      });
      return;
    }

    // Check required fields
    const requiredFields = ["Code", "Name", "Type"];
    const missingFields = requiredFields.filter(field => 
      !data.every(item => item[field] !== undefined && item[field] !== "")
    );

    if (missingFields.length > 0) {
      setImportValidation({
        isValid: false,
        message: `Missing required fields: ${missingFields.join(", ")}`
      });
      return;
    }

    // Check for duplicate codes
    const codes = data.map(item => item.Code);
    const hasDuplicates = codes.some((code, index) => codes.indexOf(code) !== index);
    
    if (hasDuplicates) {
      setImportValidation({
        isValid: false,
        message: "Duplicate account codes found in the import file"
      });
      return;
    }

    // Valid data
    setImportValidation({
      isValid: true,
      message: `${data.length} accounts ready to import`,
      data
    });
  };

  // Import accounts
  const importAccounts = async () => {
    if (!importValidation?.isValid || !importValidation.data) {
      return;
    }

    try {
      setIsImporting(true);

      // Transform data for API
      const importData = importValidation.data.map(item => ({
        code: item.Code,
        name: item.Name,
        type: item.Type,
        parentCode: item.ParentCode || null,
        balance: parseFloat(item.Balance) || 0,
        isActive: item.IsActive === "Yes" || item.IsActive === true
      }));

      // Call API to import accounts
      const response = await fetch("/api/accounting/accounts/import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ accounts: importData }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to import accounts");
      }

      toast.success("Accounts imported successfully");
      setImportValidation(null);
      onImportComplete();
    } catch (error: any) {
      console.error("Error importing accounts:", error);
      toast.error(error.message || "Failed to import accounts");
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center">
          <FileSpreadsheet className="h-5 w-5 mr-2 text-green-600" />
          Export & Import Accounts / تصدير واستيراد الحسابات
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Export Section */}
          <div className="space-y-4">
            <h3 className="font-medium">Export Accounts / تصدير الحسابات</h3>
            <p className="text-sm text-gray-500">
              Export all accounts to an Excel file that you can edit or backup.
            </p>
            <Button 
              onClick={exportToExcel} 
              disabled={isExporting || accounts.length === 0}
              className="w-full"
            >
              {isExporting ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export to Excel ({accounts.length} accounts)
                </>
              )}
            </Button>
          </div>

          {/* Import Section */}
          <div className="space-y-4">
            <h3 className="font-medium">Import Accounts / استيراد الحسابات</h3>
            <p className="text-sm text-gray-500">
              Import accounts from an Excel file. The file should have the same format as the exported file.
            </p>
            <div className="flex flex-col space-y-2">
              <Input 
                type="file" 
                accept=".xlsx, .xls" 
                onChange={handleFileUpload}
                disabled={isImporting}
                className="w-full"
              />
              
              {importValidation && (
                <div className={`mt-2 p-3 rounded-md text-sm ${
                  importValidation.isValid ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                }`}>
                  <div className="flex items-start">
                    {importValidation.isValid ? (
                      <CheckCircle2 className="h-5 w-5 mr-2 flex-shrink-0" />
                    ) : (
                      <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                    )}
                    <div>
                      <p className="font-medium">{importValidation.message}</p>
                      {importValidation.isValid && (
                        <Button 
                          onClick={importAccounts} 
                          disabled={isImporting}
                          className="mt-2"
                          size="sm"
                        >
                          {isImporting ? (
                            <>
                              <span className="animate-spin mr-2">⏳</span>
                              Importing...
                            </>
                          ) : (
                            <>
                              <Upload className="h-4 w-4 mr-2" />
                              Confirm Import
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
