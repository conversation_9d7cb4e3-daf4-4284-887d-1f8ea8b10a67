"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Metadata } from "next";

export default function SalesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const tabs = [
    {
      id: 'dashboard',
      name: "Dashboard",
      href: "/dashboard/sales",
      current: pathname === "/dashboard/sales" && !pathname.includes("/orders") && !pathname.includes("/credit-note") && !pathname.includes("/reports"),
      icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
    },
    {
      id: 'orders',
      name: "Orders",
      href: "/dashboard/sales/orders",
      current: pathname.includes("/dashboard/sales/orders"),
      icon: "M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
    },
    {
      id: 'credit-notes',
      name: "Credit Notes",
      href: "/dashboard/sales/credit-note",
      current: pathname.includes("/dashboard/sales/credit-note"),
      icon: "M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"
    },
    {
      id: 'reports',
      name: "Reports",
      href: "/dashboard/sales/reports",
      current: pathname.includes("/dashboard/sales/reports"),
      icon: "M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
    },
    {
      id: 'new-sale',
      name: "New Sale",
      href: "/dashboard/sales/new",
      current: pathname === "/dashboard/sales/new",
      icon: "M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
    },
  ];

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200">
        {/* Desktop Navigation */}
        <div className="hidden md:block">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex flex-wrap px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <Link
                  key={tab.id}
                  href={tab.href}
                  className={`
                    ${tab.current
                      ? 'border-[#3895e7] text-[#3895e7]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                    whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm flex items-center mr-6 mb-1
                  `}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 flex-shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d={tab.icon}
                    />
                  </svg>
                  <span>{tab.name}</span>
                </Link>
              ))}
            </nav>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden">
          <select
            value={tabs.find(tab => tab.current)?.id || ''}
            onChange={(e) => {
              const selectedTab = tabs.find(tab => tab.id === e.target.value);
              if (selectedTab) {
                window.location.href = selectedTab.href;
              }
            }}
            className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-[#3895e7] focus:border-[#3895e7] sm:text-sm"
          >
            {tabs.map((tab) => (
              <option key={tab.id} value={tab.id}>
                {tab.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="px-4 py-5 sm:px-6">
        {children}
      </div>
    </div>
  );
}
