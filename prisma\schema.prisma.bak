generator client {
  provider = "prisma-client-js"
}

generator ts_node {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                      String                    @id @default(uuid())
  email                   String                    @unique
  name                    String
  password                String
  role                    Role                      @default(EMPLOYEE)
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  branchId                String?
  isActive                Boolean                   @default(true)
  purchases               Purchase[]
  sales                   Sale[]
  creditNotes             CreditNote[]
  branch                  Branch?                   @relation(fields: [branchId], references: [id])
  warehouses              UserWarehouse[]
  permissions             Permission[]              @relation("PermissionToUser")
  inventoryTransfers      InventoryTransfer[]
  maintenanceServices     MaintenanceService[]
  maintenanceStatusHistory MaintenanceStatusHistory[]
}

model UserWarehouse {
  id          String    @id @default(uuid())
  userId      String
  warehouseId String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)

  @@unique([userId, warehouseId])
}

model Permission {
  id          String   @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]   @relation("PermissionToUser")
}

model Branch {
  id                 String              @id @default(uuid())
  name               String
  address            String
  phone              String
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  code               String              @unique
  isActive           Boolean             @default(true)
  purchases          Purchase[]
  sales              Sale[]
  creditNotes        CreditNote[]
  users              User[]
  warehouses         Warehouse[]
  accounts           Account[]
  journals           Journal[]
  maintenanceServices MaintenanceService[]
}

model Warehouse {
  id                      String              @id @default(uuid())
  name                    String
  branchId                String
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt
  isActive                Boolean             @default(true)
  inventory               Inventory[]
  users                   UserWarehouse[]
  branch                  Branch              @relation(fields: [branchId], references: [id])
  sourceTransfers         InventoryTransfer[] @relation("SourceWarehouse")
  destinationTransfers    InventoryTransfer[] @relation("DestinationWarehouse")
}

model Product {
  id                  String                 @id @default(uuid())
  name                String
  description         String?
  basePrice           Float
  categoryId          String
  createdAt           DateTime               @default(now())
  updatedAt           DateTime               @updatedAt
  componentType       String?
  isComponent         Boolean                @default(false)
  isCustomizable      Boolean                @default(false)
  costPrice           Float                  @default(0)
  inventory           Inventory[]
  category            Category               @relation(fields: [categoryId], references: [id])
  purchaseItems       PurchaseItem[]
  saleItems           SaleItem[]
  creditNoteItems     CreditNoteItem[]
  specifications      Specification[]
  transferItems       InventoryTransferItem[]
  maintenanceParts    MaintenancePart[]
}

model Category {
  id          String    @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Specification {
  id        String   @id @default(uuid())
  name      String
  value     String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id])
}

model Inventory {
  id          String    @id @default(uuid())
  productId   String
  warehouseId String
  quantity    Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  costPrice   Float     @default(0)
  product     Product   @relation(fields: [productId], references: [id])
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])
}

model Sale {
  id             String        @id @default(uuid())
  userId         String
  branchId       String
  date           DateTime      @default(now())
  status         SaleStatus    @default(PENDING)
  totalAmount    Float
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  applyTax       Boolean       @default(false)
  currency       String        @default("EGP")
  discountAmount Float         @default(0)
  notes          String?
  subtotalAmount Float
  taxAmount      Float         @default(0)
  taxRate        Float         @default(0)
  invoiceNumber  String        @unique
  paymentMethod  PaymentMethod @default(CASH)
  paymentStatus  PaymentStatus @default(UNPAID)
  contactId      String
  branch         Branch        @relation(fields: [branchId], references: [id])
  contact        Contact       @relation(fields: [contactId], references: [id])
  user           User          @relation(fields: [userId], references: [id])
  items          SaleItem[]
}

model SaleItem {
  id             String              @id @default(uuid())
  saleId         String
  productId      String
  quantity       Int
  unitPrice      Float
  totalPrice     Float
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  isCustomized   Boolean             @default(false)
  specifications String?
  product        Product             @relation(fields: [productId], references: [id])
  sale           Sale                @relation(fields: [saleId], references: [id])
  components     SaleItemComponent[]
}

model SaleItemComponent {
  id            String   @id @default(uuid())
  saleItemId    String
  componentId   String
  componentName String
  componentType String
  quantity      Int
  totalQuantity Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  saleItem      SaleItem @relation(fields: [saleItemId], references: [id])
}

model Purchase {
  id             String         @id @default(uuid())
  userId         String
  branchId       String
  date           DateTime       @default(now())
  status         PurchaseStatus @default(PENDING)
  totalAmount    Float
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  contactId      String
  currency       String         @default("EGP")
  discountAmount Float          @default(0)
  invoiceNumber  String         @unique
  notes          String?
  paymentMethod  PaymentMethod  @default(CASH)
  paymentStatus  PaymentStatus  @default(UNPAID)
  subtotalAmount Float
  taxAmount      Float          @default(0)
  branch         Branch         @relation(fields: [branchId], references: [id])
  contact        Contact        @relation(fields: [contactId], references: [id])
  user           User           @relation(fields: [userId], references: [id])
  items          PurchaseItem[]
}

model PurchaseItem {
  id         String   @id @default(uuid())
  purchaseId String
  productId  String
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  product    Product  @relation(fields: [productId], references: [id])
  purchase   Purchase @relation(fields: [purchaseId], references: [id])
}

model Contact {
  id                 String              @id @default(uuid())
  name               String
  phone              String              @unique
  address            String?
  email              String?
  isCustomer         Boolean             @default(false)
  isSupplier         Boolean             @default(false)
  balance            Float               @default(0)
  creditLimit        Float               @default(0)
  creditPeriod       Int                 @default(30) // Credit period in days
  lastReminderDate   DateTime?
  isActive           Boolean             @default(true)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  openingBalance     Float               @default(0)
  openingBalanceDate DateTime            @default(now())
  purchases          Purchase[]
  sales              Sale[]
  creditNotes        CreditNote[]
  transactions       Transaction[]
  journalEntries     JournalEntry[]
  maintenanceServices MaintenanceService[]
}

enum Role {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum PaymentMethod {
  CASH
  VODAFONE_CASH
  BANK_TRANSFER
  CREDIT_CARD
  CUSTOMER_ACCOUNT
}

enum PaymentStatus {
  PAID
  UNPAID
  PARTIALLY_PAID
}

enum SaleStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum PurchaseStatus {
  PENDING
  COMPLETED
  CANCELLED
}

model CreditNote {
  id                  String           @id @default(uuid())
  userId              String
  contactId           String
  branchId            String
  originalInvoiceId   String?
  originalInvoiceNumber String?
  creditNoteNumber    String           @unique
  date                DateTime         @default(now())
  status              CreditNoteStatus @default(PENDING)
  paymentStatus       PaymentStatus    @default(UNPAID)
  paymentMethod       PaymentMethod?
  totalAmount         Float
  subtotalAmount      Float
  taxAmount           Float            @default(0)
  taxRate             Float            @default(0)
  discountAmount      Float            @default(0)
  applyTax            Boolean          @default(false)
  currency            String           @default("EGP")
  notes               String?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  user                User             @relation(fields: [userId], references: [id])
  contact             Contact          @relation(fields: [contactId], references: [id])
  branch              Branch           @relation(fields: [branchId], references: [id])
  items               CreditNoteItem[]
  payments            CreditNotePayment[]
}

model CreditNoteItem {
  id                  String     @id @default(uuid())
  creditNoteId        String
  productId           String
  quantity            Int
  unitPrice           Float
  totalPrice          Float
  reason              String
  originalItemId      String?
  createdAt           DateTime   @default(now())
  updatedAt           DateTime   @updatedAt
  creditNote          CreditNote @relation(fields: [creditNoteId], references: [id])
  product             Product    @relation(fields: [productId], references: [id])
}

enum CreditNoteStatus {
  PENDING
  COMPLETED
  CANCELLED
}

model CreditNotePayment {
  id            String        @id @default(uuid())
  creditNoteId  String
  method        PaymentMethod
  amount        Float
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  creditNote    CreditNote    @relation(fields: [creditNoteId], references: [id])
}

model Transaction {
  id                String            @id @default(uuid())
  transactionNumber String            @unique
  date              DateTime          @default(now())
  description       String
  amount            Float
  type              TransactionType
  referenceType     ReferenceType
  reference         String?
  paymentMethod     PaymentMethod?
  contactId         String
  accountId         String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  contact           Contact           @relation(fields: [contactId], references: [id])
  account           Account?          @relation(fields: [accountId], references: [id])
}

model Account {
  id                  String                @id @default(uuid())
  code                String                @unique // رمز الحساب (مثل 1000، 2000)
  name                String
  accountNumber       String                @unique
  type                AccountType
  balance             Float                 @default(0)
  isActive            Boolean               @default(true)
  isDefault           Boolean               @default(false) // هل هو حساب افتراضي
  parentId            String?
  branchId            String?
  parent              Account?              @relation("AccountToAccount", fields: [parentId], references: [id])
  children            Account[]             @relation("AccountToAccount")
  branch              Branch?               @relation(fields: [branchId], references: [id])
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  transactions        Transaction[]
  debitEntries        JournalEntry[]        @relation("DebitEntries")
  creditEntries       JournalEntry[]        @relation("CreditEntries")
  paymentMethodSettings PaymentMethodSettings[]

  @@index([code])
  @@index([type])
  @@index([isActive])
  @@index([branchId])
}

enum TransactionType {
  DEBIT
  CREDIT
}

enum ReferenceType {
  SALE
  PURCHASE
  RECEIPT
  PAYMENT
  CREDIT_NOTE
  OPENING_BALANCE
  SALES_INVOICE
  PURCHASE_INVOICE
  SALES_RETURN
  PURCHASE_RETURN
  EXPENSE
  INCOME
  TRANSFER
  ADJUSTMENT
  MANUAL
  OTHER
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

model Journal {
  id                  String                @id @default(uuid())
  name                String
  code                String                @unique
  type                String
  paymentMethod       String?
  branchId            String?
  isActive            Boolean               @default(true)
  branch              Branch?               @relation(fields: [branchId], references: [id])
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  entries             JournalEntry[]
  paymentMethodSettings PaymentMethodSettings[]
}

model JournalEntry {
  id              String        @id @default(uuid())
  journalId       String
  entryNumber     String?       // رقم القيد
  date            DateTime      @default(now())
  description     String
  reference       String?       // مرجع (مثل رقم فاتورة)
  referenceType   ReferenceType? // نوع المرجع (مبيعات، مشتريات، إلخ)
  contactId       String?
  debitAccountId  String
  creditAccountId String
  amount          Float
  fiscalPeriodId  String?
  isPosted        Boolean       @default(false) // هل تم ترحيل القيد
  branchId        String?       // الفرع المرتبط بالقيد
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  journal         Journal       @relation(fields: [journalId], references: [id])
  contact         Contact?      @relation(fields: [contactId], references: [id])
  debitAccount    Account       @relation("DebitEntries", fields: [debitAccountId], references: [id])
  creditAccount   Account       @relation("CreditEntries", fields: [creditAccountId], references: [id])
  fiscalPeriod    FiscalPeriod? @relation(fields: [fiscalPeriodId], references: [id])
  branch          Branch?       @relation(fields: [branchId], references: [id])

  @@index([entryNumber])
  @@index([date])
  @@index([referenceType])
  @@index([isPosted])
  @@index([branchId])
}

model FiscalYear {
  id          String        @id @default(uuid())
  name        String
  startDate   DateTime
  endDate     DateTime
  isClosed    Boolean       @default(false)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  periods     FiscalPeriod[]
}

model FiscalPeriod {
  id            String        @id @default(uuid())
  fiscalYearId  String
  name          String
  startDate     DateTime
  endDate       DateTime
  isClosed      Boolean       @default(false)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  fiscalYear    FiscalYear    @relation(fields: [fiscalYearId], references: [id])
  journalEntries JournalEntry[]
}

model InventoryTransfer {
  id                    String                  @id @default(uuid())
  referenceNumber       String                  @unique
  sourceWarehouseId     String
  destinationWarehouseId String
  date                  DateTime                @default(now())
  notes                 String?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  userId                String
  sourceWarehouse       Warehouse               @relation("SourceWarehouse", fields: [sourceWarehouseId], references: [id])
  destinationWarehouse  Warehouse               @relation("DestinationWarehouse", fields: [destinationWarehouseId], references: [id])
  user                  User                    @relation(fields: [userId], references: [id])
  items                 InventoryTransferItem[]
}

model InventoryTransferItem {
  id                   String            @id @default(uuid())
  inventoryTransferId  String
  productId            String
  quantity             Int
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  inventoryTransfer    InventoryTransfer @relation(fields: [inventoryTransferId], references: [id], onDelete: Cascade)
  product              Product           @relation(fields: [productId], references: [id])
}

// Maintenance Module Models

enum MaintenanceStatus {
  RECEIVED
  IN_PROGRESS
  WAITING_FOR_PARTS
  COMPLETED
  DELIVERED
  CANCELLED
  REJECTED_BY_CUSTOMER
}

enum MaintenancePriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

model MaintenanceService {
  id                     String                    @id @default(uuid())
  serviceNumber          String                    @unique
  contactId              String
  deviceType             String
  brand                  String
  model                  String?
  serialNumber           String?
  problemDescription     String
  receivedDate           DateTime                  @default(now())
  estimatedCompletionDate DateTime?
  completionDate         DateTime?
  deliveryDate           DateTime?
  status                 MaintenanceStatus         @default(RECEIVED)
  priority               MaintenancePriority       @default(MEDIUM)
  initialDiagnosis       String?
  technicalNotes         String?
  estimatedCost          Float?
  finalCost              Float?
  isPaid                 Boolean                   @default(false)
  isWarranty             Boolean                   @default(false)
  warrantyDetails        String?
  estimatedHours         Float?
  actualHours            Float?
  customerSignature      Boolean                   @default(false)
  customerRating         Int?
  customerFeedback       String?
  userId                 String
  branchId               String
  createdAt              DateTime                  @default(now())
  updatedAt              DateTime                  @updatedAt
  contact                Contact                   @relation(fields: [contactId], references: [id])
  user                   User                      @relation(fields: [userId], references: [id])
  branch                 Branch                    @relation(fields: [branchId], references: [id])
  parts                  MaintenancePart[]
  statusHistory          MaintenanceStatusHistory[]
  payments               MaintenancePayment[]
}

model MaintenancePart {
  id                    String            @id @default(uuid())
  maintenanceServiceId  String
  productId             String?
  partName              String
  quantity              Int               @default(1)
  unitPrice             Float             @default(0)
  totalPrice            Float             @default(0)
  isFromInventory       Boolean           @default(true)
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  maintenanceService    MaintenanceService @relation(fields: [maintenanceServiceId], references: [id], onDelete: Cascade)
  product               Product?          @relation(fields: [productId], references: [id])
}

model MaintenanceStatusHistory {
  id                    String            @id @default(uuid())
  maintenanceServiceId  String
  status                MaintenanceStatus
  notes                 String?
  userId                String
  createdAt             DateTime          @default(now())
  maintenanceService    MaintenanceService @relation(fields: [maintenanceServiceId], references: [id], onDelete: Cascade)
  user                  User              @relation(fields: [userId], references: [id])
}

model MaintenancePayment {
  id                    String            @id @default(uuid())
  maintenanceServiceId  String
  method                PaymentMethod
  amount                Float
  date                  DateTime          @default(now())
  notes                 String?
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  maintenanceService    MaintenanceService @relation(fields: [maintenanceServiceId], references: [id])
}

model PaymentMethodSettings {
  id            String      @id @default(uuid())
  code          String      @unique // مثل CASH, VODAFONE_CASH
  name          String      // الاسم المعروض للمستخدم
  isActive      Boolean     @default(true)
  accountId     String?     // ربط بحساب في النظام المحاسبي
  account       Account?    @relation(fields: [accountId], references: [id])
  journalId     String?     // ربط بدفتر اليومية
  journal       Journal?    @relation(fields: [journalId], references: [id])
  sequence      Int         @default(0) // ترتيب العرض
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}
