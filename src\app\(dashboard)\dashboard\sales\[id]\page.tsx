"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import PaymentModal from "../components/PaymentModal";

export default function SaleDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const saleId = params.id as string;
  const [isUpdating, setIsUpdating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sale, setSale] = useState<any>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  // Fetch sale data from API
  useEffect(() => {
    const fetchSale = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/sales/${saleId}`);

        if (response.ok) {
          const data = await response.json();
          setSale(data);
          setError(null);
        } else {
          const errorData = await response.json();
          setError(errorData.error || "Failed to fetch sale");
          setSale(null);
        }
      } catch (err: any) {
        console.error("Error fetching sale:", err);
        setError(err.message || "An error occurred while fetching the sale");
        setSale(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSale();
  }, [saleId]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-700">Loading sale data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !sale) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6">
        <h2 className="text-xl font-semibold text-gray-900">Sale not found</h2>
        <p className="mt-2 text-gray-600">{error || `The sale with ID ${saleId} could not be found.`}</p>
        <div className="mt-4">
          <Link
            href="/dashboard/sales"
            className="text-indigo-600 hover:text-indigo-900"
          >
            Back to Sales
          </Link>
        </div>
      </div>
    );
  }

  // Handle status update
  const updateStatus = async (newStatus: string) => {
    setIsUpdating(true);

    try {
      const response = await fetch(`/api/sales/${saleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (response.ok) {
        // Refresh the sale data
        const updatedSale = await response.json();
        setSale(updatedSale);
        alert(`Status updated to ${newStatus}`);
      } else {
        const errorData = await response.json();
        alert(`Error updating status: ${errorData.error || 'Unknown error'}`);
      }
    } catch (err: any) {
      console.error('Error updating status:', err);
      alert(`Error updating status: ${err.message || 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle payment update
  const updatePayment = async (newStatus: string) => {
    setIsUpdating(true);

    try {
      const response = await fetch(`/api/sales/${saleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentStatus: newStatus,
        }),
      });

      if (response.ok) {
        // Refresh the sale data
        const updatedSale = await response.json();
        setSale(updatedSale);
        alert(`Payment status updated to ${newStatus}`);
      } else {
        const errorData = await response.json();
        alert(`Error updating payment status: ${errorData.error || 'Unknown error'}`);
      }
    } catch (err: any) {
      console.error('Error updating payment status:', err);
      alert(`Error updating payment status: ${err.message || 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle payment with multiple payment methods
  const handlePaymentConfirm = async (paymentMethods: any[]) => {
    setIsUpdating(true);

    try {
      // Determine payment status based on payment methods
      let paymentStatus = "PAID";
      let primaryPaymentMethod = "CASH";

      // Check if any payment is CUSTOMER_ACCOUNT
      const hasCustomerAccount = paymentMethods.some(p => p.method === "CUSTOMER_ACCOUNT");

      if (hasCustomerAccount) {
        // If all payment is on customer account, set as UNPAID
        const totalCustomerAccount = paymentMethods
          .filter(p => p.method === "CUSTOMER_ACCOUNT")
          .reduce((sum, p) => sum + p.amount, 0);

        if (totalCustomerAccount === sale.totalAmount) {
          paymentStatus = "UNPAID";
          primaryPaymentMethod = "CUSTOMER_ACCOUNT";
        } else {
          // If partial payment, set as PARTIALLY_PAID
          paymentStatus = "PARTIALLY_PAID";
          // Set primary payment method to the first non-customer account method
          const nonCustomerAccountMethod = paymentMethods.find(p => p.method !== "CUSTOMER_ACCOUNT");
          if (nonCustomerAccountMethod) {
            primaryPaymentMethod = nonCustomerAccountMethod.method;
          }
        }
      }

      const response = await fetch(`/api/sales/${saleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentStatus,
          paymentMethod: primaryPaymentMethod,
          payments: paymentMethods
        }),
      });

      if (response.ok) {
        // Refresh the sale data
        const updatedSale = await response.json();
        setSale(updatedSale);
        alert(`Payment recorded successfully`);
      } else {
        const errorData = await response.json();
        alert(`Error recording payment: ${errorData.error || 'Unknown error'}`);
      }
    } catch (err: any) {
      console.error('Error recording payment:', err);
      alert(`Error recording payment: ${err.message || 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle sale deletion
  const handleDelete = async () => {
    // Detailed confirmation message explaining the effects of deletion
    const confirmMessage =
      "تأكيد حذف الفاتورة\n\n" +
      "سيؤدي حذف هذه الفاتورة إلى:\n" +
      "• إعادة جميع المنتجات المباعة إلى المخزون\n" +
      "• إلغاء جميع المدفوعات المرتبطة بالفاتورة\n" +
      "• تخفيض المبلغ المستحق على العميل (إذا كانت الفاتورة غير مدفوعة أو مدفوعة جزئياً)\n\n" +
      "هل أنت متأكد من رغبتك في حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.";

    // Confirm deletion
    if (!window.confirm(confirmMessage)) {
      return;
    }

    setIsUpdating(true);

    try {
      const response = await fetch(`/api/sales/${saleId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        alert("تم حذف الفاتورة بنجاح وإعادة المنتجات إلى المخزون وتحديث حساب العميل");
        // Redirect to sales list
        router.push('/dashboard/sales');
      } else {
        const errorData = await response.json();
        alert(`خطأ في حذف الفاتورة: ${errorData.error || 'خطأ غير معروف'}`);
      }
    } catch (err: any) {
      console.error('Error deleting sale:', err);
      alert(`خطأ في حذف الفاتورة: ${err.message || 'خطأ غير معروف'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Print styles */}
      <style jsx global>{`
        @media print {
          body {
            font-size: 12pt;
          }
          .print\\:block {
            display: block !important;
          }
          .print\\:hidden {
            display: none !important;
          }
          .shadow {
            box-shadow: none !important;
          }
          .sm\\:rounded-md {
            border-radius: 0 !important;
          }
          .bg-yellow-50 {
            background-color: #fffbeb !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          .border-yellow-400 {
            border-color: #fbbf24 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          .text-yellow-800 {
            color: #92400e !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          .text-yellow-700 {
            color: #b45309 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
        }
      `}</style>
      {/* Payment Modal */}
      {sale && (
        <PaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          totalAmount={sale.totalAmount}
          onConfirm={handlePaymentConfirm}
        />
      )}

      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Invoice #{sale.invoiceNumber}
          </h2>
          <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
            <div className="mt-2 flex items-center text-sm text-gray-700">
              <span className="mr-1.5 font-medium">Status:</span>
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  sale.status === "COMPLETED"
                    ? "bg-green-100 text-green-800"
                    : sale.status === "PENDING"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {sale.status}
              </span>
            </div>
            <div className="mt-2 flex items-center text-sm text-gray-700">
              <span className="mr-1.5 font-medium">Payment:</span>
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  sale.paymentStatus === "PAID"
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {sale.paymentStatus}
              </span>
            </div>
            <div className="mt-2 flex items-center text-sm text-gray-700">
              <span className="mr-1.5 font-medium">Date:</span>
              <span>{new Date(sale.date).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Link
            href="/dashboard/sales"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Sales
          </Link>
          <Link
            href={`/dashboard/sales/${saleId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Edit
          </Link>
          <Link
            href={`/dashboard/sales/${saleId}/print`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
            </svg>
            Print
          </Link>
          <button
            onClick={handleDelete}
            disabled={isUpdating}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            {isUpdating ? "Deleting..." : "Delete"}
          </button>
        </div>
      </div>

      {/* Print-only notes section */}
      {sale.notes && (
        <div className="hidden print:block mb-4 p-3 border-2 border-yellow-400 rounded-md bg-yellow-50">
          <h3 className="text-lg font-bold text-yellow-800">Important Notes:</h3>
          <p className="text-yellow-700 whitespace-pre-line">{sale.notes}</p>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Customer Information
            </h3>
            <div className="mt-2 text-sm text-gray-700">
              <p className="font-medium text-gray-900">{sale.contact?.name || "Unknown Customer"}</p>
              <p>{sale.contact?.phone || "No phone"}</p>
              <p>{sale.contact?.address || "No address"}</p>
            </div>
          </div>
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Sale Information
            </h3>
            <div className="mt-2 text-sm text-gray-700">
              <p><span className="font-medium">Branch:</span> {sale.branch.name}</p>
              <p><span className="font-medium">Created By:</span> {sale.user?.name || "Unknown User"}</p>
              {sale.paymentStatus !== "UNPAID" && sale.paymentMethod && sale.paymentMethod !== "CUSTOMER_ACCOUNT" && (
                <p><span className="font-medium">Payment Method:</span> {sale.paymentMethod}</p>
              )}
              {sale.notes && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="font-medium text-yellow-800">Important Notes:</p>
                  <p className="text-yellow-700 whitespace-pre-line">{sale.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Items
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Specifications
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Warehouse
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Unit Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sale.items.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.product?.name || "Unknown Product"}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {item.specifications ? (
                        <div>
                          {(() => {
                            try {
                              // Try to parse as JSON
                              const specs = JSON.parse(item.specifications);
                              if (Array.isArray(specs)) {
                                return (
                                  <ul className="list-disc list-inside">
                                    {specs.map((spec: any, index: number) => (
                                      <li key={index}>
                                        <span className="font-medium">{spec.name}:</span> {spec.value}
                                      </li>
                                    ))}
                                  </ul>
                                );
                              } else {
                                return <span>{item.specifications}</span>;
                              }
                            } catch (e) {
                              // If parsing fails, just display the string
                              return <span>{item.specifications}</span>;
                            }
                          })()}
                        </div>
                      ) : (
                        <span>No specifications</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.warehouseId ? (
                        <span>{item.warehouseName || "Selected Warehouse"}</span>
                      ) : (
                        <span>Default Warehouse</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.unitPrice?.toFixed(2) || "0.00"} ج.م
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.totalPrice?.toFixed(2) || (item.unitPrice * item.quantity).toFixed(2) || "0.00"} ج.م
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div className="sm:flex sm:justify-between">
            <div className="sm:w-1/2 print:hidden">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Actions
              </h3>
              <div className="space-y-3">
                {sale.status !== "COMPLETED" && (
                  <button
                    type="button"
                    onClick={() => updateStatus("COMPLETED")}
                    disabled={isUpdating}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    {isUpdating ? "Updating..." : "Mark as Completed"}
                  </button>
                )}
                {sale.status !== "CANCELLED" && sale.status !== "COMPLETED" && (
                  <button
                    type="button"
                    onClick={() => updateStatus("CANCELLED")}
                    disabled={isUpdating}
                    className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    {isUpdating ? "Updating..." : "Cancel Sale"}
                  </button>
                )}
                {sale.paymentStatus !== "PAID" && (
                  <div className="mt-3">
                    <button
                      type="button"
                      onClick={() => setIsPaymentModalOpen(true)}
                      disabled={isUpdating}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      {isUpdating ? "Updating..." : "Record Payment"}
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="mt-4 sm:mt-0 sm:w-1/3">
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="flex justify-between py-2 text-sm">
                  <span className="font-medium text-gray-700">Subtotal:</span>
                  <span className="text-gray-900">{sale.subtotalAmount?.toFixed(2) || "0.00"} ج.م</span>
                </div>
                <div className="flex justify-between py-2 text-sm">
                  <span className="font-medium text-gray-700">Tax:</span>
                  <span className="text-gray-900">{sale.taxAmount?.toFixed(2) || "0.00"} ج.م</span>
                </div>
                {sale.discountAmount > 0 && (
                  <div className="flex justify-between py-2 text-sm">
                    <span className="font-medium text-gray-700">Discount:</span>
                    <span className="text-gray-900">{sale.discountAmount?.toFixed(2) || "0.00"} ج.م</span>
                  </div>
                )}
                <div className="flex justify-between py-2 text-sm font-bold border-t border-gray-200 mt-2 pt-2">
                  <span className="text-gray-900">Total:</span>
                  <span className="text-gray-900">{sale.totalAmount?.toFixed(2) || "0.00"} ج.م</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
