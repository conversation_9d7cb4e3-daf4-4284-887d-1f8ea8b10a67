import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import cache from "@/lib/cache";
import { getSalesSummary } from "@/lib/sales-queries";

// GET /api/sales/stats - Get sales statistics
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    
    // Get query parameters
    const branchId = url.searchParams.get("branchId") || undefined;
    const period = url.searchParams.get("period") || "all";
    
    // Calculate date range based on period
    let startDate: Date | undefined;
    let endDate: Date | undefined;
    
    const now = new Date();
    endDate = now;
    
    switch (period) {
      case "today":
        startDate = new Date(now.setHours(0, 0, 0, 0));
        break;
      case "yesterday":
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(startDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case "week":
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
        break;
      case "month":
        startDate = new Date(now);
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case "quarter":
        startDate = new Date(now);
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case "year":
        startDate = new Date(now);
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case "all":
      default:
        // No date filtering
        startDate = undefined;
        endDate = undefined;
    }
    
    // Generate cache key
    const cacheKey = `sales_stats_${branchId || 'all'}_${period}`;
    
    // Check if we have cached data
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached sales stats for key: ${cacheKey}`);
      return NextResponse.json(cachedData);
    }
    
    console.log(`Cache miss for key: ${cacheKey}, fetching from database`);
    
    // Get sales summary
    const summary = await getSalesSummary({
      branchId,
      startDate,
      endDate
    });
    
    // Get additional statistics
    const [
      productStats,
      categoryStats,
      hourlyStats,
      weekdayStats
    ] = await Promise.all([
      // Product statistics
      prisma.saleItem.groupBy({
        by: ['productId'],
        where: {
          sale: {
            ...(branchId ? { branchId } : {}),
            ...(startDate || endDate ? {
              date: {
                ...(startDate ? { gte: startDate } : {}),
                ...(endDate ? { lte: endDate } : {})
              }
            } : {})
          }
        },
        _sum: {
          quantity: true,
          totalPrice: true
        },
        _count: {
          id: true
        },
        orderBy: {
          _sum: {
            quantity: 'desc'
          }
        },
        take: 10
      }),
      
      // Category statistics
      prisma.saleItem.groupBy({
        by: ['product', 'productId'],
        where: {
          sale: {
            ...(branchId ? { branchId } : {}),
            ...(startDate || endDate ? {
              date: {
                ...(startDate ? { gte: startDate } : {}),
                ...(endDate ? { lte: endDate } : {})
              }
            } : {})
          }
        },
        _sum: {
          quantity: true,
          totalPrice: true
        }
      }),
      
      // Hourly sales statistics
      prisma.$queryRaw`
        SELECT 
          EXTRACT(HOUR FROM "date") as hour,
          COUNT(*) as count,
          SUM("totalAmount") as amount
        FROM "Sale"
        WHERE 
          ${branchId ? prisma.$raw`"branchId" = ${branchId}` : prisma.$raw`1=1`}
          ${startDate ? prisma.$raw`AND "date" >= ${startDate}` : prisma.$raw``}
          ${endDate ? prisma.$raw`AND "date" <= ${endDate}` : prisma.$raw``}
        GROUP BY EXTRACT(HOUR FROM "date")
        ORDER BY hour
      `,
      
      // Weekday sales statistics
      prisma.$queryRaw`
        SELECT 
          EXTRACT(DOW FROM "date") as weekday,
          COUNT(*) as count,
          SUM("totalAmount") as amount
        FROM "Sale"
        WHERE 
          ${branchId ? prisma.$raw`"branchId" = ${branchId}` : prisma.$raw`1=1`}
          ${startDate ? prisma.$raw`AND "date" >= ${startDate}` : prisma.$raw``}
          ${endDate ? prisma.$raw`AND "date" <= ${endDate}` : prisma.$raw``}
        GROUP BY EXTRACT(DOW FROM "date")
        ORDER BY weekday
      `
    ]);
    
    // Get product details for product stats
    const productIds = productStats.map(stat => stat.productId);
    const products = await prisma.product.findMany({
      where: {
        id: {
          in: productIds
        }
      },
      select: {
        id: true,
        name: true,
        category: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    
    // Format product statistics
    const formattedProductStats = productStats.map(stat => {
      const product = products.find(p => p.id === stat.productId);
      return {
        productId: stat.productId,
        productName: product?.name || 'Unknown',
        categoryName: product?.category?.name || 'Uncategorized',
        quantity: stat._sum.quantity || 0,
        amount: stat._sum.totalPrice || 0,
        orderCount: stat._count.id
      };
    });
    
    // Format hourly statistics
    const formattedHourlyStats = Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      count: 0,
      amount: 0
    }));
    
    (hourlyStats as any[]).forEach((stat: any) => {
      const hour = parseInt(stat.hour);
      if (hour >= 0 && hour < 24) {
        formattedHourlyStats[hour] = {
          hour,
          count: parseInt(stat.count),
          amount: parseFloat(stat.amount)
        };
      }
    });
    
    // Format weekday statistics
    const weekdayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const formattedWeekdayStats = Array.from({ length: 7 }, (_, i) => ({
      weekday: i,
      weekdayName: weekdayNames[i],
      count: 0,
      amount: 0
    }));
    
    (weekdayStats as any[]).forEach((stat: any) => {
      const weekday = parseInt(stat.weekday);
      if (weekday >= 0 && weekday < 7) {
        formattedWeekdayStats[weekday] = {
          weekday,
          weekdayName: weekdayNames[weekday],
          count: parseInt(stat.count),
          amount: parseFloat(stat.amount)
        };
      }
    });
    
    // Prepare the response data
    const responseData = {
      summary,
      products: formattedProductStats,
      hourlyStats: formattedHourlyStats,
      weekdayStats: formattedWeekdayStats,
      period,
      dateRange: {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString()
      }
    };
    
    // Cache the result for 5 minutes (300000 ms)
    cache.set(cacheKey, responseData, 5 * 60 * 1000);
    console.log(`Cached sales stats for key: ${cacheKey}`);
    
    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error fetching sales statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch sales statistics" },
      { status: 500 }
    );
  }
}
