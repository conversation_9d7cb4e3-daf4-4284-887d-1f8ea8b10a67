"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

interface ContactFormProps {
  contact?: {
    id: string;
    name: string;
    phone: string;
    address?: string;
    isCustomer: boolean;
    isSupplier: boolean;
    isActive: boolean;
    balance: number;
    creditLimit?: number;
    creditPeriod?: number;
    openingBalance: number;
    openingBalanceDate: string;
  };
  onClose: () => void;
  onSuccess: () => void;
}

export default function ContactForm({ contact, onClose, onSuccess }: ContactFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    name: contact?.name || "",
    phone: contact?.phone || "",
    address: contact?.address || "",
    isCustomer: contact?.isCustomer ?? true,
    isSupplier: contact?.isSupplier ?? false,
    isActive: contact?.isActive ?? true,
    creditLimit: contact?.creditLimit ?? 0,
    creditPeriod: contact?.creditPeriod ?? 30,
    openingBalance: contact?.openingBalance ?? 0,
    openingBalanceDate: contact?.openingBalanceDate ? new Date(contact.openingBalanceDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    // If toggling customer status, handle credit limit and period visibility
    if (name === "isCustomer") {
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
        // Reset credit fields if unchecking customer status
        creditLimit: checked ? prev.creditLimit : 0,
        creditPeriod: checked ? prev.creditPeriod : 30
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: checked }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validate form
    if (!formData.name.trim()) {
      setError("Name is required");
      setIsLoading(false);
      return;
    }

    if (!formData.phone.trim()) {
      setError("Phone number is required");
      setIsLoading(false);
      return;
    }

    if (!formData.isCustomer && !formData.isSupplier) {
      setError("Contact must be either a customer, supplier, or both");
      setIsLoading(false);
      return;
    }

    try {
      const url = contact ? `/api/contacts/${contact.id}` : "/api/contacts";
      const method = contact ? "PATCH" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save contact");
      }

      onSuccess();
      router.refresh();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-gray-900">
        {contact ? "Edit Contact" : "Add New Contact"}
      </h2>

      {error && (
        <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-semibold text-gray-700">
              Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-gray-900 font-medium"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-semibold text-gray-700">
              Phone Number *
            </label>
            <input
              type="text"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-gray-900 font-medium"
            />
          </div>



          <div>
            <label htmlFor="address" className="block text-sm font-semibold text-gray-700">
              Address (Optional)
            </label>
            <textarea
              id="address"
              name="address"
              value={formData.address}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-gray-900 font-medium"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label className="text-sm font-semibold text-gray-700">
              Contact Type *
            </label>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isCustomer"
                  name="isCustomer"
                  checked={formData.isCustomer}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
                />
                <label htmlFor="isCustomer" className="ml-2 block text-sm font-medium text-gray-900">
                  Customer
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isSupplier"
                  name="isSupplier"
                  checked={formData.isSupplier}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
                />
                <label htmlFor="isSupplier" className="ml-2 block text-sm font-medium text-gray-900">
                  Supplier
                </label>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="openingBalance" className="block text-sm font-semibold text-gray-700">
                Opening Balance
              </label>
              <input
                type="number"
                id="openingBalance"
                name="openingBalance"
                value={formData.openingBalance}
                onChange={handleNumberChange}
                step="0.01"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-gray-900 font-medium"
              />
            </div>

            <div>
              <label htmlFor="openingBalanceDate" className="block text-sm font-semibold text-gray-700">
                Opening Balance Date
              </label>
              <input
                type="date"
                id="openingBalanceDate"
                name="openingBalanceDate"
                value={formData.openingBalanceDate}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-gray-900 font-medium"
              />
            </div>
          </div>

          {formData.isCustomer && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="creditLimit" className="block text-sm font-semibold text-gray-700">
                  Credit Limit
                </label>
                <input
                  type="number"
                  id="creditLimit"
                  name="creditLimit"
                  value={formData.creditLimit}
                  onChange={handleNumberChange}
                  step="0.01"
                  min="0"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-gray-900 font-medium"
                />
                <p className="mt-1 text-xs text-gray-500">Maximum credit amount for this customer</p>
              </div>

              <div>
                <label htmlFor="creditPeriod" className="block text-sm font-semibold text-gray-700">
                  Credit Period (Days)
                </label>
                <input
                  type="number"
                  id="creditPeriod"
                  name="creditPeriod"
                  value={formData.creditPeriod}
                  onChange={handleNumberChange}
                  min="0"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-gray-900 font-medium"
                />
                <p className="mt-1 text-xs text-gray-500">Payment due period in days</p>
              </div>
            </div>
          )}

          {contact && (
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm font-medium text-gray-900">
                Active
              </label>
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-800 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? "Saving..." : "Save"}
          </button>
        </div>
      </form>
    </div>
  );
}
