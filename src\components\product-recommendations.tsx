"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ShoppingCart, 
  Star, 
  Tag, 
  Sparkles, 
  TrendingUp, 
  Package,
  Loader2
} from "lucide-react";

interface ProductRecommendation {
  id: string;
  name: string;
  description: string | null;
  price: number;
  discountedPrice: number | null;
  discountPercentage: number | null;
  category: string;
  categoryId: string;
  inStock: boolean;
  recommendationType: "PERSONALIZED" | "POPULAR";
}

interface ProductRecommendationsProps {
  contactId: string;
  onAddToCart?: (productId: string) => void;
  limit?: number;
  showTitle?: boolean;
}

export default function ProductRecommendations({
  contactId,
  onAddToCart,
  limit = 4,
  showTitle = true
}: ProductRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<ProductRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recommendationType, setRecommendationType] = useState<"PERSONALIZED" | "POPULAR">("PERSONALIZED");
  
  useEffect(() => {
    const fetchRecommendations = async () => {
      if (!contactId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/recommendations?contactId=${contactId}&limit=${limit}`);
        
        if (!response.ok) {
          throw new Error("Failed to fetch recommendations");
        }
        
        const data = await response.json();
        setRecommendations(data.recommendations);
        setRecommendationType(data.recommendationType);
      } catch (err) {
        console.error("Error fetching product recommendations:", err);
        setError("Could not load recommendations");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchRecommendations();
  }, [contactId, limit]);
  
  const handleAddToCart = (productId: string) => {
    if (onAddToCart) {
      onAddToCart(productId);
    }
  };
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Product Recommendations</CardTitle>
          <CardDescription>Loading personalized recommendations...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }
  
  if (error || recommendations.length === 0) {
    return null; // Don't show anything if there's an error or no recommendations
  }
  
  return (
    <Card>
      {showTitle && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">
              {recommendationType === "PERSONALIZED" ? (
                <div className="flex items-center">
                  <Sparkles className="h-5 w-5 text-yellow-500 mr-2" />
                  Personalized Recommendations
                </div>
              ) : (
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 text-blue-500 mr-2" />
                  Popular Products
                </div>
              )}
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              {recommendationType === "PERSONALIZED" ? "Based on your purchases" : "Trending items"}
            </Badge>
          </div>
          <CardDescription>
            {recommendationType === "PERSONALIZED" 
              ? "Products you might like based on your purchase history" 
              : "Popular products you might be interested in"}
          </CardDescription>
        </CardHeader>
      )}
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {recommendations.map((product) => (
            <div key={product.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-sm line-clamp-2">{product.name}</h3>
                  {product.inStock ? (
                    <Badge className="bg-green-100 text-green-800 text-xs">In Stock</Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">Out of Stock</Badge>
                  )}
                </div>
                
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <Tag className="h-3 w-3 mr-1" />
                  {product.category}
                </div>
                
                {product.description && (
                  <p className="text-xs text-gray-600 line-clamp-2 mb-3">{product.description}</p>
                )}
                
                <div className="flex justify-between items-end mt-2">
                  <div>
                    {product.discountedPrice ? (
                      <div>
                        <div className="flex items-center">
                          <p className="text-sm text-gray-500 line-through mr-1">
                            {product.price.toFixed(2)} ج.م
                          </p>
                          <Badge className="bg-red-100 text-red-800 text-xs">
                            {product.discountPercentage}% off
                          </Badge>
                        </div>
                        <p className="text-base font-bold text-green-600">
                          {product.discountedPrice.toFixed(2)} ج.م
                        </p>
                      </div>
                    ) : (
                      <p className="text-base font-bold">
                        {product.price.toFixed(2)} ج.م
                      </p>
                    )}
                  </div>
                  
                  <Button 
                    size="sm" 
                    onClick={() => handleAddToCart(product.id)}
                    disabled={!product.inStock || !onAddToCart}
                  >
                    <ShoppingCart className="h-4 w-4 mr-1" />
                    Add
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
