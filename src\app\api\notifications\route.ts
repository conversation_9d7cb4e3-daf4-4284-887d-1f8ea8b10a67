import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { format, addDays, subDays } from "date-fns";

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId") || session.user.id;
    const unreadOnly = url.searchParams.get("unreadOnly") === "true";
    const limit = parseInt(url.searchParams.get("limit") || "20", 10);
    
    // Get notifications for the user
    const notifications = await prisma.notification.findMany({
      where: {
        userId,
        ...(unreadOnly ? { read: false } : {})
      },
      orderBy: {
        createdAt: "desc"
      },
      take: limit
    });
    
    // Get unread count
    const unreadCount = await prisma.notification.count({
      where: {
        userId,
        read: false
      }
    });
    
    return NextResponse.json({
      notifications,
      unreadCount
    });
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      { error: "Failed to fetch notifications" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const data = await req.json();
    
    // Validate request
    if (!data.type || !data.title) {
      return NextResponse.json(
        { error: "Invalid request: type and title are required" },
        { status: 400 }
      );
    }
    
    // Create notification
    const notification = await prisma.notification.create({
      data: {
        userId: data.userId || session.user.id,
        type: data.type,
        title: data.title,
        message: data.message || "",
        link: data.link,
        read: false
      }
    });
    
    return NextResponse.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error("Error creating notification:", error);
    return NextResponse.json(
      { error: "Failed to create notification" },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const data = await req.json();
    
    // Validate request
    if (!data.id) {
      return NextResponse.json(
        { error: "Invalid request: notification id is required" },
        { status: 400 }
      );
    }
    
    // Update notification
    const notification = await prisma.notification.update({
      where: {
        id: data.id
      },
      data: {
        read: data.read !== undefined ? data.read : true
      }
    });
    
    return NextResponse.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error("Error updating notification:", error);
    return NextResponse.json(
      { error: "Failed to update notification" },
      { status: 500 }
    );
  }
}

// Helper function to create system notifications
export async function createSystemNotification(
  userId: string,
  type: string,
  title: string,
  message: string,
  link?: string
) {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId,
        type,
        title,
        message,
        link,
        read: false
      }
    });
    
    return notification;
  } catch (error) {
    console.error("Error creating system notification:", error);
    return null;
  }
}

// Helper function to create loyalty notifications
export async function createLoyaltyNotification(
  userId: string,
  contactId: string,
  points: number,
  reason: string
) {
  try {
    // Get contact details
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
      select: { name: true, loyaltyPoints: true, loyaltyTier: true }
    });
    
    if (!contact) {
      throw new Error("Contact not found");
    }
    
    // Create notification
    const notification = await prisma.notification.create({
      data: {
        userId,
        type: "LOYALTY",
        title: `Loyalty Update: ${contact.name}`,
        message: `${points > 0 ? "Added" : "Redeemed"} ${Math.abs(points)} points ${reason}. New balance: ${contact.loyaltyPoints} points.`,
        link: `/dashboard/customers/${contactId}/loyalty`,
        read: false,
        metadata: JSON.stringify({
          contactId,
          points,
          reason,
          loyaltyTier: contact.loyaltyTier
        })
      }
    });
    
    return notification;
  } catch (error) {
    console.error("Error creating loyalty notification:", error);
    return null;
  }
}

// Helper function to create birthday notifications
export async function createBirthdayNotifications() {
  try {
    // Get current date in MM-DD format
    const today = format(new Date(), "MM-dd");
    
    // Find customers with birthdays today
    const birthdayCustomers = await prisma.contact.findMany({
      where: {
        isCustomer: true,
        birthday: {
          not: null
        },
        // This is a simplified query that doesn't handle year wrap-around (Dec to Jan)
        // In a real implementation, you would need to handle this case
        // For now, we'll use a raw query to check the MM-DD part of the date
        // This is just a placeholder
      },
      select: {
        id: true,
        name: true,
        loyaltyTier: true,
        isVIP: true
      }
    });
    
    // Get all admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        role: "ADMIN"
      },
      select: {
        id: true
      }
    });
    
    // Create notifications for each admin about each birthday customer
    const notifications = [];
    
    for (const customer of birthdayCustomers) {
      for (const admin of adminUsers) {
        const notification = await prisma.notification.create({
          data: {
            userId: admin.id,
            type: "BIRTHDAY",
            title: `Birthday Today: ${customer.name}`,
            message: `${customer.name} has a birthday today! ${customer.isVIP ? "This is a VIP customer." : ""} Consider sending a birthday gift.`,
            link: `/dashboard/customers/${customer.id}/loyalty/birthday-gift`,
            read: false,
            metadata: JSON.stringify({
              contactId: customer.id,
              isVIP: customer.isVIP,
              loyaltyTier: customer.loyaltyTier
            })
          }
        });
        
        notifications.push(notification);
      }
    }
    
    return notifications;
  } catch (error) {
    console.error("Error creating birthday notifications:", error);
    return null;
  }
}
