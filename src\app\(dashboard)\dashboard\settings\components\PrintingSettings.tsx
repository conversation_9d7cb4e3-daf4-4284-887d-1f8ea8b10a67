"use client";

import { useState } from "react";

// Define types
type TemplateType = 'invoice' | 'receipt' | 'report' | 'label' | 'purchase_order' | 'delivery_note' | 'maintenance_receipt' | 'maintenance_report' | 'customer_statement';

interface Template {
  id: string;
  name: string;
  type: TemplateType;
  content: string;
  isDefault: boolean;
  language?: string;
  paperSize?: string;
  orientation?: string;
  branchId?: string;
}

interface CompanyInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  taxNumber: string;
  registrationNumber: string;
  logo: string | null;
}

interface PrintSettings {
  pageSize: 'A4' | 'A5' | '80mm' | 'custom';
  orientation: 'portrait' | 'landscape';
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  headerHeight: number;
  footerHeight: number;
  showLogo: boolean;
  logoPosition: 'left' | 'center' | 'right';
  showBranch: boolean;
  showQRCode: boolean;
  showBarcode: boolean;
  fontSize: number;
  fontFamily: string;
  primaryColor: string;
  secondaryColor: string;
  footerText: string;
  termsText: string;
  headerContent: string;
  footerContent: string;
  showSignature: boolean;
  signatureText: string;
  currency: string;
  dateFormat: string;
  customWidth?: number;
  customHeight?: number;
}

export default function PrintingSettings() {
  const [activeTab, setActiveTab] = useState("general");
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Default settings
  const [settings, setSettings] = useState<PrintSettings>({
    pageSize: 'A4',
    orientation: 'portrait',
    margins: {
      top: 10,
      right: 10,
      bottom: 10,
      left: 10
    },
    headerHeight: 30,
    footerHeight: 20,
    showLogo: true,
    logoPosition: 'left',
    showBranch: true,
    showQRCode: true,
    showBarcode: true,
    fontSize: 12,
    fontFamily: 'Arial',
    primaryColor: '#3895e7',
    secondaryColor: '#f3f4f6',
    footerText: 'Thank you for your business!',
    termsText: 'Terms and conditions apply.',
    headerContent: '<h1>[company_name]</h1><p>[company_address]</p><p>Phone: [company_phone]</p>',
    footerContent: '<p>Thank you for your business!</p><p>[company_website]</p>',
    showSignature: true,
    signatureText: 'Authorized Signature',
    currency: 'EGP',
    dateFormat: 'DD/MM/YYYY'
  });

  // Company information state
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: 'VERO Company',
    address: 'Cairo, Egypt',
    phone: '+20 ************',
    email: '<EMAIL>',
    website: 'www.verocompany.com',
    taxNumber: '*********',
    registrationNumber: 'REG-12345',
    logo: null
  });

  // Logo preview state
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  // Templates state
  const [templates, setTemplates] = useState<Template[]>([
    // Invoice Templates
    {
      id: '1',
      name: 'Default Invoice',
      type: 'invoice',
      content: `
        <div class="invoice">
          <div class="invoice-header">
            <h1>[company_name]</h1>
            <div class="invoice-info">
              <div>Invoice #: [invoice_number]</div>
              <div>Date: [date]</div>
            </div>
          </div>
          <div class="customer-info">
            <h2>Customer</h2>
            <div>[customer_details]</div>
          </div>
          <div class="invoice-items">
            [invoice_items]
          </div>
          <div class="invoice-summary">
            <div class="summary-row"><span>Subtotal:</span> <span>[subtotal]</span></div>
            <div class="summary-row"><span>Tax:</span> <span>[tax]</span></div>
            <div class="summary-row"><span>Discount:</span> <span>[discount]</span></div>
            <div class="summary-row total"><span>Total:</span> <span>[total]</span></div>
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },
    {
      id: '2',
      name: 'Compact Invoice',
      type: 'invoice',
      content: `
        <div class="invoice compact">
          <div class="invoice-header">
            <h1>[company_name]</h1>
            <div class="invoice-info">
              <div>Invoice #: [invoice_number]</div>
              <div>Date: [date]</div>
            </div>
          </div>
          <div class="customer-info">
            <div>[customer_details]</div>
          </div>
          <div class="invoice-items">
            [invoice_items]
          </div>
          <div class="invoice-summary">
            <div class="summary-row total"><span>Total:</span> <span>[total]</span></div>
          </div>
        </div>
      `,
      isDefault: false,
      paperSize: 'A5',
      orientation: 'portrait',
      language: 'ar'
    },

    // Receipt Templates
    {
      id: '3',
      name: 'Default Receipt',
      type: 'receipt',
      content: `
        <div class="receipt">
          <div class="receipt-header">
            <h1>[company_name]</h1>
            <div class="receipt-info">
              <div>Receipt #: [receipt_number]</div>
              <div>Date: [date]</div>
            </div>
          </div>
          <div class="receipt-items">
            [items]
          </div>
          <div class="receipt-summary">
            <div class="summary-row total"><span>Total:</span> <span>[total]</span></div>
            <div class="summary-row"><span>Payment Method:</span> <span>[payment_method]</span></div>
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: '80mm',
      orientation: 'portrait',
      language: 'ar'
    },
    {
      id: '4',
      name: 'Detailed Receipt',
      type: 'receipt',
      content: `
        <div class="receipt detailed">
          <div class="receipt-header">
            <h1>[company_name]</h1>
            <div class="receipt-info">
              <div>Receipt #: [receipt_number]</div>
              <div>Date: [date]</div>
            </div>
          </div>
          <div class="customer-info">
            <h2>Customer</h2>
            <div>[customer_details]</div>
          </div>
          <div class="receipt-items">
            [items]
          </div>
          <div class="receipt-summary">
            <div class="summary-row"><span>Subtotal:</span> <span>[subtotal]</span></div>
            <div class="summary-row"><span>Tax:</span> <span>[tax]</span></div>
            <div class="summary-row total"><span>Total:</span> <span>[total]</span></div>
            <div class="summary-row"><span>Payment Method:</span> <span>[payment_method]</span></div>
          </div>
        </div>
      `,
      isDefault: false,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },

    // Purchase Order Templates
    {
      id: '5',
      name: 'Default Purchase Order',
      type: 'purchase_order',
      content: `
        <div class="purchase-order">
          <div class="po-header">
            <h1>[company_name]</h1>
            <div class="po-info">
              <div>PO #: [po_number]</div>
              <div>Date: [date]</div>
            </div>
          </div>
          <div class="supplier-info">
            <h2>Supplier</h2>
            <div>[supplier_details]</div>
          </div>
          <div class="po-items">
            [items]
          </div>
          <div class="po-summary">
            <div class="summary-row"><span>Subtotal:</span> <span>[subtotal]</span></div>
            <div class="summary-row"><span>Tax:</span> <span>[tax]</span></div>
            <div class="summary-row"><span>Discount:</span> <span>[discount]</span></div>
            <div class="summary-row total"><span>Total:</span> <span>[total]</span></div>
          </div>
          <div class="po-terms">
            <h3>Terms and Conditions</h3>
            <div>[terms]</div>
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },

    // Delivery Note Templates
    {
      id: '6',
      name: 'Default Delivery Note',
      type: 'delivery_note',
      content: `
        <div class="delivery-note">
          <div class="delivery-header">
            <h1>[company_name]</h1>
            <div class="delivery-info">
              <div>Delivery Note #: [delivery_number]</div>
              <div>Date: [date]</div>
              <div>Reference: [reference]</div>
            </div>
          </div>
          <div class="customer-info">
            <h2>Deliver To</h2>
            <div>[customer_details]</div>
          </div>
          <div class="delivery-items">
            [items]
          </div>
          <div class="delivery-signatures">
            <div class="signature-box">
              <div>Delivered By</div>
              <div class="signature-line"></div>
            </div>
            <div class="signature-box">
              <div>Received By</div>
              <div class="signature-line"></div>
            </div>
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },

    // Maintenance Receipt Templates
    {
      id: '7',
      name: 'Default Maintenance Receipt',
      type: 'maintenance_receipt',
      content: `
        <div class="maintenance-receipt">
          <div class="receipt-header">
            <h1>[company_name]</h1>
            <div class="receipt-info">
              <div>Receipt #: [receipt_number]</div>
              <div>Date: [date]</div>
            </div>
          </div>
          <div class="customer-info">
            <h2>Customer</h2>
            <div>[customer_details]</div>
          </div>
          <div class="device-info">
            <h2>Device Information</h2>
            <div>[device_details]</div>
          </div>
          <div class="issue-description">
            <h2>Issue Description</h2>
            <div>[issue_description]</div>
          </div>
          <div class="receipt-footer">
            <div class="signatures">
              <div class="signature-box">
                <div>Customer Signature</div>
                <div class="signature-line"></div>
              </div>
              <div class="signature-box">
                <div>Technician Signature</div>
                <div class="signature-line"></div>
              </div>
            </div>
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },

    // Maintenance Report Templates
    {
      id: '8',
      name: 'Default Maintenance Report',
      type: 'maintenance_report',
      content: `
        <div class="maintenance-report">
          <div class="report-header">
            <h1>[company_name]</h1>
            <div class="report-info">
              <div>Report #: [report_number]</div>
              <div>Date: [date]</div>
            </div>
          </div>
          <div class="customer-info">
            <h2>Customer</h2>
            <div>[customer_details]</div>
          </div>
          <div class="device-info">
            <h2>Device Information</h2>
            <div>[device_details]</div>
          </div>
          <div class="diagnosis">
            <h2>Diagnosis</h2>
            <div>[diagnosis]</div>
          </div>
          <div class="work-performed">
            <h2>Work Performed</h2>
            <div>[work_performed]</div>
          </div>
          <div class="parts-replaced">
            <h2>Parts Replaced</h2>
            <div>[parts_replaced]</div>
          </div>
          <div class="report-summary">
            <div class="summary-row"><span>Labor:</span> <span>[labor_cost]</span></div>
            <div class="summary-row"><span>Parts:</span> <span>[parts_cost]</span></div>
            <div class="summary-row total"><span>Total:</span> <span>[total]</span></div>
          </div>
          <div class="report-footer">
            <div class="signatures">
              <div class="signature-box">
                <div>Customer Signature</div>
                <div class="signature-line"></div>
              </div>
              <div class="signature-box">
                <div>Technician Signature</div>
                <div class="signature-line"></div>
              </div>
            </div>
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },

    // Customer Statement Templates
    {
      id: '9',
      name: 'Default Customer Statement',
      type: 'customer_statement',
      content: `
        <div class="customer-statement">
          <div class="statement-header">
            <h1>[company_name]</h1>
            <div class="statement-info">
              <div>Statement #: [statement_number]</div>
              <div>Date: [date]</div>
              <div>Period: [period]</div>
            </div>
          </div>
          <div class="customer-info">
            <h2>Customer</h2>
            <div>[customer_details]</div>
          </div>
          <div class="account-summary">
            <div class="summary-row"><span>Opening Balance:</span> <span>[opening_balance]</span></div>
            <div class="summary-row"><span>Total Debits:</span> <span>[total_debits]</span></div>
            <div class="summary-row"><span>Total Credits:</span> <span>[total_credits]</span></div>
            <div class="summary-row total"><span>Closing Balance:</span> <span>[closing_balance]</span></div>
          </div>
          <div class="statement-transactions">
            <h2>Transactions</h2>
            [transactions]
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },

    // Report Templates
    {
      id: '10',
      name: 'Default Report',
      type: 'report',
      content: `
        <div class="report">
          <div class="report-header">
            <h1>[company_name]</h1>
            <div class="report-info">
              <div>Report: [report_title]</div>
              <div>Date: [date]</div>
              <div>Period: [period]</div>
            </div>
          </div>
          <div class="report-content">
            [report_content]
          </div>
          <div class="report-summary">
            [report_summary]
          </div>
          <div class="report-footer">
            <p>Generated on [date] by [user_name]</p>
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    },

    // Label Templates
    {
      id: '11',
      name: 'Default Product Label',
      type: 'label',
      content: `
        <div class="product-label">
          <div class="label-header">
            <h2>[company_name]</h2>
          </div>
          <div class="product-info">
            <div class="product-name">[product_name]</div>
            <div class="product-price">[product_price]</div>
            <div class="product-code">[product_code]</div>
          </div>
          <div class="label-barcode">
            [barcode]
          </div>
        </div>
      `,
      isDefault: true,
      paperSize: 'label',
      orientation: 'landscape',
      language: 'ar'
    }
  ]);

  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(templates[0]);

  // Preview mode state
  const [previewMode, setPreviewMode] = useState(false);

  // Handle logo upload
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check if file is PNG
      if (file.type !== 'image/png') {
        setError('Please upload a PNG image for the logo');
        setTimeout(() => {
          setError(null);
        }, 3000);
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        const logoData = reader.result as string;
        setLogoPreview(logoData);
        setCompanyInfo({
          ...companyInfo,
          logo: logoData
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Template functions
  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId) || null;
    setSelectedTemplate(template);
  };

  const handleTemplateNameChange = (name: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, name});
    }
  };

  const handleTemplateTypeChange = (type: TemplateType) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, type});
    }
  };

  const handleTemplateContentChange = (content: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, content});
    }
  };

  const handleTemplateDefaultChange = (isDefault: boolean) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, isDefault});
    }
  };

  const handleTemplatePaperSizeChange = (paperSize: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, paperSize});
    }
  };

  const handleTemplateOrientationChange = (orientation: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, orientation});
    }
  };

  const handleTemplateLanguageChange = (language: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({...selectedTemplate, language});
    }
  };

  const createNewTemplate = () => {
    const newTemplate: Template = {
      id: Date.now().toString(),
      name: 'New Template',
      type: 'invoice',
      content: '<h1>[company_name]</h1><p>New Template</p>',
      isDefault: false,
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'ar'
    };
    setTemplates([...templates, newTemplate]);
    setSelectedTemplate(newTemplate);
  };

  const saveTemplate = () => {
    if (selectedTemplate) {
      setTemplates(templates.map(t =>
        t.id === selectedTemplate.id ? selectedTemplate : t
      ));
      setSuccess('Template saved successfully');
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    }
  };

  const deleteTemplate = (templateId: string) => {
    setTemplates(templates.filter(t => t.id !== templateId));
    if (selectedTemplate && selectedTemplate.id === templateId) {
      setSelectedTemplate(templates.length > 1 ? templates[0] : null);
    }
  };

  // Save settings function
  const saveSettings = () => {
    setIsLoading(true);

    // Validate company information
    if (!companyInfo.name.trim()) {
      setError('Company name is required');
      setIsLoading(false);
      return;
    }

    // In a real implementation, this would save to a database or API
    // For now, we'll simulate an API call
    setTimeout(() => {
      // Here you would typically make an API call to save both settings and company info
      // const response = await fetch('/api/settings/print', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ settings, companyInfo })
      // });

      setIsLoading(false);
      setSuccess('Print settings and company information saved successfully');
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    }, 1000);
  };

  // Generate preview HTML
  const generatePreviewHTML = () => {
    if (!selectedTemplate) return '';

    // Replace variables with sample data
    let html = selectedTemplate.content;

    // Replace company information
    html = html.replace(/\[company_name\]/g, companyInfo.name);
    html = html.replace(/\[company_address\]/g, companyInfo.address);
    html = html.replace(/\[company_phone\]/g, companyInfo.phone);
    html = html.replace(/\[company_email\]/g, companyInfo.email);
    html = html.replace(/\[company_website\]/g, companyInfo.website);
    html = html.replace(/\[company_tax_number\]/g, companyInfo.taxNumber);
    html = html.replace(/\[company_registration_number\]/g, companyInfo.registrationNumber);

    // Replace other variables
    html = html.replace(/\[date\]/g, new Date().toLocaleDateString());
    html = html.replace(/\[invoice_number\]/g, 'INV-12345');
    html = html.replace(/\[receipt_number\]/g, 'REC-12345');
    html = html.replace(/\[po_number\]/g, 'PO-12345');
    html = html.replace(/\[delivery_number\]/g, 'DEL-12345');
    html = html.replace(/\[customer_details\]/g, 'Customer Name<br>123 Street<br>City, Country');
    html = html.replace(/\[supplier_details\]/g, 'Supplier Name<br>456 Avenue<br>City, Country');
    html = html.replace(/\[currency\]/g, settings.currency);

    // Sample items table
    const itemsTable = `
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: ${settings.secondaryColor};">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Item</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Qty</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Price</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Product 1</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">2</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 50.00</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 100.00</td>
          </tr>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Product 2</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">1</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 75.00</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 75.00</td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Subtotal:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 175.00</td>
          </tr>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Tax:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 17.50</td>
          </tr>
          <tr>
            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Total:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 192.50</td>
          </tr>
        </tfoot>
      </table>
    `;

    html = html.replace(/\[items\]/g, itemsTable);
    html = html.replace(/\[invoice_items\]/g, itemsTable);
    html = html.replace(/\[invoice_items_detailed\]/g, itemsTable);
    html = html.replace(/\[subtotal\]/g, `${settings.currency} 175.00`);
    html = html.replace(/\[tax\]/g, `${settings.currency} 17.50`);
    html = html.replace(/\[total\]/g, `${settings.currency} 192.50`);
    html = html.replace(/\[barcode\]/g, 'Barcode: 12345678');
    html = html.replace(/\[period\]/g, 'Jan 1, 2023 - Jan 31, 2023');

    // Add header and footer content if template doesn't have them
    if (!html.includes('[header_content]') && settings.headerContent) {
      html = settings.headerContent.replace(/\[company_name\]/g, companyInfo.name) + html;
    } else {
      html = html.replace(/\[header_content\]/g, settings.headerContent);
    }

    if (!html.includes('[footer_content]') && settings.footerContent) {
      html = html + settings.footerContent.replace(/\[company_website\]/g, companyInfo.website);
    } else {
      html = html.replace(/\[footer_content\]/g, settings.footerContent);
    }

    // Add signature if enabled
    if (settings.showSignature) {
      html = html + `<div style="margin-top: 30px; text-align: right;">
        <div style="border-top: 1px solid #000; display: inline-block; padding-top: 5px; min-width: 200px; text-align: center;">
          ${settings.signatureText}
        </div>
      </div>`;
    }

    // Sample sales data
    const salesData = `
      <table style="width:100%; border-collapse: collapse; margin: 10px 0;">
        <thead>
          <tr style="background-color: ${settings.secondaryColor};">
            <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Date</th>
            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Sales</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Jan 1, 2023</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 1,250.00</td>
          </tr>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd;">Jan 2, 2023</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 1,875.00</td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Total:</strong></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${settings.currency} 3,125.00</td>
          </tr>
        </tfoot>
      </table>
    `;

    html = html.replace(/\[sales_data\]/g, salesData);
    html = html.replace(/\[total_sales\]/g, `${settings.currency} 3,125.00`);

    return html;
  };

  return (
    <div className="bg-white shadow-md rounded-lg border border-gray-200 w-full">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Print Settings</h3>
        <p className="text-sm text-gray-500">
          Customize your printing templates and settings
        </p>
      </div>

      <div className="p-6">
        {error && (
          <div className="mb-4 p-4 border border-red-200 bg-red-50 rounded-md text-red-800">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <h4 className="font-medium">Error</h4>
            </div>
            <p className="mt-1 ml-7 text-sm">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 border border-green-200 bg-green-50 rounded-md text-green-800">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <h4 className="font-medium">Success</h4>
            </div>
            <p className="mt-1 ml-7 text-sm">{success}</p>
          </div>
        )}

        <div className="w-full">
          <div className="mb-4 overflow-x-auto border-b border-gray-200">
            <div className="inline-flex w-full md:w-auto">
              <button
                onClick={() => setActiveTab("general")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "general" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                General Settings
              </button>
              <button
                onClick={() => setActiveTab("company")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "company" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                Company Information
              </button>
              <button
                onClick={() => setActiveTab("templates")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "templates" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                Templates
              </button>
              <button
                onClick={() => setActiveTab("preview")}
                className={`px-4 py-2 font-medium text-sm ${activeTab === "preview" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
              >
                Preview
              </button>
            </div>
          </div>

          {activeTab === "company" && (
            <div className="space-y-6 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name
                  </label>
                  <input
                    type="text"
                    value={companyInfo.name}
                    onChange={(e) => setCompanyInfo({...companyInfo, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <input
                    type="text"
                    value={companyInfo.address}
                    onChange={(e) => setCompanyInfo({...companyInfo, address: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone
                  </label>
                  <input
                    type="text"
                    value={companyInfo.phone}
                    onChange={(e) => setCompanyInfo({...companyInfo, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={companyInfo.email}
                    onChange={(e) => setCompanyInfo({...companyInfo, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Website
                  </label>
                  <input
                    type="text"
                    value={companyInfo.website}
                    onChange={(e) => setCompanyInfo({...companyInfo, website: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tax Number
                  </label>
                  <input
                    type="text"
                    value={companyInfo.taxNumber}
                    onChange={(e) => setCompanyInfo({...companyInfo, taxNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Registration Number
                </label>
                <input
                  type="text"
                  value={companyInfo.registrationNumber}
                  onChange={(e) => setCompanyInfo({...companyInfo, registrationNumber: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Logo (PNG format only)
                </label>
                <div className="mt-1 flex items-center">
                  <input
                    type="file"
                    id="company-logo-upload"
                    accept="image/png"
                    onChange={handleLogoUpload}
                    className="sr-only"
                  />
                  <label
                    htmlFor="company-logo-upload"
                    className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                    </svg>
                    Upload Logo (PNG)
                  </label>
                  {logoPreview && (
                    <div className="ml-4">
                      <img src={logoPreview} alt="Company Logo" className="h-16 w-auto" />
                    </div>
                  )}
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Recommended size: 300x100 pixels. PNG format with transparent background.
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-md">
                <h4 className="text-sm font-medium text-blue-800 mb-2">Company Information Usage</h4>
                <p className="text-sm text-blue-700 mb-2">
                  This information will be used in all printed documents:
                </p>
                <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                  <li>Company name and logo will appear in the header of invoices, receipts, and reports</li>
                  <li>Contact information will be included in the footer of documents</li>
                  <li>Tax and registration numbers will be included in official documents</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === "general" && (
            <div className="space-y-6 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Page Size
                  </label>
                  <select
                    value={settings.pageSize}
                    onChange={(e) => setSettings({...settings, pageSize: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="A4">A4</option>
                    <option value="A5">A5</option>
                    <option value="80mm">80mm Receipt</option>
                    <option value="custom">Custom Size</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Orientation
                  </label>
                  <select
                    value={settings.orientation}
                    onChange={(e) => setSettings({...settings, orientation: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="portrait">Portrait</option>
                    <option value="landscape">Landscape</option>
                  </select>
                </div>
              </div>

              {settings.pageSize === 'custom' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Width (mm)
                    </label>
                    <input
                      type="number"
                      value={settings.customWidth || 210}
                      onChange={(e) => setSettings({...settings, customWidth: parseInt(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Height (mm)
                    </label>
                    <input
                      type="number"
                      value={settings.customHeight || 297}
                      onChange={(e) => setSettings({...settings, customHeight: parseInt(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>
              )}

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Margins (mm)</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Top
                    </label>
                    <input
                      type="number"
                      value={settings.margins.top}
                      onChange={(e) => setSettings({
                        ...settings,
                        margins: {...settings.margins, top: parseInt(e.target.value)}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Right
                    </label>
                    <input
                      type="number"
                      value={settings.margins.right}
                      onChange={(e) => setSettings({
                        ...settings,
                        margins: {...settings.margins, right: parseInt(e.target.value)}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Bottom
                    </label>
                    <input
                      type="number"
                      value={settings.margins.bottom}
                      onChange={(e) => setSettings({
                        ...settings,
                        margins: {...settings.margins, bottom: parseInt(e.target.value)}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Left
                    </label>
                    <input
                      type="number"
                      value={settings.margins.left}
                      onChange={(e) => setSettings({
                        ...settings,
                        margins: {...settings.margins, left: parseInt(e.target.value)}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Font Family
                  </label>
                  <select
                    value={settings.fontFamily}
                    onChange={(e) => setSettings({...settings, fontFamily: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="Arial">Arial</option>
                    <option value="Helvetica">Helvetica</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Verdana">Verdana</option>
                    <option value="Tahoma">Tahoma</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Font Size (pt)
                  </label>
                  <input
                    type="number"
                    value={settings.fontSize}
                    onChange={(e) => setSettings({...settings, fontSize: parseInt(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Primary Color
                  </label>
                  <div className="flex items-center">
                    <input
                      type="color"
                      value={settings.primaryColor}
                      onChange={(e) => setSettings({...settings, primaryColor: e.target.value})}
                      className="h-8 w-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                    <input
                      type="text"
                      value={settings.primaryColor}
                      onChange={(e) => setSettings({...settings, primaryColor: e.target.value})}
                      className="ml-2 flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Secondary Color
                  </label>
                  <div className="flex items-center">
                    <input
                      type="color"
                      value={settings.secondaryColor}
                      onChange={(e) => setSettings({...settings, secondaryColor: e.target.value})}
                      className="h-8 w-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                    <input
                      type="text"
                      value={settings.secondaryColor}
                      onChange={(e) => setSettings({...settings, secondaryColor: e.target.value})}
                      className="ml-2 flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Header Content (HTML)
                </label>
                <textarea
                  value={settings.headerContent}
                  onChange={(e) => setSettings({...settings, headerContent: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                />
                <p className="mt-1 text-xs text-gray-500">
                  You can use variables like [company_name], [company_address], etc.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Footer Content (HTML)
                </label>
                <textarea
                  value={settings.footerContent}
                  onChange={(e) => setSettings({...settings, footerContent: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                />
                <p className="mt-1 text-xs text-gray-500">
                  You can use variables like [company_website], [company_phone], etc.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Footer Text
                </label>
                <textarea
                  value={settings.footerText}
                  onChange={(e) => setSettings({...settings, footerText: e.target.value})}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Terms and Conditions
                </label>
                <textarea
                  value={settings.termsText}
                  onChange={(e) => setSettings({...settings, termsText: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency
                  </label>
                  <select
                    value={settings.currency}
                    onChange={(e) => setSettings({...settings, currency: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="EGP">Egyptian Pound (EGP)</option>
                    <option value="$">US Dollar ($)</option>
                    <option value="€">Euro (€)</option>
                    <option value="£">British Pound (£)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date Format
                  </label>
                  <select
                    value={settings.dateFormat}
                    onChange={(e) => setSettings({...settings, dateFormat: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  </select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-logo"
                    checked={settings.showLogo}
                    onChange={(e) => setSettings({...settings, showLogo: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-logo" className="ml-2 block text-sm text-gray-900">
                    Show Logo
                  </label>
                </div>

                {settings.showLogo && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Logo Position
                    </label>
                    <select
                      value={settings.logoPosition}
                      onChange={(e) => setSettings({...settings, logoPosition: e.target.value as any})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="left">Left</option>
                      <option value="center">Center</option>
                      <option value="right">Right</option>
                    </select>
                  </div>
                )}

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-branch"
                    checked={settings.showBranch}
                    onChange={(e) => setSettings({...settings, showBranch: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-branch" className="ml-2 block text-sm text-gray-900">
                    Show Branch Information
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-qrcode"
                    checked={settings.showQRCode}
                    onChange={(e) => setSettings({...settings, showQRCode: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-qrcode" className="ml-2 block text-sm text-gray-900">
                    Show QR Code
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-barcode"
                    checked={settings.showBarcode}
                    onChange={(e) => setSettings({...settings, showBarcode: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-barcode" className="ml-2 block text-sm text-gray-900">
                    Show Barcode
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-signature"
                    checked={settings.showSignature}
                    onChange={(e) => setSettings({...settings, showSignature: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-signature" className="ml-2 block text-sm text-gray-900">
                    Show Signature Line
                  </label>
                </div>
              </div>

              {settings.showSignature && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Signature Text
                  </label>
                  <input
                    type="text"
                    value={settings.signatureText}
                    onChange={(e) => setSettings({...settings, signatureText: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Logo
                </label>
                <div className="mt-1 flex items-center">
                  <input
                    type="file"
                    id="logo-upload"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    className="sr-only"
                  />
                  <label
                    htmlFor="logo-upload"
                    className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                    </svg>
                    Upload Logo
                  </label>
                  {logoPreview && (
                    <div className="ml-4">
                      <img src={logoPreview} alt="Logo Preview" className="h-16 w-auto" />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === "templates" && (
            <div className="space-y-6 mt-4">
              <div className="flex justify-between items-center">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Select Template
                  </label>
                  <select
                    value={selectedTemplate?.id || ''}
                    onChange={(e) => handleTemplateChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name} ({template.type})
                        {template.isDefault ? ' - Default' : ''}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <button
                    onClick={createNewTemplate}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    New Template
                  </button>
                </div>
              </div>

              {selectedTemplate && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Template Name
                      </label>
                      <input
                        type="text"
                        value={selectedTemplate.name}
                        onChange={(e) => handleTemplateNameChange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Template Type
                      </label>
                      <select
                        value={selectedTemplate.type}
                        onChange={(e) => handleTemplateTypeChange(e.target.value as TemplateType)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="invoice">Invoice</option>
                        <option value="receipt">Receipt</option>
                        <option value="report">Report</option>
                        <option value="label">Label</option>
                        <option value="purchase_order">Purchase Order</option>
                        <option value="delivery_note">Delivery Note</option>
                        <option value="maintenance_receipt">Maintenance Receipt</option>
                        <option value="maintenance_report">Maintenance Report</option>
                        <option value="customer_statement">Customer Statement</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Paper Size
                      </label>
                      <select
                        value={selectedTemplate.paperSize || 'A4'}
                        onChange={(e) => handleTemplatePaperSizeChange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="A4">A4</option>
                        <option value="A5">A5</option>
                        <option value="80mm">80mm Receipt</option>
                        <option value="label">Label</option>
                        <option value="custom">Custom Size</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Orientation
                      </label>
                      <select
                        value={selectedTemplate.orientation || 'portrait'}
                        onChange={(e) => handleTemplateOrientationChange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="portrait">Portrait</option>
                        <option value="landscape">Landscape</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Language
                      </label>
                      <select
                        value={selectedTemplate.language || 'ar'}
                        onChange={(e) => handleTemplateLanguageChange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="ar">Arabic</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <label className="block text-sm font-medium text-gray-700">
                        Template Content (HTML)
                      </label>
                      <div className="text-xs text-gray-500">
                        Use variables like [company_name], [total], etc.
                      </div>
                    </div>
                    <textarea
                      value={selectedTemplate.content}
                      onChange={(e) => handleTemplateContentChange(e.target.value)}
                      rows={12}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="is-default"
                        checked={selectedTemplate.isDefault}
                        onChange={(e) => handleTemplateDefaultChange(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is-default" className="ml-2 block text-sm text-gray-900">
                        Set as Default Template for {selectedTemplate.type.charAt(0).toUpperCase() + selectedTemplate.type.slice(1).replace('_', ' ')}
                      </label>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => deleteTemplate(selectedTemplate.id)}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete
                      </button>

                      <button
                        onClick={saveTemplate}
                        disabled={isLoading}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        {isLoading ? (
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                          </svg>
                        )}
                        Save Template
                      </button>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-md">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">Available Variables</h4>

                    {/* Common variables for all templates */}
                    <div className="mb-3">
                      <h5 className="text-xs font-medium text-blue-800 mb-1">Common Variables:</h5>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                        <div>[company_name]</div>
                        <div>[company_address]</div>
                        <div>[company_phone]</div>
                        <div>[company_email]</div>
                        <div>[company_website]</div>
                        <div>[company_tax_number]</div>
                        <div>[company_registration_number]</div>
                        <div>[date]</div>
                        <div>[currency]</div>
                        <div>[barcode]</div>
                        <div>[qrcode]</div>
                      </div>
                    </div>

                    {/* Template-specific variables */}
                    {selectedTemplate?.type === 'invoice' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Invoice Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[invoice_number]</div>
                          <div>[customer_details]</div>
                          <div>[invoice_items]</div>
                          <div>[invoice_items_detailed]</div>
                          <div>[subtotal]</div>
                          <div>[tax]</div>
                          <div>[discount]</div>
                          <div>[total]</div>
                          <div>[payment_method]</div>
                          <div>[payment_status]</div>
                          <div>[due_date]</div>
                          <div>[terms]</div>
                          <div>[notes]</div>
                          <div>[user_name]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'receipt' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Receipt Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[receipt_number]</div>
                          <div>[customer_details]</div>
                          <div>[items]</div>
                          <div>[subtotal]</div>
                          <div>[tax]</div>
                          <div>[discount]</div>
                          <div>[total]</div>
                          <div>[payment_method]</div>
                          <div>[cash_received]</div>
                          <div>[change]</div>
                          <div>[cashier_name]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'purchase_order' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Purchase Order Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[po_number]</div>
                          <div>[supplier_details]</div>
                          <div>[items]</div>
                          <div>[subtotal]</div>
                          <div>[tax]</div>
                          <div>[discount]</div>
                          <div>[total]</div>
                          <div>[expected_delivery_date]</div>
                          <div>[payment_terms]</div>
                          <div>[terms]</div>
                          <div>[notes]</div>
                          <div>[user_name]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'delivery_note' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Delivery Note Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[delivery_number]</div>
                          <div>[reference]</div>
                          <div>[customer_details]</div>
                          <div>[items]</div>
                          <div>[delivery_date]</div>
                          <div>[delivery_address]</div>
                          <div>[delivery_instructions]</div>
                          <div>[driver_name]</div>
                          <div>[vehicle_number]</div>
                          <div>[notes]</div>
                          <div>[user_name]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'maintenance_receipt' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Maintenance Receipt Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[receipt_number]</div>
                          <div>[customer_details]</div>
                          <div>[device_details]</div>
                          <div>[device_serial]</div>
                          <div>[device_model]</div>
                          <div>[issue_description]</div>
                          <div>[estimated_cost]</div>
                          <div>[estimated_completion]</div>
                          <div>[technician_name]</div>
                          <div>[notes]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'maintenance_report' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Maintenance Report Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[report_number]</div>
                          <div>[receipt_number]</div>
                          <div>[customer_details]</div>
                          <div>[device_details]</div>
                          <div>[diagnosis]</div>
                          <div>[work_performed]</div>
                          <div>[parts_replaced]</div>
                          <div>[labor_cost]</div>
                          <div>[parts_cost]</div>
                          <div>[total]</div>
                          <div>[warranty_period]</div>
                          <div>[technician_name]</div>
                          <div>[notes]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'customer_statement' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Customer Statement Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[statement_number]</div>
                          <div>[customer_details]</div>
                          <div>[period]</div>
                          <div>[opening_balance]</div>
                          <div>[closing_balance]</div>
                          <div>[total_debits]</div>
                          <div>[total_credits]</div>
                          <div>[transactions]</div>
                          <div>[payment_due_date]</div>
                          <div>[account_manager]</div>
                          <div>[notes]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'report' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Report Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[report_title]</div>
                          <div>[period]</div>
                          <div>[report_content]</div>
                          <div>[report_summary]</div>
                          <div>[sales_data]</div>
                          <div>[total_sales]</div>
                          <div>[user_name]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}

                    {selectedTemplate?.type === 'label' && (
                      <div className="mb-3">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Label Variables:</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-700">
                          <div>[product_name]</div>
                          <div>[product_code]</div>
                          <div>[product_price]</div>
                          <div>[product_cost]</div>
                          <div>[barcode]</div>
                          <div>[qrcode]</div>
                          <div>[expiry_date]</div>
                          <div>[manufacture_date]</div>
                          <div>[category]</div>
                          <div>[branch_name]</div>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          )}

          {activeTab === "preview" && (
            <div className="space-y-6 mt-4">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700">Print Preview</h4>
                  {selectedTemplate && (
                    <p className="text-xs text-gray-500 mt-1">
                      Previewing: {selectedTemplate.name} ({selectedTemplate.type})
                    </p>
                  )}
                </div>
                <div className="flex space-x-2">
                  <select
                    value={selectedTemplate?.id || ''}
                    onChange={(e) => handleTemplateChange(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>

                  <button
                    onClick={() => setPreviewMode(!previewMode)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    {previewMode ? 'Hide Preview' : 'Show Preview'}
                  </button>

                  <button
                    onClick={() => {
                      // In a real implementation, this would trigger a print dialog
                      alert('Print functionality would be triggered here');
                    }}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                    </svg>
                    Print
                  </button>
                </div>
              </div>

              {previewMode && selectedTemplate && (
                <div className="border p-4 rounded-md">
                  <div className="overflow-auto max-w-full">
                    <div
                      className="p-4 border rounded-md bg-white mx-auto"
                      style={{
                        fontFamily: settings.fontFamily,
                        fontSize: `${settings.fontSize}pt`,
                        width: settings.pageSize === 'A4' ? '210mm' :
                               settings.pageSize === 'A5' ? '148mm' :
                               settings.pageSize === '80mm' ? '80mm' :
                               `${settings.customWidth}mm`,
                        minHeight: settings.pageSize === 'A4' ? '297mm' :
                                settings.pageSize === 'A5' ? '210mm' :
                                settings.pageSize === '80mm' ? '150mm' :
                                `${settings.customHeight}mm`,
                        padding: `${settings.margins.top}mm ${settings.margins.right}mm ${settings.margins.bottom}mm ${settings.margins.left}mm`,
                        maxWidth: '100%',
                        boxSizing: 'border-box',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        position: 'relative'
                      }}
                    >
                      {/* Logo */}
                      {settings.showLogo && logoPreview && (
                        <div style={{ textAlign: settings.logoPosition, marginBottom: '10px' }}>
                          <img src={logoPreview} alt="Company Logo" style={{ maxHeight: '50px', maxWidth: '200px' }} />
                        </div>
                      )}

                      {/* Render the template with sample data */}
                      <div dangerouslySetInnerHTML={{ __html: generatePreviewHTML() }} />

                      {/* Footer */}
                      {settings.footerText && (
                        <div className="text-center mt-8" style={{ color: settings.primaryColor }}>
                          <p>{settings.footerText}</p>
                        </div>
                      )}

                      {/* Terms */}
                      {settings.termsText && (
                        <div className="mt-4 pt-4 border-t text-xs" style={{ borderColor: settings.secondaryColor }}>
                          <p>{settings.termsText}</p>
                        </div>
                      )}

                      {/* QR Code */}
                      {settings.showQRCode && (
                        <div style={{ position: 'absolute', bottom: '10mm', right: '10mm' }}>
                          <div style={{
                            width: '80px',
                            height: '80px',
                            backgroundColor: '#f0f0f0',
                            border: '1px solid #ddd',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '10px'
                          }}>
                            QR Code
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Print Settings</h4>
                  <div className="bg-gray-50 p-4 rounded-md text-sm">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-gray-500">Page Size:</div>
                      <div>{settings.pageSize === 'custom' ? `Custom (${settings.customWidth}mm x ${settings.customHeight}mm)` : settings.pageSize}</div>

                      <div className="text-gray-500">Orientation:</div>
                      <div>{settings.orientation}</div>

                      <div className="text-gray-500">Margins:</div>
                      <div>T: {settings.margins.top}mm, R: {settings.margins.right}mm, B: {settings.margins.bottom}mm, L: {settings.margins.left}mm</div>

                      <div className="text-gray-500">Font:</div>
                      <div>{settings.fontFamily}, {settings.fontSize}pt</div>

                      <div className="text-gray-500">Show Logo:</div>
                      <div>{settings.showLogo ? 'Yes' : 'No'}</div>

                      <div className="text-gray-500">Show Branch Info:</div>
                      <div>{settings.showBranch ? 'Yes' : 'No'}</div>

                      <div className="text-gray-500">Show QR Code:</div>
                      <div>{settings.showQRCode ? 'Yes' : 'No'}</div>

                      <div className="text-gray-500">Show Barcode:</div>
                      <div>{settings.showBarcode ? 'Yes' : 'No'}</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Template Information</h4>
                  {selectedTemplate ? (
                    <div className="bg-gray-50 p-4 rounded-md text-sm">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-gray-500">Name:</div>
                        <div>{selectedTemplate.name}</div>

                        <div className="text-gray-500">Type:</div>
                        <div>{selectedTemplate.type.replace('_', ' ')}</div>

                        <div className="text-gray-500">Default:</div>
                        <div>{selectedTemplate.isDefault ? 'Yes' : 'No'}</div>

                        <div className="text-gray-500">Content Size:</div>
                        <div>{selectedTemplate.content.length} characters</div>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50 p-4 rounded-md text-sm text-gray-500">
                      No template selected
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-md">
                <h4 className="text-sm font-medium text-blue-800 mb-2">How Print Settings Apply to Other Modules</h4>
                <p className="text-sm text-blue-700 mb-2">
                  These print settings will be applied to all documents generated throughout the system:
                </p>
                <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                  <li>Sales invoices will use the default invoice template</li>
                  <li>Purchase orders will use the default purchase order template</li>
                  <li>Receipts will use the default receipt template</li>
                  <li>Reports will use the default report template</li>
                  <li>All documents will use the font, colors, and margins defined here</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
        <button
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          onClick={() => {
            setSettings({
              pageSize: 'A4',
              orientation: 'portrait',
              margins: {
                top: 10,
                right: 10,
                bottom: 10,
                left: 10
              },
              headerHeight: 30,
              footerHeight: 20,
              showLogo: true,
              logoPosition: 'left',
              showBranch: true,
              showQRCode: true,
              showBarcode: true,
              fontSize: 12,
              fontFamily: 'Arial',
              primaryColor: '#3895e7',
              secondaryColor: '#f3f4f6',
              footerText: 'Thank you for your business!',
              termsText: 'Terms and conditions apply.',
              headerContent: '<h1>[company_name]</h1><p>[company_address]</p><p>Phone: [company_phone]</p>',
              footerContent: '<p>Thank you for your business!</p><p>[company_website]</p>',
              showSignature: true,
              signatureText: 'Authorized Signature',
              currency: 'EGP',
              dateFormat: 'DD/MM/YYYY'
            });
            setSuccess('Settings reset to defaults');
            setTimeout(() => {
              setSuccess(null);
            }, 3000);
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Reset to Defaults
        </button>

        <div className="flex space-x-2">
          <button
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={() => {
              // In a real implementation, this would open a help dialog
              alert('Print settings help would be shown here');
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Help
          </button>

          <button
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={saveSettings}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                Save Settings
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
