"use client";

import { useState, useEffect } from "react";
import { User, Phone, PlusCircle, UserPlus } from "lucide-react";
import AddContactModal from "./AddContactModal";

interface Contact {
  id: string;
  name: string;
  phone: string;
  isCustomer?: boolean;
  isSupplier?: boolean;
}

interface ContactSearchInputProps {
  onSelectContact: (contact: Contact) => void;
  placeholder?: string;
  contactType?: "customer" | "supplier" | "all";
  selectedContactId?: string;
  buttonLabel?: string;
  className?: string;
}

export default function ContactSearchInput({
  onSelectContact,
  placeholder = "Search contact by name or phone",
  contactType = "all",
  selectedContactId = "",
  buttonLabel = "Add New",
  className = "",
}: ContactSearchInputProps) {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [showContactDropdown, setShowContactDropdown] = useState(false);
  const [showAddContactModal, setShowAddContactModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  // Fetch contacts
  useEffect(() => {
    const fetchContacts = async () => {
      setIsLoading(true);
      try {
        let url = "/api/contacts";
        if (contactType !== "all") {
          url += `?type=${contactType}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error("Failed to fetch contacts");
        }

        const data = await response.json();
        setContacts(data);

        // If there's a selected contact ID, find and set the selected contact
        if (selectedContactId) {
          const selected = data.find((c: Contact) => c.id === selectedContactId);
          if (selected) {
            setSelectedContact(selected);
            setSearchTerm(selected.name);
          }
        }
      } catch (error: any) {
        console.error("Error fetching contacts:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContacts();
  }, [contactType, selectedContactId]);

  // Filter contacts based on search term
  useEffect(() => {
    if (searchTerm) {
      const filtered = contacts.filter(
        (contact) =>
          contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          contact.phone.includes(searchTerm)
      );
      setFilteredContacts(filtered);
      setShowContactDropdown(true);
    } else {
      setFilteredContacts([]);
      setShowContactDropdown(false);
    }
  }, [searchTerm, contacts]);

  // Handle contact selection
  const handleSelectContact = (contact: Contact) => {
    setSelectedContact(contact);
    setSearchTerm(contact.name);
    setShowContactDropdown(false);
    onSelectContact(contact);
  };

  // Handle adding a new contact
  const handleAddNewContact = (newContact: Contact) => {
    // Add the new contact to the contacts list
    setContacts((prev) => [...prev, newContact]);

    // Select the new contact
    handleSelectContact(newContact);
  };

  // Handle opening the add contact modal
  const handleOpenAddContactModal = () => {
    setShowAddContactModal(true);
    setShowContactDropdown(false);
  };

  return (
    <div className={`relative ${className}`}>
      <div className="flex">
        <div className="relative flex-grow">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={placeholder}
            className="border border-gray-300 rounded-md px-4 py-2 w-full text-gray-900 font-medium pl-10"
            autoComplete="off"
          />
          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        </div>
        <button
          type="button"
          onClick={handleOpenAddContactModal}
          className="ml-2 px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 active:bg-indigo-800 active:transform active:scale-95 transition-all duration-150 flex items-center"
          title={`Add New ${contactType === "customer" ? "Customer" : contactType === "supplier" ? "Supplier" : "Contact"}`}
        >
          <UserPlus className="h-5 w-5" />
        </button>
      </div>

      {showContactDropdown && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md max-h-60 overflow-auto">
          {filteredContacts.length > 0 ? (
            filteredContacts.map((contact) => (
              <div
                key={contact.id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer active:bg-gray-200 transition-colors duration-150"
                onClick={() => handleSelectContact(contact)}
              >
                <div className="font-medium">{contact.name}</div>
                <div className="text-sm text-gray-500 flex items-center">
                  <Phone className="h-3 w-3 mr-1" />
                  {contact.phone}
                </div>
              </div>
            ))
          ) : searchTerm.length > 0 ? (
            <div className="p-4">
              <p className="text-gray-500 mb-2">No contacts found with this name or phone number.</p>
              <button
                type="button"
                onClick={handleOpenAddContactModal}
                className="flex items-center text-indigo-600 hover:text-indigo-800 active:text-indigo-900 active:transform active:scale-95 transition-all duration-150"
              >
                <PlusCircle className="h-4 w-4 mr-1" />
                Add "{searchTerm}" as a new {contactType === "customer" ? "customer" : contactType === "supplier" ? "supplier" : "contact"}
              </button>
            </div>
          ) : null}
        </div>
      )}

      {/* Add Contact Modal */}
      <AddContactModal
        isOpen={showAddContactModal}
        onClose={() => setShowAddContactModal(false)}
        onSuccess={handleAddNewContact}
        initialPhone={searchTerm}
        contactType={contactType}
      />
    </div>
  );
}
