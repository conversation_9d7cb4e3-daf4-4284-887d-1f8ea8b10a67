import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import cache from "@/lib/cache";
import { format, subDays, parseISO } from "date-fns";

// GET /api/reports/sales/products - Get product sales report data
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    
    // Get query parameters
    const categoryId = url.searchParams.get("categoryId") || undefined;
    const search = url.searchParams.get("search") || "";
    const startDate = url.searchParams.get("startDate") 
      ? new Date(url.searchParams.get("startDate") as string) 
      : subDays(new Date(), 30);
    const endDate = url.searchParams.get("endDate") 
      ? new Date(url.searchParams.get("endDate") as string) 
      : new Date();
    
    // Generate cache key
    const cacheKey = `product_sales_report_${categoryId || 'all'}_${search}_${startDate.toISOString()}_${endDate.toISOString()}`;
    
    // Check if we have cached data
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached product report data for key: ${cacheKey}`);
      return NextResponse.json(cachedData);
    }
    
    console.log(`Cache miss for key: ${cacheKey}, fetching from database`);
    
    // Build where clause for sales
    const saleWhere: any = {
      date: {
        gte: startDate,
        lte: endDate
      }
    };
    
    // Build where clause for products
    const productWhere: any = {};
    
    if (categoryId && categoryId !== "all") {
      productWhere.categoryId = categoryId;
    }
    
    if (search) {
      productWhere.name = {
        contains: search,
        mode: 'insensitive'
      };
    }
    
    // Fetch all required data in parallel
    const [
      topProducts,
      productCategories,
      salesTrend,
      productPerformance
    ] = await Promise.all([
      // Top selling products
      prisma.$queryRaw`
        SELECT 
          p.id,
          p.name,
          c.name as category,
          SUM(si.quantity) as "quantitySold",
          SUM(si."totalPrice") as "totalRevenue",
          CASE 
            WHEN SUM(si.quantity) > 0 
            THEN SUM(si."totalPrice") / SUM(si.quantity) 
            ELSE 0 
          END as "averagePrice"
        FROM "SaleItem" si
        JOIN "Product" p ON si."productId" = p.id
        LEFT JOIN "Category" c ON p."categoryId" = c.id
        JOIN "Sale" s ON si."saleId" = s.id
        WHERE 
          s.date >= ${startDate} AND 
          s.date <= ${endDate}
          ${categoryId && categoryId !== "all" ? prisma.$raw`AND p."categoryId" = ${categoryId}` : prisma.$raw``}
          ${search ? prisma.$raw`AND p.name ILIKE ${`%${search}%`}` : prisma.$raw``}
        GROUP BY p.id, p.name, c.name
        ORDER BY "quantitySold" DESC
        LIMIT 20
      `,
      
      // Sales by product category
      prisma.$queryRaw`
        SELECT 
          c.id,
          c.name,
          COUNT(DISTINCT p.id) as "productsCount",
          SUM(si.quantity) as "totalQuantity",
          SUM(si."totalPrice") as "totalRevenue"
        FROM "SaleItem" si
        JOIN "Product" p ON si."productId" = p.id
        LEFT JOIN "Category" c ON p."categoryId" = c.id
        JOIN "Sale" s ON si."saleId" = s.id
        WHERE 
          s.date >= ${startDate} AND 
          s.date <= ${endDate}
          ${categoryId && categoryId !== "all" ? prisma.$raw`AND p."categoryId" = ${categoryId}` : prisma.$raw``}
          ${search ? prisma.$raw`AND p.name ILIKE ${`%${search}%`}` : prisma.$raw``}
        GROUP BY c.id, c.name
        ORDER BY "totalRevenue" DESC
      `,
      
      // Sales trend over time
      prisma.$queryRaw`
        SELECT 
          TO_CHAR(s.date, 'YYYY-MM-DD') as date,
          SUM(si.quantity) as quantity,
          SUM(si."totalPrice") as revenue
        FROM "SaleItem" si
        JOIN "Product" p ON si."productId" = p.id
        JOIN "Sale" s ON si."saleId" = s.id
        WHERE 
          s.date >= ${startDate} AND 
          s.date <= ${endDate}
          ${categoryId && categoryId !== "all" ? prisma.$raw`AND p."categoryId" = ${categoryId}` : prisma.$raw``}
          ${search ? prisma.$raw`AND p.name ILIKE ${`%${search}%`}` : prisma.$raw``}
        GROUP BY TO_CHAR(s.date, 'YYYY-MM-DD')
        ORDER BY date
      `,
      
      // Product performance metrics
      prisma.$queryRaw`
        WITH ProductSales AS (
          SELECT 
            p.id,
            p.name,
            c.name as category,
            p."costPrice",
            p."basePrice",
            SUM(si.quantity) as "quantitySold",
            SUM(si."totalPrice") as revenue,
            MAX(i.quantity) as "stockLevel"
          FROM "Product" p
          LEFT JOIN "Category" c ON p."categoryId" = c.id
          LEFT JOIN "SaleItem" si ON p.id = si."productId"
          LEFT JOIN "Sale" s ON si."saleId" = s.id AND s.date >= ${startDate} AND s.date <= ${endDate}
          LEFT JOIN "Inventory" i ON p.id = i."productId"
          WHERE 
            ${categoryId && categoryId !== "all" ? prisma.$raw`p."categoryId" = ${categoryId} AND` : prisma.$raw``}
            ${search ? prisma.$raw`p.name ILIKE ${`%${search}%`} AND` : prisma.$raw``}
            p."isActive" = true
          GROUP BY p.id, p.name, c.name, p."costPrice", p."basePrice"
        )
        SELECT 
          id,
          name,
          category,
          "quantitySold",
          revenue,
          CASE 
            WHEN revenue > 0 AND "costPrice" > 0 
            THEN ROUND(((revenue - ("quantitySold" * "costPrice")) / revenue) * 100, 2)
            ELSE 0 
          END as "profitMargin",
          "stockLevel",
          CASE 
            WHEN "stockLevel" > 0 AND "quantitySold" > 0 
            THEN ROUND("quantitySold"::numeric / "stockLevel", 2)
            ELSE 0 
          END as "turnoverRate",
          CASE 
            WHEN "quantitySold" > 10 AND "profitMargin" > 20 THEN 'High Performing'
            WHEN "quantitySold" > 5 OR "profitMargin" > 15 THEN 'Average'
            ELSE 'Low Performing'
          END as status
        FROM ProductSales
        ORDER BY "quantitySold" DESC
        LIMIT 50
      `
    ]);
    
    // Calculate total revenue for percentage calculation
    const totalRevenue = productCategories.reduce((sum: number, category: any) => sum + parseFloat(category.totalRevenue), 0);
    
    // Add percentage to product categories
    const formattedCategories = productCategories.map((category: any) => ({
      ...category,
      percentage: totalRevenue > 0 ? parseFloat(category.totalRevenue) / totalRevenue : 0
    }));
    
    // Prepare the response data
    const responseData = {
      topProducts,
      productCategories: formattedCategories,
      salesTrend,
      productPerformance
    };
    
    // Cache the result for 5 minutes (300000 ms)
    cache.set(cacheKey, responseData, 5 * 60 * 1000);
    console.log(`Cached product report data for key: ${cacheKey}`);
    
    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error fetching product sales report data:", error);
    return NextResponse.json(
      { error: "Failed to fetch product sales report data" },
      { status: 500 }
    );
  }
}
