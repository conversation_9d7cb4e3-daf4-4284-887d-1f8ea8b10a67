import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/settings/accounting - Get accounting settings
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view settings
    const hasViewPermission = await hasPermission("view_settings");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view accounting settings" },
        { status: 403 }
      );
    }

    // Get accounting settings from database
    const settings = await db.accountingSettings.findFirst();

    // If no settings exist, return default values
    if (!settings) {
      return NextResponse.json({
        defaultCurrency: "EGP",
        defaultTaxRate: 14,
        fiscalYearStart: "01-01",
        fiscalYearEnd: "12-31",
        autoCreateAccounts: true,
        requireApproval: false,
        allowNegativeInventory: false,
        integrateWithSales: true,
        integrateWithPurchases: true,
        integrateWithInventory: true,
        integrateWithContacts: true,
      });
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error fetching accounting settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch accounting settings" },
      { status: 500 }
    );
  }
}

// POST /api/settings/accounting - Update accounting settings
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit settings
    const hasEditPermission = await hasPermission("edit_settings");
    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to edit accounting settings" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.defaultCurrency || !data.fiscalYearStart || !data.fiscalYearEnd) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if settings already exist
    const existingSettings = await db.accountingSettings.findFirst();

    let settings;

    if (existingSettings) {
      // Update existing settings
      settings = await db.accountingSettings.update({
        where: {
          id: existingSettings.id,
        },
        data: {
          defaultCurrency: data.defaultCurrency,
          defaultTaxRate: data.defaultTaxRate || 14,
          fiscalYearStart: data.fiscalYearStart,
          fiscalYearEnd: data.fiscalYearEnd,
          autoCreateAccounts: data.autoCreateAccounts !== false,
          requireApproval: data.requireApproval === true,
          allowNegativeInventory: data.allowNegativeInventory === true,
          integrateWithSales: data.integrateWithSales !== false,
          integrateWithPurchases: data.integrateWithPurchases !== false,
          integrateWithInventory: data.integrateWithInventory !== false,
          integrateWithContacts: data.integrateWithContacts !== false,
        },
      });
    } else {
      // Create new settings
      settings = await db.accountingSettings.create({
        data: {
          defaultCurrency: data.defaultCurrency,
          defaultTaxRate: data.defaultTaxRate || 14,
          fiscalYearStart: data.fiscalYearStart,
          fiscalYearEnd: data.fiscalYearEnd,
          autoCreateAccounts: data.autoCreateAccounts !== false,
          requireApproval: data.requireApproval === true,
          allowNegativeInventory: data.allowNegativeInventory === true,
          integrateWithSales: data.integrateWithSales !== false,
          integrateWithPurchases: data.integrateWithPurchases !== false,
          integrateWithInventory: data.integrateWithInventory !== false,
          integrateWithContacts: data.integrateWithContacts !== false,
        },
      });
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error updating accounting settings:", error);
    return NextResponse.json(
      { error: "Failed to update accounting settings" },
      { status: 500 }
    );
  }
}
