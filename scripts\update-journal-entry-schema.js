const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const fs = require('fs');
const path = require('path');

async function updateJournalEntrySchema() {
  try {
    console.log('Starting journal entry schema update...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', 'prisma', 'migrations', 'manual', 'update_journal_entry.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL content into individual statements
    const statements = sqlContent
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    // Execute each SQL statement
    for (const statement of statements) {
      try {
        console.log(`Executing: ${statement}`);
        await prisma.$executeRawUnsafe(`${statement};`);
      } catch (error) {
        console.error(`Error executing statement: ${statement}`);
        console.error(error);
        // Continue with the next statement even if this one fails
      }
    }

    console.log('Journal entry schema update completed successfully!');
  } catch (error) {
    console.error('Error updating journal entry schema:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateJournalEntrySchema();
