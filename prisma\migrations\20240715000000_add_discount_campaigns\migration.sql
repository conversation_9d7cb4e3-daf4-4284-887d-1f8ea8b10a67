-- Create DiscountCampaign table
CREATE TABLE "DiscountCampaign" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DiscountCampaign_pkey" PRIMARY KEY ("id")
);

-- Create CampaignDiscount table for many-to-many relationship
CREATE TABLE "CampaignDiscount" (
    "id" TEXT NOT NULL,
    "campaignId" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CampaignDiscount_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on campaignId and discountId
CREATE UNIQUE INDEX "CampaignDiscount_campaignId_discountId_key" ON "CampaignDiscount"("campaignId", "discountId");

-- Add foreign key constraints
ALTER TABLE "CampaignDiscount" ADD CONSTRAINT "CampaignDiscount_campaignId_fkey" FOREIGN KEY ("campaignId") REFERENCES "DiscountCampaign"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "CampaignDiscount" ADD CONSTRAINT "CampaignDiscount_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX "DiscountCampaign_startDate_endDate_idx" ON "DiscountCampaign"("startDate", "endDate");
CREATE INDEX "CampaignDiscount_campaignId_idx" ON "CampaignDiscount"("campaignId");
CREATE INDEX "CampaignDiscount_discountId_idx" ON "CampaignDiscount"("discountId");
