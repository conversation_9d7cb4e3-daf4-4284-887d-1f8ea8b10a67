import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

/**
 * Check if the current user has the specified permission
 * @param permissionName The name of the permission to check
 * @returns A boolean indicating whether the user has the permission
 */
export async function hasPermission(permissionName: string): Promise<boolean> {
  try {
    console.log(`Checking permission: ${permissionName}`);
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      console.log("No session or user email found");
      return false;
    }

    console.log(`User email: ${session.user.email}`);

    // Admin users have all permissions
    const user = await db.user.findUnique({
      where: {
        email: session.user.email,
      },
      select: {
        id: true,
        role: true,
        permissions: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!user) {
      console.log(`User not found for email: ${session.user.email}`);
      return false;
    }

    console.log(`User found: ${JSON.stringify(user)}`);

    // Admin users have all permissions
    if (user.role === "ADMIN") {
      console.log("User is ADMIN, granting all permissions");
      console.log(`Permission ${permissionName} granted to admin user ${user.id}`);
      return true;
    }

    // Special case: if checking for view_products, also check for view_inventory
    if (permissionName === "view_products") {
      const hasViewInventory = user.permissions.some(p => p.name === "view_inventory");
      if (hasViewInventory) {
        console.log("User has view_inventory permission, granting view_products as well");
        return true;
      }
    }

    // Check if the user has the specific permission
    const hasPermission = user.permissions.some(p => p.name === permissionName);
    console.log(`Permission check result for ${permissionName}: ${hasPermission}`);
    console.log(`User permissions: ${JSON.stringify(user.permissions.map(p => p.name))}`);

    return hasPermission;
  } catch (error) {
    console.error("Error checking permission:", error);
    console.error(error.stack);
    return false;
  }
}

/**
 * Check if the current user has any of the specified permissions
 * @param permissionNames An array of permission names to check
 * @returns A boolean indicating whether the user has any of the permissions
 */
export async function hasAnyPermission(permissionNames: string[]): Promise<boolean> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return false;
    }

    const user = await db.user.findUnique({
      where: {
        email: session.user.email,
      },
      select: {
        role: true,
        permissions: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      return false;
    }

    // Admin users have all permissions
    if (user.role === "ADMIN") {
      return true;
    }

    // Check if the user has any of the specified permissions
    return user.permissions.some(p => permissionNames.includes(p.name));
  } catch (error) {
    console.error("Error checking permissions:", error);
    return false;
  }
}

/**
 * Check if the current user has all of the specified permissions
 * @param permissionNames An array of permission names to check
 * @returns A boolean indicating whether the user has all of the permissions
 */
export async function hasAllPermissions(permissionNames: string[]): Promise<boolean> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return false;
    }

    const user = await db.user.findUnique({
      where: {
        email: session.user.email,
      },
      select: {
        role: true,
        permissions: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      return false;
    }

    // Admin users have all permissions
    if (user.role === "ADMIN") {
      return true;
    }

    // Check if the user has all of the specified permissions
    const userPermissionNames = user.permissions.map(p => p.name);
    return permissionNames.every(name => userPermissionNames.includes(name));
  } catch (error) {
    console.error("Error checking permissions:", error);
    return false;
  }
}
