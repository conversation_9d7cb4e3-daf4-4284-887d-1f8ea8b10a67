import { PrismaAdapter } from "@auth/prisma-adapter";
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { db } from "@/lib/db";
import bcrypt from "bcryptjs";
import { User } from "@prisma/client";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/login",
    error: "/login", // Error page
  },
  debug: process.env.NODE_ENV === "development",
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Find user by email (case insensitive)
          const user = await db.user.findFirst({
            where: {
              email: {
                equals: credentials.email,
                mode: 'insensitive', // Case insensitive search
              },
            },
          });

          if (!user) {
            console.log(`Login failed: User not found with email ${credentials.email}`);
            return null;
          }

          // Check if user is active
          if (!user.isActive) {
            console.log(`Login failed: User ${user.email} is inactive`);
            return null;
          }

          // Verify password
          const passwordMatch = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!passwordMatch) {
            console.log(`Login failed: Invalid password for user ${user.email}`);
            return null;
          }

          console.log(`Login successful: User ${user.email} (${user.role})`);

          // Return user data
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async session({ token, session }) {
      if (token) {
        // Check if there's an error in the token (e.g., inactive user)
        if (token.error) {
          // Return minimal session to force logout
          return { ...session, error: token.error };
        }

        // Add user data to session
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.role = token.role as string;

        // Add branch information if available
        if (token.branchId) {
          session.user.branchId = token.branchId as string;
          session.user.branchName = token.branchName as string;
        }
      }
      return session;
    },
    async jwt({ token, user }) {
      try {
        // Find user by email (case insensitive)
        const dbUser = await db.user.findFirst({
          where: {
            email: {
              equals: token.email as string,
              mode: 'insensitive', // Case insensitive search
            },
          },
          include: {
            branch: true, // Include branch information
          },
        });

        if (!dbUser) {
          console.log(`JWT callback: User not found with email ${token.email}`);
          if (user) {
            token.id = user.id;
          }
          return token;
        }

        // Check if user is active
        if (!dbUser.isActive) {
          console.log(`JWT callback: User ${dbUser.email} is inactive`);
          // Return token without user data to force logout
          return { ...token, error: "User is inactive" };
        }

        // Update last login time - commented out due to schema mismatch
        // await db.user.update({
        //   where: { id: dbUser.id },
        //   data: { lastLogin: new Date() },
        // });

        // Return user data with branch information
        return {
          id: dbUser.id,
          name: dbUser.name,
          email: dbUser.email,
          role: dbUser.role,
          branchId: dbUser.branchId,
          branchName: dbUser.branch?.name || null,
        };
      } catch (error) {
        console.error("JWT callback error:", error);
        return token;
      }
    },
  },
};
