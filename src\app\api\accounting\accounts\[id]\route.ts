import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/accounts/[id] - Get account by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounts
    const hasViewPermission = await hasPermission("view_accounts");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view accounts" },
        { status: 403 }
      );
    }

    // Get account by ID
    const account = await db.account.findUnique({
      where: {
        id: params.id,
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    if (!account) {
      return NextResponse.json(
        { error: "Account not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: account });
  } catch (error: any) {
    console.error("Error fetching account:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch account" },
      { status: 500 }
    );
  }
}

// PUT /api/accounting/accounts/[id] - Update account
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to update accounts
    const hasUpdatePermission = await hasPermission("manage_accounts");
    if (!hasUpdatePermission) {
      return NextResponse.json(
        { error: "You don't have permission to update accounts" },
        { status: 403 }
      );
    }

    // Parse request body
    const data = await req.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: "Account name is required" }, { status: 400 });
    }

    if (!data.code) {
      return NextResponse.json({ error: "Account code is required" }, { status: 400 });
    }

    if (!data.type) {
      return NextResponse.json({ error: "Account type is required" }, { status: 400 });
    }

    // Check if account exists
    const existingAccount = await db.account.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingAccount) {
      return NextResponse.json(
        { error: "Account not found" },
        { status: 404 }
      );
    }

    // Check if code is already used by another account
    const duplicateCode = await db.account.findFirst({
      where: {
        code: data.code,
        id: {
          not: params.id,
        },
      },
    });

    if (duplicateCode) {
      return NextResponse.json(
        { error: "An account with this code already exists" },
        { status: 400 }
      );
    }

    // Update account
    // Convert empty branchId string to null
    const branchId = data.branchId === "" ? null : data.branchId || null;
    const parentId = data.parentId === "" ? null : data.parentId || null;

    const updatedAccount = await db.account.update({
      where: {
        id: params.id,
      },
      data: {
        name: data.name,
        code: data.code,
        type: data.type,
        parentId: parentId,
        branchId: branchId,
        isActive: data.isActive !== undefined ? data.isActive : existingAccount.isActive,
      },
    });

    return NextResponse.json({ data: updatedAccount });
  } catch (error: any) {
    console.error("Error updating account:", error);
    return NextResponse.json(
      { error: error.message || "Failed to update account" },
      { status: 500 }
    );
  }
}

// DELETE /api/accounting/accounts/[id] - Delete account
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to delete accounts
    const hasDeletePermission = await hasPermission("manage_accounts");
    if (!hasDeletePermission) {
      return NextResponse.json(
        { error: "You don't have permission to delete accounts" },
        { status: 403 }
      );
    }

    // Check if account exists
    const existingAccount = await db.account.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingAccount) {
      return NextResponse.json(
        { error: "Account not found" },
        { status: 404 }
      );
    }

    // Check if account has transactions
    const journalEntries = await db.journalEntry.findFirst({
      where: {
        OR: [
          { debitAccountId: params.id },
          { creditAccountId: params.id },
        ],
      },
    });

    if (journalEntries) {
      return NextResponse.json(
        { error: "Cannot delete account with existing transactions" },
        { status: 400 }
      );
    }

    // Check if account has child accounts
    const childAccounts = await db.account.findFirst({
      where: {
        parentId: params.id,
      },
    });

    if (childAccounts) {
      return NextResponse.json(
        { error: "Cannot delete account with child accounts" },
        { status: 400 }
      );
    }

    // Delete account
    await db.account.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error deleting account:", error);
    return NextResponse.json(
      { error: error.message || "Failed to delete account" },
      { status: 500 }
    );
  }
}
