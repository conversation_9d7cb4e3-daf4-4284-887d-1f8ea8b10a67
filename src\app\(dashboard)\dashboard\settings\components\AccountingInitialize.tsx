"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, RefreshCw } from "lucide-react";

interface AccountingStatus {
  isInitialized: boolean;
  counts: {
    accounts: number;
    journals: number;
    fiscalYears: number;
  };
}

export default function AccountingInitialize() {
  const [status, setStatus] = useState<AccountingStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch accounting module status
  const fetchStatus = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/accounting/initialize');

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        if (!response.ok) {
          try {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to fetch accounting status');
          } catch (jsonError) {
            console.error("Error parsing JSON error:", jsonError);
            throw new Error('Error parsing server error response');
          }
        }

        try {
          const data = await response.json();
          setStatus(data);
        } catch (jsonError) {
          console.error("Error parsing JSON:", jsonError);
          throw new Error('Error parsing server response');
        }
      } else {
        // Handle non-JSON response
        const textResponse = await response.text();
        console.error("Non-JSON response:", textResponse);

        // Check if the response contains a specific error message
        if (textResponse.includes("Cannot read properties of undefined (reading 'CASH')")) {
          throw new Error('Error: Payment method configuration is missing. Please set up payment methods first.');
        } else {
          throw new Error('Server returned an invalid response format');
        }
      }
    } catch (error) {
      console.error('Error fetching accounting status:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      // Set default status if we can't fetch it
      setStatus({
        isInitialized: false,
        counts: {
          accounts: 0,
          journals: 0,
          fiscalYears: 0
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize accounting module
  const initializeAccounting = async () => {
    setIsInitializing(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/accounting/initialize', {
        method: 'POST',
      });

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        if (!response.ok) {
          try {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to initialize accounting module');
          } catch (jsonError) {
            console.error("Error parsing JSON error:", jsonError);
            throw new Error('Error parsing server error response');
          }
        }

        try {
          const data = await response.json();
          setSuccess(data.message || 'Accounting module initialized successfully');

          // Refresh status
          fetchStatus();
        } catch (jsonError) {
          console.error("Error parsing JSON:", jsonError);
          throw new Error('Error parsing server response');
        }
      } else {
        // Handle non-JSON response
        const textResponse = await response.text();
        console.error("Non-JSON response:", textResponse);

        // Check if the response contains a specific error message
        if (textResponse.includes("Cannot read properties of undefined (reading 'CASH')")) {
          throw new Error('Error: Payment method configuration is missing. Please set up payment methods first.');
        } else {
          throw new Error('Server returned an invalid response format');
        }
      }
    } catch (error) {
      console.error('Error initializing accounting module:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsInitializing(false);
    }
  };

  // Fetch status on component mount
  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Accounting Module</CardTitle>
        <CardDescription>
          Initialize and manage the accounting module
        </CardDescription>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Loading status...</span>
          </div>
        ) : error ? (
          <>
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>

            {error.includes('Payment method configuration is missing') && (
              <div className="mt-4 p-4 bg-blue-50 rounded-md">
                <h4 className="text-sm font-medium text-blue-800 mb-2">How to fix this issue:</h4>
                <ol className="list-decimal pl-5 text-sm text-blue-700 space-y-1">
                  <li>Go to the Settings page and set up payment methods first</li>
                  <li>Make sure you have at least the CASH payment method configured</li>
                  <li>After configuring payment methods, try initializing the accounting module again</li>
                </ol>
                <p className="mt-2 text-sm text-blue-600">
                  The accounting module requires payment methods to be set up because it creates accounts for each payment method.
                </p>
              </div>
            )}
          </>
        ) : success ? (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle className="text-green-700">Success</AlertTitle>
            <AlertDescription className="text-green-600">{success}</AlertDescription>
          </Alert>
        ) : null}

        {status && (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <span className="font-medium">Accounting Module Status:</span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${status.isInitialized ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                {status.isInitialized ? 'Initialized' : 'Not Initialized'}
              </span>
            </div>

            {status.isInitialized && (
              <div className="grid grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-sm text-blue-600">Accounts</div>
                  <div className="text-2xl font-bold text-blue-700">{status.counts.accounts}</div>
                </div>

                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-sm text-purple-600">Journals</div>
                  <div className="text-2xl font-bold text-purple-700">{status.counts.journals}</div>
                </div>

                <div className="p-4 bg-teal-50 rounded-lg">
                  <div className="text-sm text-teal-600">Fiscal Years</div>
                  <div className="text-2xl font-bold text-teal-700">{status.counts.fiscalYears}</div>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={fetchStatus}
          disabled={isLoading || isInitializing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh Status
        </Button>

        <Button
          onClick={initializeAccounting}
          disabled={isLoading || isInitializing || (status?.isInitialized && status.counts.accounts > 0)}
        >
          {isInitializing ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Initializing...
            </>
          ) : status?.isInitialized ? (
            'Already Initialized'
          ) : (
            'Initialize Accounting Module'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
