"use client";

import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { X } from "lucide-react";

interface AIAssistantSettingsProps {
  onClose: () => void;
}

interface Settings {
  isEnabled: boolean;
  autoSuggest: boolean;
  voiceEnabled: boolean;
  notificationsOn: boolean;
}

export function AIAssistantSettings({ onClose }: AIAssistantSettingsProps) {
  const [settings, setSettings] = useState<Settings>({
    isEnabled: true,
    autoSuggest: true,
    voiceEnabled: false,
    notificationsOn: true,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/ai-assistant/settings");
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error("Error fetching AI assistant settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/ai-assistant/settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        toast.success("تم حفظ الإعدادات بنجاح");
      } else {
        toast.error("حدث خطأ أثناء حفظ الإعدادات");
      }
    } catch (error) {
      console.error("Error saving AI assistant settings:", error);
      toast.error("حدث خطأ أثناء حفظ الإعدادات");
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (key: keyof Settings, value: boolean) => {
    setSettings((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium text-lg">إعدادات المساعد الذكي</h3>
        <button
          onClick={onClose}
          className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">تفعيل المساعد الذكي</p>
            <p className="text-sm text-gray-500">إظهار المساعد الذكي في التطبيق</p>
          </div>
          <Switch
            checked={settings.isEnabled}
            onCheckedChange={(checked) => handleChange("isEnabled", checked)}
            disabled={isLoading}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">اقتراحات تلقائية</p>
            <p className="text-sm text-gray-500">تقديم اقتراحات بناءً على نشاطك</p>
          </div>
          <Switch
            checked={settings.autoSuggest}
            onCheckedChange={(checked) => handleChange("autoSuggest", checked)}
            disabled={isLoading}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">تفعيل الصوت</p>
            <p className="text-sm text-gray-500">قراءة ردود المساعد بصوت مسموع</p>
          </div>
          <Switch
            checked={settings.voiceEnabled}
            onCheckedChange={(checked) => handleChange("voiceEnabled", checked)}
            disabled={isLoading}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">الإشعارات</p>
            <p className="text-sm text-gray-500">تلقي إشعارات من المساعد الذكي</p>
          </div>
          <Switch
            checked={settings.notificationsOn}
            onCheckedChange={(checked) => handleChange("notificationsOn", checked)}
            disabled={isLoading}
          />
        </div>

        <Button
          onClick={saveSettings}
          className="w-full"
          disabled={isLoading}
        >
          حفظ الإعدادات
        </Button>
      </div>
    </div>
  );
}
