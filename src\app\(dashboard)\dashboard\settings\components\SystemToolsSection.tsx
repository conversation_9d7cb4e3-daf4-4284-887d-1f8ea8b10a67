"use client";

import { useState } from "react";
import { Database, Bot, Refresh<PERSON>w, Server, Wrench, DollarSign, Tool } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import Modal from "./Modal";
import DatabaseInitForm from "./DatabaseInitForm";
import DatabaseBackupForm from "./DatabaseBackupForm";
import InitializeCoreDataForm from "./InitializeCoreDataForm";
import AddMaintenancePermissions from "./AddMaintenancePermissions";
import AccountingInitialize from "./AccountingInitialize";
import Link from "next/link";

export default function SystemToolsSection() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAISetupLoading, setIsAISetupLoading] = useState(false);
  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);

  const openDatabaseInitModal = () => {
    setModalContent(
      <DatabaseInitForm
        onClose={() => setIsModalOpen(false)}
        onSuccess={() => {
          setIsModalOpen(false);
          toast.success("Database operation completed successfully!");
        }}
      />
    );
    setIsModalOpen(true);
  };

  const openDatabaseBackupModal = () => {
    setModalContent(
      <DatabaseBackupForm
        onClose={() => setIsModalOpen(false)}
        onSuccess={() => {
          setIsModalOpen(false);
          toast.success("Database backup operation completed successfully!");
        }}
      />
    );
    setIsModalOpen(true);
  };

  const handleSetupAIAssistant = async () => {
    if (!confirm("Are you sure you want to set up the AI assistant? This will add the necessary database tables and initialize the AI assistant.")) {
      return;
    }

    setIsAISetupLoading(true);

    try {
      const response = await fetch("/api/system/ai-assistant-setup", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("AI assistant set up successfully!");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to set up AI assistant");
      }
    } catch (error) {
      console.error("Error setting up AI assistant:", error);
      toast.error("Failed to set up AI assistant");
    } finally {
      setIsAISetupLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <h4 className="text-lg font-semibold text-gray-900 mb-4">System Tools</h4>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Database Management Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-blue-600" />
              Database Management
            </CardTitle>
            <CardDescription>
              Initialize, reset, or clean your database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Use these tools to manage your database. Be careful as some operations cannot be undone.
              </p>
              <div className="flex flex-col space-y-2">
                <Button
                  variant="outline"
                  onClick={openDatabaseInitModal}
                  className="justify-start"
                >
                  <Database className="h-4 w-4 mr-2" />
                  Database Initialization
                </Button>
                <Button
                  variant="outline"
                  onClick={openDatabaseBackupModal}
                  className="justify-start"
                >
                  <Server className="h-4 w-4 mr-2" />
                  Database Backup & Restore
                </Button>
                <Link href="/dashboard/settings/backup" className="w-full">
                  <Button variant="outline" className="w-full justify-start">
                    <Server className="h-4 w-4 mr-2" />
                    Backup Settings
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AI Assistant Setup Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bot className="h-5 w-5 mr-2 text-blue-600" />
              AI Assistant Setup
            </CardTitle>
            <CardDescription>
              Set up the AI assistant for your ERP system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                The AI assistant helps users navigate the system, provides information about customers, inventory, and sales, and assists with common tasks.
              </p>
              <Button
                onClick={handleSetupAIAssistant}
                disabled={isAISetupLoading}
                className="w-full"
              >
                {isAISetupLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Setting up AI Assistant...
                  </>
                ) : (
                  <>
                    <Bot className="h-4 w-4 mr-2" />
                    Setup AI Assistant
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Fix Transactions Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wrench className="h-5 w-5 mr-2 text-blue-600" />
              Fix Transactions
            </CardTitle>
            <CardDescription>
              Fix transaction descriptions and data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Update transaction descriptions to make them more readable in account statements and fix any data inconsistencies.
              </p>
              <Link href="/dashboard/admin/fix-transactions">
                <Button className="w-full">
                  <Wrench className="h-4 w-4 mr-2" />
                  Fix Transactions
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Core Data Initialization Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Tool className="h-5 w-5 mr-2 text-blue-600" />
              Core Data Initialization
            </CardTitle>
            <CardDescription>
              Initialize core system data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Ensure all required data exists in the system for proper functionality.
              </p>
              <InitializeCoreDataForm />
            </div>
          </CardContent>
        </Card>

        {/* Maintenance Permissions Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Tool className="h-5 w-5 mr-2 text-blue-600" />
              Maintenance Permissions
            </CardTitle>
            <CardDescription>
              Set up maintenance module permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Add and configure permissions for the maintenance module.
              </p>
              <AddMaintenancePermissions />
            </div>
          </CardContent>
        </Card>

        {/* Accounting Module Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-blue-600" />
              Accounting Module
            </CardTitle>
            <CardDescription>
              Initialize accounting module
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Set up and initialize the accounting module with required accounts and journals.
              </p>
              <AccountingInitialize />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modal for database operations */}
      {isModalOpen && (
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        >
          {modalContent}
        </Modal>
      )}
    </div>
  );
}
