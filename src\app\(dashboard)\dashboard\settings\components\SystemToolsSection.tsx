"use client";

import { useState } from "react";
import { Database, Server, Wrench, DollarSign, Tool } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Modal from "./Modal";
import DatabaseInitForm from "./DatabaseInitForm";
import DatabaseBackupForm from "./DatabaseBackupForm";
import InitializeCoreDataForm from "./InitializeCoreDataForm";
import AddMaintenancePermissions from "./AddMaintenancePermissions";
import AccountingInitialize from "./AccountingInitialize";
import Link from "next/link";

export default function SystemToolsSection() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);

  const openDatabaseInitModal = () => {
    setModalContent(
      <DatabaseInitForm
        onClose={() => setIsModalOpen(false)}
        onSuccess={() => {
          setIsModalOpen(false);
          toast.success("Database operation completed successfully!");
        }}
      />
    );
    setIsModalOpen(true);
  };

  const openDatabaseBackupModal = () => {
    setModalContent(
      <DatabaseBackupForm
        onClose={() => setIsModalOpen(false)}
        onSuccess={() => {
          setIsModalOpen(false);
          toast.success("Database backup operation completed successfully!");
        }}
      />
    );
    setIsModalOpen(true);
  };



  return (
    <div className="space-y-6">
      <h4 className="text-lg font-semibold text-gray-900 mb-4">System Tools</h4>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Database Management Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-blue-600" />
              Database Management
            </CardTitle>
            <CardDescription>
              Initialize, reset, or clean your database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Use these tools to manage your database. Be careful as some operations cannot be undone.
              </p>
              <div className="flex flex-col space-y-2">
                <Button
                  variant="outline"
                  onClick={openDatabaseInitModal}
                  className="justify-start"
                >
                  <Database className="h-4 w-4 mr-2" />
                  Database Initialization
                </Button>
                <Button
                  variant="outline"
                  onClick={openDatabaseBackupModal}
                  className="justify-start"
                >
                  <Server className="h-4 w-4 mr-2" />
                  Database Backup & Restore
                </Button>
                <Link href="/dashboard/settings/backup" className="w-full">
                  <Button variant="outline" className="w-full justify-start">
                    <Server className="h-4 w-4 mr-2" />
                    Backup Settings
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>



        {/* Fix Transactions Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wrench className="h-5 w-5 mr-2 text-blue-600" />
              Fix Transactions
            </CardTitle>
            <CardDescription>
              Fix transaction descriptions and data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Update transaction descriptions to make them more readable in account statements and fix any data inconsistencies.
              </p>
              <Link href="/dashboard/admin/fix-transactions">
                <Button className="w-full">
                  <Wrench className="h-4 w-4 mr-2" />
                  Fix Transactions
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Core Data Initialization Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Tool className="h-5 w-5 mr-2 text-blue-600" />
              Core Data Initialization
            </CardTitle>
            <CardDescription>
              Initialize core system data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Ensure all required data exists in the system for proper functionality.
              </p>
              <InitializeCoreDataForm />
            </div>
          </CardContent>
        </Card>

        {/* Maintenance Permissions Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Tool className="h-5 w-5 mr-2 text-blue-600" />
              Maintenance Permissions
            </CardTitle>
            <CardDescription>
              Set up maintenance module permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Add and configure permissions for the maintenance module.
              </p>
              <AddMaintenancePermissions />
            </div>
          </CardContent>
        </Card>

        {/* Accounting Module Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-blue-600" />
              Accounting Module
            </CardTitle>
            <CardDescription>
              Initialize accounting module
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Set up and initialize the accounting module with required accounts and journals.
              </p>
              <AccountingInitialize />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modal for database operations */}
      {isModalOpen && (
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        >
          {modalContent}
        </Modal>
      )}
    </div>
  );
}
