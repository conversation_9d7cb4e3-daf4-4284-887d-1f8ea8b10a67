"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from "recharts";
import {
  Download,
  Filter,
  Calendar,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  Building,
  FileText,
  Printer,
  AlertTriangle,
  Clock,
  Truck,
  Layers,
  CheckCircle
} from "lucide-react";
import { format, subDays, subMonths, startOfMonth, endOfMonth } from "date-fns";
import Link from "next/link";

// Define chart colors
const COLORS = ['#3895e7', '#307aa8', '#4bc0c0', '#ffcd56', '#ff9f40', '#ff6384'];

export default function UnifiedDashboardPage() {
  // State for date range and filters
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedBranch, setSelectedBranch] = useState<string>("all");
  const [timeFrame, setTimeFrame] = useState<string>("month");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // State for dashboard data
  const [dashboardData, setDashboardData] = useState<any>({
    sales: {
      total: 0,
      count: 0,
      paid: 0,
      paidCount: 0,
      byDate: [],
      topProducts: []
    },
    purchases: {
      total: 0,
      count: 0,
      unpaid: 0,
      byDate: [],
      dueInvoices: []
    },
    inventory: {
      totalProducts: 0,
      lowStock: [],
      totalValue: 0,
      byCategory: []
    },
    finance: {
      cashBalance: 0,
      accountsReceivable: 0,
      accountsPayable: 0,
      paymentMethods: []
    }
  });

  // State for branches
  const [branches, setBranches] = useState<any[]>([]);

  // Fetch branches
  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (response.ok) {
          const data = await response.json();
          setBranches(data);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
      }
    };

    fetchBranches();
  }, []);

  // Update date range based on time frame
  useEffect(() => {
    const now = new Date();

    switch (timeFrame) {
      case "week":
        setStartDate(subDays(now, 7));
        setEndDate(now);
        break;
      case "month":
        setStartDate(subDays(now, 30));
        setEndDate(now);
        break;
      case "quarter":
        setStartDate(subDays(now, 90));
        setEndDate(now);
        break;
      case "year":
        setStartDate(subDays(now, 365));
        setEndDate(now);
        break;
      case "custom":
        // Don't change dates for custom timeframe
        break;
      default:
        setStartDate(subDays(now, 30));
        setEndDate(now);
    }
  }, [timeFrame]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);

      try {
        const formattedStartDate = format(startDate, 'yyyy-MM-dd');
        const formattedEndDate = format(endDate, 'yyyy-MM-dd');

        const response = await fetch(
          `/api/dashboard/unified?startDate=${formattedStartDate}&endDate=${formattedEndDate}&branchId=${selectedBranch}&timeFrame=${timeFrame}`
        );

        if (response.ok) {
          const data = await response.json();
          setDashboardData(data);
        } else {
          console.error('Error fetching dashboard data:', await response.text());
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [startDate, endDate, selectedBranch, timeFrame]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  return (
    <div className="space-y-6 print:space-y-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 print:hidden">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Unified Dashboard</h2>
          <p className="text-muted-foreground">
            Comprehensive overview of your business operations
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={() => window.print()}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="print:hidden">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Time Frame</label>
              <Tabs defaultValue={timeFrame} onValueChange={setTimeFrame} className="w-full">
                <TabsList className="grid grid-cols-5 w-full">
                  <TabsTrigger value="week">Week</TabsTrigger>
                  <TabsTrigger value="month">Month</TabsTrigger>
                  <TabsTrigger value="quarter">Quarter</TabsTrigger>
                  <TabsTrigger value="year">Year</TabsTrigger>
                  <TabsTrigger value="custom">Custom</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {timeFrame === "custom" && (
              <>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Start Date</label>
                  <DatePicker date={startDate} setDate={setStartDate} />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">End Date</label>
                  <DatePicker date={endDate} setDate={setEndDate} />
                </div>
              </>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium">Branch</label>
              <Select value={selectedBranch} onValueChange={setSelectedBranch}>
                <SelectTrigger>
                  <SelectValue placeholder="Select branch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Branches</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-3 text-lg text-gray-700">Loading dashboard data...</span>
        </div>
      ) : (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Sales</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(dashboardData.sales.total)}</h3>
                    <p className="text-sm text-gray-500 mt-1">{dashboardData.sales.count} invoices</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <ShoppingCart className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Purchases</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(dashboardData.purchases.total)}</h3>
                    <p className="text-sm text-gray-500 mt-1">{dashboardData.purchases.count} invoices</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <Truck className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Inventory Value</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(dashboardData.inventory.totalValue)}</h3>
                    <p className="text-sm text-gray-500 mt-1">{dashboardData.inventory.totalProducts} products</p>
                  </div>
                  <div className="p-3 bg-yellow-100 rounded-full">
                    <Package className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Cash Balance</p>
                    <h3 className="text-2xl font-bold mt-1">{formatCurrency(dashboardData.finance.cashBalance)}</h3>
                    <div className="flex items-center text-sm mt-1">
                      <span className={dashboardData.finance.cashBalance >= 0 ? "text-green-500" : "text-red-500"}>
                        {dashboardData.finance.cashBalance >= 0 ? (
                          <ArrowUpRight className="h-4 w-4 inline mr-1" />
                        ) : (
                          <ArrowDownRight className="h-4 w-4 inline mr-1" />
                        )}
                        {Math.abs(dashboardData.finance.cashBalance / (dashboardData.sales.total || 1) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                  <div className="p-3 bg-indigo-100 rounded-full">
                    <DollarSign className="h-6 w-6 text-indigo-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sales & Purchases Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Sales Trend</CardTitle>
                <CardDescription>Sales performance over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={dashboardData.sales.byDate}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                      <Area type="monotone" dataKey="amount" stroke="#3895e7" fill="#3895e7" fillOpacity={0.3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Purchases vs. Sales</CardTitle>
                <CardDescription>Comparison of purchases and sales</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        ...dashboardData.sales.byDate.map(item => ({
                          date: item.date,
                          sales: item.amount,
                          purchases: dashboardData.purchases.byDate.find(p => p.date === item.date)?.amount || 0
                        }))
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                      <Legend />
                      <Bar dataKey="sales" name="Sales" fill="#3895e7" />
                      <Bar dataKey="purchases" name="Purchases" fill="#307aa8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Inventory & Finance */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Inventory by Category</CardTitle>
                <CardDescription>Distribution of inventory value by category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={dashboardData.inventory.byCategory}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {dashboardData.inventory.byCategory.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Distribution of payments by method</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={dashboardData.finance.paymentMethods}
                      layout="vertical"
                      margin={{ top: 20, right: 30, left: 70, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" />
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                      <Bar dataKey="amount" fill="#3895e7" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 text-right">
                  <Link href="/dashboard/accounting/payment-methods" className="text-sm text-blue-600 hover:underline">
                    View all payment methods
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Alerts & Notifications */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Low Stock Products</CardTitle>
                <CardDescription>Products that need to be restocked</CardDescription>
              </CardHeader>
              <CardContent>
                {dashboardData.inventory.lowStock.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Package className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-lg font-medium text-gray-900">No low stock products</h3>
                    <p className="mt-1 text-sm text-gray-500">All products have sufficient stock levels.</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left">
                      <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                          <th className="px-6 py-3">Product</th>
                          <th className="px-6 py-3">Current Stock</th>
                          <th className="px-6 py-3">Min. Stock</th>
                          <th className="px-6 py-3">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.inventory.lowStock.map((product) => (
                          <tr key={product.id} className="bg-white border-b hover:bg-gray-50">
                            <td className="px-6 py-4 font-medium">
                              <Link href={`/dashboard/inventory/products/${product.id}`} className="text-blue-600 hover:underline">
                                {product.name}
                              </Link>
                            </td>
                            <td className="px-6 py-4">{product.currentStock}</td>
                            <td className="px-6 py-4">{product.minStock}</td>
                            <td className="px-6 py-4">
                              <Badge className="bg-red-100 text-red-800">
                                <AlertTriangle className="mr-1 h-3 w-3" />
                                Low Stock
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Due Invoices</CardTitle>
                <CardDescription>Upcoming and overdue purchase invoices</CardDescription>
              </CardHeader>
              <CardContent>
                {dashboardData.purchases.dueInvoices.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-lg font-medium text-gray-900">No due invoices</h3>
                    <p className="mt-1 text-sm text-gray-500">All invoices have been paid.</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left">
                      <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                          <th className="px-6 py-3">Invoice #</th>
                          <th className="px-6 py-3">Supplier</th>
                          <th className="px-6 py-3">Due Date</th>
                          <th className="px-6 py-3">Amount</th>
                          <th className="px-6 py-3">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.purchases.dueInvoices.map((invoice) => (
                          <tr key={invoice.id} className="bg-white border-b hover:bg-gray-50">
                            <td className="px-6 py-4 font-medium">
                              <Link href={`/dashboard/purchases/${invoice.id}`} className="text-blue-600 hover:underline">
                                {invoice.invoiceNumber}
                              </Link>
                            </td>
                            <td className="px-6 py-4">{invoice.supplierName}</td>
                            <td className="px-6 py-4">{format(new Date(invoice.dueDate), 'MMM d, yyyy')}</td>
                            <td className="px-6 py-4 font-medium">{formatCurrency(invoice.amount)}</td>
                            <td className="px-6 py-4">
                              <Badge className={
                                invoice.status === "overdue" ? "bg-red-100 text-red-800" :
                                invoice.status === "due-today" ? "bg-orange-100 text-orange-800" :
                                "bg-yellow-100 text-yellow-800"
                              }>
                                {invoice.status === "overdue" ? (
                                  <AlertTriangle className="mr-1 h-3 w-3" />
                                ) : (
                                  <Clock className="mr-1 h-3 w-3" />
                                )}
                                {invoice.status === "overdue" ? "Overdue" :
                                 invoice.status === "due-today" ? "Due Today" : "Due Soon"}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
