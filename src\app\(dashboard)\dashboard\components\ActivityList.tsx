"use client";

import { motion } from "framer-motion";
import { formatDistanceToNow } from "date-fns";
import { ar } from "date-fns/locale";
import Link from "next/link";

interface Activity {
  id: string;
  type: "sale" | "purchase" | "inventory" | "customer";
  title: string;
  description: string;
  date: string;
  amount?: number;
  status?: string;
  link: string;
}

interface ActivityListProps {
  activities: Activity[];
  title: string;
  emptyMessage?: string;
  icon?: React.ReactNode;
}

export default function ActivityList({
  activities,
  title,
  emptyMessage = "No recent activity",
  icon,
}: ActivityListProps) {
  // Function to get status color
  const getStatusColor = (status?: string) => {
    switch (status?.toUpperCase()) {
      case "PAID":
        return "bg-green-100 text-green-800";
      case "UNPAID":
        return "bg-yellow-100 text-yellow-800";
      case "PARTIALLY_PAID":
        return "bg-orange-100 text-orange-800";
      case "COMPLETED":
        return "bg-blue-100 text-blue-800";
      case "PENDING":
        return "bg-purple-100 text-purple-800";
      case "CANCELLED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: ar });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
      className="bg-white shadow-lg rounded-lg overflow-hidden"
    >
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          {icon && <span className="mr-2">{icon}</span>}
          {title}
        </h3>
        {activities.length > 0 && (
          <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
            {activities.length}
          </span>
        )}
      </div>

      {activities.length > 0 ? (
        <ul className="divide-y divide-gray-200">
          {activities.map((activity, index) => (
            <motion.li
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2, delay: 0.1 * index }}
              className="px-6 py-4 hover:bg-gray-50"
            >
              <Link href={activity.link} className="block">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {activity.title}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {activity.description}
                    </p>
                  </div>
                  <div className="flex flex-col items-end">
                    {activity.amount !== undefined && (
                      <span className="text-sm font-semibold text-gray-900">
                        {activity.amount.toFixed(2)} EGP
                      </span>
                    )}
                    {activity.status && (
                      <span
                        className={`mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(
                          activity.status
                        )}`}
                      >
                        {activity.status}
                      </span>
                    )}
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {formatDate(activity.date)}
                </div>
              </Link>
            </motion.li>
          ))}
        </ul>
      ) : (
        <div className="px-6 py-8 text-center">
          <p className="text-sm text-gray-500">{emptyMessage}</p>
        </div>
      )}
    </motion.div>
  );
}
