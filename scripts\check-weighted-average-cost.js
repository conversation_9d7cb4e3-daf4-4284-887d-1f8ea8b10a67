// Script to check weighted average cost calculation
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking weighted average cost calculation...');

    // Get a product ID from command line arguments or use a default
    const productId = process.argv[2] || null;

    // Build query
    const query = {
      include: {
        inventory: true,
      },
      orderBy: {
        name: 'asc',
      },
    };

    // If product ID is provided, filter by it
    if (productId) {
      query.where = { id: productId };
    }

    // Get products with their inventory
    const products = await prisma.product.findMany(query);

    console.log(`Found ${products.length} products`);

    // For each product, calculate what the weighted average cost should be
    for (const product of products) {
      console.log(`\nProduct: ${product.name} (ID: ${product.id})`);
      console.log(`Current cost price: ${product.costPrice}`);

      // Get all purchase items for this product
      const purchaseItems = await prisma.purchaseItem.findMany({
        where: {
          productId: product.id,
        },
        include: {
          purchase: true,
        },
        orderBy: {
          purchase: {
            date: 'asc',
          },
        },
      });

      console.log(`Found ${purchaseItems.length} purchase items`);

      if (purchaseItems.length === 0) {
        console.log('No purchase items found, skipping...');
        continue;
      }

      // Calculate weighted average cost
      let totalQuantity = 0;
      let totalValue = 0;

      for (const item of purchaseItems) {
        const quantity = item.quantity;
        const unitPrice = item.unitPrice;
        const itemValue = quantity * unitPrice;

        totalQuantity += quantity;
        totalValue += itemValue;

        console.log(`Purchase ${item.purchase.invoiceNumber}: ${quantity} units at ${unitPrice} = ${itemValue}`);
      }

      const weightedAverageCost = totalQuantity > 0 ? totalValue / totalQuantity : 0;

      console.log(`\nCalculated weighted average cost: ${weightedAverageCost.toFixed(2)}`);
      console.log(`Current product cost price: ${product.costPrice}`);
      console.log(`Difference: ${(weightedAverageCost - product.costPrice).toFixed(2)}`);

      // Check inventory
      if (product.inventory && product.inventory.length > 0) {
        console.log('\nInventory:');
        for (const inv of product.inventory) {
          console.log(`- Warehouse ${inv.warehouseId}: ${inv.quantity} units at ${inv.costPrice}`);
        }
      } else {
        console.log('\nNo inventory found');
      }

      // Ask if user wants to update the cost price
      if (process.argv.includes('--update')) {
        console.log(`\nUpdating product cost price to ${weightedAverageCost.toFixed(2)}...`);
        
        // Update product cost price
        await prisma.product.update({
          where: {
            id: product.id,
          },
          data: {
            costPrice: parseFloat(weightedAverageCost.toFixed(2)),
          },
        });

        // Update inventory cost price
        if (product.inventory && product.inventory.length > 0) {
          for (const inv of product.inventory) {
            await prisma.inventory.update({
              where: {
                id: inv.id,
              },
              data: {
                costPrice: parseFloat(weightedAverageCost.toFixed(2)),
              },
            });
          }
        }

        console.log('Update completed');
      }
    }
  } catch (error) {
    console.error('Error checking weighted average cost:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
