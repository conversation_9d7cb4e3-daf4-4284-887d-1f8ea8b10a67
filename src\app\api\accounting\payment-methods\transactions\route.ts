import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/payment-methods/transactions - Get transactions by payment method
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounting data
    const hasViewPermission = await hasPermission("view_accounting");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view payment method transactions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const paymentMethodId = url.searchParams.get("paymentMethodId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const period = url.searchParams.get("period") || "month";
    const limit = parseInt(url.searchParams.get("limit") || "100");
    const page = parseInt(url.searchParams.get("page") || "1");

    // Validate required parameters
    if (!paymentMethodId) {
      return NextResponse.json(
        { error: "Payment method ID is required" },
        { status: 400 }
      );
    }

    // Get payment method
    const paymentMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id: paymentMethodId,
      },
      include: {
        account: true,
        journal: true,
      },
    });

    if (!paymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    // Build date filter
    const dateFilter: any = {};
    if (startDate && endDate) {
      dateFilter.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      dateFilter.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      dateFilter.date = {
        lte: new Date(endDate),
      };
    } else {
      // Default to current month if no dates provided
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      dateFilter.date = {
        gte: firstDayOfMonth,
      };
    }

    // Get transactions from journal entries
    let transactions = [];
    let totalCount = 0;

    if (paymentMethod.journalId) {
      // Get journal entries for this payment method's journal
      const journalEntries = await db.journalEntry.findMany({
        where: {
          journalId: paymentMethod.journalId,
          ...dateFilter,
        },
        include: {
          debitAccount: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          creditAccount: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          contact: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
        orderBy: {
          date: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      // Count total entries
      totalCount = await db.journalEntry.count({
        where: {
          journalId: paymentMethod.journalId,
          ...dateFilter,
        },
      });

      // Format journal entries as transactions
      transactions = journalEntries.map(entry => ({
        id: entry.id,
        date: entry.date,
        description: entry.description,
        amount: entry.amount,
        type: entry.debitAccount.id === paymentMethod.accountId ? "DEBIT" : "CREDIT",
        account: entry.debitAccount.id === paymentMethod.accountId 
          ? entry.creditAccount 
          : entry.debitAccount,
        contact: entry.contact,
        reference: entry.reference,
        referenceType: entry.referenceType,
      }));
    } else if (paymentMethod.accountId) {
      // If no journal is linked, get entries that reference this account
      const accountEntries = await db.journalEntry.findMany({
        where: {
          OR: [
            { debitAccountId: paymentMethod.accountId },
            { creditAccountId: paymentMethod.accountId },
          ],
          ...dateFilter,
        },
        include: {
          journal: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          debitAccount: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          creditAccount: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          contact: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
        orderBy: {
          date: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      // Count total entries
      totalCount = await db.journalEntry.count({
        where: {
          OR: [
            { debitAccountId: paymentMethod.accountId },
            { creditAccountId: paymentMethod.accountId },
          ],
          ...dateFilter,
        },
      });

      // Format account entries as transactions
      transactions = accountEntries.map(entry => ({
        id: entry.id,
        date: entry.date,
        description: entry.description,
        amount: entry.amount,
        type: entry.debitAccountId === paymentMethod.accountId ? "DEBIT" : "CREDIT",
        account: entry.debitAccountId === paymentMethod.accountId 
          ? entry.creditAccount 
          : entry.debitAccount,
        journal: entry.journal,
        contact: entry.contact,
        reference: entry.reference,
        referenceType: entry.referenceType,
      }));
    }

    // Calculate summary statistics
    const totalDebits = transactions
      .filter(t => t.type === "DEBIT")
      .reduce((sum, t) => sum + t.amount, 0);
    
    const totalCredits = transactions
      .filter(t => t.type === "CREDIT")
      .reduce((sum, t) => sum + t.amount, 0);
    
    const balance = totalDebits - totalCredits;

    return NextResponse.json({
      success: true,
      data: {
        paymentMethod,
        transactions,
        summary: {
          totalDebits,
          totalCredits,
          balance,
        },
        pagination: {
          total: totalCount,
          page,
          limit,
          pages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching payment method transactions:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch payment method transactions" },
      { status: 500 }
    );
  }
}
