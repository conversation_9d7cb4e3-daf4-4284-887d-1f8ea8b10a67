import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

/**
 * GET /api/print-templates
 * Retrieve print templates
 */
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const isDefault = searchParams.get('default') === 'true';
    const language = searchParams.get('language');
    const branchId = searchParams.get('branchId');
    
    let whereClause: any = {};
    
    if (type) {
      whereClause.type = type;
    }
    
    if (isDefault) {
      whereClause.isDefault = true;
    }
    
    if (language) {
      whereClause.language = language;
    }
    
    if (branchId) {
      whereClause.branchId = branchId;
    }
    
    const templates = await db.printTemplate.findMany({
      where: whereClause,
      orderBy: {
        name: 'asc'
      }
    });
    
    // If no templates exist and a type is specified, create default templates
    if (templates.length === 0 && type) {
      const defaultTemplates = await createDefaultTemplates(type);
      return NextResponse.json(defaultTemplates);
    }
    
    return NextResponse.json(templates);
  } catch (error) {
    console.error('Error fetching print templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch print templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/print-templates
 * Create a new print template
 */
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const data = await request.json();
    
    // Validate the data
    if (!data.name || !data.type || !data.content) {
      return NextResponse.json(
        { error: 'Name, type, and content are required' },
        { status: 400 }
      );
    }
    
    // If this is set as default, unset any other default templates of the same type
    if (data.isDefault) {
      await db.printTemplate.updateMany({
        where: {
          type: data.type,
          isDefault: true,
          language: data.language || 'ar',
          branchId: data.branchId || null
        },
        data: {
          isDefault: false
        }
      });
    }
    
    // Create the template
    const template = await db.printTemplate.create({
      data: {
        name: data.name,
        type: data.type,
        content: data.content,
        isDefault: data.isDefault || false,
        language: data.language || 'ar',
        paperSize: data.paperSize || 'A4',
        orientation: data.orientation || 'portrait',
        branchId: data.branchId || null,
        variables: data.variables || null,
        previewImage: data.previewImage || null
      }
    });
    
    // If this is the first template of this type, update print settings
    if (data.isDefault) {
      const settings = await db.printSettings.findFirst();
      if (settings && settings.defaultTemplateSettings) {
        const defaultTemplateSettings = settings.defaultTemplateSettings as any;
        defaultTemplateSettings[data.type] = {
          ...defaultTemplateSettings[data.type],
          defaultTemplateId: template.id
        };
        
        await db.printSettings.update({
          where: { id: settings.id },
          data: {
            defaultTemplateSettings
          }
        });
      }
    }
    
    return NextResponse.json(template);
  } catch (error) {
    console.error('Error creating print template:', error);
    return NextResponse.json(
      { error: 'Failed to create print template' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to create default templates
 */
async function createDefaultTemplates(type: string) {
  let template;
  
  switch (type) {
    case 'invoice':
      template = await db.printTemplate.create({
        data: {
          name: 'Default Invoice',
          type: 'invoice',
          content: `
            <div class="invoice">
              <div class="invoice-header">
                <h1>{{company_name}}</h1>
                <div class="invoice-info">
                  <div>Invoice #: {{invoice_number}}</div>
                  <div>Date: {{date}}</div>
                </div>
              </div>
              <div class="customer-info">
                <h2>Customer</h2>
                <div>{{customer_details}}</div>
              </div>
              <div class="invoice-items">
                {{invoice_items}}
              </div>
              <div class="invoice-summary">
                <div class="summary-row"><span>Subtotal:</span> <span>{{subtotal}}</span></div>
                <div class="summary-row"><span>Tax:</span> <span>{{tax}}</span></div>
                <div class="summary-row"><span>Discount:</span> <span>{{discount}}</span></div>
                <div class="summary-row total"><span>Total:</span> <span>{{total}}</span></div>
              </div>
            </div>
          `,
          isDefault: true,
          language: 'ar',
          paperSize: 'A4',
          orientation: 'portrait'
        }
      });
      break;
      
    case 'receipt':
      template = await db.printTemplate.create({
        data: {
          name: 'Default Receipt',
          type: 'receipt',
          content: `
            <div class="receipt">
              <div class="receipt-header">
                <h1>{{company_name}}</h1>
                <div class="receipt-info">
                  <div>Receipt #: {{receipt_number}}</div>
                  <div>Date: {{date}}</div>
                </div>
              </div>
              <div class="receipt-items">
                {{items}}
              </div>
              <div class="receipt-summary">
                <div class="summary-row total"><span>Total:</span> <span>{{total}}</span></div>
                <div class="summary-row"><span>Payment Method:</span> <span>{{payment_method}}</span></div>
              </div>
            </div>
          `,
          isDefault: true,
          language: 'ar',
          paperSize: '80mm',
          orientation: 'portrait'
        }
      });
      break;
      
    case 'purchase_order':
      template = await db.printTemplate.create({
        data: {
          name: 'Default Purchase Order',
          type: 'purchase_order',
          content: `
            <div class="purchase-order">
              <div class="po-header">
                <h1>{{company_name}}</h1>
                <div class="po-info">
                  <div>PO #: {{po_number}}</div>
                  <div>Date: {{date}}</div>
                </div>
              </div>
              <div class="supplier-info">
                <h2>Supplier</h2>
                <div>{{supplier_details}}</div>
              </div>
              <div class="po-items">
                {{items}}
              </div>
              <div class="po-summary">
                <div class="summary-row"><span>Subtotal:</span> <span>{{subtotal}}</span></div>
                <div class="summary-row"><span>Tax:</span> <span>{{tax}}</span></div>
                <div class="summary-row"><span>Discount:</span> <span>{{discount}}</span></div>
                <div class="summary-row total"><span>Total:</span> <span>{{total}}</span></div>
              </div>
            </div>
          `,
          isDefault: true,
          language: 'ar',
          paperSize: 'A4',
          orientation: 'portrait'
        }
      });
      break;
      
    case 'maintenance_receipt':
      template = await db.printTemplate.create({
        data: {
          name: 'Default Maintenance Receipt',
          type: 'maintenance_receipt',
          content: `
            <div class="maintenance-receipt">
              <div class="receipt-header">
                <h1>{{company_name}}</h1>
                <div class="receipt-info">
                  <div>Receipt #: {{receipt_number}}</div>
                  <div>Date: {{date}}</div>
                </div>
              </div>
              <div class="customer-info">
                <h2>Customer</h2>
                <div>{{customer_details}}</div>
              </div>
              <div class="device-info">
                <h2>Device Information</h2>
                <div>{{device_details}}</div>
              </div>
              <div class="issue-description">
                <h2>Issue Description</h2>
                <div>{{issue_description}}</div>
              </div>
              <div class="receipt-footer">
                <div class="signatures">
                  <div class="signature-box">
                    <div>Customer Signature</div>
                    <div class="signature-line">_________________</div>
                  </div>
                  <div class="signature-box">
                    <div>Technician Signature</div>
                    <div class="signature-line">_________________</div>
                  </div>
                </div>
              </div>
            </div>
          `,
          isDefault: true,
          language: 'ar',
          paperSize: 'A5',
          orientation: 'portrait'
        }
      });
      break;
      
    // Add more default templates for other document types
      
    default:
      template = null;
  }
  
  return template ? [template] : [];
}
