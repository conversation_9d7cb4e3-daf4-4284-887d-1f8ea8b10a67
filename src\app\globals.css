@import "tailwindcss";

:root {
  /* Base colors */
  --background: #ffffff;
  --foreground: #171717;

  /* Text colors */
  --input-text: #000000;
  --input-placeholder: #4b5563;
  --label-text: #111827;
  --table-header: #111827;
  --table-text: #1f2937;

  /* Primary colors */
  --primary-color: #3895e7; /* Light Blue */
  --primary-light: #64b5f6; /* Lighter blue */
  --primary-dark: #307aa8; /* Darker blue */
  --primary-hover: #2c6f9c;
  --primary-text: #ffffff;

  /* Secondary colors */
  --secondary-color: #f3f4f6;
  --secondary-hover: #e5e7eb;
  --secondary-text: #111827;

  /* Accent colors */
  --accent-color: #307aa8; /* Dark Blue */
  --accent-light: #3895e7;
  --accent-dark: #1e5b80;

  /* Border colors */
  --input-border: #d1d5db;
  --input-focus-border: #3895e7;
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;

  /* Sidebar colors - Updated with blue to green gradient */
  --sidebar-from: #3895e7; /* Light Blue */
  --sidebar-to: #2ecc71; /* Green */
  --sidebar-hover: rgba(255, 255, 255, 0.15);
  --sidebar-active: rgba(255, 255, 255, 0.25);
  --sidebar-text: #ffffff;
  --sidebar-text-hover: #ffffff;

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* وضع الليل (Dark Mode) */
.dark,
.dark-mode {
  /* Base colors */
  --background: #0f172a; /* Dark blue */
  --foreground: #f8fafc;

  /* Text colors */
  --input-text: #f1f5f9;
  --input-placeholder: #64748b;
  --label-text: #f3f4f6;
  --table-header: #f3f4f6;
  --table-text: #e5e7eb;

  /* Primary colors */
  --primary-color: #3895e7; /* Light Blue */
  --primary-light: #64b5f6;
  --primary-dark: #307aa8;
  --primary-hover: #307aa8;
  --primary-text: #ffffff;

  /* Secondary colors */
  --secondary-color: #1e293b;
  --secondary-hover: #334155;
  --secondary-text: #f1f5f9;

  /* Accent colors */
  --accent-color: #3895e7; /* Light Blue in dark mode */
  --accent-light: #64b5f6;
  --accent-dark: #307aa8;

  /* Border colors */
  --input-border: #475569;
  --input-focus-border: #3895e7;
  --border-light: #334155;
  --border-medium: #475569;

  /* Sidebar colors - Updated with blue to green gradient */
  --sidebar-from: #3895e7; /* Light Blue */
  --sidebar-to: #2ecc71; /* Green */
  --sidebar-hover: rgba(255, 255, 255, 0.15);
  --sidebar-active: rgba(255, 255, 255, 0.25);
  --sidebar-text: #f1f5f9;
  --sidebar-text-hover: #ffffff;

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* تحسين مربعات الإدخال */
input, textarea, select {
  color: var(--input-text) !important;
  border-color: var(--input-border) !important;
  font-weight: 500 !important;
}

input::placeholder, textarea::placeholder, select::placeholder {
  color: var(--input-placeholder) !important;
  opacity: 0.8 !important;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--input-focus-border) !important;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2) !important;
  outline: none !important;
}

/* تحسين تسميات الحقول */
label {
  color: var(--label-text) !important;
  font-weight: 600 !important;
}

/* تحسين نصوص الجداول */
table th {
  color: var(--table-header) !important;
  font-weight: 600 !important;
}

table td {
  color: var(--table-text) !important;
  font-weight: 500 !important;
}

/* تحسين أزرار الاختيار */
.dropdown-item {
  color: var(--table-text) !important;
  background-color: white !important;
}

/* تحسين القوائم المنسدلة */
select option {
  color: black !important;
  background-color: white !important;
}

/* تحسين تجربة النقر لجميع العناصر التفاعلية */
button,
a,
.clickable,
[role="button"],
input[type="button"],
input[type="submit"],
input[type="reset"],
.sidebar-item,
.ripple {
  transition: transform 0.1s ease-in-out !important;
}

button:active,
a:active,
.clickable:active,
[role="button"]:active,
input[type="button"]:active,
input[type="submit"]:active,
input[type="reset"]:active,
.sidebar-item:active,
.ripple:active {
  transform: scale(0.97) !important;
}

/* تحسين الأزرار الأساسية */
.btn-primary,
button[type="submit"],
.bg-indigo-600:not(nav *),
.bg-indigo-700:not(nav *),
.bg-indigo-500:not(nav *),
.bg-blue-600:not(nav *),
.bg-blue-700:not(nav *),
.bg-blue-500:not(nav *),
[class*="bg-indigo-"]:not(nav *),
[class*="bg-blue-"]:not(nav *),
.text-white.bg-indigo-600:not(nav *),
.text-white.bg-blue-600:not(nav *) {
  background-color: var(--primary-color) !important;
  color: var(--primary-text) !important;
  border-color: var(--primary-color) !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.btn-primary:hover,
button[type="submit"]:hover,
.hover\:bg-indigo-700:hover:not(nav *),
.hover\:bg-indigo-600:hover:not(nav *),
.hover\:bg-blue-700:hover:not(nav *),
.hover\:bg-blue-600:hover:not(nav *),
.bg-indigo-600:hover:not(nav *),
.bg-blue-600:hover:not(nav *),
.text-white.bg-indigo-600:hover:not(nav *),
.text-white.bg-blue-600:hover:not(nav *),
[class*="bg-indigo-"]:hover:not(nav *),
[class*="bg-blue-"]:hover:not(nav *) {
  background-color: var(--primary-hover) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين الأزرار الثانوية */
.btn-secondary,
.bg-gray-100,
.bg-gray-200 {
  background-color: var(--secondary-color) !important;
  color: var(--secondary-text) !important;
  border-color: var(--border-light) !important;
}

.btn-secondary:hover,
.hover\:bg-gray-200:hover,
.hover\:bg-gray-100:hover {
  background-color: var(--secondary-hover) !important;
}

/* تحسين الروابط */
a {
  color: var(--primary-color) !important;
  font-weight: 500 !important;
}

a:hover {
  color: var(--primary-hover) !important;
}

/* تحسين التلميحات */
.dark-mode .group-hover\:block {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
  border-color: #475569 !important;
}

/* أنماط خاصة للقائمة الجانبية - تحديث مع التدرج المطلوب */
.sidebar {
  background: linear-gradient(to bottom, var(--sidebar-from), var(--sidebar-to)) !important;
  box-shadow: var(--shadow-lg) !important;
  transition: all 0.3s ease-in-out !important;
}

.sidebar-item {
  display: flex !important;
  align-items: center !important;
  padding: 0.75rem 1rem !important;
  margin-bottom: 0.25rem !important;
  border-radius: 0.5rem !important;
  transition: all 0.3s ease-in-out !important;
  color: var(--sidebar-text) !important;
  font-weight: 500 !important;
  position: relative !important;
  overflow: hidden !important;
}

.sidebar-item:hover {
  background-color: var(--sidebar-hover) !important;
  transform: translateX(0.25rem) !important;
  color: var(--sidebar-text-hover) !important;
}

.sidebar-item.active {
  background-color: var(--sidebar-active) !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

.sidebar-icon {
  margin-right: 0.75rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
  transition: transform 0.3s ease !important;
}

.sidebar-item:hover .sidebar-icon {
  transform: scale(1.1) !important;
}

/* Smooth rotation for dropdown arrows */
.sidebar-item svg {
  transition: transform 0.3s ease-in-out !important;
}

/* Material Design Ripple Effect - Mejorado */
.ripple {
  position: relative !important;
  overflow: hidden !important;
  transform: translate3d(0, 0, 0) !important;
}

.ripple:after {
  content: "" !important;
  display: block !important;
  position: absolute !important;
  width: 100% !important;
  height: 100% !important;
  top: 0 !important;
  left: 0 !important;
  pointer-events: none !important;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%) !important;
  background-repeat: no-repeat !important;
  background-position: 50% !important;
  transform: scale(10, 10) !important;
  opacity: 0 !important;
  transition: transform 0.4s ease-out, opacity 0.8s ease-out !important;
  will-change: transform, opacity !important;
}

.ripple:active:after {
  transform: scale(0, 0) !important;
  opacity: 0.4 !important;
  transition: 0s !important;
}

/* Efecto de elevación para tarjetas y elementos interactivos */
.card,
.interactive-element,
.hover-shadow {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

.card:hover,
.interactive-element:hover,
.hover-shadow:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;
}

nav a, nav a:hover {
  transition: background-color 0.3s ease !important;
  transform: none !important;
  box-shadow: none !important;
}
