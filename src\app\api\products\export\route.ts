import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import * as XLSX from 'xlsx';

// GET /api/products/export - Export products to Excel
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view products
    const hasViewPermission = await hasPermission("view_products");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to export products" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const categoryId = url.searchParams.get("categoryId");
    const search = url.searchParams.get("search");

    // Build filter object
    const filter: any = {};

    // Add category filter if provided
    if (categoryId) {
      filter.categoryId = categoryId;
    }

    // Add search filter if provided
    if (search) {
      filter.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get products from database
    const products = await db.product.findMany({
      where: filter,
      include: {
        category: true,
        specifications: true,
        inventory: {
          include: {
            warehouse: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Transform the data for Excel export
    const productsForExport = products.map(product => {
      // Calculate total inventory
      const totalInventory = product.inventory.reduce((total, item) => total + item.quantity, 0);
      
      // Get inventory by warehouse
      const inventoryByWarehouse = product.inventory.reduce((acc, item) => {
        acc[item.warehouse.name] = item.quantity;
        return acc;
      }, {} as Record<string, number>);

      // Get specifications as a single string
      const specificationsStr = product.specifications
        .map(spec => `${spec.name}: ${spec.value}`)
        .join(', ');

      return {
        ID: product.id,
        Name: product.name,
        Description: product.description || '',
        Category: product.category.name,
        'Base Price': product.basePrice,
        'Cost Price': product.costPrice,
        'Total Inventory': totalInventory,
        'Is Customizable': product.isCustomizable ? 'Yes' : 'No',
        'Is Component': product.isComponent ? 'Yes' : 'No',
        'Component Type': product.componentType || '',
        'Specifications': specificationsStr,
        ...inventoryByWarehouse
      };
    });

    // Create a worksheet
    const worksheet = XLSX.utils.json_to_sheet(productsForExport);
    
    // Create a workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');
    
    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
    
    // Set headers for file download
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="products.xlsx"'
      }
    });
  } catch (error) {
    console.error("Error exporting products:", error);
    return NextResponse.json(
      { error: "Failed to export products" },
      { status: 500 }
    );
  }
}
