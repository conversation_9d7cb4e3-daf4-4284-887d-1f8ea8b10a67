import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/products - Get all products
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view products
    const hasViewPermission = await hasPermission("view_products") || session.user.role === "ADMIN";

    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view products" },
        { status: 403 }
      );
    }

    // Log user information
    console.log("User accessing products:", {
      email: session.user.email,
      role: session.user.role
    });

    // Get query parameters
    const url = new URL(req.url);
    const categoryId = url.searchParams.get("categoryId");
    const search = url.searchParams.get("search");
    const isComponent = url.searchParams.get("isComponent");
    const componentType = url.searchParams.get("componentType");

    // Build filter object
    const filter: any = {};

    if (categoryId) {
      filter.categoryId = categoryId;
    }

    if (isComponent === "true") {
      filter.isComponent = true;
    } else if (isComponent === "false") {
      filter.isComponent = false;
    }

    if (componentType) {
      filter.componentType = {
        contains: componentType,
        mode: "insensitive"
      };
    }

    // Add search filter if provided
    if (search) {
      filter.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get products from database
    const products = await db.product.findMany({
      where: filter,
      include: {
        category: true,
        specifications: true,
        inventory: {
          include: {
            warehouse: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Transform the data to include warehouse names
    const transformedProducts = products.map(product => {
      // Create a clean object without circular references
      const cleanProduct = {
        ...product,
        // Format inventory with warehouse names
        inventory: product.inventory.map(inv => ({
          warehouseId: inv.warehouseId,
          warehouseName: inv.warehouse.name,
          quantity: inv.quantity,
        })),
      };

      return cleanProduct;
    });

    return NextResponse.json(transformedProducts);
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 }
    );
  }
}

// POST /api/products - Create a new product
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add products
    const hasAddPermission = await hasPermission("add_products");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add products" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.categoryId || data.basePrice === undefined) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Create the product
    const product = await db.product.create({
      data: {
        name: data.name,
        description: data.description || "",
        basePrice: data.basePrice,
        costPrice: data.costPrice || 0,
        categoryId: data.categoryId,
        isCustomizable: data.isCustomizable || false,
        isComponent: data.isComponent || false,
        componentType: data.isComponent ? data.componentType : null,
      },
    });

    // Create specifications if provided
    if (data.specifications && data.specifications.length > 0) {
      for (const spec of data.specifications) {
        await db.specification.create({
          data: {
            name: spec.name,
            value: spec.value,
            productId: product.id,
          },
        });
      }
    }

    // Get the created product with specifications
    const createdProduct = await db.product.findUnique({
      where: {
        id: product.id,
      },
      include: {
        category: true,
        specifications: true,
      },
    });

    return NextResponse.json(createdProduct, { status: 201 });
  } catch (error) {
    console.error("Error creating product:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create product" },
      { status: 500 }
    );
  }
}