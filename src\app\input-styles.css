/* تحسينات إضافية لمربعات الإدخال */

/* تحسين مربعات الإدخال الأساسية - تحسين التباين */
.form-input,
.shadow-sm,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="date"],
textarea,
select {
  color: #000000 !important;
  font-weight: 500 !important;
  border-width: 1px !important;
  border-color: #9ca3af !important; /* رمادي متوسط لتحسين التباين */
  background-color: #ffffff !important;
  transition: all 0.2s ease-in-out !important;
}

/* تحسين حالة التركيز */
.form-input:focus,
.shadow-sm:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="date"]:focus,
textarea:focus,
select:focus {
  border-color: #3895e7 !important; /* أزرق */
  box-shadow: 0 0 0 2px rgba(56, 149, 231, 0.25) !important;
  outline: none !important;
}

/* تحسين النص الافتراضي */
.form-input::placeholder,
.shadow-sm::placeholder,
input::placeholder,
textarea::placeholder {
  color: #6b7280 !important; /* رمادي أغمق للتباين */
  opacity: 0.9 !important;
}

/* تحسين تسميات الحقول */
label,
.form-label,
.block.text-sm {
  color: #111827 !important;
  font-weight: 600 !important;
  margin-bottom: 0.25rem !important;
}

/* تحسين رؤوس الجداول */
thead th,
th.px-6,
th[scope="col"] {
  color: #111827 !important;
  font-weight: 600 !important;
  background-color: #f3f4f6 !important;
}

/* تحسين خلايا الجداول */
tbody td,
td.px-6 {
  color: #1f2937 !important;
  font-weight: 500 !important;
}

/* تحسين القوائم المنسدلة */
select,
.form-select {
  background-color: white !important;
  color: #000000 !important;
  font-weight: 500 !important;
}

select option,
.form-select option {
  color: #000000 !important;
  background-color: white !important;
  font-weight: 500 !important;
}

/* تحسين مربعات البحث */
input[type="search"],
input[id*="search"],
input[placeholder*="Search"] {
  background-color: white !important;
  color: #000000 !important;
  border-color: #d1d5db !important;
  font-weight: 500 !important;
}

/* تحسين أزرار الاختيار في القوائم المنسدلة */
.dropdown-item,
.dropdown-option,
.select-option {
  color: #000000 !important;
  background-color: white !important;
  font-weight: 500 !important;
}

/* تحسين الخطوط في الأزرار */
button,
.btn,
.button {
  font-weight: 600 !important;
}

/* تحسين الأزرار الأساسية - تحديث للون الأزرق الفاتح */
.bg-indigo-600:not(nav *),
.bg-indigo-500:not(nav *),
.bg-indigo-700:not(nav *),
.bg-blue-600:not(nav *),
.bg-blue-500:not(nav *),
.bg-blue-700:not(nav *),
button[type="submit"],
button.primary,
.btn-primary,
.button-primary,
[class*="bg-indigo"]:not(nav *),
[class*="bg-blue"]:not(nav *),
.text-white.bg-indigo-600:not(nav *),
.text-white.bg-blue-600:not(nav *),
a.bg-indigo-600:not(nav *),
a.bg-blue-600:not(nav *),
button.bg-indigo-600:not(nav *),
button.bg-blue-600:not(nav *),
.focus\:ring-indigo-500:focus,
.focus\:ring-blue-500:focus {
  background-color: #3895e7 !important; /* أزرق فاتح */
  color: #ffffff !important; /* أبيض */
  border-color: #3895e7 !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* تحسين الأزرار الأساسية عند التحويم */
.hover\:bg-indigo-700:hover:not(nav *),
.hover\:bg-indigo-600:hover:not(nav *),
.hover\:bg-blue-700:hover:not(nav *),
.hover\:bg-blue-600:hover:not(nav *),
button[type="submit"]:hover,
button.primary:hover,
.btn-primary:hover,
.button-primary:hover,
.bg-indigo-600:hover:not(nav *),
.bg-blue-600:hover:not(nav *),
.text-white.bg-indigo-600:hover:not(nav *),
.text-white.bg-blue-600:hover:not(nav *),
a.bg-indigo-600:hover:not(nav *),
a.bg-blue-600:hover:not(nav *),
button.bg-indigo-600:hover:not(nav *),
button.bg-blue-600:hover:not(nav *),
[class*="bg-indigo"]:hover:not(nav *),
[class*="bg-blue"]:hover:not(nav *) {
  background-color: #307aa8 !important; /* تركواز */
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين حلقة التركيز للأزرار */
.focus\:ring-indigo-500:focus,
.focus\:border-indigo-500:focus,
.focus\:ring-blue-500:focus,
.focus\:border-blue-500:focus,
button:focus,
.btn:focus,
.button:focus {
  --tw-ring-color: rgba(56, 149, 231, 0.5) !important;
  --tw-ring-offset-color: #3895e7 !important;
  border-color: #3895e7 !important;
  outline: none !important;
}

/* تحسين نصوص العناوين */
h1, h2, h3, h4, h5, h6 {
  color: #111827 !important;
  font-weight: 700 !important;
}

/* تحسين نصوص الفقرات */
p, span, div {
  color: #1f2937 !important;
}

/* استثناءات للنصوص في الأزرار والعناصر الخاصة */
button *, .btn *, .button *, .sidebar *, .sidebar-item * {
  color: inherit !important;
}

/* تحسين الروابط - تحديث للون الأزرق الفاتح */
a {
  color: #3895e7 !important;
  font-weight: 500 !important;
  transition: all 0.2s ease-in-out !important;
  text-decoration: none !important;
}

a:hover {
  color: #307aa8 !important;
  text-decoration: underline !important;
}

/* تحسين الروابط في الأزرار */
a.btn, a.button, button a {
  text-decoration: none !important;
}

/* تحسين الروابط النشطة */
a.active, .active a {
  font-weight: 600 !important;
}
