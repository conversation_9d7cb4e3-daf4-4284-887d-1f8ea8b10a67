import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/transactions - Get all transactions
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to view transactions
    const hasViewPermission = await hasPermission("view_accounts");
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view transactions" },
        { status: 403 }
      );
    }
    
    // Get query parameters
    const url = new URL(req.url);
    const accountId = url.searchParams.get("accountId");
    const contactId = url.searchParams.get("contactId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const type = url.searchParams.get("type");
    const referenceType = url.searchParams.get("referenceType");
    
    // Build filter object
    const filter: any = {};
    
    if (accountId) {
      filter.accountId = accountId;
    }
    
    if (contactId) {
      filter.contactId = contactId;
    }
    
    if (type) {
      filter.type = type;
    }
    
    if (referenceType) {
      filter.referenceType = referenceType;
    }
    
    // Date range filter
    if (startDate && endDate) {
      filter.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      filter.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      filter.date = {
        lte: new Date(endDate),
      };
    }
    
    // Get transactions from database
    const transactions = await db.transaction.findMany({
      where: filter,
      include: {
        account: true,
        contact: true,
      },
      orderBy: {
        date: "desc",
      },
    });
    
    return NextResponse.json(transactions);
  } catch (error) {
    console.error("Error fetching transactions:", error);
    return NextResponse.json(
      { error: "Failed to fetch transactions" },
      { status: 500 }
    );
  }
}

// POST /api/transactions - Create a new transaction
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to add transactions
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add transactions" },
        { status: 403 }
      );
    }
    
    const data = await req.json();
    
    // Validate required fields
    if (!data.accountId || !data.amount || !data.type || !data.description) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Start a transaction
    const result = await db.$transaction(async (tx) => {
      // Get the account
      const account = await tx.account.findUnique({
        where: {
          id: data.accountId,
        },
      });
      
      if (!account) {
        throw new Error("Account not found");
      }
      
      // Create the transaction
      const transaction = await tx.transaction.create({
        data: {
          accountId: data.accountId,
          contactId: data.contactId || null,
          amount: data.amount,
          type: data.type,
          description: data.description,
          reference: data.reference || null,
          referenceType: data.referenceType || null,
          paymentMethod: data.paymentMethod || null,
          transactionNumber: data.transactionNumber || null,
          date: data.date ? new Date(data.date) : new Date(),
        },
      });
      
      // Update account balance
      const balanceChange = data.type === "DEBIT" ? data.amount : -data.amount;
      
      await tx.account.update({
        where: {
          id: data.accountId,
        },
        data: {
          balance: account.balance + balanceChange,
        },
      });
      
      // Update contact balance if provided
      if (data.contactId) {
        const contact = await tx.contact.findUnique({
          where: {
            id: data.contactId,
          },
        });
        
        if (contact) {
          // For customer receipts (DEBIT) or supplier payments (CREDIT)
          let contactBalanceChange = 0;
          
          if (data.referenceType === "RECEIPT" && data.type === "DEBIT") {
            // Customer payment received - decrease customer balance
            contactBalanceChange = -data.amount;
          } else if (data.referenceType === "PAYMENT" && data.type === "CREDIT") {
            // Payment to supplier - decrease supplier balance
            contactBalanceChange = -data.amount;
          } else if (data.referenceType === "SALE" && data.type === "DEBIT") {
            // Sale to customer - increase customer balance
            contactBalanceChange = data.amount;
          } else if (data.referenceType === "PURCHASE" && data.type === "CREDIT") {
            // Purchase from supplier - increase supplier balance
            contactBalanceChange = data.amount;
          }
          
          await tx.contact.update({
            where: {
              id: data.contactId,
            },
            data: {
              balance: contact.balance + contactBalanceChange,
            },
          });
        }
      }
      
      return transaction;
    });
    
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Error creating transaction:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create transaction" },
      { status: 500 }
    );
  }
}
