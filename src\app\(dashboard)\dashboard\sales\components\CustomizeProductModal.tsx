"use client";

import { useState, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";

// Component types - using string type to support dynamic component types
type ComponentType = string;

// Component interface
interface Component {
  id: string;
  name: string;
  type: ComponentType;
  price: number;
  stock: number;
  capacity?: string; // For storage and RAM
  speed?: string; // For RAM and CPU
  description?: string;
  count?: number; // For multiple RAM sticks
}

// Product interface
interface Product {
  id: string;
  name: string;
  basePrice: number;
  isCustomizable: boolean;
  availableComponents?: {
    [key in ComponentType]?: Component[];
  };
}

interface CustomizeProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  onConfirm: (customizedProduct: CustomizedProduct, existingItemId?: string) => void;
}

export interface CustomizedProduct {
  productId: string;
  productName: string;
  basePrice: number;
  costPrice: number; // Add cost price for validation
  totalPrice: number;
  selectedComponents: {
    [key in ComponentType]?: Component;
  };
  editingItemId?: string; // Add optional editingItemId for editing existing items
}

// Empty components data structure
const emptyComponents: { [key: string]: Component[] } = {};

export default function CustomizeProductModal({
  isOpen,
  onClose,
  product,
  onConfirm,
}: CustomizeProductModalProps) {
  // Check if we're editing an existing item (using editingItemId from product)
  const isEditing = (product as any).editingItemId !== undefined;
  const editingItemId = (product as any).editingItemId;
  const currentComponents = (product as any).currentComponents;

  // State for available components from API
  const [apiComponents, setApiComponents] = useState<{ [key in ComponentType]?: Component[] }>(emptyComponents);
  const [isLoadingComponents, setIsLoadingComponents] = useState(false);

  // Fetch component types from API
  useEffect(() => {
    const fetchComponentTypes = async () => {
      setIsLoadingComponents(true);
      try {
        console.log("Fetching component types for product:", product);
        console.log("Product is customizable:", product.isCustomizable);
        console.log("Product has availableComponents:", !!product.availableComponents);

        const response = await fetch('/api/component-types');
        if (response.ok) {
          const componentTypes = await response.json();
          console.log("Component types fetched:", componentTypes);

          // Fetch all products that are components
          try {
            console.log("Fetching all component products...");
            const productsResponse = await fetch(`/api/products?isComponent=true`);
            if (productsResponse.ok) {
              const allComponentProducts = await productsResponse.json();
              console.log("All component products:", allComponentProducts);

              // Group products by component type
              const componentsByType: { [key in ComponentType]?: Component[] } = { ...emptyComponents };

              // Initialize component types with empty arrays
              for (const componentType of componentTypes) {
                componentsByType[componentType.name as ComponentType] = [];
              }

              // Group products by their component type
              for (const product of allComponentProducts) {
                if (product.componentType) {
                  const typeName = product.componentType;

                  // Skip products with unknown component types
                  if (!componentsByType[typeName]) {
                    console.log(`Creating new component type array for: ${typeName}`);
                    componentsByType[typeName] = [];
                  }

                  // Calculate total stock across all warehouses
                  const totalStock = product.inventory && Array.isArray(product.inventory)
                    ? product.inventory.reduce((total: number, inv: any) => total + inv.quantity, 0)
                    : 0;

                  // Create the component object
                  const component = {
                    id: product.id,
                    name: product.name,
                    type: typeName as ComponentType,
                    price: product.basePrice,
                    stock: totalStock,
                    capacity: product.specifications && Array.isArray(product.specifications)
                      ? product.specifications.find((spec: any) => spec.name === 'Capacity')?.value
                      : undefined,
                    speed: product.specifications && Array.isArray(product.specifications)
                      ? product.specifications.find((spec: any) => spec.name === 'Speed')?.value
                      : undefined,
                    description: product.description,
                    componentTypeName: typeName,
                  };

                  // Add to the appropriate type array
                  componentsByType[typeName].push(component);
                }
              }

              console.log("Grouped components by type:", componentsByType);
              setApiComponents(componentsByType);
            } else {
              console.error("Failed to fetch component products:", await productsResponse.text());
            }
          } catch (error) {
            console.error("Error fetching component products:", error);
          }
        } else {
          console.error("Failed to fetch component types:", await response.text());
        }
      } catch (error) {
        console.error('Error fetching component types:', error);
      } finally {
        setIsLoadingComponents(false);
      }
    };

    if (isOpen) {
      fetchComponentTypes();
    }
  }, [isOpen]);

  // Initialize selected components with values from existing item if available
  const [selectedComponents, setSelectedComponents] = useState<{
    [key in ComponentType]?: Component;
  }>(isEditing && currentComponents ? currentComponents : {});

  // Set initial price based on existing item or product base price
  const initialPrice = product.basePrice;
  const [totalPrice, setTotalPrice] = useState(initialPrice);

  // Set available components from product, API, or use empty components
  const availableComponents = product.availableComponents || apiComponents || emptyComponents;

  // State for custom price - initialize from existing item if editing
  const [useCustomPrice, setUseCustomPrice] = useState(isEditing);
  const [customPrice, setCustomPrice] = useState(initialPrice);
  const [costPrice, setCostPrice] = useState(product.basePrice);

  // Update total price when components change
  useEffect(() => {
    let newCostPrice = product.basePrice;

    Object.values(selectedComponents).forEach((component) => {
      if (component) {
        newCostPrice += component.price;
      }
    });

    setCostPrice(newCostPrice);

    if (!useCustomPrice) {
      setTotalPrice(newCostPrice);
      setCustomPrice(newCostPrice);
    }
  }, [selectedComponents, product.basePrice, useCustomPrice]);

  // Handle custom price change
  const handleCustomPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value)) {
      // Allow any value during typing
      setCustomPrice(value);
      setTotalPrice(value);
    }
  };

  // Handle custom price blur (validate when focus leaves the field)
  const handleCustomPriceBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (isNaN(value) || value < costPrice) {
      // Don't allow price below cost
      alert(`Price cannot be lower than cost price (${costPrice.toFixed(2)} ج.م)`);
      setCustomPrice(costPrice);
      setTotalPrice(costPrice);
    }
  };

  // Calculate total capacity for RAM or storage components
  const calculateTotalCapacity = (component: Component): string => {
    if (!component || !component.count) {
      return "0GB";
    }

    // Try to extract capacity from component name if capacity field is not available
    let capacityStr = component.capacity;
    if (!capacityStr) {
      // Look for patterns like "8GB", "8G", "8 GB", etc. in the component name
      const nameMatch = component.name.match(/(\d+)\s*[GgTt][Bb]?/);
      if (nameMatch) {
        capacityStr = nameMatch[0];
      } else {
        console.log("Could not extract capacity from component:", component);
        return "0GB";
      }
    }

    // Extract numeric value from capacity string (e.g., "8GB", "8G", "8 GB" -> 8)
    const capacityMatch = capacityStr.match(/(\d+)/);
    if (!capacityMatch) {
      console.log("Could not parse capacity value from:", capacityStr);
      return "0GB";
    }

    const capacityValue = parseInt(capacityMatch[1]);
    const totalCapacity = capacityValue * component.count;

    console.log(`Calculated total capacity: ${capacityValue} × ${component.count} = ${totalCapacity}GB`);

    return `${totalCapacity}GB`;
  };

  // Handle component selection
  const handleComponentChange = (type: ComponentType, componentId: string) => {
    if (componentId === "") {
      // Remove component if empty selection
      const newSelectedComponents = { ...selectedComponents };
      delete newSelectedComponents[type];
      setSelectedComponents(newSelectedComponents);
      return;
    }

    const component = availableComponents[type]?.find(c => c.id === componentId);

    if (component) {
      // For RAM, set default count to 1
      if (type === "RAM") {
        setSelectedComponents({
          ...selectedComponents,
          [type]: {
            ...component,
            count: 1
          },
        });
      } else {
        setSelectedComponents({
          ...selectedComponents,
          [type]: component,
        });
      }
    }
  };

  // Handle confirmation
  const handleConfirm = () => {
    const customizedProduct: CustomizedProduct = {
      productId: product.id,
      productName: product.name,
      basePrice: product.basePrice,
      costPrice: costPrice, // Add cost price for validation
      totalPrice,
      selectedComponents,
    };

    if (isEditing && editingItemId) {
      // Add the editingItemId to the customizedProduct object
      customizedProduct.editingItemId = editingItemId;
      // Pass the customized product with the editingItemId
      onConfirm(customizedProduct);
    } else {
      // Create new item
      onConfirm(customizedProduct);
    }
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900"
                >
                  {isEditing ? `Edit ${product.name}` : `Customize ${product.name}`}
                </Dialog.Title>

                <div className="mt-4 space-y-4">
                  <div className="bg-gray-50 p-4 rounded-md">
                    <p className="text-sm text-gray-500">Base Price: {product.basePrice.toFixed(2)} ج.م</p>
                    <p className="text-sm text-gray-500 mt-1">Cost Price: {costPrice.toFixed(2)} ج.م</p>

                    <div className="mt-3 flex items-center">
                      <input
                        id="custom-price-toggle"
                        name="custom-price-toggle"
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        checked={useCustomPrice}
                        onChange={(e) => setUseCustomPrice(e.target.checked)}
                      />
                      <label htmlFor="custom-price-toggle" className="ml-2 block text-sm text-gray-700">
                        Set custom price
                      </label>
                    </div>

                    {useCustomPrice && (
                      <div className="mt-2">
                        <label htmlFor="custom-price" className="block text-sm font-medium text-gray-700">
                          Custom Price (min: {costPrice.toFixed(2)} ج.م)
                        </label>
                        <div className="mt-1 relative rounded-md shadow-sm">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 sm:text-sm">ج.م</span>
                          </div>
                          <input
                            type="number"
                            name="custom-price"
                            id="custom-price"
                            step="0.01"
                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-12 pr-12 sm:text-sm border-gray-300 rounded-md"
                            value={customPrice}
                            onChange={handleCustomPriceChange}
                            onBlur={handleCustomPriceBlur}
                          />
                        </div>
                      </div>
                    )}

                    <p className="text-sm font-bold text-gray-900 mt-3">Total Price: {totalPrice.toFixed(2)} ج.م</p>
                  </div>

                  {/* Ordered Component Types */}
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {isLoadingComponents ? (
                      <div className="col-span-2 flex justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                        <span className="ml-2 text-sm text-gray-500">Loading components...</span>
                      </div>
                    ) : (
                      <>
                        {/* RAM Section - Always First and Full Width */}
                        {availableComponents["RAM"] && availableComponents["RAM"].length > 0 && (
                          <div className="col-span-2">
                            <label className="block text-sm font-medium text-gray-700">RAM</label>
                            <div className="flex space-x-2">
                              <div className="flex-grow">
                                <select
                                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                                  value={selectedComponents["RAM"]?.id || ""}
                                  onChange={(e) => handleComponentChange("RAM", e.target.value)}
                                >
                                  <option value="" className="text-black font-medium">Select RAM</option>
                                  {availableComponents["RAM"].map((component) => (
                                    <option
                                      key={component.id}
                                      value={component.id}
                                      disabled={component.stock <= 0}
                                      className="text-black font-medium"
                                    >
                                      {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
                                    </option>
                                  ))}
                                </select>
                              </div>
                              {selectedComponents["RAM"] && (
                                <div className="w-32">
                                  <select
                                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                                    value={selectedComponents["RAM"]?.count || 1}
                                    onChange={(e) => {
                                      const count = parseInt(e.target.value);
                                      const component = selectedComponents["RAM"];
                                      if (component && !isNaN(count) && count > 0 && count <= component.stock) {
                                        const updatedComponent = {
                                          ...component,
                                          count: count,
                                          price: (component.price / (component.count || 1)) * count
                                        };
                                        setSelectedComponents({
                                          ...selectedComponents,
                                          "RAM": updatedComponent
                                        });
                                      }
                                    }}
                                  >
                                    {[1, 2, 3, 4].map((num) => (
                                      <option
                                        key={num}
                                        value={num}
                                        disabled={num > (selectedComponents["RAM"]?.stock || 0)}
                                        className="text-black font-medium"
                                      >
                                        {num} {num === 1 ? 'Stick' : 'Sticks'}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              )}
                            </div>
                            {selectedComponents["RAM"] &&
                             selectedComponents["RAM"]?.count &&
                             selectedComponents["RAM"]?.count! > 1 &&
                             selectedComponents["RAM"]?.capacity && (
                              <div className="mt-1 text-xs text-green-600">
                                Total RAM: {calculateTotalCapacity(selectedComponents["RAM"])}
                              </div>
                            )}
                          </div>
                        )}

                        {/* Storage Section - HDD and SSD side by side */}
                        {(availableComponents["HDD"]?.length > 0 || availableComponents["SSD"]?.length > 0) && (
                          <>
                            {/* HDD Selection */}
                            {availableComponents["HDD"]?.length > 0 && (
                              <div>
                                <label className="block text-sm font-medium text-gray-700">HDD</label>
                                <select
                                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                                  value={selectedComponents["HDD"]?.id || ""}
                                  onChange={(e) => handleComponentChange("HDD", e.target.value)}
                                >
                                  <option value="" className="text-black font-medium">Select HDD</option>
                                  {availableComponents["HDD"].map((component) => (
                                    <option
                                      key={component.id}
                                      value={component.id}
                                      disabled={component.stock <= 0}
                                      className="text-black font-medium"
                                    >
                                      {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            )}

                            {/* SSD Selection */}
                            {availableComponents["SSD"]?.length > 0 && (
                              <div>
                                <label className="block text-sm font-medium text-gray-700">SATA SSD</label>
                                <select
                                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                                  value={selectedComponents["SSD"]?.id || ""}
                                  onChange={(e) => handleComponentChange("SSD", e.target.value)}
                                >
                                  <option value="" className="text-black font-medium">Select SATA SSD</option>
                                  {availableComponents["SSD"].map((component) => (
                                    <option
                                      key={component.id}
                                      value={component.id}
                                      disabled={component.stock <= 0}
                                      className="text-black font-medium"
                                    >
                                      {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            )}
                          </>
                        )}

                        {/* NVMe Section */}
                        {availableComponents["NVMe"]?.length > 0 && (
                          <div className={availableComponents["CPU"]?.length > 0 || availableComponents["GPU"]?.length > 0 ? "" : "col-span-2"}>
                            <label className="block text-sm font-medium text-gray-700">NVMe SSD</label>
                            <select
                              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                              value={selectedComponents["NVMe"]?.id || ""}
                              onChange={(e) => handleComponentChange("NVMe", e.target.value)}
                            >
                              <option value="" className="text-black font-medium">Select NVMe SSD</option>
                              {availableComponents["NVMe"].map((component) => (
                                <option
                                  key={component.id}
                                  value={component.id}
                                  disabled={component.stock <= 0}
                                  className="text-black font-medium"
                                >
                                  {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
                                </option>
                              ))}
                            </select>
                          </div>
                        )}

                        {/* CPU and GPU Section - side by side */}
                        {(availableComponents["CPU"]?.length > 0 || availableComponents["GPU"]?.length > 0) && (
                          <>
                            {/* CPU Selection */}
                            {availableComponents["CPU"]?.length > 0 && (
                              <div>
                                <label className="block text-sm font-medium text-gray-700">CPU</label>
                                <select
                                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                                  value={selectedComponents["CPU"]?.id || ""}
                                  onChange={(e) => handleComponentChange("CPU", e.target.value)}
                                >
                                  <option value="" className="text-black font-medium">Select CPU</option>
                                  {availableComponents["CPU"].map((component) => (
                                    <option
                                      key={component.id}
                                      value={component.id}
                                      disabled={component.stock <= 0}
                                      className="text-black font-medium"
                                    >
                                      {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            )}

                            {/* GPU Selection */}
                            {availableComponents["GPU"]?.length > 0 && (
                              <div>
                                <label className="block text-sm font-medium text-gray-700">Graphics Card</label>
                                <select
                                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                                  value={selectedComponents["GPU"]?.id || ""}
                                  onChange={(e) => handleComponentChange("GPU", e.target.value)}
                                >
                                  <option value="" className="text-black font-medium">Select Graphics Card</option>
                                  {availableComponents["GPU"].map((component) => (
                                    <option
                                      key={component.id}
                                      value={component.id}
                                      disabled={component.stock <= 0}
                                      className="text-black font-medium"
                                    >
                                      {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            )}
                          </>
                        )}

                        {/* Other Component Types */}
                        {Object.entries(availableComponents).map(([type, components]) => {
                          // Skip if no components for this type or if it's one of the special types we already handled
                          if (!components || components.length === 0 ||
                              ["RAM", "HDD", "SSD", "NVMe", "CPU", "GPU"].includes(type)) {
                            return null;
                          }

                          return (
                            <div key={type}>
                              <label className="block text-sm font-medium text-gray-700">{type}</label>
                              <select
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
                                value={selectedComponents[type as ComponentType]?.id || ""}
                                onChange={(e) => handleComponentChange(type as ComponentType, e.target.value)}
                              >
                                <option value="" className="text-black font-medium">Select {type}</option>
                                {components.map((component) => (
                                  <option
                                    key={component.id}
                                    value={component.id}
                                    disabled={component.stock <= 0}
                                    className="text-black font-medium"
                                  >
                                    {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
                                  </option>
                                ))}
                              </select>
                            </div>
                          );
                        })}
                      </>
                    )}
                  </div>

                  {/* Selected Components Summary */}
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900">Selected Components</h4>
                    <div className="mt-2 border border-gray-200 rounded-md overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Component</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specification</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Base Product</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.basePrice.toFixed(2)} ج.م</td>
                          </tr>
                          {Object.entries(selectedComponents).map(([type, component]) => (
                            <tr key={type}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{type}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {component.name}
                                {type === "RAM" && component.count && component.count > 1 && (
                                  <span className="ml-1 text-xs text-indigo-600">
                                    ({component.count} sticks, total {calculateTotalCapacity(component)})
                                  </span>
                                )}
                                {(type === "SSD" || type === "HDD" || type === "NVMe") && component.capacity && (
                                  <span className="ml-1 text-xs text-gray-500">
                                    ({component.capacity})
                                  </span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{component.price.toFixed(2)} ج.م</td>
                            </tr>
                          ))}
                          {useCustomPrice && (
                            <tr className="bg-yellow-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" colSpan={2}>Custom Price Adjustment</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {(totalPrice - costPrice).toFixed(2)} ج.م
                              </td>
                            </tr>
                          )}
                          <tr className="bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" colSpan={2}>Total</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">{totalPrice.toFixed(2)} ج.م</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2"
                    onClick={handleConfirm}
                  >
                    Confirm
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
