/**
 * <PERSON><PERSON><PERSON> to apply accounting migration
 *
 * This script sets up the accounting module in your database
 * It uses Prisma directly instead of relying on external tools like psql
 */

const { execSync } = require('child_process');
const readline = require('readline');
const { PrismaClient } = require('@prisma/client');

// Initialize Prisma client
const prisma = new PrismaClient();

// Function to prompt for input
function prompt(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

// Function to check database connection
async function checkDatabaseConnection() {
  try {
    // Try a simple query to check connection
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection error:', error.message);
    return false;
  }
}

// Main function
async function main() {
  try {
    console.log('=== Accounting Migration Tool ===');
    console.log('This tool will set up the accounting module in your database.');
    console.log('It will initialize default accounts and journals.');
    console.log('');

    // Check database connection
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      console.log('Could not connect to the database. Please check your connection settings.');

      if (!process.env.DATABASE_URL) {
        console.log('DATABASE_URL environment variable is not set.');
        console.log('Please provide your PostgreSQL connection details:');

        const user = await prompt('Username (default: openpg): ') || 'openpg';
        const password = await prompt('Password (default: openpgpwd): ') || 'openpgpwd';
        const host = await prompt('Host (default: localhost): ') || 'localhost';
        const port = await prompt('Port (default: 5432): ') || '5432';
        const database = await prompt('Database name (default: vero): ') || 'vero';

        // Set DATABASE_URL environment variable
        process.env.DATABASE_URL = `postgresql://${user}:${password}@${host}:${port}/${database}`;
        console.log(`Using connection: postgresql://${user}:***@${host}:${port}/${database}`);
      }

      // Try to reconnect with new settings
      await prisma.$disconnect();
      await prisma.$connect();
    } else {
      console.log('Successfully connected to the database.');
    }

    console.log('\nStarting accounting migration...');

    // Run the setup-accounting-prisma.js script directly
    // This avoids issues with external tools like psql
    require('./setup-accounting-prisma');

    console.log('\nAccounting migration completed successfully!');
    console.log('\nYou can now use the accounting module in your application.');
    console.log('The following features are now available:');
    console.log('- Chart of Accounts');
    console.log('- Journals and Journal Entries');
    console.log('- General Ledger');
    console.log('- Trial Balance');
    console.log('- Profit & Loss Statement');
    console.log('- Balance Sheet');

  } catch (error) {
    console.error('\nError during accounting migration:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

// Run the main function
main();
