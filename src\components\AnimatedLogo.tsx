'use client';

import React, { useEffect, useState } from 'react';

const AnimatedLogo: React.FC = () => {
  const [isAnimating, setIsAnimating] = useState(true);

  useEffect(() => {
    // إعادة تشغيل الحركة كل 5 دقائق (300000 مللي ثانية)
    const interval = setInterval(() => {
      setIsAnimating(false);
      // إعادة تشغيل الحركة بعد 50 مللي ثانية للسماح بإعادة تعيين الحالة
      setTimeout(() => {
        setIsAnimating(true);
      }, 50);
    }, 300000); // 5 دقائق

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`animated-logo ${isAnimating ? 'animate' : ''}`}>
      <svg width="140" height="36" viewBox="0 0 170 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        {/* V */}
        <path
          d="M10 6L16 26H19L25 6H22L19 20L16 6H10Z"
          fill="white"
          className="logo-letter"
        />
        {/* E */}
        <path
          d="M30 6V26H42V23H34V18H40V15H34V9H42V6H30Z"
          fill="white"
          className="logo-letter"
        />
        {/* R */}
        <path
          d="M47 6V26H51V19H53L57 26H61L56 18C58 17.5 60 16 60 13C60 10 58 6 54 6H47ZM51 9H53C55 9 56 10 56 13C56 15 55 16 53 16H51V9Z"
          fill="white"
          className="logo-letter"
        />
        {/* O */}
        <path
          d="M66 16C66 10 70 5 76 5C82 5 86 10 86 16C86 22 82 27 76 27C70 27 66 22 66 16ZM70 16C70 20 72 23 76 23C80 23 82 20 82 16C82 12 80 9 76 9C72 9 70 12 70 16Z"
          fill="white"
          className="logo-letter"
        />

        {/* E */}
        <path
          d="M95 6V26H107V23H99V18H105V15H99V9H107V6H95Z"
          fill="white"
          className="logo-letter"
        />

        {/* R */}
        <path
          d="M112 6V26H116V19H118L122 26H126L121 18C123 17.5 125 16 125 13C125 10 123 6 119 6H112ZM116 9H118C120 9 121 10 121 13C121 15 120 16 118 16H116V9Z"
          fill="white"
          className="logo-letter"
        />

        {/* P */}
        <path
          d="M131 6V26H135V16H141C144 16 147 14 147 11C147 8 144 6 141 6H131ZM135 9H140C142 9 143 10 143 11C143 12 142 13 140 13H135V9Z"
          fill="white"
          className="logo-letter"
        />

        {/* Animated line under the logo */}
        <path
          d="M30 32H120"
          stroke="white"
          strokeWidth="1.5"
          strokeDasharray="90"
          strokeDashoffset="90"
          className="logo-line"
        />
      </svg>

      <style jsx>{`
        .animated-logo {
          position: relative;
          display: inline-block;
          margin-top: -4px;
          margin-bottom: -4px;
        }

        .animated-logo:not(.animate) .logo-letter {
          opacity: 1;
          transform: translateY(0);
        }

        .animated-logo:not(.animate) .logo-line {
          stroke-dashoffset: 0;
        }

        .animate .logo-letter {
          opacity: 0;
          animation: fadeIn 0.4s ease-out forwards;
        }

        .animate .logo-letter:nth-child(1) {
          animation-delay: 0.05s;
        }

        .animate .logo-letter:nth-child(2) {
          animation-delay: 0.1s;
        }

        .animate .logo-letter:nth-child(3) {
          animation-delay: 0.15s;
        }

        .animate .logo-letter:nth-child(4) {
          animation-delay: 0.2s;
        }

        .animate .logo-letter:nth-child(5) {
          animation-delay: 0.25s;
        }

        .animate .logo-letter:nth-child(6) {
          animation-delay: 0.3s;
        }

        .animate .logo-letter:nth-child(7) {
          animation-delay: 0.35s;
        }

        .animate .logo-line {
          animation: drawLine 0.5s ease-out 0.4s forwards;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(5px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes drawLine {
          to {
            stroke-dashoffset: 0;
          }
        }
      `}</style>
    </div>
  );
};

export default AnimatedLogo;
