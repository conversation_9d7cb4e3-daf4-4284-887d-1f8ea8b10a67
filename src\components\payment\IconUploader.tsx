"use client";

import React, { useState, useRef } from 'react';
import { Upload, X, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface IconUploaderProps {
  onIconUploaded: (iconUrl: string) => void;
  className?: string;
}

export default function IconUploader({ onIconUploaded, className = '' }: IconUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please upload an image file');
      return;
    }

    // Validate file size (max 100KB)
    if (file.size > 100 * 1024) {
      setError('Image size should be less than 100KB');
      return;
    }

    setError(null);
    setIsUploading(true);

    try {
      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Create form data for upload
      const formData = new FormData();
      formData.append('file', file);

      // Upload to server
      const response = await fetch('/api/upload/icon', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload icon');
      }

      const data = await response.json();
      onIconUploaded(data.url);
    } catch (error) {
      console.error('Error uploading icon:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload icon');
      setPreview(null);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/png,image/jpeg,image/svg+xml"
        className="hidden"
      />

      {preview ? (
        <div className="relative mb-2">
          <img
            src={preview}
            alt="Icon preview"
            className="w-16 h-16 object-contain border border-gray-300 rounded-md p-1"
          />
          <button
            onClick={() => setPreview(null)}
            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-0.5"
          >
            <X size={14} />
          </button>
        </div>
      ) : (
        <div
          onClick={() => fileInputRef.current?.click()}
          className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center cursor-pointer hover:border-indigo-500 transition-colors mb-2"
        >
          <Upload size={24} className="text-gray-400" />
        </div>
      )}

      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => fileInputRef.current?.click()}
        disabled={isUploading}
        className="mb-1"
      >
        {isUploading ? 'Uploading...' : preview ? 'Change Icon' : 'Upload Icon'}
      </Button>

      {error && (
        <div className="text-red-500 text-xs flex items-center mt-1">
          <AlertCircle size={12} className="mr-1" />
          {error}
        </div>
      )}

      {preview && !error && (
        <div className="text-green-500 text-xs flex items-center mt-1">
          <Check size={12} className="mr-1" />
          Icon ready to use
        </div>
      )}

      <p className="text-xs text-gray-500 mt-1">
        Max size: 100KB. Formats: PNG, JPEG, SVG
      </p>
    </div>
  );
}
