"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

interface Branch {
  id: string;
  name: string;
}

interface AccountFormProps {
  account?: {
    id: string;
    name: string;
    type: string;
    balance: number;
    branchId?: string;
    isDefault: boolean;
    accountNumber?: string;
    code?: string;
    parentId?: string;
  };
  onClose: () => void;
  onSuccess: () => void;
}

export default function AccountForm({ account, onClose, onSuccess }: AccountFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isFetchingBranches, setIsFetchingBranches] = useState(false);
  const [accounts, setAccounts] = useState<any[]>([]);
  const [isFetchingAccounts, setIsFetchingAccounts] = useState(false);

  const [formData, setFormData] = useState({
    name: account?.name || "",
    type: account?.type || "ASSET",
    balance: account?.balance || 0,
    branchId: account?.branchId || "",
    isDefault: account?.isDefault || false,
    code: account?.code || "",
    accountNumber: account?.accountNumber || "",
    parentId: account?.parentId || "",
    isConsolidated: false, // New field to indicate if this is a consolidated account
  });

  // Fetch branches
  useEffect(() => {
    const fetchBranches = async () => {
      setIsFetchingBranches(true);
      try {
        const response = await fetch("/api/branches");
        if (response.ok) {
          const data = await response.json();
          setBranches(data);
        }
      } catch (error) {
        console.error("Error fetching branches:", error);
      } finally {
        setIsFetchingBranches(false);
      }
    };

    fetchBranches();
  }, []);

  // Fetch accounts for parent account selection
  useEffect(() => {
    const fetchAccounts = async () => {
      if (account?.id) {
        // Don't include the current account in the list of potential parents
        setIsFetchingAccounts(true);
        try {
          const response = await fetch("/api/accounts");
          if (response.ok) {
            const data = await response.json();
            // Filter out the current account and its children
            const filteredAccounts = data.filter((acc: any) => acc.id !== account.id);
            setAccounts(filteredAccounts);
          }
        } catch (error) {
          console.error("Error fetching accounts:", error);
        } finally {
          setIsFetchingAccounts(false);
        }
      } else {
        // For new accounts, fetch all accounts
        setIsFetchingAccounts(true);
        try {
          const response = await fetch("/api/accounts");
          if (response.ok) {
            const data = await response.json();
            setAccounts(data);
          }
        } catch (error) {
          console.error("Error fetching accounts:", error);
        } finally {
          setIsFetchingAccounts(false);
        }
      }
    };

    fetchAccounts();
  }, [account?.id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const url = account ? `/api/accounts/${account.id}` : "/api/accounts";
      const method = account ? "PATCH" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save account");
      }

      onSuccess();
      router.refresh();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-lg font-medium mb-4">
        {account ? "Edit Account" : "Add New Account"}
      </h2>

      {error && (
        <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Account Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
            />
          </div>

          <div>
            <label htmlFor="code" className="block text-sm font-medium text-gray-700">
              Account Code
            </label>
            <input
              type="text"
              id="code"
              name="code"
              value={formData.code}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              placeholder="e.g. 1010, 2020, etc."
            />
          </div>

          <div>
            <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700">
              Account Number (System Generated if Empty)
            </label>
            <input
              type="text"
              id="accountNumber"
              name="accountNumber"
              value={formData.accountNumber}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              placeholder="Will be auto-generated if left empty"
            />
          </div>

          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700">
              Account Type
            </label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
            >
              <option value="ASSET">Asset</option>
              <option value="LIABILITY">Liability</option>
              <option value="EQUITY">Equity</option>
              <option value="REVENUE">Revenue</option>
              <option value="EXPENSE">Expense</option>
            </select>
          </div>

          <div>
            <label htmlFor="balance" className="block text-sm font-medium text-gray-700">
              Initial Balance
            </label>
            <input
              type="number"
              id="balance"
              name="balance"
              value={formData.balance}
              onChange={handleNumberChange}
              step="0.01"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
            />
          </div>

          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="isConsolidated"
              name="isConsolidated"
              checked={formData.isConsolidated}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
            />
            <label htmlFor="isConsolidated" className="ml-2 block text-sm text-gray-700">
              Consolidated Account (Combines data from branch accounts)
            </label>
          </div>

          {formData.isConsolidated && (
            <div>
              <label htmlFor="parentId" className="block text-sm font-medium text-gray-700">
                Parent Account
              </label>
              <select
                id="parentId"
                name="parentId"
                value={formData.parentId}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                disabled={isFetchingAccounts}
              >
                <option value="">No Parent Account</option>
                {accounts.map((acc) => (
                  <option key={acc.id} value={acc.id}>
                    {acc.name} ({acc.code})
                  </option>
                ))}
              </select>
              <p className="text-sm text-gray-500 mt-1">
                Select a parent account if this is a sub-account
              </p>
            </div>
          )}

          {!formData.isConsolidated && (
            <div>
              <label htmlFor="branchId" className="block text-sm font-medium text-gray-700">
                Branch
              </label>
              <select
                id="branchId"
                name="branchId"
                value={formData.branchId}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                disabled={isFetchingBranches}
              >
                <option value="">No Branch (Global Account)</option>
                {branches.map((branch) => (
                  <option key={branch.id} value={branch.id}>
                    {branch.name}
                  </option>
                ))}
              </select>
              <p className="text-sm text-gray-500 mt-1">
                Select a branch for branch-specific accounts or leave empty for global accounts
              </p>
            </div>
          )}

          <div className="flex items-center">
            <input
              type="checkbox"
              id="isDefault"
              name="isDefault"
              checked={formData.isDefault}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
            />
            <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700">
              Default Account (for this type)
            </label>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading || isFetchingBranches}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? "Saving..." : "Save"}
          </button>
        </div>
      </form>
    </div>
  );
}
