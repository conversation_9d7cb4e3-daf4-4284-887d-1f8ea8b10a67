"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select-radix";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Loader2,
  RefreshCw,
  Plus,
  Search,
  FileText,
  ArrowRight,
  Calendar,
  CreditCard,
  Wallet,
  Smartphone,
  Building2,
  Download,
  Filter,
  ChevronDown,
  ChevronUp,
  Info
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { format } from "date-fns";
import { DatePicker } from "@/components/ui/date-picker";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface PaymentMethod {
  id: string;
  code: string;
  name: string;
  iconName: string;
  color: string;
  isActive: boolean;
  accountId: string | null;
  journalId: string | null;
  account: {
    id: string;
    name: string;
    code: string;
    balance?: number;
  } | null;
  journal: {
    id: string;
    name: string;
    code: string;
  } | null;
}

interface JournalEntry {
  id: string;
  date: string;
  description: string;
  amount: number;
  entryNumber: string;
  debitAccount: {
    id: string;
    code: string;
    name: string;
  };
  creditAccount: {
    id: string;
    code: string;
    name: string;
  };
  reference: string | null;
  referenceType: string | null;
  contact: {
    id: string;
    name: string;
    phone?: string;
  } | null;
}

interface JournalSummary {
  totalEntries: number;
  totalAmount: number;
  totalDebit: number;
  totalCredit: number;
  balance: number;
  firstEntryDate: string | null;
  lastEntryDate: string | null;
  paymentVouchers: number;
  receiptVouchers: number;
  paymentVouchersAmount: number;
  receiptVouchersAmount: number;
}

export default function PaymentJournalsPage() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingEntries, setIsLoadingEntries] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [period, setPeriod] = useState<string>("month");
  const [activeTab, setActiveTab] = useState<string>("entries");
  const [summary, setSummary] = useState<JournalSummary>({
    totalEntries: 0,
    totalAmount: 0,
    totalDebit: 0,
    totalCredit: 0,
    balance: 0,
    firstEntryDate: null,
    lastEntryDate: null,
    paymentVouchers: 0,
    receiptVouchers: 0,
    paymentVouchersAmount: 0,
    receiptVouchersAmount: 0
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/accounting/payment-methods");
        if (response.ok) {
          const data = await response.json();
          setPaymentMethods(data.data || []);

          // Select first payment method by default
          if (data.data && data.data.length > 0) {
            setSelectedMethod(data.data[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching payment methods:", error);
        toast.error("Failed to load payment methods");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPaymentMethods();
  }, []);

  // Fetch journal entries when payment method changes
  useEffect(() => {
    if (selectedMethod) {
      fetchJournalEntries();
    }
  }, [selectedMethod, startDate, endDate]);

  // Handle period change
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
    const today = new Date();

    switch (value) {
      case "day":
        setStartDate(today);
        setEndDate(today);
        break;
      case "week":
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        setStartDate(weekStart);
        setEndDate(today);
        break;
      case "month":
        setStartDate(new Date(today.getFullYear(), today.getMonth(), 1));
        setEndDate(today);
        break;
      case "year":
        setStartDate(new Date(today.getFullYear(), 0, 1));
        setEndDate(today);
        break;
      case "custom":
        // Keep current dates for custom period
        break;
    }
  };

  // Refresh data
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      await fetchJournalEntries();
      toast.success("Data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Failed to refresh data");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Fetch journal entries
  const fetchJournalEntries = async () => {
    if (!selectedMethod) return;

    setIsLoadingEntries(true);
    try {
      // Find selected payment method
      const method = paymentMethods.find(m => m.id === selectedMethod);
      if (!method) {
        setEntries([]);
        setSummary({
          totalEntries: 0,
          totalAmount: 0,
          totalDebit: 0,
          totalCredit: 0,
          balance: 0,
          firstEntryDate: null,
          lastEntryDate: null
        });
        return;
      }

      // Build query parameters
      const params = new URLSearchParams();

      // If the payment method has a journal, filter by journal ID
      if (method.journalId) {
        params.append("journalId", method.journalId);
      }
      // If the payment method has an account, filter by account ID
      else if (method.accountId) {
        params.append("accountId", method.accountId);
      } else {
        // If neither journal nor account is linked, we can't fetch entries
        setEntries([]);
        setSummary({
          totalEntries: 0,
          totalAmount: 0,
          totalDebit: 0,
          totalCredit: 0,
          balance: 0,
          firstEntryDate: null,
          lastEntryDate: null
        });
        return;
      }

      // Add date filters
      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }

      if (endDate) {
        params.append("endDate", endDate.toISOString());
      }

      // Add search term if provided
      if (searchTerm) {
        params.append("search", searchTerm);
      }

      // Fetch entries from the API
      const response = await fetch(`/api/accounting/journals/entries?${params.toString()}`);

      if (response.ok) {
        const data = await response.json();
        const journalEntries = data.data || [];

        // Also fetch payment vouchers and receipt vouchers that use this payment method
        let voucherEntries: JournalEntry[] = [];

        // Only fetch voucher entries if they're not already included via journal
        if (!method.journalId) {
          // Fetch payment vouchers
          const paymentVouchersParams = new URLSearchParams();
          paymentVouchersParams.append("paymentMethodId", method.id);

          if (startDate) {
            paymentVouchersParams.append("startDate", startDate.toISOString());
          }

          if (endDate) {
            paymentVouchersParams.append("endDate", endDate.toISOString());
          }

          const paymentVouchersResponse = await fetch(`/api/accounting/payment-vouchers?${paymentVouchersParams.toString()}`);

          if (paymentVouchersResponse.ok) {
            const paymentVouchersData = await paymentVouchersResponse.json();
            const paymentVouchers = paymentVouchersData.data || [];

            // Filter vouchers that have journal entries
            const vouchersWithEntries = paymentVouchers.filter(v => v.journalEntryId);

            // Fetch the journal entries for these vouchers
            if (vouchersWithEntries.length > 0) {
              const entryIds = vouchersWithEntries.map(v => v.journalEntryId);
              const entriesResponse = await fetch(`/api/accounting/journal-entries?ids=${entryIds.join(',')}`);

              if (entriesResponse.ok) {
                const entriesData = await entriesResponse.json();
                voucherEntries = [...voucherEntries, ...(entriesData.data || [])];
              }
            }
          }

          // Fetch receipt vouchers
          const receiptVouchersParams = new URLSearchParams();
          receiptVouchersParams.append("paymentMethodId", method.id);

          if (startDate) {
            receiptVouchersParams.append("startDate", startDate.toISOString());
          }

          if (endDate) {
            receiptVouchersParams.append("endDate", endDate.toISOString());
          }

          const receiptVouchersResponse = await fetch(`/api/accounting/receipt-vouchers?${receiptVouchersParams.toString()}`);

          if (receiptVouchersResponse.ok) {
            const receiptVouchersData = await receiptVouchersResponse.json();
            const receiptVouchers = receiptVouchersData.data || [];

            // Filter vouchers that have journal entries
            const vouchersWithEntries = receiptVouchers.filter(v => v.journalEntryId);

            // Fetch the journal entries for these vouchers
            if (vouchersWithEntries.length > 0) {
              const entryIds = vouchersWithEntries.map(v => v.journalEntryId);
              const entriesResponse = await fetch(`/api/accounting/journal-entries?ids=${entryIds.join(',')}`);

              if (entriesResponse.ok) {
                const entriesData = await entriesResponse.json();
                voucherEntries = [...voucherEntries, ...(entriesData.data || [])];
              }
            }
          }
        }

        // Combine journal entries with voucher entries
        const allEntries = [...journalEntries, ...voucherEntries];

        // Remove duplicates (in case some entries appear in both sets)
        const uniqueEntries = allEntries.filter((entry, index, self) =>
          index === self.findIndex(e => e.id === entry.id)
        );

        // Sort entries by date (newest first)
        const sortedEntries = uniqueEntries.sort((a, b) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );

        setEntries(sortedEntries);

        // Calculate summary
        if (method.accountId) {
          const accountId = method.accountId;

          let totalDebit = 0;
          let totalCredit = 0;

          sortedEntries.forEach(entry => {
            if (entry.debitAccount.id === accountId) {
              totalDebit += entry.amount;
            }
            if (entry.creditAccount.id === accountId) {
              totalCredit += entry.amount;
            }
          });

          const balance = totalDebit - totalCredit;
          const totalAmount = sortedEntries.reduce((sum, entry) => sum + entry.amount, 0);

          // Sort by date for first/last entry date (oldest to newest)
          const chronologicalEntries = [...sortedEntries].sort((a, b) =>
            new Date(a.date).getTime() - new Date(b.date).getTime()
          );

          const firstEntryDate = chronologicalEntries.length > 0 ? chronologicalEntries[0].date : null;
          const lastEntryDate = chronologicalEntries.length > 0 ? chronologicalEntries[chronologicalEntries.length - 1].date : null;

          // Count payment vouchers and receipt vouchers
          let paymentVouchers = 0;
          let receiptVouchers = 0;
          let paymentVouchersAmount = 0;
          let receiptVouchersAmount = 0;

          sortedEntries.forEach(entry => {
            if (entry.referenceType === "PAYMENT_VOUCHER") {
              paymentVouchers++;
              paymentVouchersAmount += entry.amount;
            } else if (entry.referenceType === "RECEIPT_VOUCHER") {
              receiptVouchers++;
              receiptVouchersAmount += entry.amount;
            }
          });

          setSummary({
            totalEntries: sortedEntries.length,
            totalAmount,
            totalDebit,
            totalCredit,
            balance,
            firstEntryDate,
            lastEntryDate,
            paymentVouchers,
            receiptVouchers,
            paymentVouchersAmount,
            receiptVouchersAmount
          });
        }
      }
    } catch (error) {
      console.error("Error fetching journal entries:", error);
      toast.error("Failed to load journal entries");
    } finally {
      setIsLoadingEntries(false);
    }
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Get payment method icon
  const getPaymentMethodIcon = (code: string) => {
    switch (code.toUpperCase()) {
      case 'CASH':
        return <Wallet className="h-5 w-5" />;
      case 'VODAFONE_CASH':
        return <Smartphone className="h-5 w-5" />;
      case 'BANK_TRANSFER':
        return <Building2 className="h-5 w-5" />;
      case 'VISA':
        return <CreditCard className="h-5 w-5" />;
      default:
        return <Wallet className="h-5 w-5" />;
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchJournalEntries();
  };

  // Clear search
  const clearSearch = () => {
    setSearchTerm("");
    fetchJournalEntries();
  };

  // Export to CSV
  const exportToCSV = () => {
    if (entries.length === 0) {
      toast.error("No data to export");
      return;
    }

    try {
      // Create CSV content
      const headers = ["Date", "Entry #", "Description", "Debit Account", "Credit Account", "Amount", "Reference", "Contact"];

      const rows = entries.map(entry => [
        format(new Date(entry.date), 'yyyy-MM-dd'),
        entry.entryNumber,
        entry.description,
        `${entry.debitAccount.code} - ${entry.debitAccount.name}`,
        `${entry.creditAccount.code} - ${entry.creditAccount.name}`,
        entry.amount.toString(),
        entry.reference || '',
        entry.contact ? entry.contact.name : ''
      ]);

      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n');

      // Create download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');

      // Set link properties
      const selectedPaymentMethod = paymentMethods.find(m => m.id === selectedMethod);
      const fileName = `${selectedPaymentMethod?.name.replace(/\s+/g, '_')}_journal_${format(new Date(), 'yyyy-MM-dd')}.csv`;

      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);

      // Trigger download and cleanup
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success("Export successful");
    } catch (error) {
      console.error("Error exporting to CSV:", error);
      toast.error("Failed to export data");
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Payment Journals / يوميات الدفع</h1>
          <p className="text-gray-500">View and manage payment method journal entries</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={refreshData} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
            <Filter className="h-4 w-4 mr-2" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Payment Method Journals / يوميات طرق الدفع</CardTitle>
              <CardDescription>View journal entries for each payment method</CardDescription>
            </div>
            {entries.length > 0 && (
              <Button variant="outline" size="sm" onClick={exportToCSV}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                <span className="ml-2 text-gray-500">Loading payment methods...</span>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="paymentMethod">Payment Method / طريقة الدفع</Label>
                    <Select value={selectedMethod || ""} onValueChange={setSelectedMethod}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentMethods.map(method => (
                          <SelectItem key={method.id} value={method.id}>
                            <div className="flex items-center">
                              {getPaymentMethodIcon(method.code)}
                              <span className="ml-2">{method.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Period / الفترة</Label>
                    <Select value={period} onValueChange={handlePeriodChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="day">Today / اليوم</SelectItem>
                        <SelectItem value="week">This Week / هذا الأسبوع</SelectItem>
                        <SelectItem value="month">This Month / هذا الشهر</SelectItem>
                        <SelectItem value="year">This Year / هذا العام</SelectItem>
                        <SelectItem value="custom">Custom / مخصص</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Start Date / تاريخ البداية</Label>
                    <DatePicker date={startDate} setDate={setStartDate} />
                  </div>

                  <div>
                    <Label>End Date / تاريخ النهاية</Label>
                    <DatePicker date={endDate} setDate={setEndDate} />
                  </div>
                </div>

                {showFilters && (
                  <div className="pt-4">
                    <form onSubmit={handleSearch} className="flex space-x-2">
                      <div className="flex-1">
                        <Input
                          placeholder="Search by description, reference, or contact..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                      <Button type="submit" variant="secondary">
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </Button>
                      {searchTerm && (
                        <Button type="button" variant="outline" onClick={clearSearch}>
                          Clear
                        </Button>
                      )}
                    </form>
                  </div>
                )}

                {selectedMethod && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                    <Card className="bg-gray-50">
                      <CardContent className="pt-6">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-medium flex items-center">
                            {getPaymentMethodIcon(paymentMethods.find(m => m.id === selectedMethod)?.code || "")}
                            <span className="ml-2">
                              {paymentMethods.find(m => m.id === selectedMethod)?.name}
                            </span>
                          </h3>
                          <Badge variant={summary.balance >= 0 ? "success" : "destructive"}>
                            {summary.balance >= 0 ? "Positive" : "Negative"} Balance
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Total Entries</p>
                            <p className="text-lg font-semibold">{summary.totalEntries}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Total Amount</p>
                            <p className="text-lg font-semibold">{formatCurrency(summary.totalAmount)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Total Debit</p>
                            <p className="text-lg font-semibold text-green-600">{formatCurrency(summary.totalDebit)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Total Credit</p>
                            <p className="text-lg font-semibold text-red-600">{formatCurrency(summary.totalCredit)}</p>
                          </div>
                        </div>
                        <Separator className="my-4" />
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm text-gray-500">Current Balance</p>
                            <p className={`text-xl font-bold ${summary.balance >= 0 ? "text-green-600" : "text-red-600"}`}>
                              {formatCurrency(summary.balance)}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">Period</p>
                            <p className="text-sm font-medium">
                              {startDate && endDate && `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-gray-50">
                      <CardContent className="pt-6">
                        <h3 className="text-lg font-medium mb-4">Account Information</h3>
                        {paymentMethods.find(m => m.id === selectedMethod)?.account ? (
                          <div className="space-y-4">
                            <div>
                              <p className="text-sm text-gray-500">Account Name</p>
                              <p className="text-lg font-semibold">
                                {paymentMethods.find(m => m.id === selectedMethod)?.account?.name}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Account Code</p>
                              <p className="text-lg font-semibold">
                                {paymentMethods.find(m => m.id === selectedMethod)?.account?.code}
                              </p>
                            </div>
                            <Separator className="my-2" />
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="text-sm text-gray-500">Journal</p>
                                <p className="text-lg font-semibold">
                                  {paymentMethods.find(m => m.id === selectedMethod)?.journal?.name || "Not linked"}
                                </p>
                              </div>
                              <Link href="/dashboard/settings/accounting/payment-methods">
                                <Button variant="link" size="sm">
                                  <ArrowRight className="h-4 w-4 mr-1" />
                                  Manage
                                </Button>
                              </Link>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center p-4">
                            <Info className="h-8 w-8 text-amber-500 mx-auto mb-2" />
                            <p className="text-sm text-gray-500 mb-2">This payment method is not linked to an account.</p>
                            <Link href="/dashboard/settings/accounting/payment-methods">
                              <Button variant="outline" size="sm">
                                Link Account
                              </Button>
                            </Link>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                )}

                <Tabs value={activeTab} onValueChange={setActiveTab} className="pt-4">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="entries">Journal Entries / قيود اليومية</TabsTrigger>
                    <TabsTrigger value="summary">Summary / الملخص</TabsTrigger>
                  </TabsList>

                  <TabsContent value="entries" className="pt-4">
                    {isLoadingEntries ? (
                      <div className="flex items-center justify-center p-4">
                        <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                        <span className="ml-2 text-gray-500">Loading journal entries...</span>
                      </div>
                    ) : entries.length > 0 ? (
                      <div className="rounded-md border overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="bg-gray-100 border-b">
                                <th className="px-4 py-3 text-left">Date</th>
                                <th className="px-4 py-3 text-left">Entry #</th>
                                <th className="px-4 py-3 text-left">Description</th>
                                <th className="px-4 py-3 text-left">Debit Account</th>
                                <th className="px-4 py-3 text-left">Credit Account</th>
                                <th className="px-4 py-3 text-right">Amount</th>
                                <th className="px-4 py-3 text-left">Reference</th>
                                <th className="px-4 py-3 text-left">Contact</th>
                              </tr>
                            </thead>
                            <tbody>
                              {entries.map(entry => (
                                <tr key={entry.id} className="border-b hover:bg-gray-50">
                                  <td className="px-4 py-3 text-left">{format(new Date(entry.date), 'MMM d, yyyy')}</td>
                                  <td className="px-4 py-3 text-left">{entry.entryNumber}</td>
                                  <td className="px-4 py-3 text-left">{entry.description}</td>
                                  <td className="px-4 py-3 text-left">{entry.debitAccount.code} - {entry.debitAccount.name}</td>
                                  <td className="px-4 py-3 text-left">{entry.creditAccount.code} - {entry.creditAccount.name}</td>
                                  <td className="px-4 py-3 text-right font-medium">{formatCurrency(entry.amount)}</td>
                                  <td className="px-4 py-3 text-left">
                                    {entry.reference ? (
                                      <span className="inline-flex items-center">
                                        {entry.reference}
                                        {entry.referenceType && (
                                          <Badge
                                            variant="outline"
                                            className={`ml-2 ${
                                              entry.referenceType === "PAYMENT_VOUCHER"
                                                ? "bg-red-50 text-red-700 border-red-200"
                                                : entry.referenceType === "RECEIPT_VOUCHER"
                                                ? "bg-green-50 text-green-700 border-green-200"
                                                : ""
                                            }`}
                                          >
                                            {entry.referenceType === "PAYMENT_VOUCHER"
                                              ? "Payment Voucher"
                                              : entry.referenceType === "RECEIPT_VOUCHER"
                                              ? "Receipt Voucher"
                                              : entry.referenceType}
                                          </Badge>
                                        )}
                                      </span>
                                    ) : (
                                      '-'
                                    )}
                                  </td>
                                  <td className="px-4 py-3 text-left">
                                    {entry.contact ? (
                                      <span className="inline-flex items-center">
                                        {entry.contact.name}
                                        {entry.contact.phone && (
                                          <span className="text-xs text-gray-500 ml-1">
                                            ({entry.contact.phone})
                                          </span>
                                        )}
                                      </span>
                                    ) : (
                                      '-'
                                    )}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center p-8 border rounded-md bg-gray-50">
                        <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No Journal Entries Found</h3>
                        <p className="text-gray-500 mb-4">
                          No journal entries found for this payment method in the selected period.
                        </p>
                        <Button variant="outline" onClick={() => handlePeriodChange("month")}>
                          View This Month
                        </Button>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="summary" className="pt-4">
                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="text-lg font-medium mb-4">Journal Summary / ملخص اليومية</h3>

                        {entries.length > 0 ? (
                          <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="bg-gray-50 p-4 rounded-md">
                                <h4 className="text-sm font-medium text-gray-500 mb-1">Total Entries</h4>
                                <p className="text-2xl font-bold">{summary.totalEntries}</p>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <h4 className="text-sm font-medium text-gray-500 mb-1">First Entry Date</h4>
                                <p className="text-2xl font-bold">
                                  {summary.firstEntryDate ? format(new Date(summary.firstEntryDate), 'MMM d, yyyy') : '-'}
                                </p>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <h4 className="text-sm font-medium text-gray-500 mb-1">Last Entry Date</h4>
                                <p className="text-2xl font-bold">
                                  {summary.lastEntryDate ? format(new Date(summary.lastEntryDate), 'MMM d, yyyy') : '-'}
                                </p>
                              </div>
                            </div>

                            <Separator />

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="bg-green-50 p-4 rounded-md">
                                <h4 className="text-sm font-medium text-gray-500 mb-1">Total Debit</h4>
                                <p className="text-2xl font-bold text-green-600">{formatCurrency(summary.totalDebit)}</p>
                              </div>
                              <div className="bg-red-50 p-4 rounded-md">
                                <h4 className="text-sm font-medium text-gray-500 mb-1">Total Credit</h4>
                                <p className="text-2xl font-bold text-red-600">{formatCurrency(summary.totalCredit)}</p>
                              </div>
                              <div className={`${summary.balance >= 0 ? 'bg-green-50' : 'bg-red-50'} p-4 rounded-md`}>
                                <h4 className="text-sm font-medium text-gray-500 mb-1">Balance</h4>
                                <p className={`text-2xl font-bold ${summary.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {formatCurrency(summary.balance)}
                                </p>
                              </div>
                            </div>

                            <Separator className="my-4" />

                            <h4 className="text-lg font-medium mb-3">Vouchers Summary / ملخص الأذونات</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="bg-red-50 p-4 rounded-md">
                                <div className="flex justify-between items-center mb-2">
                                  <h5 className="text-sm font-medium text-gray-500">Payment Vouchers / أذونات الصرف</h5>
                                  <Badge variant="outline" className="bg-red-100 text-red-700 border-red-200">
                                    {summary.paymentVouchers} Vouchers
                                  </Badge>
                                </div>
                                <p className="text-2xl font-bold text-red-600">{formatCurrency(summary.paymentVouchersAmount)}</p>
                              </div>
                              <div className="bg-green-50 p-4 rounded-md">
                                <div className="flex justify-between items-center mb-2">
                                  <h5 className="text-sm font-medium text-gray-500">Receipt Vouchers / أذونات الاستلام</h5>
                                  <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
                                    {summary.receiptVouchers} Vouchers
                                  </Badge>
                                </div>
                                <p className="text-2xl font-bold text-green-600">{formatCurrency(summary.receiptVouchersAmount)}</p>
                              </div>
                            </div>

                            <div className="bg-gray-50 p-4 rounded-md">
                              <h4 className="text-sm font-medium text-gray-500 mb-2">Period Summary</h4>
                              <div className="flex justify-between items-center">
                                <div>
                                  <p className="text-sm text-gray-500">Start Date</p>
                                  <p className="text-lg font-medium">{startDate ? format(startDate, 'MMM d, yyyy') : '-'}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">End Date</p>
                                  <p className="text-lg font-medium">{endDate ? format(endDate, 'MMM d, yyyy') : '-'}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Total Amount</p>
                                  <p className="text-lg font-medium">{formatCurrency(summary.totalAmount)}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center p-8 border rounded-md bg-gray-50">
                            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                            <h3 className="text-lg font-medium mb-2">No Data Available</h3>
                            <p className="text-gray-500 mb-4">
                              There are no journal entries to summarize for the selected period.
                            </p>
                            <Button variant="outline" onClick={() => handlePeriodChange("month")}>
                              View This Month
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </>
            )}
          </div>
        </CardContent>
        <CardFooter className="border-t pt-6 flex justify-between">
          <div className="text-sm text-gray-500">
            Showing {entries.length} entries for the selected period
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={refreshData} disabled={isRefreshing}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            {entries.length > 0 && (
              <Button variant="outline" size="sm" onClick={exportToCSV}>
                <Download className="h-4 w-4 mr-2" />
                Export to CSV
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
