"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function SalesOrdersLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const tabs = [
    {
      id: 'all-orders',
      name: "All Orders",
      href: "/dashboard/sales/orders",
      current: pathname === "/dashboard/sales/orders",
      icon: "M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
    },
    {
      id: 'completed',
      name: "Completed",
      href: "/dashboard/sales/orders/completed",
      current: pathname === "/dashboard/sales/orders/completed",
      icon: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
    },
    {
      id: 'pending',
      name: "Pending",
      href: "/dashboard/sales/orders/pending",
      current: pathname === "/dashboard/sales/orders/pending",
      icon: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
    },
    {
      id: 'create-new',
      name: "Create New",
      href: "/dashboard/sales/new",
      current: pathname === "/dashboard/sales/new",
      icon: "M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200">
        {/* Desktop Navigation */}
        <div className="hidden md:block">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex flex-wrap px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <Link
                  key={tab.id}
                  href={tab.href}
                  className={`
                    ${tab.current
                      ? 'border-[#3895e7] text-[#3895e7]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                    whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm flex items-center mr-6 mb-1
                  `}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 flex-shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d={tab.icon}
                    />
                  </svg>
                  <span>{tab.name}</span>
                </Link>
              ))}
            </nav>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden">
          <select
            value={tabs.find(tab => tab.current)?.id || ''}
            onChange={(e) => {
              const selectedTab = tabs.find(tab => tab.id === e.target.value);
              if (selectedTab) {
                window.location.href = selectedTab.href;
              }
            }}
            className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-[#3895e7] focus:border-[#3895e7] sm:text-sm"
          >
            {tabs.map((tab) => (
              <option key={tab.id} value={tab.id}>
                {tab.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="px-4 py-5 sm:px-6">
        {children}
      </div>
    </div>
  );
}
