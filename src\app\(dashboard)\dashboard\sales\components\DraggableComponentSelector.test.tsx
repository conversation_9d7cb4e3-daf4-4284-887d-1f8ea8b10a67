import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DraggableComponentSelector from './DraggableComponentSelector';
import { act } from 'react-dom/test-utils';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => {
  const actual = jest.requireActual('framer-motion');
  return {
    ...actual,
    motion: {
      div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    },
  };
});

// Mock component data
const mockComponents = [
  {
    id: 'cpu-1',
    name: 'Intel i5',
    type: 'CPU',
    price: 200,
    stock: 10,
    speed: '3.2 GHz'
  },
  {
    id: 'cpu-2',
    name: 'Intel i7',
    type: 'CPU',
    price: 300,
    stock: 5,
    speed: '4.0 GHz'
  },
  {
    id: 'cpu-3',
    name: 'AMD Ryzen 5',
    type: 'CPU',
    price: 250,
    stock: 0, // Out of stock
    speed: '3.6 GHz'
  }
];

// Mock functions
const mockOnSelect = jest.fn();

describe('DraggableComponentSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component type and available components', () => {
    render(
      <DraggableComponentSelector
        componentType="CPU"
        components={mockComponents}
        onSelect={mockOnSelect}
      />
    );

    // Check if component type is displayed
    expect(screen.getByText('CPU')).toBeInTheDocument();
    
    // Check if all components are displayed
    expect(screen.getByText('Intel i5')).toBeInTheDocument();
    expect(screen.getByText('Intel i7')).toBeInTheDocument();
    expect(screen.getByText('AMD Ryzen 5')).toBeInTheDocument();
    
    // Check if prices are displayed
    expect(screen.getByText('200.00 ج.م')).toBeInTheDocument();
    expect(screen.getByText('300.00 ج.م')).toBeInTheDocument();
    expect(screen.getByText('250.00 ج.م')).toBeInTheDocument();
    
    // Check if out of stock message is displayed
    expect(screen.getByText('Out of stock')).toBeInTheDocument();
  });

  it('displays the drop zone with placeholder text when no component is selected', () => {
    render(
      <DraggableComponentSelector
        componentType="CPU"
        components={mockComponents}
        onSelect={mockOnSelect}
      />
    );

    // Check if drop zone placeholder is displayed
    expect(screen.getByText('Drag a CPU here')).toBeInTheDocument();
  });

  it('displays the selected component in the drop zone', () => {
    render(
      <DraggableComponentSelector
        componentType="CPU"
        components={mockComponents}
        selectedComponent={mockComponents[0]}
        onSelect={mockOnSelect}
      />
    );

    // Check if selected component is displayed in the drop zone
    expect(screen.getByText('Intel i5')).toBeInTheDocument();
    expect(screen.getByText('Speed: 3.2 GHz')).toBeInTheDocument();
    
    // Check if remove button is displayed
    expect(screen.getByText('Remove')).toBeInTheDocument();
  });

  it('calls onSelect when a component is clicked', async () => {
    render(
      <DraggableComponentSelector
        componentType="CPU"
        components={mockComponents}
        onSelect={mockOnSelect}
      />
    );

    // Click on a component
    const component = screen.getByText('Intel i7').closest('div');
    if (component) {
      await act(async () => {
        fireEvent.click(component);
      });
    }

    // Check if onSelect was called with the correct component
    expect(mockOnSelect).toHaveBeenCalledTimes(1);
    expect(mockOnSelect).toHaveBeenCalledWith(mockComponents[1]);
  });

  it('calls onSelect with undefined when remove button is clicked', async () => {
    render(
      <DraggableComponentSelector
        componentType="CPU"
        components={mockComponents}
        selectedComponent={mockComponents[0]}
        onSelect={mockOnSelect}
      />
    );

    // Click on the remove button
    const removeButton = screen.getByText('Remove');
    await act(async () => {
      fireEvent.click(removeButton);
    });

    // Check if onSelect was called with undefined
    expect(mockOnSelect).toHaveBeenCalledTimes(1);
    expect(mockOnSelect).toHaveBeenCalledWith(undefined);
  });

  it('handles drag and drop interactions', async () => {
    // Note: Testing drag and drop is complex with jsdom
    // This is a simplified test that simulates the drag events
    
    render(
      <DraggableComponentSelector
        componentType="CPU"
        components={mockComponents}
        onSelect={mockOnSelect}
      />
    );

    // Find a component
    const component = screen.getByText('Intel i5').closest('div');
    if (!component) return;

    // Simulate drag start
    await act(async () => {
      fireEvent.dragStart(component);
    });

    // Find the drop zone
    const dropZone = screen.getByText('Drag a CPU here').closest('div');
    if (!dropZone) return;

    // Simulate drop
    await act(async () => {
      fireEvent.drop(dropZone);
    });

    // Since we can't fully simulate the drag and drop in jsdom,
    // we'll just verify that the component can be clicked
    await act(async () => {
      fireEvent.click(component);
    });

    // Check if onSelect was called
    expect(mockOnSelect).toHaveBeenCalled();
  });

  it('displays empty state when no components are available', () => {
    render(
      <DraggableComponentSelector
        componentType="GPU"
        components={[]}
        onSelect={mockOnSelect}
      />
    );

    // Check if empty state message is displayed
    expect(screen.getByText('No GPU components available')).toBeInTheDocument();
  });
});
