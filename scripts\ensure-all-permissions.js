// This script ensures all required permissions exist in the database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Define all required permissions for the system
const requiredPermissions = [
  { name: "view_users", description: "View users" },
  { name: "add_users", description: "Add new users" },
  { name: "edit_users", description: "Edit existing users" },
  { name: "delete_users", description: "Delete users" },
  { name: "view_branches", description: "View branches" },
  { name: "add_branches", description: "Add new branches" },
  { name: "edit_branches", description: "Edit existing branches" },
  { name: "delete_branches", description: "Delete branches" },
  { name: "view_warehouses", description: "View warehouses" },
  { name: "add_warehouses", description: "Add new warehouses" },
  { name: "edit_warehouses", description: "Edit existing warehouses" },
  { name: "delete_warehouses", description: "Delete warehouses" },
  { name: "view_products", description: "View products" },
  { name: "add_products", description: "Add new products" },
  { name: "edit_products", description: "Edit existing products" },
  { name: "delete_products", description: "Delete products" },
  { name: "view_inventory", description: "View inventory" },
  { name: "add_inventory", description: "Add inventory" },
  { name: "edit_inventory", description: "Edit inventory" },
  { name: "delete_inventory", description: "Delete inventory" },
  { name: "view_sales", description: "View sales" },
  { name: "add_sales", description: "Add new sales" },
  { name: "edit_sales", description: "Edit existing sales" },
  { name: "delete_sales", description: "Delete sales" },
  { name: "view_purchases", description: "View purchases" },
  { name: "add_purchases", description: "Add new purchases" },
  { name: "edit_purchases", description: "Edit existing purchases" },
  { name: "delete_purchases", description: "Delete purchases" },
  { name: "view_contacts", description: "View contacts" },
  { name: "add_contacts", description: "Add new contacts" },
  { name: "edit_contacts", description: "Edit existing contacts" },
  { name: "delete_contacts", description: "Delete contacts" },
  { name: "view_reports", description: "View reports" },
  { name: "view_settings", description: "View settings" },
  { name: "edit_settings", description: "Edit settings" },
  { name: "view_dashboard", description: "View dashboard" },
  { name: "view_accounting", description: "View accounting" },
  { name: "add_accounting", description: "Add accounting entries" },
  { name: "edit_accounting", description: "Edit accounting entries" },
  { name: "delete_accounting", description: "Delete accounting entries" },
  { name: "view_credit_notes", description: "View credit notes" },
  { name: "add_credit_notes", description: "Add credit notes" },
  { name: "edit_credit_notes", description: "Edit credit notes" },
  { name: "delete_credit_notes", description: "Delete credit notes" },
];

async function main() {
  try {
    console.log('Ensuring all required permissions exist...');

    // Get all admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        role: "ADMIN",
      },
    });

    if (adminUsers.length === 0) {
      console.log('No admin users found. Please run the restore-admin-user script first.');
      return;
    }

    // Create missing permissions and add them to admin users
    for (const permission of requiredPermissions) {
      // Check if permission exists
      let existingPermission = await prisma.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        // Create the permission
        existingPermission = await prisma.permission.create({
          data: permission,
        });
        console.log(`Created permission: ${permission.name}`);
      } else {
        console.log(`Permission already exists: ${permission.name}`);
      }

      // Add permission to all admin users
      for (const adminUser of adminUsers) {
        // Check if admin already has this permission
        const hasPermission = await prisma.user.findFirst({
          where: {
            id: adminUser.id,
            permissions: {
              some: {
                id: existingPermission.id,
              },
            },
          },
        });

        if (!hasPermission) {
          await prisma.user.update({
            where: {
              id: adminUser.id,
            },
            data: {
              permissions: {
                connect: {
                  id: existingPermission.id,
                },
              },
            },
          });
          console.log(`Added permission ${permission.name} to admin user ${adminUser.name}`);
        }
      }
    }

    console.log('All required permissions have been ensured.');
  } catch (error) {
    console.error('Error ensuring permissions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
