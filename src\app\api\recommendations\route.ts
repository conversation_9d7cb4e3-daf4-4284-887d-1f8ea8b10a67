import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    const contactId = url.searchParams.get("contactId");
    const limit = parseInt(url.searchParams.get("limit") || "5", 10);
    
    if (!contactId) {
      return NextResponse.json(
        { error: "Contact ID is required" },
        { status: 400 }
      );
    }
    
    // Get contact's purchase history
    const purchaseHistory = await prisma.saleItem.findMany({
      where: {
        sale: {
          contactId,
          status: "COMPLETED"
        }
      },
      select: {
        productId: true,
        product: {
          select: {
            categoryId: true
          }
        },
        quantity: true,
        sale: {
          select: {
            date: true
          }
        }
      },
      orderBy: {
        sale: {
          date: "desc"
        }
      },
      take: 50 // Consider last 50 purchases
    });
    
    if (purchaseHistory.length === 0) {
      // If no purchase history, recommend popular products
      const popularProducts = await prisma.product.findMany({
        where: {
          isActive: true
        },
        select: {
          id: true,
          name: true,
          description: true,
          basePrice: true,
          categoryId: true,
          category: {
            select: {
              name: true
            }
          },
          inventory: {
            select: {
              quantity: true
            }
          }
        },
        orderBy: {
          saleItems: {
            _count: "desc"
          }
        },
        take: limit
      });
      
      return NextResponse.json({
        recommendations: popularProducts.map(product => ({
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.basePrice,
          category: product.category.name,
          categoryId: product.categoryId,
          inStock: product.inventory.some(inv => inv.quantity > 0),
          recommendationType: "POPULAR"
        })),
        recommendationType: "POPULAR"
      });
    }
    
    // Extract product and category preferences
    const productFrequency: Record<string, number> = {};
    const categoryFrequency: Record<string, number> = {};
    
    purchaseHistory.forEach(item => {
      // Count product frequency
      productFrequency[item.productId] = (productFrequency[item.productId] || 0) + item.quantity;
      
      // Count category frequency
      if (item.product.categoryId) {
        categoryFrequency[item.product.categoryId] = (categoryFrequency[item.product.categoryId] || 0) + item.quantity;
      }
    });
    
    // Get favorite categories (sorted by frequency)
    const favoriteCategories = Object.entries(categoryFrequency)
      .sort((a, b) => b[1] - a[1])
      .map(entry => entry[0]);
    
    // Get frequently purchased products
    const frequentProducts = Object.entries(productFrequency)
      .sort((a, b) => b[1] - a[1])
      .map(entry => entry[0]);
    
    // Get recommendations based on favorite categories
    let recommendations = await prisma.product.findMany({
      where: {
        isActive: true,
        categoryId: {
          in: favoriteCategories.slice(0, 3) // Top 3 categories
        },
        id: {
          notIn: frequentProducts.slice(0, 10) // Exclude recently purchased products
        }
      },
      select: {
        id: true,
        name: true,
        description: true,
        basePrice: true,
        categoryId: true,
        category: {
          select: {
            name: true
          }
        },
        inventory: {
          select: {
            quantity: true
          }
        }
      },
      orderBy: {
        saleItems: {
          _count: "desc"
        }
      },
      take: limit
    });
    
    // If not enough recommendations, add some popular products
    if (recommendations.length < limit) {
      const additionalProducts = await prisma.product.findMany({
        where: {
          isActive: true,
          id: {
            notIn: [...recommendations.map(p => p.id), ...frequentProducts.slice(0, 10)]
          }
        },
        select: {
          id: true,
          name: true,
          description: true,
          basePrice: true,
          categoryId: true,
          category: {
            select: {
              name: true
            }
          },
          inventory: {
            select: {
              quantity: true
            }
          }
        },
        orderBy: {
          saleItems: {
            _count: "desc"
          }
        },
        take: limit - recommendations.length
      });
      
      recommendations = [...recommendations, ...additionalProducts];
    }
    
    // Get loyalty tier discount if applicable
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
      select: { loyaltyTier: true }
    });
    
    // Format recommendations with loyalty tier discount
    const formattedRecommendations = recommendations.map(product => {
      // Calculate potential discount based on loyalty tier
      let discountPercentage = 0;
      if (contact?.loyaltyTier) {
        switch (contact.loyaltyTier) {
          case "PLATINUM":
            discountPercentage = 10;
            break;
          case "GOLD":
            discountPercentage = 7;
            break;
          case "SILVER":
            discountPercentage = 5;
            break;
          case "BRONZE":
            discountPercentage = 2;
            break;
        }
      }
      
      const discountedPrice = product.basePrice * (1 - discountPercentage / 100);
      
      return {
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.basePrice,
        discountedPrice: discountPercentage > 0 ? discountedPrice : null,
        discountPercentage: discountPercentage > 0 ? discountPercentage : null,
        category: product.category.name,
        categoryId: product.categoryId,
        inStock: product.inventory.some(inv => inv.quantity > 0),
        recommendationType: "PERSONALIZED"
      };
    });
    
    return NextResponse.json({
      recommendations: formattedRecommendations,
      recommendationType: "PERSONALIZED"
    });
  } catch (error) {
    console.error("Error generating recommendations:", error);
    return NextResponse.json(
      { error: "Failed to generate recommendations" },
      { status: 500 }
    );
  }
}
