import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import {
  createInventoryJournalEntries,
  TransactionType
} from "@/lib/accounting";

// GET /api/inventory - Get all inventory items
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view inventory
    // We'll check for either view_products or view_inventory permissions
    const hasViewPermission = await hasPermission("view_products") ||
                             await hasPermission("view_inventory") ||
                             session.user.role === "ADMIN";

    if (!hasViewPermission) {
      console.error("User without proper permissions is trying to access inventory:", session.user.email);
      return NextResponse.json(
        { error: "You don't have permission to view inventory. Please contact your administrator." },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const search = url.searchParams.get("search");
    const warehouseId = url.searchParams.get("warehouseId");
    const categoryId = url.searchParams.get("categoryId");

    // Build filter object
    const filter: any = {};

    // Add warehouse filter if provided
    if (warehouseId) {
      filter.warehouseId = warehouseId;
    }

    // Add product filter if search is provided
    if (search) {
      filter.product = {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ],
      };
    }

    // Add category filter if provided
    if (categoryId) {
      filter.product = {
        ...filter.product,
        categoryId: categoryId,
      };
    }

    // Get inventory items from database
    const inventoryItems = await db.inventory.findMany({
      where: filter,
      include: {
        product: {
          include: {
            category: true,
          },
        },
        warehouse: true,
      },
      orderBy: [
        {
          product: {
            name: "asc",
          },
        },
        {
          warehouse: {
            name: "asc",
          },
        },
      ],
    });

    // Format the data for response
    const formattedItems = inventoryItems.map((item) => ({
      id: item.id,
      productId: item.productId,
      productName: item.product.name,
      warehouseId: item.warehouseId,
      warehouseName: item.warehouse.name,
      quantity: item.quantity,
      categoryId: item.product.categoryId,
      categoryName: item.product.category?.name || "Uncategorized",
    }));

    return NextResponse.json(formattedItems);
  } catch (error) {
    console.error("Error fetching inventory:", error);
    return NextResponse.json(
      { error: "Failed to fetch inventory" },
      { status: 500 }
    );
  }
}

// POST /api/inventory - Add or update inventory
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add/update inventory
    const hasAddPermission = await hasPermission("add_products");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add/update inventory" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.productId) {
      return NextResponse.json(
        { error: "Product ID is required" },
        { status: 400 }
      );
    }

    if (!data.warehouseId) {
      return NextResponse.json(
        { error: "Warehouse ID is required" },
        { status: 400 }
      );
    }

    if (typeof data.quantity !== "number") {
      return NextResponse.json(
        { error: "Quantity must be a number" },
        { status: 400 }
      );
    }

    // Check if product exists
    const product = await db.product.findUnique({
      where: {
        id: data.productId,
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if warehouse exists
    const warehouse = await db.warehouse.findUnique({
      where: {
        id: data.warehouseId,
      },
    });

    if (!warehouse) {
      return NextResponse.json(
        { error: "Warehouse not found" },
        { status: 404 }
      );
    }

    // Check if inventory item already exists
    const existingInventory = await db.inventory.findFirst({
      where: {
        productId: data.productId,
        warehouseId: data.warehouseId,
      },
    });

    let inventory;
    let oldQuantity = 0;
    let costPrice = product.costPrice || 0;
    let reason = data.reason || "Manual inventory adjustment";
    let branchId = warehouse.branchId;

    if (existingInventory) {
      oldQuantity = existingInventory.quantity;

      // Update existing inventory
      inventory = await db.inventory.update({
        where: {
          id: existingInventory.id,
        },
        data: {
          quantity: data.quantity,
          costPrice: data.costPrice || existingInventory.costPrice || product.costPrice || 0,
        },
        include: {
          product: {
            include: {
              category: true,
            },
          },
          warehouse: true,
        },
      });
    } else {
      // Create new inventory item
      inventory = await db.inventory.create({
        data: {
          productId: data.productId,
          warehouseId: data.warehouseId,
          quantity: data.quantity,
          costPrice: data.costPrice || product.costPrice || 0,
        },
        include: {
          product: {
            include: {
              category: true,
            },
          },
          warehouse: true,
        },
      });
    }

    // Create accounting entries for inventory adjustment
    try {
      await createInventoryJournalEntries(
        data.productId,
        data.warehouseId,
        oldQuantity,
        data.quantity,
        costPrice,
        reason,
        {
          transactionType: TransactionType.INVENTORY,
          referenceId: inventory.id,
          referenceNumber: `INV-ADJ-${inventory.id.substring(0, 8)}`,
          description: `Inventory adjustment for ${inventory.product.name}`,
          branchId: branchId,
          date: new Date(),
        }
      );
    } catch (accountingError) {
      console.error("Error creating accounting entries for inventory adjustment:", accountingError);
      // Don't fail the inventory update if accounting entries fail
    }

    // Format the response
    const formattedItem = {
      id: inventory.id,
      productId: inventory.productId,
      productName: inventory.product.name,
      warehouseId: inventory.warehouseId,
      warehouseName: inventory.warehouse.name,
      quantity: inventory.quantity,
      categoryId: inventory.product.categoryId,
      categoryName: inventory.product.category?.name || "Uncategorized",
      costPrice: inventory.costPrice || product.costPrice || 0,
    };

    return NextResponse.json(formattedItem);
  } catch (error) {
    console.error("Error adding/updating inventory:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to add/update inventory" },
      { status: 500 }
    );
  }
}
