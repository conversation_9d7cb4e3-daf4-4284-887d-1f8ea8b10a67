const fs = require('fs');
const path = require('path');

// Función para eliminar un directorio y su contenido
function removeDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`El directorio no existe: ${dirPath}`);
    return;
  }

  console.log(`Eliminando directorio: ${dirPath}`);
  
  try {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`Directorio eliminado: ${dirPath}`);
  } catch (error) {
    console.error(`Error al eliminar el directorio ${dirPath}:`, error);
  }
}

// Función principal
function removeAccountingFiles() {
  try {
    console.log('Iniciando eliminación de archivos del módulo contable...');
    
    // Directorios a eliminar
    const dirsToRemove = [
      'src/app/(dashboard)/dashboard/accounting',
      'src/components/sidebar/accounting-nav.tsx',
      'src/app/api/accounting'
    ];
    
    // Eliminar directorios
    dirsToRemove.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir);
      removeDirectory(fullPath);
    });
    
    console.log('Los archivos del módulo contable han sido eliminados.');
    
  } catch (error) {
    console.error('Error al eliminar los archivos del módulo contable:', error);
  }
}

removeAccountingFiles();
