"use client";

import { useState, useEffect } from "react";
import StatCard from "./components/StatCard";
import ActivityList from "./components/ActivityList";
import LowStockProducts from "./components/LowStockProducts";
import ContactsBalance from "./components/ContactsBalance";
import OverdueMaintenanceServices from "./components/OverdueMaintenanceServices";
import IntegrationStatus from "./components/IntegrationStatus";
import PeriodFilter from "./components/PeriodFilter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  Re<PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from "recharts";
import {
  Download,
  Filter,
  Calendar,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  Building,
  FileText,
  Printer,
  AlertTriangle,
  Clock,
  Truck,
  Layers,
  CheckCircle
} from "lucide-react";
import { format, subDays, subMonths, startOfMonth, endOfMonth } from "date-fns";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";

// Define chart colors
const COLORS = ['#3895e7', '#307aa8', '#4bc0c0', '#ffcd56', '#ff9f40', '#ff6384'];

export default function DashboardPage() {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState("today");
  const [activeTab, setActiveTab] = useState("overview");

  // Unified dashboard state
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedBranch, setSelectedBranch] = useState<string>("all");
  const [timeFrame, setTimeFrame] = useState<string>("month");
  const [branches, setBranches] = useState<any[]>([]);
  const [dashboardData, setDashboardData] = useState<any>({
    sales: {
      total: 0,
      count: 0,
      paid: 0,
      paidCount: 0,
      byDate: [],
      topProducts: []
    },
    purchases: {
      total: 0,
      count: 0,
      unpaid: 0,
      byDate: [],
      dueInvoices: []
    },
    inventory: {
      totalProducts: 0,
      lowStock: [],
      totalValue: 0,
      byCategory: []
    },
    finance: {
      cashBalance: 0,
      accountsReceivable: 0,
      accountsPayable: 0,
      paymentMethods: []
    }
  });

  useEffect(() => {
    const fetchDashboardStats = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/dashboard/stats?period=${period}`);
        if (!response.ok) {
          const errorData = await response.json();
          console.error("API Error:", errorData);
          throw new Error(errorData.details || "Failed to fetch dashboard statistics");
        }
        const data = await response.json();
        console.log("Dashboard API response:", data);
        console.log("Contacts with balance:", data.contacts?.withBalance);
        setStats(data);
        setError(null);
      } catch (error) {
        console.error("Error fetching dashboard stats:", error);
        setError(error instanceof Error ? error.message : "Failed to load dashboard data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardStats();
  }, [period]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Transform recent sales and purchases into activity items
  const transformActivities = () => {
    if (!stats) return [];

    const salesActivities = stats.recentActivity.sales.map((sale: any) => ({
      id: sale.id,
      type: "sale",
      title: `Sale to ${sale.contact.name}`,
      description: `Invoice #${sale.invoiceNumber}`,
      date: sale.date,
      amount: sale.totalAmount,
      status: sale.paymentStatus,
      link: `/dashboard/sales/${sale.id}`,
    }));

    const purchaseActivities = stats.recentActivity.purchases.map((purchase: any) => ({
      id: purchase.id,
      type: "purchase",
      title: `Purchase from ${purchase.contact.name}`,
      description: `Invoice #${purchase.invoiceNumber}`,
      date: purchase.date,
      amount: purchase.totalAmount,
      status: purchase.paymentStatus,
      link: `/dashboard/purchases/${purchase.id}`,
    }));

    // Combine and sort by date (newest first)
    return [...salesActivities, ...purchaseActivities].sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <p className="mt-4 text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Function to reset dashboard
  const resetDashboard = async () => {
    if (confirm("Are you sure you want to reset the dashboard? This will fix any permission issues.")) {
      try {
        setIsLoading(true);
        const response = await fetch('/api/dashboard/reset', {
          method: 'POST',
        });

        if (response.ok) {
          alert("Dashboard reset successfully. The page will now reload.");
          window.location.reload();
        } else {
          const data = await response.json();
          alert(`Error: ${data.error || "Failed to reset dashboard"}`);
        }
      } catch (error) {
        console.error("Error resetting dashboard:", error);
        alert("Failed to reset dashboard. Check console for details.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (error) {
    // Add diagnostic function
    const runDiagnostics = async () => {
      try {
        setError("Running diagnostics...");
        const response = await fetch(`/api/dashboard/diagnose?period=${period}`);
        if (response.ok) {
          const diagnosticData = await response.json();
          console.log("Diagnostic results:", diagnosticData);

          // Format diagnostic results for display
          let diagnosticMessage = "Diagnostic Results:\n\n";

          // Database connection
          diagnosticMessage += `Database Connection: ${diagnosticData.database.success ? '✅' : '❌'}\n`;
          if (!diagnosticData.database.success) {
            diagnosticMessage += `Error: ${diagnosticData.database.error}\n`;
          }

          // Queries
          diagnosticMessage += "\nQueries:\n";
          for (const [queryName, result] of Object.entries(diagnosticData.queries)) {
            const queryResult = result as any;
            diagnosticMessage += `- ${queryName}: ${queryResult.success ? '✅' : '❌'} (${queryResult.count} records)\n`;
            if (!queryResult.success) {
              diagnosticMessage += `  Error: ${queryResult.error}\n`;
            }
          }

          // Set diagnostic message as error to display it
          setError(diagnosticMessage);
        } else {
          const errorData = await response.json();
          setError(`Diagnostic failed: ${errorData.error || "Unknown error"}`);
        }
      } catch (err) {
        console.error("Error running diagnostics:", err);
        setError(`Error running diagnostics: ${err instanceof Error ? err.message : "Unknown error"}`);
      }
    };

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <div className="flex items-center justify-center text-red-500 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-center text-gray-900 mb-2">Error Loading Dashboard</h3>
          <div className="bg-gray-100 p-4 rounded-md mb-6 max-h-60 overflow-y-auto">
            <pre className="text-sm text-gray-800 whitespace-pre-wrap">{error}</pre>
          </div>
          <div className="flex flex-col space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors"
            >
              Retry
            </button>
            <button
              onClick={runDiagnostics}
              className="w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition-colors"
            >
              Run Diagnostics
            </button>
            <button
              onClick={resetDashboard}
              className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
            >
              Reset Dashboard
            </button>
            <a
              href="/dashboard/fix-permissions"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center"
            >
              Fix Permissions
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4 md:mb-0">Dashboard</h1>
          {error && (
            <button
              onClick={resetDashboard}
              className="ml-4 text-xs bg-red-600 text-white py-1 px-2 rounded-md hover:bg-red-700 transition-colors"
              title="Reset dashboard if you're experiencing issues"
            >
              Reset Dashboard
            </button>
          )}
        </div>
        <div className="flex items-center gap-4">
          <PeriodFilter period={period} onChange={setPeriod} />
          <Button variant="outline" onClick={() => window.print()}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-5">
        <StatCard
          title="Total Sales"
          value={stats.sales.total}
          icon={
            <svg
              className="h-6 w-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          }
          color="indigo"
          link="/dashboard/sales"
          subValue={`${stats.sales.count} invoices`}
          subText={`${stats.sales.paid.toFixed(2)} ج.م paid`}
        />

        <StatCard
          title="Total Purchases"
          value={stats.purchases.total}
          icon={
            <svg
              className="h-6 w-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
              />
            </svg>
          }
          color="green"
          link="/dashboard/purchases"
          subValue={`${stats.purchases.count} invoices`}
        />

        <StatCard
          title="Products"
          value={stats.products.count}
          icon={
            <svg
              className="h-6 w-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
              />
            </svg>
          }
          color="yellow"
          link="/dashboard/products"
          subValue={stats.products.lowStock.length > 0 ? `${stats.products.lowStock.length} low stock` : "Stock levels good"}
        />

        <StatCard
          title="Customers"
          value={stats.customers.count}
          icon={
            <svg
              className="h-6 w-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          }
          color="blue"
          link="/dashboard/contacts?type=customer"
          subValue={`${stats.suppliers.count} suppliers`}
          subText="View all contacts"
        />

        <StatCard
          title="Maintenance"
          value={stats.maintenance?.count || 0}
          icon={
            <svg
              className="h-6 w-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          }
          color="red"
          link="/dashboard/maintenance"
          subValue={stats.maintenance?.overdueCount > 0 ? `${stats.maintenance.overdueCount} overdue` : "No overdue services"}
        />
      </div>

      {/* Activity and Alerts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
        <div className="space-y-6">
          <ActivityList
            activities={transformActivities()}
            title="Recent Activity"
            emptyMessage="No recent activity"
            icon={
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-indigo-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            }
          />



          {/* System Integration Status */}
          <IntegrationStatus />
        </div>

        <div className="space-y-6">
          <LowStockProducts products={stats.products.lowStock} />
          <ContactsBalance contacts={stats.contacts?.withBalance || []} />
          <OverdueMaintenanceServices />
        </div>
      </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6 mt-6">
          {/* Analytics Content */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Sales</p>
                    <h3 className="text-2xl font-bold mt-1">{stats ? formatCurrency(stats.sales.total) : '0'}</h3>
                    <p className="text-sm text-gray-500 mt-1">{stats ? stats.sales.count : 0} invoices</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <ShoppingCart className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Purchases</p>
                    <h3 className="text-2xl font-bold mt-1">{stats ? formatCurrency(stats.purchases.total) : '0'}</h3>
                    <p className="text-sm text-gray-500 mt-1">{stats ? stats.purchases.count : 0} invoices</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <Truck className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Products</p>
                    <h3 className="text-2xl font-bold mt-1">{stats ? stats.products.count : 0}</h3>
                    <p className="text-sm text-gray-500 mt-1">{stats ? stats.products.lowStock.length : 0} low stock</p>
                  </div>
                  <div className="p-3 bg-yellow-100 rounded-full">
                    <Package className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Customers</p>
                    <h3 className="text-2xl font-bold mt-1">{stats ? stats.customers.count : 0}</h3>
                    <p className="text-sm text-gray-500 mt-1">{stats ? stats.suppliers.count : 0} suppliers</p>
                  </div>
                  <div className="p-3 bg-indigo-100 rounded-full">
                    <Users className="h-6 w-6 text-indigo-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Sales Trend</CardTitle>
                <CardDescription>Sales performance over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={stats?.recentActivity?.sales || []}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                      <Area type="monotone" dataKey="totalAmount" stroke="#3895e7" fill="#3895e7" fillOpacity={0.3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest sales and purchases</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transformActivities().slice(0, 5).map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full ${activity.type === 'sale' ? 'bg-blue-100' : 'bg-green-100'}`}>
                          {activity.type === 'sale' ? (
                            <ShoppingCart className={`h-4 w-4 ${activity.type === 'sale' ? 'text-blue-600' : 'text-green-600'}`} />
                          ) : (
                            <Truck className={`h-4 w-4 ${activity.type === 'sale' ? 'text-blue-600' : 'text-green-600'}`} />
                          )}
                        </div>
                        <div>
                          <p className="font-medium text-sm">{activity.title}</p>
                          <p className="text-xs text-gray-500">{activity.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-sm">{formatCurrency(activity.amount)}</p>
                        <Badge variant={activity.status === 'PAID' ? 'default' : 'secondary'} className="text-xs">
                          {activity.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
