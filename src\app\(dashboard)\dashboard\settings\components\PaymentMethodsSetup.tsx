"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, RefreshCw, Plus, Trash, X, Upload } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import IconUploader from "@/components/payment/IconUploader";
import PaymentMethodIcon from "@/components/payment/PaymentMethodIcon";

interface Account {
  id: string;
  name: string;
  accountNumber: string;
  balance: number;
}

interface Journal {
  id: string;
  name: string;
  code: string;
}

interface PaymentMethod {
  id: string;
  name: string;
  code: string;
  isActive: boolean;
  sequence: number;
  accountId: string | null;
  journalId: string | null;
  account: Account | null;
  journal: Journal | null;
  iconName: string | null;
  iconUrl: string | null;
  color: string | null;
  branchIds: string[];
}

export default function PaymentMethodsSetup() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [journals, setJournals] = useState<Journal[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newMethodName, setNewMethodName] = useState("");
  const [newMethodCode, setNewMethodCode] = useState("");
  const [newMethodIcon, setNewMethodIcon] = useState("");
  const [newMethodIconUrl, setNewMethodIconUrl] = useState<string | null>(null);
  const [newMethodColor, setNewMethodColor] = useState("#3895e7");
  const [branches, setBranches] = useState<{id: string, name: string}[]>([]);
  const [selectedBranches, setSelectedBranches] = useState<string[]>([]);
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null);

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      // Fetch payment methods from API
      const response = await fetch('/api/settings/payment-methods?from=settings');

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to fetch payment methods:', errorData);
        setError(errorData.error || 'Failed to fetch payment methods');
        setPaymentMethods([]);
        return;
      }

      const data = await response.json();
      setPaymentMethods(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred while fetching payment methods');
      setPaymentMethods([]);
    }
  };

  // Add payment method
  const addPaymentMethod = async () => {
    if (!newMethodName || !newMethodCode) {
      setError("Name and code are required");
      return;
    }

    // Check if payment method with this code already exists
    const codeExists = paymentMethods.some(
      method => method.code.toLowerCase() === newMethodCode.toUpperCase().toLowerCase()
    );

    if (codeExists) {
      setError("Payment method with this code already exists");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Send POST request to API
      const response = await fetch('/api/settings/payment-methods?from=settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newMethodName,
          code: newMethodCode.toUpperCase(),
          isActive: true,
          iconName: newMethodIcon || null,
          iconUrl: newMethodIconUrl || null,
          color: newMethodColor || null,
          branchIds: selectedBranches.length > 0 ? selectedBranches : []
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to add payment method');
        return;
      }

      const newMethod = await response.json();

      setPaymentMethods([...paymentMethods, newMethod]);
      setSuccess(`Payment method "${newMethodName}" added successfully`);

      // Clear form
      setNewMethodName("");
      setNewMethodCode("");
      setNewMethodIcon("");
      setNewMethodIconUrl(null);
      setNewMethodColor("#3895e7");
      setSelectedBranches([]);
    } catch (error) {
      console.error('Error adding payment method:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete payment method
  const deletePaymentMethod = async (id: string) => {
    if (!confirm("Are you sure you want to delete this payment method?")) {
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Send DELETE request to API
      const response = await fetch(`/api/settings/payment-methods/${id}?from=settings`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete payment method');
      }

      const updatedMethods = paymentMethods.filter(method => method.id !== id);
      setPaymentMethods(updatedMethods);
      setSuccess("Payment method deleted successfully");
    } catch (error) {
      console.error('Error deleting payment method:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch accounts
  const fetchAccounts = async () => {
    try {
      // Add from=settings to bypass permission check
      const response = await fetch('/api/accounts?isActive=true&from=settings');

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to fetch accounts:', errorData);
        setError(errorData.error || 'Failed to fetch accounts');
        setAccounts([]);
        return;
      }

      const data = await response.json();
      setAccounts(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred while fetching accounts');
      setAccounts([]);
    }
  };

  // Fetch journals
  const fetchJournals = async () => {
    try {
      // Add from=settings to bypass permission check
      const response = await fetch('/api/journals?isActive=true&from=settings');

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to fetch journals:', errorData);
        setError(errorData.error || 'Failed to fetch journals');
        setJournals([]);
        return;
      }

      const data = await response.json();
      setJournals(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching journals:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred while fetching journals');
      setJournals([]);
    }
  };

  // Fetch branches
  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches?isActive=true&from=settings');

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to fetch branches:', errorData);
        setError(errorData.error || 'Failed to fetch branches');
        setBranches([]);
        return;
      }

      const data = await response.json();
      setBranches(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching branches:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred while fetching branches');
      setBranches([]);
    }
  };

  // Update payment method
  const updatePaymentMethod = async (id: string, data: any) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Send PATCH request to API
      const response = await fetch(`/api/settings/payment-methods/${id}?from=settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update payment method');
      }

      const updatedMethod = await response.json();

      // Update payment methods list
      setPaymentMethods(paymentMethods.map(method =>
        method.id === id ? updatedMethod : method
      ));

      setSuccess(`Payment method "${updatedMethod.name}" updated successfully`);
      setEditingMethod(null);
    } catch (error) {
      console.error('Error updating payment method:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch all data in parallel with better error handling
        const results = await Promise.allSettled([
          fetchPaymentMethods(),
          fetchAccounts(),
          fetchJournals(),
          fetchBranches()
        ]);

        // Check for any rejected promises
        const rejected = results.filter(result => result.status === 'rejected');
        if (rejected.length > 0) {
          // Log all errors
          rejected.forEach((result: any) => {
            console.error('Error in data fetching:', result.reason);
          });

          // Set a user-friendly error message
          setError('Some data could not be loaded. Please try again or contact support if the issue persists.');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>
              Configure payment methods for your business
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.href = '/dashboard/settings/payment-methods/reorder'}
          >
            Reorder Payment Methods
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading && paymentMethods.length === 0 ? (
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-500" />
            <span className="ml-2 text-gray-500">Loading payment methods...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : success ? (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle className="text-green-700">Success</AlertTitle>
            <AlertDescription className="text-green-600">{success}</AlertDescription>
          </Alert>
        ) : null}

        <div className="space-y-4">
          {/* Add new payment method form */}
          <div className="p-4 border rounded-md">
            <h3 className="text-sm font-medium mb-3">Add New Payment Method</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="method-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  id="method-name"
                  value={newMethodName}
                  onChange={(e) => setNewMethodName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g. Cash, Credit Card"
                />
              </div>
              <div>
                <label htmlFor="method-code" className="block text-sm font-medium text-gray-700 mb-1">
                  Code
                </label>
                <input
                  type="text"
                  id="method-code"
                  value={newMethodCode}
                  onChange={(e) => setNewMethodCode(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g. CASH, VISA"
                />
              </div>
              <div>
                <label htmlFor="method-icon" className="block text-sm font-medium text-gray-700 mb-1">
                  Icon Name
                </label>
                <select
                  id="method-icon"
                  value={newMethodIcon}
                  onChange={(e) => setNewMethodIcon(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="">-- Select Icon --</option>

                  {/* Basic payment icons */}
                  <optgroup label="Basic Payment Icons">
                    <option value="cash">Cash</option>
                    <option value="credit-card">Credit Card</option>
                    <option value="bank">Bank</option>
                    <option value="mobile">Mobile Payment</option>
                    <option value="wallet">Wallet</option>
                    <option value="money">Money</option>
                  </optgroup>

                  {/* Additional payment icons */}
                  <optgroup label="Additional Payment Icons">
                    <option value="landmark">Landmark</option>
                    <option value="qr-code">QR Code</option>
                    <option value="receipt">Receipt</option>
                    <option value="coins">Coins</option>
                    <option value="circle-dollar">Circle Dollar</option>
                    <option value="percent">Percent</option>
                    <option value="shopping-cart">Shopping Cart</option>
                    <option value="store">Store</option>
                    <option value="truck">Truck</option>
                    <option value="briefcase">Briefcase</option>
                    <option value="gift">Gift</option>
                    <option value="scan">Scan</option>
                  </optgroup>

                  {/* Digital payment icons */}
                  <optgroup label="Digital Payment Icons">
                    <option value="bitcoin">Bitcoin</option>
                    <option value="paypal">PayPal</option>
                  </optgroup>

                  {/* Transaction icons */}
                  <optgroup label="Transaction Icons">
                    <option value="repeat">Repeat</option>
                    <option value="transfer">Transfer</option>
                    <option value="badge-check">Badge Check</option>
                    <option value="badge-dollar">Badge Dollar</option>
                  </optgroup>
                </select>
              </div>
              <div>
                <label htmlFor="method-color" className="block text-sm font-medium text-gray-700 mb-1">
                  Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    id="method-color"
                    value={newMethodColor}
                    onChange={(e) => setNewMethodColor(e.target.value)}
                    className="h-8 w-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <input
                    type="text"
                    value={newMethodColor}
                    onChange={(e) => setNewMethodColor(e.target.value)}
                    className="ml-2 flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="#3895e7"
                  />
                </div>
              </div>
            </div>

            <div className="mt-4 flex items-center">
              <div className="mr-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Custom Icon
                </label>
                <IconUploader
                  onIconUploaded={(iconUrl) => setNewMethodIconUrl(iconUrl)}
                />
              </div>
              {newMethodIconUrl && (
                <div className="flex items-center">
                  <div className="w-16 h-16 border border-gray-300 rounded-md flex items-center justify-center">
                    <img
                      src={newMethodIconUrl}
                      alt="Custom icon"
                      className="max-w-full max-h-full p-1"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => setNewMethodIconUrl(null)}
                    className="ml-2 text-red-500 hover:text-red-700"
                  >
                    <X size={16} />
                  </button>
                </div>
              )}
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Branches
              </label>
              <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                {branches.length === 0 ? (
                  <div className="text-gray-500 text-sm">No branches found</div>
                ) : (
                  branches.map((branch) => (
                    <div key={branch.id} className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        id={`branch-${branch.id}`}
                        checked={selectedBranches.includes(branch.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedBranches([...selectedBranches, branch.id]);
                          } else {
                            setSelectedBranches(selectedBranches.filter(id => id !== branch.id));
                          }
                        }}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`branch-${branch.id}`} className="ml-2 block text-sm text-gray-900">
                        {branch.name}
                      </label>
                    </div>
                  ))
                )}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                If no branches are selected, the payment method will be available in all branches.
              </div>
            </div>

            <div className="mt-4">
              <Button
                onClick={addPaymentMethod}
                disabled={isLoading || !newMethodName || !newMethodCode}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Payment Method
              </Button>
            </div>
          </div>

          {/* Payment methods list */}
          {paymentMethods.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Code
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Icon
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Color
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Account
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Journal
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Branches
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paymentMethods.map((method) => (
                    <tr key={method.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {method.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {method.code}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${method.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {method.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {method.iconUrl ? (
                          <div className="flex items-center">
                            <img
                              src={method.iconUrl}
                              alt="Custom icon"
                              className="w-6 h-6 mr-2 object-contain"
                            />
                            <span className="text-blue-600">Custom</span>
                          </div>
                        ) : method.iconName ? (
                          <div className="flex items-center">
                            <span className="mr-2">
                              <PaymentMethodIcon
                                methodCode={method.code}
                                iconName={method.iconName}
                                color={method.color || undefined}
                                size={16}
                              />
                            </span>
                            <span className="text-blue-600">{method.iconName}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">Default</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {method.color ? (
                          <div className="flex items-center">
                            <div className="h-4 w-4 rounded-full mr-2" style={{ backgroundColor: method.color }}></div>
                            <span>{method.color}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">Default</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {method.account ? (
                          <span className="text-blue-600">{method.account.name}</span>
                        ) : (
                          <span className="text-gray-400">Not linked</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {method.journal ? (
                          <span className="text-blue-600">{method.journal.name}</span>
                        ) : (
                          <span className="text-gray-400">Not linked</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {method.branchIds && method.branchIds.length > 0 ? (
                          <span className="text-blue-600">{method.branchIds.length} branches</span>
                        ) : (
                          <span className="text-gray-400">All branches</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => setEditingMethod(method)}
                            className="text-blue-600 hover:text-blue-900"
                            disabled={isLoading}
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => deletePaymentMethod(method.id)}
                            className="text-red-600 hover:text-red-900"
                            disabled={isLoading}
                          >
                            <Trash className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              No payment methods found. Add your first payment method.
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter>
        <Button
          variant="outline"
          onClick={async () => {
            try {
              setIsLoading(true);
              setError(null);

              // Fetch all data in parallel with better error handling
              const results = await Promise.allSettled([
                fetchPaymentMethods(),
                fetchAccounts(),
                fetchJournals(),
                fetchBranches()
              ]);

              // Check for any rejected promises
              const rejected = results.filter(result => result.status === 'rejected');
              if (rejected.length > 0) {
                // Log all errors
                rejected.forEach((result: any) => {
                  console.error('Error in data refreshing:', result.reason);
                });

                // Set a user-friendly error message
                setError('Some data could not be refreshed. Please try again or contact support if the issue persists.');
              } else {
                setSuccess("Data refreshed successfully");
                setTimeout(() => setSuccess(null), 3000);
              }
            } catch (error) {
              console.error('Error refreshing data:', error);
              setError('Failed to refresh data. Please try again.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardFooter>

      {/* Edit Payment Method Dialog */}
      {editingMethod && (
        <Dialog open={!!editingMethod} onOpenChange={(open) => !open && setEditingMethod(null)}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit Payment Method</DialogTitle>
              <button
                onClick={() => setEditingMethod(null)}
                className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="edit-name" className="text-right text-sm font-medium">
                  Name
                </label>
                <input
                  id="edit-name"
                  value={editingMethod.name}
                  onChange={(e) => setEditingMethod({...editingMethod, name: e.target.value})}
                  className="col-span-3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="edit-code" className="text-right text-sm font-medium">
                  Code
                </label>
                <input
                  id="edit-code"
                  value={editingMethod.code}
                  disabled
                  className="col-span-3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-gray-500 sm:text-sm"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="edit-account" className="text-right text-sm font-medium">
                  Account
                </label>
                <select
                  id="edit-account"
                  value={editingMethod.accountId || ""}
                  onChange={(e) => setEditingMethod({...editingMethod, accountId: e.target.value || null})}
                  className="col-span-3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="">-- Select Account --</option>
                  {accounts.map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.accountNumber})
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="edit-journal" className="text-right text-sm font-medium">
                  Journal
                </label>
                <select
                  id="edit-journal"
                  value={editingMethod.journalId || ""}
                  onChange={(e) => setEditingMethod({...editingMethod, journalId: e.target.value || null})}
                  className="col-span-3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="">-- Select Journal --</option>
                  {journals.map((journal) => (
                    <option key={journal.id} value={journal.id}>
                      {journal.name} ({journal.code})
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="edit-status" className="text-right text-sm font-medium">
                  Status
                </label>
                <select
                  id="edit-status"
                  value={editingMethod.isActive ? "active" : "inactive"}
                  onChange={(e) => setEditingMethod({...editingMethod, isActive: e.target.value === "active"})}
                  className="col-span-3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="edit-icon" className="text-right text-sm font-medium">
                  Icon
                </label>
                <select
                  id="edit-icon"
                  value={editingMethod.iconName || ""}
                  onChange={(e) => setEditingMethod({...editingMethod, iconName: e.target.value || null})}
                  className="col-span-3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="">-- Select Icon --</option>

                  {/* Basic payment icons */}
                  <optgroup label="Basic Payment Icons">
                    <option value="cash">Cash</option>
                    <option value="credit-card">Credit Card</option>
                    <option value="bank">Bank</option>
                    <option value="mobile">Mobile Payment</option>
                    <option value="wallet">Wallet</option>
                    <option value="money">Money</option>
                  </optgroup>

                  {/* Additional payment icons */}
                  <optgroup label="Additional Payment Icons">
                    <option value="landmark">Landmark</option>
                    <option value="qr-code">QR Code</option>
                    <option value="receipt">Receipt</option>
                    <option value="coins">Coins</option>
                    <option value="circle-dollar">Circle Dollar</option>
                    <option value="percent">Percent</option>
                    <option value="shopping-cart">Shopping Cart</option>
                    <option value="store">Store</option>
                    <option value="truck">Truck</option>
                    <option value="briefcase">Briefcase</option>
                    <option value="gift">Gift</option>
                    <option value="scan">Scan</option>
                  </optgroup>

                  {/* Digital payment icons */}
                  <optgroup label="Digital Payment Icons">
                    <option value="bitcoin">Bitcoin</option>
                    <option value="paypal">PayPal</option>
                  </optgroup>

                  {/* Transaction icons */}
                  <optgroup label="Transaction Icons">
                    <option value="repeat">Repeat</option>
                    <option value="transfer">Transfer</option>
                    <option value="badge-check">Badge Check</option>
                    <option value="badge-dollar">Badge Dollar</option>
                  </optgroup>
                </select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="edit-color" className="text-right text-sm font-medium">
                  Color
                </label>
                <div className="col-span-3 flex items-center">
                  <input
                    type="color"
                    id="edit-color"
                    value={editingMethod.color || "#3895e7"}
                    onChange={(e) => setEditingMethod({...editingMethod, color: e.target.value})}
                    className="h-8 w-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <input
                    type="text"
                    value={editingMethod.color || ""}
                    onChange={(e) => setEditingMethod({...editingMethod, color: e.target.value})}
                    className="ml-2 flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="#3895e7"
                  />
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right text-sm font-medium">
                  Custom Icon
                </label>
                <div className="col-span-3 flex items-center">
                  <IconUploader
                    onIconUploaded={(iconUrl) => setEditingMethod({...editingMethod, iconUrl})}
                  />

                  {editingMethod.iconUrl && (
                    <div className="flex items-center ml-4">
                      <div className="w-12 h-12 border border-gray-300 rounded-md flex items-center justify-center">
                        <img
                          src={editingMethod.iconUrl}
                          alt="Custom icon"
                          className="max-w-full max-h-full p-1"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => setEditingMethod({...editingMethod, iconUrl: null})}
                        className="ml-2 text-red-500 hover:text-red-700"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <label className="text-right text-sm font-medium pt-2">
                  Branches
                </label>
                <div className="col-span-3">
                  <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                    {branches.length === 0 ? (
                      <div className="text-gray-500 text-sm">No branches found</div>
                    ) : (
                      branches.map((branch) => (
                        <div key={branch.id} className="flex items-center mb-2">
                          <input
                            type="checkbox"
                            id={`edit-branch-${branch.id}`}
                            checked={editingMethod.branchIds?.includes(branch.id) || false}
                            onChange={(e) => {
                              const currentBranchIds = editingMethod.branchIds || [];
                              if (e.target.checked) {
                                setEditingMethod({
                                  ...editingMethod,
                                  branchIds: [...currentBranchIds, branch.id]
                                });
                              } else {
                                setEditingMethod({
                                  ...editingMethod,
                                  branchIds: currentBranchIds.filter(id => id !== branch.id)
                                });
                              }
                            }}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`edit-branch-${branch.id}`} className="ml-2 block text-sm text-gray-900">
                            {branch.name}
                          </label>
                        </div>
                      ))
                    )}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    If no branches are selected, the payment method will be available in all branches.
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setEditingMethod(null)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={() => updatePaymentMethod(editingMethod.id, {
                  name: editingMethod.name,
                  isActive: editingMethod.isActive,
                  accountId: editingMethod.accountId,
                  journalId: editingMethod.journalId,
                  iconName: editingMethod.iconName,
                  iconUrl: editingMethod.iconUrl,
                  color: editingMethod.color,
                  branchIds: editingMethod.branchIds
                })}
                disabled={isLoading}
              >
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
}
