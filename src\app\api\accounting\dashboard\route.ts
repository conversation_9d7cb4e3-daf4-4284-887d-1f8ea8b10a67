import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { startOfDay, endOfDay } from "date-fns";

// GET /api/accounting/dashboard - Get accounting dashboard data
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get today's date range
    const today = new Date();
    const startOfToday = startOfDay(today);
    const endOfToday = endOfDay(today);

    // Get payment method balances
    const paymentMethods = await db.paymentMethodSettings.findMany({
      where: {
        isActive: true,
      },
      include: {
        account: true,
      },
    });

    // Extract balances for each payment method
    let cashBalance = 0;
    let vodafoneCashBalance = 0;
    let bankBalance = 0;
    let visaBalance = 0;

    paymentMethods.forEach(method => {
      if (method.account) {
        switch (method.code) {
          case "CASH":
            cashBalance = method.account.balance;
            break;
          case "VODAFONE_CASH":
            vodafoneCashBalance = method.account.balance;
            break;
          case "BANK_TRANSFER":
            bankBalance = method.account.balance;
            break;
          case "VISA":
            visaBalance = method.account.balance;
            break;
        }
      }
    });

    // Get today's sales
    const todaysSales = await db.sale.aggregate({
      where: {
        createdAt: {
          gte: startOfToday,
          lte: endOfToday,
        },
      },
      _sum: {
        totalAmount: true,
      },
    });

    // Get today's purchases
    const todaysPurchases = await db.purchase.aggregate({
      where: {
        createdAt: {
          gte: startOfToday,
          lte: endOfToday,
        },
      },
      _sum: {
        totalAmount: true,
      },
    });

    // Get today's expenses (payment vouchers)
    const todaysExpenses = await db.paymentVoucher.aggregate({
      where: {
        date: {
          gte: startOfToday,
          lte: endOfToday,
        },
      },
      _sum: {
        amount: true,
      },
    });

    // Get today's receipts (receipt vouchers)
    const todaysReceipts = await db.receiptVoucher.aggregate({
      where: {
        date: {
          gte: startOfToday,
          lte: endOfToday,
        },
      },
      _sum: {
        amount: true,
      },
    });

    // Get pending payment vouchers
    const pendingPaymentVouchers = await db.paymentVoucher.count({
      where: {
        status: "PENDING",
      },
    });

    // Get pending receipt vouchers
    const pendingReceiptVouchers = await db.receiptVoucher.count({
      where: {
        status: "PENDING",
      },
    });

    // Get unpaid sales invoices
    const pendingSalesInvoices = await db.sale.count({
      where: {
        status: "UNPAID",
      },
    });

    // Get unpaid purchase invoices
    const pendingPurchaseInvoices = await db.purchase.count({
      where: {
        status: "UNPAID",
      },
    });

    // Get total receivables (customer accounts)
    const receivablesAccount = await db.account.findFirst({
      where: {
        type: "RECEIVABLE",
      },
    });

    const totalReceivables = receivablesAccount?.balance || 0;

    // Get total payables (supplier accounts)
    const payablesAccount = await db.account.findFirst({
      where: {
        type: "PAYABLE",
      },
    });

    const totalPayables = payablesAccount?.balance || 0;

    // Get recent transactions (journal entries)
    const recentTransactions = await db.journalEntry.findMany({
      take: 10,
      orderBy: {
        date: "desc",
      },
      include: {
        journal: true,
        debitAccount: {
          select: {
            name: true,
            code: true,
          },
        },
        creditAccount: {
          select: {
            name: true,
            code: true,
          },
        },
        contact: {
          select: {
            name: true,
          },
        },
      },
    });

    // Return dashboard data
    return NextResponse.json({
      cashBalance,
      vodafoneCashBalance,
      bankBalance,
      visaBalance,
      totalReceivables,
      totalPayables,
      recentTransactions,
      todaysSales: todaysSales._sum.totalAmount || 0,
      todaysPurchases: todaysPurchases._sum.totalAmount || 0,
      todaysExpenses: todaysExpenses._sum.amount || 0,
      todaysReceipts: todaysReceipts._sum.amount || 0,
      pendingPaymentVouchers,
      pendingReceiptVouchers,
      pendingSalesInvoices,
      pendingPurchaseInvoices,
    });
  } catch (error: any) {
    console.error("Error generating accounting dashboard:", error);
    return NextResponse.json(
      { error: error.message || "Failed to generate accounting dashboard" },
      { status: 500 }
    );
  }
}
