generator client {
  provider = "prisma-client-js"
}

generator ts_node {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                       String                     @id @default(uuid())
  email                    String                     @unique
  name                     String
  password                 String
  role                     Role                       @default(EMPLOYEE)
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  branchId                 String?
  isActive                 Boolean                    @default(true)
  CreditNote               CreditNote[]
  InventoryTransfer        InventoryTransfer[]
  MaintenanceService       MaintenanceService[]
  MaintenanceStatusHistory MaintenanceStatusHistory[]
  purchases                Purchase[]
  sales                    Sale[]
  paymentVouchers          PaymentVoucher[]
  receiptVouchers          ReceiptVoucher[]
  branch                   Branch?                    @relation(fields: [branchId], references: [id])
  warehouses               UserWarehouse[]
  permissions              Permission[]               @relation("PermissionToUser")
  notifications            Notification[]

}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  type      String
  title     String
  message   String
  link      String?
  read      Boolean  @default(false)
  metadata  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([read])
  @@index([createdAt])
}

model UserWarehouse {
  id          String    @id @default(uuid())
  userId      String
  warehouseId String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)

  @@unique([userId, warehouseId])
}

model Permission {
  id          String   @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]   @relation("PermissionToUser")
}

model Branch {
  id                 String               @id @default(uuid())
  name               String
  address            String
  phone              String
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  code               String               @unique
  isActive           Boolean              @default(true)
  accounts           Account[]
  CreditNote         CreditNote[]
  journals           Journal[]
  MaintenanceService MaintenanceService[]
  purchases          Purchase[]
  sales              Sale[]
  paymentVouchers    PaymentVoucher[]
  receiptVouchers    ReceiptVoucher[]
  users              User[]
  warehouses         Warehouse[]
}

model Warehouse {
  id                                                                    String              @id @default(uuid())
  name                                                                  String
  branchId                                                              String
  createdAt                                                             DateTime            @default(now())
  updatedAt                                                             DateTime            @updatedAt
  isActive                                                              Boolean             @default(true)
  inventory                                                             Inventory[]
  inventoryMovements                                                    InventoryMovement[]
  InventoryTransfer_InventoryTransfer_destinationWarehouseIdToWarehouse InventoryTransfer[] @relation("InventoryTransfer_destinationWarehouseIdToWarehouse")
  InventoryTransfer_InventoryTransfer_sourceWarehouseIdToWarehouse      InventoryTransfer[] @relation("InventoryTransfer_sourceWarehouseIdToWarehouse")
  users                                                                 UserWarehouse[]
  saleItems                                                             SaleItem[] // Add relation to SaleItem
  branch                                                                Branch              @relation(fields: [branchId], references: [id])
}

model Product {
  id                    String                  @id @default(uuid())
  name                  String
  description           String?
  basePrice             Float
  categoryId            String
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  componentType         String?
  isComponent           Boolean                 @default(false)
  isCustomizable        Boolean                 @default(false)
  costPrice             Float                   @default(0)
  CreditNoteItem        CreditNoteItem[]
  inventory             Inventory[]
  InventoryTransferItem InventoryTransferItem[]
  MaintenancePart       MaintenancePart[]
  category              Category                @relation(fields: [categoryId], references: [id])
  purchaseItems         PurchaseItem[]
  saleItems             SaleItem[]
  specifications        Specification[]
  discounts             Discount[]
  usedAsComponent       SaleItemComponent[]     @relation("ProductToSaleItemComponent")
}

model Category {
  id          String     @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  products    Product[]
  discounts   Discount[]
}

model Specification {
  id        String   @id @default(uuid())
  name      String
  value     String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id])
}

model Inventory {
  id          String    @id @default(uuid())
  productId   String
  warehouseId String
  quantity    Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  costPrice   Float     @default(0)
  product     Product   @relation(fields: [productId], references: [id])
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])
}

model InventoryMovement {
  id             String    @id @default(uuid())
  productId      String
  warehouseId    String
  quantity       Int
  type           String // SALE, PURCHASE, ADJUSTMENT, TRANSFER_IN, TRANSFER_OUT, COMPONENT_SALE, COMPONENT_PURCHASE
  date           DateTime  @default(now())
  documentNumber String?
  reference      String?
  branchId       String?
  contactId      String? // Add contactId to link movements to customers/suppliers
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  warehouse      Warehouse @relation(fields: [warehouseId], references: [id])
  contact        Contact?  @relation(fields: [contactId], references: [id])

  @@index([contactId])
  @@index([type])
  @@index([date])
}

model Sale {
  id             String         @id @default(uuid())
  userId         String
  branchId       String
  date           DateTime       @default(now())
  status         SaleStatus     @default(PENDING)
  totalAmount    Float
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  applyTax       Boolean        @default(false)
  currency       String         @default("EGP")
  discountAmount Float          @default(0)
  notes          String?
  subtotalAmount Float
  taxAmount      Float          @default(0)
  taxRate        Float          @default(0)
  invoiceNumber  String         @unique
  paymentMethod  PaymentMethod  @default(CASH)
  paymentStatus  PaymentStatus  @default(UNPAID)
  contactId      String
  branch         Branch         @relation(fields: [branchId], references: [id])
  contact        Contact        @relation(fields: [contactId], references: [id])
  user           User           @relation(fields: [userId], references: [id])
  items          SaleItem[]
  discounts      SaleDiscount[]
}

model SaleItem {
  id                   String              @id @default(uuid())
  saleId               String
  productId            String
  quantity             Int
  unitPrice            Float
  totalPrice           Float
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  isCustomized         Boolean             @default(false)
  specifications       String?
  customizedComponents String?
  warehouseId          String?
  product              Product             @relation(fields: [productId], references: [id])
  sale                 Sale                @relation(fields: [saleId], references: [id])
  warehouse            Warehouse?          @relation(fields: [warehouseId], references: [id])
  components           SaleItemComponent[]
  discounts            SaleItemDiscount[]
}

model SaleItemComponent {
  id            String   @id @default(uuid())
  saleItemId    String
  componentId   String
  componentName String
  componentType String
  quantity      Int
  totalQuantity Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  saleItem      SaleItem @relation(fields: [saleItemId], references: [id])
  component     Product  @relation("ProductToSaleItemComponent", fields: [componentId], references: [id])
}

model Purchase {
  id               String            @id @default(uuid())
  userId           String
  branchId         String
  date             DateTime          @default(now())
  dueDate          DateTime?
  status           PurchaseStatus    @default(PENDING)
  totalAmount      Float
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  contactId        String
  currency         String            @default("EGP")
  discountAmount   Float             @default(0)
  invoiceNumber    String            @unique
  notes            String?
  paymentMethod    PaymentMethod     @default(CASH)
  paymentStatus    PaymentStatus     @default(UNPAID)
  subtotalAmount   Float
  taxAmount        Float             @default(0)
  reminderSent     Boolean           @default(false)
  lastReminderDate DateTime?
  branch           Branch            @relation(fields: [branchId], references: [id])
  contact          Contact           @relation(fields: [contactId], references: [id])
  user             User              @relation(fields: [userId], references: [id])
  items            PurchaseItem[]
  payments         PurchasePayment[]

  @@index([branchId])
  @@index([contactId])
  @@index([userId])
  @@index([date])
  @@index([status])
  @@index([dueDate])
  @@index([paymentStatus])
}

model PurchaseItem {
  id         String   @id @default(uuid())
  purchaseId String
  productId  String
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  product    Product  @relation(fields: [productId], references: [id])
  purchase   Purchase @relation(fields: [purchaseId], references: [id])
}

model PurchasePayment {
  id            String        @id @default(uuid())
  purchaseId    String
  amount        Float
  paymentMethod PaymentMethod
  paymentDate   DateTime
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  purchase      Purchase      @relation(fields: [purchaseId], references: [id], onDelete: Cascade)

  @@index([purchaseId])
  @@index([paymentDate])
}

model Contact {
  id                 String               @id @default(uuid())
  name               String
  phone              String               @unique
  address            String?
  isCustomer         Boolean              @default(false)
  isSupplier         Boolean              @default(false)
  balance            Float                @default(0)
  creditLimit        Float                @default(0)
  creditPeriod       Int                  @default(30)
  lastReminderDate   DateTime?
  isActive           Boolean              @default(true)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  openingBalance     Float                @default(0)
  openingBalanceDate DateTime             @default(now())
  email              String?
  isVIP              Boolean              @default(false)
  loyaltyPoints      Int                  @default(0)
  loyaltyTier        String?
  birthday           DateTime?
  defaultDiscountId  String?
  CreditNote         CreditNote[]
  MaintenanceService MaintenanceService[]
  purchases          Purchase[]
  sales              Sale[]
  paymentVouchers    PaymentVoucher[]
  receiptVouchers    ReceiptVoucher[]
  customerDiscounts  CustomerDiscount[]
  defaultDiscount    Discount?            @relation(fields: [defaultDiscountId], references: [id])
  inventoryMovements InventoryMovement[] // Add relation to inventory movements

  @@index([isVIP])
  @@index([loyaltyTier])
}

model CreditNote {
  id                    String              @id
  userId                String
  contactId             String
  branchId              String
  originalInvoiceId     String?
  originalInvoiceNumber String?
  creditNoteNumber      String              @unique
  date                  DateTime            @default(now())
  status                CreditNoteStatus    @default(PENDING)
  paymentStatus         PaymentStatus       @default(UNPAID)
  paymentMethod         PaymentMethod?
  totalAmount           Float
  subtotalAmount        Float
  taxAmount             Float               @default(0)
  taxRate               Float               @default(0)
  discountAmount        Float               @default(0)
  applyTax              Boolean             @default(false)
  currency              String              @default("EGP")
  notes                 String?
  createdAt             DateTime            @default(now())
  updatedAt             DateTime
  Branch                Branch              @relation(fields: [branchId], references: [id])
  Contact               Contact             @relation(fields: [contactId], references: [id])
  User                  User                @relation(fields: [userId], references: [id])
  CreditNoteItem        CreditNoteItem[]
  CreditNotePayment     CreditNotePayment[]
}

model CreditNoteItem {
  id             String     @id
  creditNoteId   String
  productId      String
  quantity       Int
  unitPrice      Float
  totalPrice     Float
  reason         String
  originalItemId String?
  createdAt      DateTime   @default(now())
  updatedAt      DateTime
  CreditNote     CreditNote @relation(fields: [creditNoteId], references: [id])
  Product        Product    @relation(fields: [productId], references: [id])
}

model CreditNotePayment {
  id           String        @id
  creditNoteId String
  method       PaymentMethod
  amount       Float
  createdAt    DateTime      @default(now())
  updatedAt    DateTime
  CreditNote   CreditNote    @relation(fields: [creditNoteId], references: [id])
}

model InventoryTransfer {
  id                                                            String                  @id
  referenceNumber                                               String                  @unique
  sourceWarehouseId                                             String
  destinationWarehouseId                                        String
  date                                                          DateTime                @default(now())
  notes                                                         String?
  createdAt                                                     DateTime                @default(now())
  updatedAt                                                     DateTime
  userId                                                        String
  Warehouse_InventoryTransfer_destinationWarehouseIdToWarehouse Warehouse               @relation("InventoryTransfer_destinationWarehouseIdToWarehouse", fields: [destinationWarehouseId], references: [id])
  Warehouse_InventoryTransfer_sourceWarehouseIdToWarehouse      Warehouse               @relation("InventoryTransfer_sourceWarehouseIdToWarehouse", fields: [sourceWarehouseId], references: [id])
  User                                                          User                    @relation(fields: [userId], references: [id])
  InventoryTransferItem                                         InventoryTransferItem[]
}

model InventoryTransferItem {
  id                  String            @id
  inventoryTransferId String
  productId           String
  quantity            Int
  createdAt           DateTime          @default(now())
  updatedAt           DateTime
  InventoryTransfer   InventoryTransfer @relation(fields: [inventoryTransferId], references: [id], onDelete: Cascade)
  Product             Product           @relation(fields: [productId], references: [id])
}

model MaintenancePart {
  id                   String             @id
  maintenanceServiceId String
  productId            String?
  partName             String
  quantity             Int                @default(1)
  unitPrice            Float              @default(0)
  totalPrice           Float              @default(0)
  isFromInventory      Boolean            @default(true)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime
  MaintenanceService   MaintenanceService @relation(fields: [maintenanceServiceId], references: [id], onDelete: Cascade)
  Product              Product?           @relation(fields: [productId], references: [id])
}

model MaintenancePayment {
  id                   String             @id
  maintenanceServiceId String
  method               PaymentMethod
  amount               Float
  date                 DateTime           @default(now())
  notes                String?
  createdAt            DateTime           @default(now())
  updatedAt            DateTime
  MaintenanceService   MaintenanceService @relation(fields: [maintenanceServiceId], references: [id])
}

model MaintenanceService {
  id                       String                     @id
  serviceNumber            String                     @unique
  contactId                String
  deviceType               String
  brand                    String
  model                    String?
  serialNumber             String?
  problemDescription       String
  receivedDate             DateTime                   @default(now())
  estimatedCompletionDate  DateTime?
  completionDate           DateTime?
  deliveryDate             DateTime?
  status                   MaintenanceStatus          @default(RECEIVED)
  priority                 MaintenancePriority        @default(MEDIUM)
  initialDiagnosis         String?
  technicalNotes           String?
  estimatedCost            Float?
  finalCost                Float?
  isPaid                   Boolean                    @default(false)
  isWarranty               Boolean                    @default(false)
  warrantyDetails          String?
  estimatedHours           Float?
  actualHours              Float?
  customerSignature        Boolean                    @default(false)
  customerRating           Int?
  customerFeedback         String?
  userId                   String
  branchId                 String
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime
  MaintenancePart          MaintenancePart[]
  MaintenancePayment       MaintenancePayment[]
  Branch                   Branch                     @relation(fields: [branchId], references: [id])
  Contact                  Contact                    @relation(fields: [contactId], references: [id])
  User                     User                       @relation(fields: [userId], references: [id])
  MaintenanceStatusHistory MaintenanceStatusHistory[]
}

model MaintenanceStatusHistory {
  id                   String             @id
  maintenanceServiceId String
  status               MaintenanceStatus
  notes                String?
  userId               String
  createdAt            DateTime           @default(now())
  MaintenanceService   MaintenanceService @relation(fields: [maintenanceServiceId], references: [id], onDelete: Cascade)
  User                 User               @relation(fields: [userId], references: [id])
}

model PaymentMethodSettings {
  id              String            @id @default(uuid())
  code            String            @unique
  name            String
  isActive        Boolean           @default(true)
  accountId       String?
  journalId       String?
  sequence        Int               @default(0)
  iconName        String? // اسم الأيقونة (مثل cash, credit-card, bank)
  iconUrl         String? // رابط الأيقونة المخصصة المحملة
  color           String? // لون طريقة الدفع (مثل #3895e7)
  branchIds       String[] // قائمة بمعرفات الفروع المرتبطة بطريقة الدفع
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  paymentVouchers PaymentVoucher[]
  receiptVouchers ReceiptVoucher[]

  account Account? @relation(fields: [accountId], references: [id])
  journal Journal? @relation(fields: [journalId], references: [id])

  @@index([sequence])
}

model Account {
  id                    String                  @id @default(uuid())
  code                  String                  @unique
  name                  String
  type                  AccountType
  parentId              String?
  balance               Float                   @default(0)
  isActive              Boolean                 @default(true)
  branchId              String?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  branch                Branch?                 @relation(fields: [branchId], references: [id])
  parent                Account?                @relation("AccountHierarchy", fields: [parentId], references: [id])
  children              Account[]               @relation("AccountHierarchy")
  creditEntries         JournalEntry[]          @relation("CreditAccount")
  debitEntries          JournalEntry[]          @relation("DebitAccount")
  paymentMethodSettings PaymentMethodSettings[]

  @@index([code])
  @@index([type])
  @@index([parentId])
  @@index([isActive])
}

model Journal {
  id                    String                  @id @default(uuid())
  code                  String                  @unique
  name                  String
  type                  JournalType
  branchId              String?
  isActive              Boolean                 @default(true)
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  branch                Branch?                 @relation(fields: [branchId], references: [id])
  entries               JournalEntry[]
  paymentMethodSettings PaymentMethodSettings[]

  @@index([code])
  @@index([type])
  @@index([isActive])
}

model JournalEntry {
  id              String        @id @default(uuid())
  journalId       String
  entryNumber     String
  date            DateTime      @default(now())
  description     String
  debitAccountId  String
  creditAccountId String
  amount          Float
  reference       String?
  referenceType   String?
  contactId       String?
  isPosted        Boolean       @default(false)
  fiscalPeriodId  String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  creditAccount   Account       @relation("CreditAccount", fields: [creditAccountId], references: [id])
  debitAccount    Account       @relation("DebitAccount", fields: [debitAccountId], references: [id])
  fiscalPeriod    FiscalPeriod? @relation(fields: [fiscalPeriodId], references: [id])
  journal         Journal       @relation(fields: [journalId], references: [id])

  @@unique([entryNumber, journalId])
  @@index([journalId])
  @@index([date])
  @@index([debitAccountId])
  @@index([creditAccountId])
  @@index([isPosted])
}

model FiscalYear {
  id        String         @id @default(uuid())
  name      String
  startDate DateTime
  endDate   DateTime
  isClosed  Boolean        @default(false)
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt
  periods   FiscalPeriod[]

  @@index([isClosed])
}

model FiscalPeriod {
  id             String         @id @default(uuid())
  fiscalYearId   String
  name           String
  startDate      DateTime
  endDate        DateTime
  isClosed       Boolean        @default(false)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  fiscalYear     FiscalYear     @relation(fields: [fiscalYearId], references: [id])
  journalEntries JournalEntry[]

  @@index([fiscalYearId])
  @@index([isClosed])
  @@index([startDate, endDate])
}

enum Role {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum PaymentMethod {
  CASH
  VODAFONE_CASH
  BANK_TRANSFER
  CREDIT_CARD
  CUSTOMER_ACCOUNT
}

enum PaymentStatus {
  PAID
  UNPAID
  PARTIALLY_PAID
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum DiscountScope {
  ITEM
  INVOICE
  CUSTOMER
}

model Discount {
  id                String             @id @default(uuid())
  name              String
  description       String?
  type              DiscountType
  value             Float
  scope             DiscountScope
  minAmount         Float?
  maxAmount         Float?
  startDate         DateTime?
  endDate           DateTime?
  isActive          Boolean            @default(true)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  categoryId        String?
  productId         String?
  category          Category?          @relation(fields: [categoryId], references: [id])
  product           Product?           @relation(fields: [productId], references: [id])
  customerDiscounts CustomerDiscount[]
  saleDiscounts     SaleDiscount[]
  saleItemDiscounts SaleItemDiscount[]
  contactsDefault   Contact[]
  campaignDiscounts CampaignDiscount[]

  @@index([isActive])
  @@index([startDate, endDate])
  @@index([scope])
}

model DiscountCampaign {
  id                String             @id @default(uuid())
  name              String
  description       String?
  startDate         DateTime
  endDate           DateTime
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  campaignDiscounts CampaignDiscount[]

  @@index([startDate, endDate])
}

model CampaignDiscount {
  id         String           @id @default(uuid())
  campaignId String
  discountId String
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  campaign   DiscountCampaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  discount   Discount         @relation(fields: [discountId], references: [id], onDelete: Cascade)

  @@unique([campaignId, discountId])
  @@index([campaignId])
  @@index([discountId])
}

model CustomerDiscount {
  id         String   @id @default(uuid())
  discountId String
  contactId  String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  discount   Discount @relation(fields: [discountId], references: [id], onDelete: Cascade)
  contact    Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@unique([discountId, contactId])
  @@index([contactId])
}

model SaleDiscount {
  id         String       @id @default(uuid())
  saleId     String
  discountId String
  type       DiscountType
  value      Float
  amount     Float
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  sale       Sale         @relation(fields: [saleId], references: [id], onDelete: Cascade)
  discount   Discount     @relation(fields: [discountId], references: [id], onDelete: Cascade)

  @@index([saleId])
}

model SaleItemDiscount {
  id         String       @id @default(uuid())
  saleItemId String
  discountId String
  type       DiscountType
  value      Float
  amount     Float
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  saleItem   SaleItem     @relation(fields: [saleItemId], references: [id], onDelete: Cascade)
  discount   Discount     @relation(fields: [discountId], references: [id], onDelete: Cascade)

  @@index([saleItemId])
}

enum SaleStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum PurchaseStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

enum CreditNoteStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum MaintenancePriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum MaintenanceStatus {
  RECEIVED
  IN_PROGRESS
  WAITING_FOR_PARTS
  COMPLETED
  DELIVERED
  CANCELLED
  REJECTED_BY_CUSTOMER
}

enum ReferenceType {
  SALE
  PURCHASE
  RECEIPT
  PAYMENT
  CREDIT_NOTE
  OPENING_BALANCE
  PAYMENT_VOUCHER
  RECEIPT_VOUCHER
  OTHER
}

enum TransactionType {
  DEBIT
  CREDIT
}

enum JournalType {
  CASH
  VODAFONE_CASH
  BANK_TRANSFER
  VISA
  CUSTOMER_ACCOUNT
  GENERAL
  PAYMENT_VOUCHER
  RECEIPT_VOUCHER
}

model PaymentVoucher {
  id             String               @id @default(uuid())
  voucherNumber  String               @unique
  date           DateTime             @default(now())
  amount         Float
  description    String
  paymentMethodId String
  contactId      String?
  userId         String
  branchId       String
  status         String               @default("COMPLETED")
  reference      String?
  referenceType  String?
  journalEntryId String?
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt

  paymentMethod  PaymentMethodSettings @relation(fields: [paymentMethodId], references: [id])
  contact        Contact?             @relation(fields: [contactId], references: [id])
  user           User                 @relation(fields: [userId], references: [id])
  branch         Branch               @relation(fields: [branchId], references: [id])

  @@index([date])
  @@index([paymentMethodId])
  @@index([contactId])
  @@index([branchId])
  @@index([status])
}

model ReceiptVoucher {
  id             String               @id @default(uuid())
  voucherNumber  String               @unique
  date           DateTime             @default(now())
  amount         Float
  description    String
  paymentMethodId String
  contactId      String?
  userId         String
  branchId       String
  status         String               @default("COMPLETED")
  reference      String?
  referenceType  String?
  journalEntryId String?
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt

  paymentMethod  PaymentMethodSettings @relation(fields: [paymentMethodId], references: [id])
  contact        Contact?             @relation(fields: [contactId], references: [id])
  user           User                 @relation(fields: [userId], references: [id])
  branch         Branch               @relation(fields: [branchId], references: [id])

  @@index([date])
  @@index([paymentMethodId])
  @@index([contactId])
  @@index([branchId])
  @@index([status])
}


