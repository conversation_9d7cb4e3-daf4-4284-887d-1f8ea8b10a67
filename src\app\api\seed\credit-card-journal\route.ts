import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// POST /api/seed/credit-card-journal - Create credit card journal
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to add journals
    const hasAddPermission = await hasPermission("add_accounts");
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to add journals" },
        { status: 403 }
      );
    }
    
    // Check if credit card journal already exists
    const existingJournal = await db.journal.findFirst({
      where: {
        paymentMethod: "CREDIT_CARD",
      },
    });
    
    if (existingJournal) {
      return NextResponse.json(
        { message: "Credit Card Journal already exists", journal: existingJournal },
        { status: 200 }
      );
    }
    
    // Create credit card journal
    const journal = await db.journal.create({
      data: {
        name: "Credit Card Journal",
        description: "Journal for credit card transactions",
        paymentMethod: "CREDIT_CARD",
        isActive: true,
      },
    });
    
    return NextResponse.json(
      { message: "Credit Card Journal created successfully", journal },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating credit card journal:", error);
    return NextResponse.json(
      { error: "Failed to create credit card journal" },
      { status: 500 }
    );
  }
}
