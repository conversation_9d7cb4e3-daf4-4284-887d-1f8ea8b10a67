"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { useState, useEffect } from "react";
import { formatCurrency } from "@/lib/utils";

interface StatementTotals {
  debit: number;
  credit: number;
  balance: number;
}

interface Contact {
  id: string;
  name: string;
  phone: string;
  balance: number;
  isCustomer: boolean;
  isSupplier: boolean;
  statement?: {
    totals: StatementTotals;
  };
}

interface ContactsBalanceProps {
  contacts: Contact[];
}

export default function ContactsBalance({ contacts }: ContactsBalanceProps) {
  const [filter, setFilter] = useState<"all" | "customers" | "suppliers">("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "balance">("balance");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [contactsWithStatement, setContactsWithStatement] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Debug: Log contacts to see what data we're receiving
  useEffect(() => {
    console.log("ContactsBalance - Received contacts:", contacts);
  }, [contacts]);

  // Fetch statement data for contacts
  useEffect(() => {
    const fetchStatementData = async () => {
      if (!contacts || contacts.length === 0) return;

      setIsLoading(true);
      const updatedContacts = [...contacts];

      // Limit to first 5 contacts to avoid too many API calls
      const contactsToFetch = updatedContacts.slice(0, 5);

      // Fetch statement data for each contact in parallel
      const promises = contactsToFetch.map(async (contact, index) => {
        try {
          const response = await fetch(`/api/contacts/${contact.id}/statement`);
          if (response.ok) {
            const data = await response.json();
            return {
              index,
              statement: {
                totals: data.totals
              }
            };
          }
        } catch (error) {
          console.error(`Error fetching statement for contact ${contact.id}:`, error);
        }
        return { index, statement: null };
      });

      // Wait for all promises to resolve
      const results = await Promise.all(promises);

      // Update contacts with statement data
      results.forEach(result => {
        if (result && result.statement) {
          updatedContacts[result.index] = {
            ...updatedContacts[result.index],
            statement: result.statement
          };
        }
      });

      setContactsWithStatement(updatedContacts);
      setIsLoading(false);
    };

    fetchStatementData();
  }, [contacts]);

  // Filter and sort contacts
  const filteredContacts = (contactsWithStatement.length > 0 ? contactsWithStatement : contacts)
    .filter((contact) => {
      // Only show contacts with non-zero balance
      const hasNonZeroBalance = contact.statement
        ? (contact.statement.totals.debit > 0 || contact.statement.totals.credit > 0)
        : (contact.balance !== 0);

      if (!hasNonZeroBalance) return false;

      // Filter by type
      if (filter === "customers" && !contact.isCustomer) return false;
      if (filter === "suppliers" && !contact.isSupplier) return false;

      // Filter by search term
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        return (
          contact.name.toLowerCase().includes(term) ||
          contact.phone.toLowerCase().includes(term)
        );
      }

      return true;
    })
    .sort((a, b) => {
      // Sort by selected field
      if (sortBy === "name") {
        return sortOrder === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else {
        // Sort by balance
        // Use statement balance if available, otherwise use contact balance
        const aBalance = a.statement?.totals.balance ?? a.balance;
        const bBalance = b.statement?.totals.balance ?? b.balance;
        return sortOrder === "asc"
          ? aBalance - bBalance
          : bBalance - aBalance;
      }
    });

  // Toggle sort order
  const toggleSort = (field: "name" | "balance") => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
      className="bg-white shadow-lg rounded-lg overflow-hidden"
    >
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-blue-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
          Contacts with Balances
        </h3>
        {filteredContacts.length > 0 && (
          <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
            {filteredContacts.length}
          </span>
        )}
      </div>

      {/* Note about statement data */}
      <div className="px-6 py-2 text-xs text-blue-700 bg-blue-50 border-b border-blue-100">
        <span className="font-medium">Note:</span> Showing only contacts with non-zero balances. Debit and credit values are calculated from contact statements.
      </div>

      {/* Filters */}
      <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
        <div className="flex flex-wrap gap-2 justify-between items-center">
          <div className="flex space-x-2">
            <button
              onClick={() => setFilter("all")}
              className={`px-3 py-1 text-xs rounded-full ${
                filter === "all"
                  ? "bg-blue-100 text-blue-800"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              All
            </button>
            <button
              onClick={() => setFilter("customers")}
              className={`px-3 py-1 text-xs rounded-full ${
                filter === "customers"
                  ? "bg-blue-100 text-blue-800"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              Customers
            </button>
            <button
              onClick={() => setFilter("suppliers")}
              className={`px-3 py-1 text-xs rounded-full ${
                filter === "suppliers"
                  ? "bg-blue-100 text-blue-800"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              Suppliers
            </button>
          </div>
          <div className="relative">
            <input
              type="text"
              placeholder="Search contacts..."
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                onClick={() => setSearchTerm("")}
              >
                ✕
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Table header */}
      <div className="px-6 py-2 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-12 gap-2">
          <button
            className="col-span-5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider flex items-center"
            onClick={() => toggleSort("name")}
          >
            Contact
            {sortBy === "name" && (
              <span className="ml-1">
                {sortOrder === "asc" ? "↑" : "↓"}
              </span>
            )}
          </button>
          <div className="col-span-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Type
          </div>
          <button
            className="col-span-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider flex items-center justify-end"
            onClick={() => toggleSort("balance")}
          >
            Debit
            {sortBy === "balance" && sortOrder === "desc" && (
              <span className="ml-1">↓</span>
            )}
          </button>
          <div className="col-span-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
            Credit
          </div>
        </div>
      </div>

      {isLoading && contactsWithStatement.length === 0 && (
        <div className="px-6 py-4 text-center">
          <div className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-500 mr-2"></div>
          <span className="text-sm text-gray-500">Loading statement data...</span>
        </div>
      )}

      {filteredContacts.length > 0 ? (
        <div className="max-h-80 overflow-y-auto">
          {filteredContacts.map((contact, index) => (
            <motion.div
              key={contact.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, delay: 0.05 * index }}
              className="px-6 py-3 border-b border-gray-200 hover:bg-gray-50"
            >
              <div className="block">
                <div className="grid grid-cols-12 gap-2 items-center">
                  <div className="col-span-5">
                    <Link href={`/dashboard/contacts/${contact.id}`}>
                      <p className="text-sm font-medium text-gray-900 truncate hover:text-blue-600">
                        {contact.name}
                      </p>
                    </Link>
                    <p className="text-xs text-gray-500 mt-0.5 truncate">
                      {contact.phone}
                    </p>
                  </div>
                  <div className="col-span-2">
                    <div className="flex flex-wrap gap-1">
                      {contact.isCustomer && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          Customer
                        </span>
                      )}
                      {contact.isSupplier && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                          Supplier
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="col-span-2 text-right">
                    {/* Display debit from statement if available */}
                    {contact.statement ? (
                      <span className={`text-sm font-medium ${contact.statement.totals.debit > 0 ? "text-red-600" : "text-gray-500"}`}>
                        {contact.statement.totals.debit > 0 ? formatCurrency(contact.statement.totals.debit) : "-"}
                      </span>
                    ) : isLoading && index < 5 ? (
                      // Show loading indicator for contacts being loaded
                      <span className="text-sm text-gray-400 flex items-center justify-end">
                        <div className="inline-block animate-spin rounded-full h-3 w-3 border-t-1 border-b-1 border-gray-400 mr-1"></div>
                        Loading...
                      </span>
                    ) : (
                      // Fallback to old calculation if statement not available
                      <span className={`text-sm font-medium ${
                        (contact.isCustomer && contact.balance > 0) ||
                        (contact.isSupplier && !contact.isCustomer && contact.balance < 0)
                          ? "text-red-600"
                          : "text-gray-500"
                      }`}>
                        {(contact.isCustomer && contact.balance > 0) ?
                          formatCurrency(contact.balance) :
                          (contact.isSupplier && !contact.isCustomer && contact.balance < 0) ?
                          formatCurrency(Math.abs(contact.balance)) :
                          "-"}
                      </span>
                    )}
                  </div>
                  <div className="col-span-3 text-right">
                    {/* Display credit from statement if available */}
                    {contact.statement ? (
                      <span className={`text-sm font-medium ${contact.statement.totals.credit > 0 ? "text-green-600" : "text-gray-500"}`}>
                        {contact.statement.totals.credit > 0 ? formatCurrency(contact.statement.totals.credit) : "-"}
                      </span>
                    ) : isLoading && index < 5 ? (
                      // Show loading indicator for contacts being loaded
                      <span className="text-sm text-gray-400 flex items-center justify-end">
                        <div className="inline-block animate-spin rounded-full h-3 w-3 border-t-1 border-b-1 border-gray-400 mr-1"></div>
                        Loading...
                      </span>
                    ) : (
                      // Fallback to old calculation if statement not available
                      <span className={`text-sm font-medium ${
                        (contact.isCustomer && contact.balance < 0) ||
                        (contact.isSupplier && !contact.isCustomer && contact.balance > 0)
                          ? "text-green-600"
                          : "text-gray-500"
                      }`}>
                        {(contact.isCustomer && contact.balance < 0) ?
                          formatCurrency(Math.abs(contact.balance)) :
                          (contact.isSupplier && !contact.isCustomer && contact.balance > 0) ?
                          formatCurrency(contact.balance) :
                          "-"}
                      </span>
                    )}
                  </div>
                </div>
                <div className="mt-2 flex justify-end space-x-2 text-xs">
                  <Link
                    href={`/dashboard/contacts/${contact.id}`}
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    View Details
                  </Link>
                  <span className="text-gray-400">|</span>
                  <Link
                    href={`/dashboard/contacts/${contact.id}/statement`}
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    View Statement
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="px-6 py-8 text-center">
          <p className="text-sm text-gray-500">No contacts found</p>
        </div>
      )}

      <div className="bg-gray-50 px-6 py-3">
        <div className="text-sm">
          <Link
            href="/dashboard/contacts"
            className="font-medium text-indigo-600 hover:text-indigo-500 hover:underline flex items-center justify-between"
          >
            <span>View all contacts</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
