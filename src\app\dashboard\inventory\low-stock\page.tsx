"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, RefreshCw, Printer, FileDown } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter } from "next/navigation";
import { formatCurrency } from "@/lib/utils";

export default function LowStockPage() {
  const { toast } = useToast();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [lowStockItems, setLowStockItems] = useState([]);
  const [groupedItems, setGroupedItems] = useState([]);
  const [warehouses, setWarehouses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [threshold, setThreshold] = useState(5);
  const [activeTab, setActiveTab] = useState("all");
  
  // Fetch low stock items
  const fetchLowStockItems = async () => {
    setLoading(true);
    try {
      let url = `/api/inventory/low-stock?threshold=${threshold}`;
      
      if (selectedWarehouse !== "all") {
        url += `&warehouseId=${selectedWarehouse}`;
      }
      
      if (selectedCategory !== "all") {
        url += `&categoryId=${selectedCategory}`;
      }
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error("Failed to fetch low stock items");
      }
      
      const data = await response.json();
      setLowStockItems(data.lowStockItems);
      setGroupedItems(data.groupedByWarehouse);
    } catch (error) {
      console.error("Error fetching low stock items:", error);
      toast({
        title: "Error",
        description: "Failed to fetch low stock items",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch warehouses and categories
  const fetchFilters = async () => {
    try {
      // Fetch warehouses
      const warehousesResponse = await fetch("/api/warehouses");
      if (warehousesResponse.ok) {
        const warehousesData = await warehousesResponse.json();
        setWarehouses(warehousesData.warehouses);
      }
      
      // Fetch categories
      const categoriesResponse = await fetch("/api/categories");
      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories);
      }
    } catch (error) {
      console.error("Error fetching filters:", error);
    }
  };
  
  // Initial data load
  useEffect(() => {
    fetchFilters();
    fetchLowStockItems();
  }, []);
  
  // Refresh data when filters change
  useEffect(() => {
    fetchLowStockItems();
  }, [selectedWarehouse, selectedCategory, threshold]);
  
  // Handle export to CSV
  const exportToCSV = () => {
    if (lowStockItems.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no low stock items to export",
        variant: "destructive",
      });
      return;
    }
    
    // Create CSV content
    const headers = ["Product", "Warehouse", "Branch", "Quantity", "Category", "Cost Price", "Base Price"];
    const csvContent = [
      headers.join(","),
      ...lowStockItems.map(item => [
        `"${item.productName}"`,
        `"${item.warehouseName}"`,
        `"${item.branchName}"`,
        item.quantity,
        `"${item.categoryName}"`,
        item.costPrice,
        item.basePrice
      ].join(","))
    ].join("\n");
    
    // Create download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `low-stock-report-${new Date().toISOString().split("T")[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Handle print
  const handlePrint = () => {
    window.print();
  };
  
  return (
    <div className="container mx-auto p-4 space-y-6 print:p-0">
      <div className="flex justify-between items-center print:hidden">
        <h1 className="text-2xl font-bold">Low Stock Items</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchLowStockItems}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" onClick={exportToCSV}>
            <FileDown className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 print:hidden">
        <div>
          <Label htmlFor="warehouse">Warehouse</Label>
          <Select value={selectedWarehouse} onValueChange={setSelectedWarehouse}>
            <SelectTrigger id="warehouse">
              <SelectValue placeholder="Select Warehouse" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Warehouses</SelectItem>
              {warehouses.map(warehouse => (
                <SelectItem key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="category">Category</Label>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger id="category">
              <SelectValue placeholder="Select Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="threshold">Threshold</Label>
          <div className="flex items-center gap-2">
            <Input
              id="threshold"
              type="number"
              min="1"
              value={threshold}
              onChange={e => setThreshold(parseInt(e.target.value) || 5)}
            />
            <span>items</span>
          </div>
        </div>
      </div>
      
      {lowStockItems.length === 0 && !loading ? (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No low stock items found</AlertTitle>
          <AlertDescription>
            All items are above the threshold of {threshold} units.
          </AlertDescription>
        </Alert>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="print:hidden">
          <TabsList>
            <TabsTrigger value="all">All Items</TabsTrigger>
            <TabsTrigger value="byWarehouse">By Warehouse</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>All Low Stock Items</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3, 4, 5].map(i => (
                      <Skeleton key={i} className="h-10 w-full" />
                    ))}
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Warehouse</TableHead>
                        <TableHead>Branch</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {lowStockItems.map(item => (
                        <TableRow key={item.id}>
                          <TableCell>{item.productName}</TableCell>
                          <TableCell>{item.warehouseName}</TableCell>
                          <TableCell>{item.branchName}</TableCell>
                          <TableCell>
                            <Badge variant={item.quantity === 0 ? "destructive" : "outline"}>
                              {item.quantity}
                            </Badge>
                          </TableCell>
                          <TableCell>{item.categoryName}</TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/dashboard/products/${item.productId}`)}
                            >
                              View Product
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="byWarehouse" className="mt-4">
            <div className="space-y-6">
              {loading ? (
                <div className="space-y-4">
                  {[1, 2].map(i => (
                    <Skeleton key={i} className="h-64 w-full" />
                  ))}
                </div>
              ) : (
                groupedItems.map(group => (
                  <Card key={group.warehouseId}>
                    <CardHeader>
                      <CardTitle>{group.warehouseName} - {group.branchName}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Product</TableHead>
                            <TableHead>Quantity</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead className="text-right">Cost Price</TableHead>
                            <TableHead className="text-right">Base Price</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {group.items.map(item => (
                            <TableRow key={item.id}>
                              <TableCell>{item.productName}</TableCell>
                              <TableCell>
                                <Badge variant={item.quantity === 0 ? "destructive" : "outline"}>
                                  {item.quantity}
                                </Badge>
                              </TableCell>
                              <TableCell>{item.categoryName}</TableCell>
                              <TableCell className="text-right">{formatCurrency(item.costPrice)}</TableCell>
                              <TableCell className="text-right">{formatCurrency(item.basePrice)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      )}
      
      {/* Print view */}
      <div className="hidden print:block">
        <h1 className="text-2xl font-bold mb-4">Low Stock Report</h1>
        <p className="mb-4">Generated on: {new Date().toLocaleDateString()}</p>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>Warehouse</TableHead>
              <TableHead>Branch</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Category</TableHead>
              <TableHead className="text-right">Cost Price</TableHead>
              <TableHead className="text-right">Base Price</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lowStockItems.map(item => (
              <TableRow key={item.id}>
                <TableCell>{item.productName}</TableCell>
                <TableCell>{item.warehouseName}</TableCell>
                <TableCell>{item.branchName}</TableCell>
                <TableCell>{item.quantity}</TableCell>
                <TableCell>{item.categoryName}</TableCell>
                <TableCell className="text-right">{formatCurrency(item.costPrice)}</TableCell>
                <TableCell className="text-right">{formatCurrency(item.basePrice)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
