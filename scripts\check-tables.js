const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTables() {
  try {
    const accounts = await prisma.account.findMany();
    console.log(`Cuentas: ${accounts.length}`);

    const journals = await prisma.journal.findMany();
    console.log(`Diarios: ${journals.length}`);

    const fiscalYears = await prisma.fiscalYear.findMany();
    console.log(`Años fiscales: ${fiscalYears.length}`);

    console.log('Verificación completada.');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTables();
