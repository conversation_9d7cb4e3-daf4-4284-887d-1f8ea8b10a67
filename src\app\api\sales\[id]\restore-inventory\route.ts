import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { cache } from "@/lib/cache";

// POST /api/sales/[id]/restore-inventory - Restore inventory for a sale
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the sale ID from the URL
    const saleId = params.id;

    // Get the request body to check if we have specific components to restore
    let requestData = {};
    try {
      requestData = await req.json();
      console.log("Request data:", requestData);
    } catch (e) {
      console.log("No request body or invalid JSON");
    }

    // Check if sale exists
    const existingSale = await db.sale.findUnique({
      where: {
        id: saleId,
      },
      include: {
        items: {
          include: {
            components: true
          }
        },
      },
    });

    if (!existingSale) {
      return NextResponse.json(
        { error: "Sale not found" },
        { status: 404 }
      );
    }

    // Start a transaction to restore inventory
    await db.$transaction(async (tx) => {
      console.log(`Restoring inventory for sale ${saleId}`);

      // Check if we have specific components to restore
      const specificComponents = (requestData as any).components || [];

      if (specificComponents.length > 0) {
        console.log(`Restoring specific components: ${specificComponents.length}`);

        // Process each specific component
        for (const component of specificComponents) {
          console.log(`Processing specific component:`, component);

          if (!component.id || !component.warehouseId) {
            console.warn(`Missing required fields for component:`, component);
            continue;
          }

          // Find the inventory record for this component
          const componentInventory = await tx.inventory.findFirst({
            where: {
              productId: component.id,
              warehouseId: component.warehouseId,
            },
          });

          if (componentInventory) {
            // Calculate the quantity to return
            const quantityToReturn = component.totalCount || (component.count || 1);

            console.log(`Returning ${quantityToReturn} of component ${component.id} (${component.name}) to inventory`);

            // Return component to inventory
            await tx.inventory.update({
              where: {
                id: componentInventory.id,
              },
              data: {
                quantity: {
                  increment: quantityToReturn
                },
              },
            });

            // Create inventory movement record
            try {
              if (tx.inventoryMovement) {
                try {
                  // Create movement data with basic fields
                  const movementData: any = {
                    productId: component.id,
                    warehouseId: component.warehouseId,
                    quantity: quantityToReturn, // Positive for incoming
                    type: "COMPONENT_RETURN",
                    date: new Date(),
                    documentNumber: existingSale.invoiceNumber,
                    reference: `Sale ${existingSale.invoiceNumber} edit canceled - Component returned to inventory`,
                    branchId: existingSale.branchId,
                  };

                  // Try to add contactId if the field exists in the schema
                  try {
                    if (existingSale.contactId) {
                      movementData.contactId = existingSale.contactId;
                    }
                  } catch (contactError) {
                    console.log("Note: contactId field not available in InventoryMovement model, skipping");
                  }

                  await tx.inventoryMovement.create({
                    data: movementData
                  });
                } catch (movementError) {
                  console.error("Error creating inventory movement:", movementError);
                }
              }
            } catch (err) {
              // If inventoryMovement table doesn't exist, just log and continue
              console.log(`Note: Could not create inventory movement record: ${err instanceof Error ? err.message : String(err)}`);
            }
          } else {
            console.warn(`Component inventory not found for component ${component.id} in warehouse ${component.warehouseId}`);
          }
        }
      } else {
        // Process each item in the sale
        for (const item of existingSale.items) {
          console.log(`Processing item ${item.id} (${item.productId})`);

          // Check if the item has components
          if (item.components && item.components.length > 0) {
            console.log(`Item has ${item.components.length} components`);

            // Process each component
            for (const component of item.components) {
              console.log(`Processing component ${component.id} (${component.componentId})`);

              // Find the inventory record for this component
              const componentInventory = await tx.inventory.findFirst({
                where: {
                  productId: component.componentId,
                  warehouseId: item.warehouseId,
                },
              });

              if (componentInventory) {
                // Calculate the quantity to return
                const quantityToReturn = component.totalQuantity || (component.quantity * item.quantity);

                console.log(`Returning ${quantityToReturn} of component ${component.componentId} to inventory`);

                // Return component to inventory
                await tx.inventory.update({
                  where: {
                    id: componentInventory.id,
                  },
                  data: {
                    quantity: {
                      increment: quantityToReturn
                    },
                  },
                });

                // Create inventory movement record
                try {
                  if (tx.inventoryMovement) {
                    try {
                      // Create movement data with basic fields
                      const movementData: any = {
                        productId: component.componentId,
                        warehouseId: item.warehouseId,
                        quantity: quantityToReturn, // Positive for incoming
                        type: "COMPONENT_RETURN",
                        date: new Date(),
                        documentNumber: existingSale.invoiceNumber,
                        reference: `Sale ${existingSale.invoiceNumber} edit canceled - Component returned to inventory`,
                        branchId: existingSale.branchId,
                      };

                      // Try to add contactId if the field exists in the schema
                      try {
                        if (existingSale.contactId) {
                          movementData.contactId = existingSale.contactId;
                        }
                      } catch (contactError) {
                        console.log("Note: contactId field not available in InventoryMovement model, skipping");
                      }

                      await tx.inventoryMovement.create({
                        data: movementData
                      });
                    } catch (movementError) {
                      console.error("Error creating inventory movement:", movementError);
                    }
                  }
                } catch (err) {
                  // If inventoryMovement table doesn't exist, just log and continue
                  console.log(`Note: Could not create inventory movement record: ${err instanceof Error ? err.message : String(err)}`);
                }
              } else {
                console.warn(`Component inventory not found for component ${component.componentId} in warehouse ${item.warehouseId}`);
              }
            }
          }
        }
      }
    });

    // Invalidate cache
    cache.delete(`sale_detail_${saleId}`);
    cache.deletePattern(/^sales_/);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error restoring inventory:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to restore inventory" },
      { status: 500 }
    );
  }
}
