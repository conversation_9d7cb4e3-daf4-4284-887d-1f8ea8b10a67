import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import { generateServiceNumber } from "@/lib/maintenance";

// GET /api/maintenance - Get all maintenance services
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view maintenance services
    const canViewMaintenance = await hasPermission("view_maintenance") || session.user.role === "ADMIN";

    if (!canViewMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to view maintenance services" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const status = url.searchParams.get("status");
    const contactId = url.searchParams.get("contactId");
    const search = url.searchParams.get("search");
    const overdue = url.searchParams.get("overdue") === "true";
    const limit = parseInt(url.searchParams.get("limit") || "100");
    const page = parseInt(url.searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {
      where: {},
      include: {
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            parts: true,
            statusHistory: true,
          },
        },
      },
      orderBy: {
        receivedDate: "desc",
      },
      take: limit,
      skip,
    };

    // Add filters
    if (status) {
      query.where.status = status;
    }

    if (contactId) {
      query.where.contactId = contactId;
    }

    if (search) {
      query.where.OR = [
        { serviceNumber: { contains: search } },
        { deviceType: { contains: search, mode: "insensitive" } },
        { brand: { contains: search, mode: "insensitive" } },
        { model: { contains: search, mode: "insensitive" } },
        { serialNumber: { contains: search, mode: "insensitive" } },
        { contact: { name: { contains: search, mode: "insensitive" } } },
        { contact: { phone: { contains: search } } },
      ];
    }

    // Filter for overdue services (more than 15 days and not delivered)
    if (overdue) {
      const fifteenDaysAgo = new Date();
      fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);
      
      query.where.AND = [
        { receivedDate: { lte: fifteenDaysAgo } },
        { status: { not: "DELIVERED" } },
        { status: { not: "CANCELLED" } },
      ];
    }

    // Get total count for pagination
    const totalCount = await db.maintenanceService.count({
      where: query.where,
    });

    // Execute query
    const maintenanceServices = await db.maintenanceService.findMany(query);

    return NextResponse.json({
      data: maintenanceServices,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching maintenance services:", error);
    return NextResponse.json(
      { error: "Failed to fetch maintenance services" },
      { status: 500 }
    );
  }
}

// POST /api/maintenance - Create a new maintenance service
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add maintenance services
    const canAddMaintenance = await hasPermission("add_maintenance") || session.user.role === "ADMIN";

    if (!canAddMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to add maintenance services" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.contactId || !data.deviceType || !data.brand || !data.problemDescription) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get user's branch
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { branchId: true },
    });

    if (!user?.branchId) {
      return NextResponse.json(
        { error: "User is not assigned to a branch" },
        { status: 400 }
      );
    }

    // Generate service number
    const serviceNumber = await generateServiceNumber(user.branchId);

    // Create the maintenance service
    const maintenanceService = await db.maintenanceService.create({
      data: {
        serviceNumber,
        contactId: data.contactId,
        deviceType: data.deviceType,
        brand: data.brand,
        model: data.model || null,
        serialNumber: data.serialNumber || null,
        problemDescription: data.problemDescription,
        receivedDate: data.receivedDate ? new Date(data.receivedDate) : new Date(),
        estimatedCompletionDate: data.estimatedCompletionDate ? new Date(data.estimatedCompletionDate) : null,
        priority: data.priority || "MEDIUM",
        initialDiagnosis: data.initialDiagnosis || null,
        estimatedCost: data.estimatedCost || null,
        isWarranty: data.isWarranty || false,
        warrantyDetails: data.warrantyDetails || null,
        userId: session.user.id,
        branchId: user.branchId,
      },
    });

    // Create initial status history entry
    await db.maintenanceStatusHistory.create({
      data: {
        maintenanceServiceId: maintenanceService.id,
        status: "RECEIVED",
        notes: "Device received for maintenance",
        userId: session.user.id,
      },
    });

    return NextResponse.json(maintenanceService);
  } catch (error) {
    console.error("Error creating maintenance service:", error);
    return NextResponse.json(
      { error: "Failed to create maintenance service" },
      { status: 500 }
    );
  }
}
