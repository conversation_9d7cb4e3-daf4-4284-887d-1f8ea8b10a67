import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// POST /api/dashboard/reset - Reset dashboard state
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only allow admins to reset dashboard
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only administrators can reset dashboard" },
        { status: 403 }
      );
    }

    // Check if view_dashboard permission exists
    const viewDashboardPermission = await db.permission.findFirst({
      where: {
        name: "view_dashboard",
      },
    });

    // Create view_dashboard permission if it doesn't exist
    if (!viewDashboardPermission) {
      const newPermission = await db.permission.create({
        data: {
          name: "view_dashboard",
          description: "View dashboard",
        },
      });

      // Add this permission to all users
      const users = await db.user.findMany();
      
      for (const user of users) {
        await db.user.update({
          where: {
            id: user.id,
          },
          data: {
            permissions: {
              connect: {
                id: newPermission.id,
              },
            },
          },
        });
      }
    }

    // Clear any dashboard-related cache if needed
    // This is a placeholder for any future dashboard cache clearing logic

    return NextResponse.json({
      success: true,
      message: "Dashboard reset successfully",
    });
  } catch (error) {
    console.error("Error resetting dashboard:", error);
    return NextResponse.json(
      { error: "Failed to reset dashboard" },
      { status: 500 }
    );
  }
}
