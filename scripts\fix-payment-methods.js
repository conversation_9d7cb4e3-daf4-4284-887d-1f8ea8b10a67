// Script to fix payment methods and their account associations
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting payment methods fix script...');

    // 1. Ensure all required accounts exist
    console.log('Ensuring required accounts exist...');
    const requiredAccounts = [
      { code: "1000", name: "Cash", type: "ASSET" },
      { code: "1010", name: "Vodafone Cash", type: "ASSET" },
      { code: "1020", name: "Bank Account", type: "ASSET" },
      { code: "1030", name: "Credit Card", type: "ASSET" },
      { code: "1100", name: "Accounts Receivable", type: "ASSET" },
      { code: "1200", name: "Inventory", type: "ASSET" },
      { code: "2000", name: "Accounts Payable", type: "LIABILITY" },
      { code: "3000", name: "Owner's Equity", type: "EQUITY" },
      { code: "4000", name: "Sales Revenue", type: "REVENUE" },
      { code: "4100", name: "Sales Returns", type: "REVENUE" },
      { code: "5000", name: "Cost of Goods Sold", type: "EXPENSE" },
      { code: "5100", name: "Inventory Adjustment", type: "EXPENSE" },
      { code: "6000", name: "Operating Expenses", type: "EXPENSE" },
    ];

    const createdAccounts = {};

    // Check if each account exists, create if it doesn't
    for (const account of requiredAccounts) {
      let existingAccount = await prisma.account.findFirst({
        where: {
          OR: [
            { code: account.code },
            { name: account.name, type: account.type },
          ],
        },
      });

      if (!existingAccount) {
        console.log(`Creating account: ${account.name} (${account.code})`);
        existingAccount = await prisma.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            balance: 0,
            isActive: true,
          },
        });
      } else {
        console.log(`Account already exists: ${existingAccount.name} (${existingAccount.code})`);
      }

      createdAccounts[account.name] = existingAccount;
    }

    // 2. Ensure all required journals exist
    console.log('Ensuring required journals exist...');
    const requiredJournals = [
      { code: "CASH", name: "Cash Journal", type: "CASH" },
      { code: "VFCASH", name: "Vodafone Cash Journal", type: "VODAFONE_CASH" },
      { code: "BANK", name: "Bank Journal", type: "BANK_TRANSFER" },
      { code: "VISA", name: "Credit Card Journal", type: "VISA" },
      { code: "GENERAL", name: "General Journal", type: "GENERAL" },
    ];

    const createdJournals = {};

    // Check if each journal exists, create if it doesn't
    for (const journal of requiredJournals) {
      let existingJournal = await prisma.journal.findFirst({
        where: {
          OR: [
            { code: journal.code },
            { name: journal.name, type: journal.type },
          ],
        },
      });

      if (!existingJournal) {
        console.log(`Creating journal: ${journal.name} (${journal.code})`);
        existingJournal = await prisma.journal.create({
          data: {
            code: journal.code,
            name: journal.name,
            type: journal.type,
            isActive: true,
          },
        });
      } else {
        console.log(`Journal already exists: ${existingJournal.name} (${existingJournal.code})`);
      }

      createdJournals[journal.name] = existingJournal;
    }

    // 3. Update payment method settings
    console.log('Updating payment method settings...');
    const paymentMethodsMap = {
      'CASH': {
        account: createdAccounts['Cash'],
        journal: createdJournals['Cash Journal'],
        name: 'Cash',
        iconName: 'cash',
        color: '#3895e7',
      },
      'VODAFONE_CASH': {
        account: createdAccounts['Vodafone Cash'],
        journal: createdJournals['Vodafone Cash Journal'],
        name: 'Vodafone Cash',
        iconName: 'mobile',
        color: '#e60000',
      },
      'BANK_TRANSFER': {
        account: createdAccounts['Bank Account'],
        journal: createdJournals['Bank Journal'],
        name: 'Bank Transfer',
        iconName: 'bank',
        color: '#307aa8',
      },
      'VISA': {
        account: createdAccounts['Credit Card'],
        journal: createdJournals['Credit Card Journal'],
        name: 'Credit Card',
        iconName: 'credit-card',
        color: '#1a1f71',
      },
      'CUSTOMER_ACCOUNT': {
        account: createdAccounts['Accounts Receivable'],
        journal: null,
        name: 'Customer Account',
        iconName: 'user',
        color: '#6b7280',
      },
    };

    // Update each payment method
    for (const [code, config] of Object.entries(paymentMethodsMap)) {
      let paymentMethod = await prisma.paymentMethodSettings.findFirst({
        where: { code },
      });

      if (paymentMethod) {
        console.log(`Updating payment method: ${code}`);
        await prisma.paymentMethodSettings.update({
          where: { id: paymentMethod.id },
          data: {
            accountId: config.account.id,
            journalId: config.journal?.id || null,
            name: config.name,
            iconName: config.iconName,
            color: config.color,
            isActive: true,
          },
        });
      } else {
        console.log(`Creating payment method: ${code}`);
        await prisma.paymentMethodSettings.create({
          data: {
            code,
            name: config.name,
            accountId: config.account.id,
            journalId: config.journal?.id || null,
            iconName: config.iconName,
            color: config.color,
            isActive: true,
            sequence: Object.keys(paymentMethodsMap).indexOf(code) + 1,
          },
        });
      }
    }

    console.log('Payment methods fix script completed successfully!');
  } catch (error) {
    console.error('Error in payment methods fix script:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
