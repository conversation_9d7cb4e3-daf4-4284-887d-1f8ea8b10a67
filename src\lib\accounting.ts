import { db } from "@/lib/db";

// Transaction types
export enum TransactionType {
  DEBIT = "DEBIT",
  CREDIT = "CREDIT"
}

// Reference types for transactions
export enum ReferenceType {
  SALE = "SALE",
  PURCHASE = "PURCHASE",
  RECEIPT = "RECEIPT",
  PAYMENT = "PAYMENT",
  CREDIT_NOTE = "CREDIT_NOTE",
  OPENING_BALANCE = "OPENING_BALANCE",
  SALES_INVOICE = "SALES_INVOICE",
  PURCHASE_INVOICE = "PURCHASE_INVOICE",
  SALES_RETURN = "SALES_RETURN",
  PURCHASE_RETURN = "PURCHASE_RETURN",
  EXPENSE = "EXPENSE",
  INCOME = "INCOME",
  TRANSFER = "TRANSFER",
  ADJUSTMENT = "ADJUSTMENT",
  MANUAL = "MANUAL",
  OTHER = "OTHER"
}

// Payment methods - kept for backward compatibility
// In new code, use the PaymentMethodSettings table instead
export enum PaymentMethod {
  CASH = "CASH",
  VODAFONE_CASH = "VODAFONE_CASH",
  BANK_TRANSFER = "BANK_TRANSFER",
  VISA = "VISA",
  CUSTOMER_ACCOUNT = "CUSTOMER_ACCOUNT",
  OTHER = "OTHER"
}

// Function to get payment method account
export async function getPaymentMethodAccount(methodCode: string) {
  try {
    console.log(`Getting account for payment method: ${methodCode}`);

    // First, try to get the account from PaymentMethodSettings
    const paymentMethodSetting = await db.paymentMethodSettings.findFirst({
      where: {
        code: methodCode,
        isActive: true,
      },
      include: {
        account: true,
      },
    });

    console.log(`Payment method setting found:`, paymentMethodSetting);

    if (paymentMethodSetting?.account) {
      console.log(`Using account from payment method settings:`, paymentMethodSetting.account);
      return paymentMethodSetting.account;
    }

    console.log(`No account found in payment method settings, falling back to hardcoded accounts`);

    // Fallback to hardcoded accounts for backward compatibility
    let account;
    switch (methodCode) {
      case "CASH":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1000", // Cash account
          },
        });
        break;
      case "VODAFONE_CASH":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1010", // Vodafone Cash account
          },
        });
        break;
      case "BANK_TRANSFER":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1020", // Bank account
          },
        });
        break;
      case "VISA":
      case "CREDIT_CARD":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1030", // Visa account
          },
        });
        break;
      default:
        throw new Error(`Unsupported payment method: ${methodCode}`);
    }

    console.log(`Using hardcoded account for ${methodCode}:`, account);
    return account;
  } catch (error) {
    console.error(`Error getting account for payment method ${methodCode}:`, error);
    throw error;
  }
}

// Account types
export enum AccountType {
  ASSET = "ASSET",
  LIABILITY = "LIABILITY",
  EQUITY = "EQUITY",
  REVENUE = "REVENUE",
  EXPENSE = "EXPENSE"
}

// Journal types
export enum JournalType {
  CASH = "CASH",
  VODAFONE_CASH = "VODAFONE_CASH",
  BANK_TRANSFER = "BANK_TRANSFER",
  VISA = "VISA",
  CUSTOMER_ACCOUNT = "CUSTOMER_ACCOUNT",
  GENERAL = "GENERAL"
}

/**
 * Update a contact's balance based on their transactions
 * @param contactId The ID of the contact to update
 * @returns The updated contact
 */
export async function updateContactBalance(contactId: string) {
  try {
    // Get the contact
    const contact = await db.contact.findUnique({
      where: {
        id: contactId,
      },
    });

    if (!contact) {
      throw new Error("Contact not found");
    }

    // Calculate the correct balance
    const balance = await calculateContactBalance(contactId);

    // Update the contact with the calculated balance
    const updatedContact = await db.contact.update({
      where: {
        id: contactId,
      },
      data: {
        balance,
      },
    });

    return updatedContact;
  } catch (error) {
    console.error(`Error updating contact balance for ${contactId}:`, error);
    throw error;
  }
}

/**
 * Calculate a contact's balance based on their transactions
 * @param contactId The ID of the contact
 * @returns The calculated balance
 */
export async function calculateContactBalance(contactId: string): Promise<number> {
  // Get the contact
  const contact = await db.contact.findUnique({
    where: {
      id: contactId,
    },
  });

  if (!contact) {
    throw new Error("Contact not found");
  }

  // Start with opening balance
  let balance = contact.openingBalance || 0;

  // Get all sales for this contact
  const sales = await db.sale.findMany({
    where: {
      contactId,
    },
    include: {
      payments: true,
    },
  });

  // Get all purchases for this contact
  const purchases = await db.purchase.findMany({
    where: {
      contactId,
    },
    include: {
      payments: true,
    },
  });

  // Get all credit notes for this contact
  const creditNotes = await db.creditNote.findMany({
    where: {
      contactId,
    },
    include: {
      payments: true,
    },
  });

  // Get all transactions for this contact
  const transactions = await db.transaction.findMany({
    where: {
      contactId,
    },
  });

  // Calculate balance from sales
  for (const sale of sales) {
    if (contact.isCustomer) {
      // For customers, unpaid sales increase their balance (they owe us money)
      const paidAmount = sale.payments.reduce((sum, payment) => sum + payment.amount, 0);
      const unpaidAmount = sale.totalAmount - paidAmount;

      if (unpaidAmount > 0) {
        balance += unpaidAmount;
      }
    }
  }

  // Calculate balance from purchases
  for (const purchase of purchases) {
    if (contact.isSupplier) {
      // For suppliers, unpaid purchases increase their balance (we owe them money)
      const paidAmount = purchase.payments.reduce((sum, payment) => sum + payment.amount, 0);
      const unpaidAmount = purchase.totalAmount - paidAmount;

      if (unpaidAmount > 0) {
        balance += unpaidAmount;
      }
    }
  }

  // Calculate balance from credit notes
  for (const creditNote of creditNotes) {
    if (contact.isCustomer) {
      // For customers, unpaid credit notes decrease their balance (we owe them money)
      const paidAmount = creditNote.payments.reduce((sum, payment) => sum + payment.amount, 0);
      const unpaidAmount = creditNote.totalAmount - paidAmount;

      if (unpaidAmount > 0) {
        balance -= unpaidAmount;
      }
    }
  }

  // Adjust for any other transactions not covered by sales, purchases, and credit notes
  for (const transaction of transactions) {
    // Skip transactions that are already accounted for in sales, purchases, and credit notes
    if (
      (transaction.referenceType === "SALE" && sales.some(s => s.id === transaction.reference)) ||
      (transaction.referenceType === "PURCHASE" && purchases.some(p => p.id === transaction.reference)) ||
      (transaction.referenceType === "CREDIT_NOTE" && creditNotes.some(c => c.id === transaction.reference))
    ) {
      continue;
    }

    // Handle other transactions based on type
    if (transaction.type === "DEBIT") {
      balance += transaction.amount;
    } else if (transaction.type === "CREDIT") {
      balance -= transaction.amount;
    }
  }

  return balance;
}

/**
 * Create a journal entry for a transaction
 * @param data The journal entry data
 * @returns The created journal entry
 */
export async function createJournalEntry(data: {
  journalId: string;
  description: string;
  debitAccountId: string;
  creditAccountId: string;
  amount: number;
  date?: Date;
  reference?: string;
  referenceType?: string;
  contactId?: string;
}) {
  try {
    // Get the journal
    const journal = await db.journal.findUnique({
      where: {
        id: data.journalId,
      },
    });

    if (!journal) {
      throw new Error("Journal not found");
    }

    // Get the last entry number for this journal
    const lastEntry = await db.journalEntry.findFirst({
      where: {
        journalId: data.journalId,
      },
      orderBy: {
        entryNumber: 'desc',
      },
    });

    // Generate entry number
    let entryNumber = "1";
    if (lastEntry) {
      const lastEntryNumber = parseInt(lastEntry.entryNumber);
      entryNumber = (lastEntryNumber + 1).toString();
    }

    // Get current fiscal period
    const currentDate = data.date || new Date();
    const fiscalPeriod = await db.fiscalPeriod.findFirst({
      where: {
        startDate: { lte: currentDate },
        endDate: { gte: currentDate },
        isClosed: false,
      },
    });

    // Create the journal entry
    const journalEntry = await db.journalEntry.create({
      data: {
        journalId: data.journalId,
        entryNumber,
        date: currentDate,
        description: data.description,
        debitAccountId: data.debitAccountId,
        creditAccountId: data.creditAccountId,
        amount: data.amount,
        reference: data.reference || null,
        referenceType: data.referenceType || null,
        contactId: data.contactId || null,
        fiscalPeriodId: fiscalPeriod?.id || null,
        isPosted: true,
      },
    });

    // Update account balances
    await db.account.update({
      where: { id: data.debitAccountId },
      data: { balance: { increment: data.amount } },
    });

    await db.account.update({
      where: { id: data.creditAccountId },
      data: { balance: { decrement: data.amount } },
    });

    return journalEntry;
  } catch (error) {
    console.error("Error creating journal entry:", error);
    throw error;
  }
}

/**
 * Create journal entries for a sale
 * @param saleId The ID of the sale
 * @returns The created journal entries
 */
export async function createSaleJournalEntries(saleId: string) {
  try {
    // Get the sale with related data
    const sale = await db.sale.findUnique({
      where: {
        id: saleId,
      },
      include: {
        contact: true,
        items: {
          include: {
            product: true,
          },
        },
        payments: true,
      },
    });

    if (!sale) {
      throw new Error("Sale not found");
    }

    const journalEntries = [];

    // Get the sales revenue account
    const salesRevenueAccount = await db.account.findFirst({
      where: {
        type: "REVENUE",
        code: "4000", // Sales Revenue account
      },
    });

    if (!salesRevenueAccount) {
      throw new Error("Sales revenue account not found");
    }

    // Get the inventory asset account
    const inventoryAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1200", // Inventory account
      },
    });

    if (!inventoryAccount) {
      throw new Error("Inventory account not found");
    }

    // Get the cost of goods sold account
    const cogsAccount = await db.account.findFirst({
      where: {
        type: "EXPENSE",
        code: "5000", // Cost of Goods Sold account
      },
    });

    if (!cogsAccount) {
      throw new Error("Cost of Goods Sold account not found");
    }

    // Get the accounts receivable account
    const accountsReceivableAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1100", // Accounts Receivable account
      },
    });

    if (!accountsReceivableAccount) {
      throw new Error("Accounts Receivable account not found");
    }

    // Process payments
    for (const payment of sale.payments) {
      // Get the payment method account using the new function
      const paymentMethodAccount = await getPaymentMethodAccount(payment.method);

      if (!paymentMethodAccount) {
        throw new Error(`Account for payment method ${payment.method} not found`);
      }

      // Create journal entry for the payment
      const paymentJournalEntry = await createJournalEntry({
        journalId: payment.journalId || "default-journal-id",
        description: `Payment for sale #${sale.invoiceNumber}`,
        debitAccountId: paymentMethodAccount.id,
        creditAccountId: salesRevenueAccount.id,
        amount: payment.amount,
        date: payment.date,
        reference: sale.id,
        referenceType: "SALE",
        contactId: sale.contactId,
      });

      journalEntries.push(paymentJournalEntry);
    }

    // If there's an unpaid amount, create a journal entry for accounts receivable
    const totalPaid = sale.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const unpaidAmount = sale.totalAmount - totalPaid;

    if (unpaidAmount > 0 && sale.contact.isCustomer) {
      // Create journal entry for accounts receivable
      const arJournalEntry = await createJournalEntry({
        journalId: "default-journal-id",
        description: `Accounts receivable for sale #${sale.invoiceNumber}`,
        debitAccountId: accountsReceivableAccount.id,
        creditAccountId: salesRevenueAccount.id,
        amount: unpaidAmount,
        date: sale.date,
        reference: sale.id,
        referenceType: "SALE",
        contactId: sale.contactId,
      });

      journalEntries.push(arJournalEntry);
    }

    // Create journal entry for cost of goods sold
    let totalCostOfGoodsSold = 0;
    for (const item of sale.items) {
      totalCostOfGoodsSold += item.costPrice * item.quantity;
    }

    if (totalCostOfGoodsSold > 0) {
      const cogsJournalEntry = await createJournalEntry({
        journalId: "default-journal-id",
        description: `Cost of goods sold for sale #${sale.invoiceNumber}`,
        debitAccountId: cogsAccount.id,
        creditAccountId: inventoryAccount.id,
        amount: totalCostOfGoodsSold,
        date: sale.date,
        reference: sale.id,
        referenceType: "SALE",
        contactId: sale.contactId,
      });

      journalEntries.push(cogsJournalEntry);
    }

    return journalEntries;
  } catch (error) {
    console.error("Error creating sale journal entries:", error);
    throw error;
  }
}

/**
 * Create journal entries for a purchase
 * @param purchaseId The ID of the purchase
 * @returns The created journal entries
 */
export async function createPurchaseJournalEntries(purchaseId: string) {
  try {
    // Get the purchase with related data
    const purchase = await db.purchase.findUnique({
      where: {
        id: purchaseId,
      },
      include: {
        contact: true,
        items: {
          include: {
            product: true,
          },
        },
        payments: true,
      },
    });

    if (!purchase) {
      throw new Error("Purchase not found");
    }

    const journalEntries = [];

    // Get the inventory asset account
    const inventoryAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1200", // Inventory account
      },
    });

    if (!inventoryAccount) {
      throw new Error("Inventory account not found");
    }

    // Get the accounts payable account
    const accountsPayableAccount = await db.account.findFirst({
      where: {
        type: "LIABILITY",
        code: "2000", // Accounts Payable account
      },
    });

    if (!accountsPayableAccount) {
      throw new Error("Accounts Payable account not found");
    }

    // Process payments
    for (const payment of purchase.payments) {
      // Get the payment method account using the new function
      const paymentMethodAccount = await getPaymentMethodAccount(payment.method);

      if (!paymentMethodAccount) {
        throw new Error(`Account for payment method ${payment.method} not found`);
      }

      // Create journal entry for the payment
      const paymentJournalEntry = await createJournalEntry({
        journalId: payment.journalId || "default-journal-id",
        description: `Payment for purchase #${purchase.invoiceNumber}`,
        debitAccountId: inventoryAccount.id,
        creditAccountId: paymentMethodAccount.id,
        amount: payment.amount,
        date: payment.date,
        reference: purchase.id,
        referenceType: "PURCHASE",
        contactId: purchase.contactId,
      });

      journalEntries.push(paymentJournalEntry);
    }

    // If there's an unpaid amount, create a journal entry for accounts payable
    const totalPaid = purchase.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const unpaidAmount = purchase.totalAmount - totalPaid;

    if (unpaidAmount > 0 && purchase.contact.isSupplier) {
      // Create journal entry for accounts payable
      const apJournalEntry = await createJournalEntry({
        journalId: "default-journal-id",
        description: `Accounts payable for purchase #${purchase.invoiceNumber}`,
        debitAccountId: inventoryAccount.id,
        creditAccountId: accountsPayableAccount.id,
        amount: unpaidAmount,
        date: purchase.date,
        reference: purchase.id,
        referenceType: "PURCHASE",
        contactId: purchase.contactId,
      });

      journalEntries.push(apJournalEntry);
    }

    return journalEntries;
  } catch (error) {
    console.error("Error creating purchase journal entries:", error);
    throw error;
  }
}

/**
 * Create journal entries for a credit note
 * @param creditNoteId The ID of the credit note
 * @returns The created journal entries
 */
export async function createCreditNoteJournalEntries(creditNoteId: string) {
  try {
    // Get the credit note with related data
    const creditNote = await db.creditNote.findUnique({
      where: {
        id: creditNoteId,
      },
      include: {
        contact: true,
        items: {
          include: {
            product: true,
          },
        },
        payments: true,
        sale: true,
      },
    });

    if (!creditNote) {
      throw new Error("Credit note not found");
    }

    const journalEntries = [];

    // Get the sales returns account
    const salesReturnsAccount = await db.account.findFirst({
      where: {
        type: "REVENUE",
        code: "4100", // Sales Returns account
      },
    });

    if (!salesReturnsAccount) {
      throw new Error("Sales returns account not found");
    }

    // Get the inventory asset account
    const inventoryAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1200", // Inventory account
      },
    });

    if (!inventoryAccount) {
      throw new Error("Inventory account not found");
    }

    // Get the cost of goods sold account
    const cogsAccount = await db.account.findFirst({
      where: {
        type: "EXPENSE",
        code: "5000", // Cost of Goods Sold account
      },
    });

    if (!cogsAccount) {
      throw new Error("Cost of Goods Sold account not found");
    }

    // Get the accounts receivable account
    const accountsReceivableAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1100", // Accounts Receivable account
      },
    });

    if (!accountsReceivableAccount) {
      throw new Error("Accounts Receivable account not found");
    }

    // Process payments
    for (const payment of creditNote.payments) {
      // Get the payment method account using the new function
      const paymentMethodAccount = await getPaymentMethodAccount(payment.method);

      if (!paymentMethodAccount) {
        throw new Error(`Account for payment method ${payment.method} not found`);
      }

      // Create journal entry for the payment
      const paymentJournalEntry = await createJournalEntry({
        journalId: payment.journalId || "default-journal-id",
        description: `Payment for credit note #${creditNote.creditNoteNumber}`,
        debitAccountId: salesReturnsAccount.id,
        creditAccountId: paymentMethodAccount.id,
        amount: payment.amount,
        date: payment.date,
        reference: creditNote.id,
        referenceType: "CREDIT_NOTE",
        contactId: creditNote.contactId,
      });

      journalEntries.push(paymentJournalEntry);
    }

    // If there's an unpaid amount, create a journal entry for accounts receivable
    const totalPaid = creditNote.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const unpaidAmount = creditNote.totalAmount - totalPaid;

    if (unpaidAmount > 0 && creditNote.contact.isCustomer) {
      // Create journal entry for accounts receivable
      const arJournalEntry = await createJournalEntry({
        journalId: "default-journal-id",
        description: `Accounts receivable for credit note #${creditNote.creditNoteNumber}`,
        debitAccountId: salesReturnsAccount.id,
        creditAccountId: accountsReceivableAccount.id,
        amount: unpaidAmount,
        date: creditNote.date,
        reference: creditNote.id,
        referenceType: "CREDIT_NOTE",
        contactId: creditNote.contactId,
      });

      journalEntries.push(arJournalEntry);
    }

    // Create journal entry for inventory return
    let totalCostOfGoodsReturned = 0;
    for (const item of creditNote.items) {
      totalCostOfGoodsReturned += item.costPrice * item.quantity;
    }

    if (totalCostOfGoodsReturned > 0) {
      const inventoryJournalEntry = await createJournalEntry({
        journalId: "default-journal-id",
        description: `Inventory return for credit note #${creditNote.creditNoteNumber}`,
        debitAccountId: inventoryAccount.id,
        creditAccountId: cogsAccount.id,
        amount: totalCostOfGoodsReturned,
        date: creditNote.date,
        reference: creditNote.id,
        referenceType: "CREDIT_NOTE",
        contactId: creditNote.contactId,
      });

      journalEntries.push(inventoryJournalEntry);
    }

    return journalEntries;
  } catch (error) {
    console.error("Error creating credit note journal entries:", error);
    throw error;
  }
}

/**
 * Create journal entries for inventory adjustments
 * @param inventoryId The ID of the inventory adjustment
 * @returns The created journal entries
 */
export async function createInventoryJournalEntries(inventoryId: string) {
  try {
    // Get the inventory adjustment with related data
    const inventory = await db.inventoryAdjustment.findUnique({
      where: {
        id: inventoryId,
      },
      include: {
        product: true,
      },
    });

    if (!inventory) {
      throw new Error("Inventory adjustment not found");
    }

    const journalEntries = [];

    // Get the inventory asset account
    const inventoryAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1200", // Inventory account
      },
    });

    if (!inventoryAccount) {
      throw new Error("Inventory account not found");
    }

    // Get the inventory adjustment account
    const inventoryAdjustmentAccount = await db.account.findFirst({
      where: {
        type: "EXPENSE",
        code: "5100", // Inventory Adjustment account
      },
    });

    if (!inventoryAdjustmentAccount) {
      throw new Error("Inventory adjustment account not found");
    }

    // Create journal entry for the inventory adjustment
    if (inventory.quantityChange > 0) {
      // Increase in inventory
      const journalEntry = await createJournalEntry({
        journalId: "default-journal-id",
        description: `Inventory increase adjustment for ${inventory.product.name}`,
        debitAccountId: inventoryAccount.id,
        creditAccountId: inventoryAdjustmentAccount.id,
        amount: inventory.quantityChange * inventory.costPrice,
        date: inventory.date,
        reference: inventory.id,
        referenceType: "ADJUSTMENT",
      });

      journalEntries.push(journalEntry);
    } else if (inventory.quantityChange < 0) {
      // Decrease in inventory
      const journalEntry = await createJournalEntry({
        journalId: "default-journal-id",
        description: `Inventory decrease adjustment for ${inventory.product.name}`,
        debitAccountId: inventoryAdjustmentAccount.id,
        creditAccountId: inventoryAccount.id,
        amount: Math.abs(inventory.quantityChange) * inventory.costPrice,
        date: inventory.date,
        reference: inventory.id,
        referenceType: "ADJUSTMENT",
      });

      journalEntries.push(journalEntry);
    }

    return journalEntries;
  } catch (error) {
    console.error("Error creating inventory journal entries:", error);
    throw error;
  }
}

/**
 * Ensure that all required accounts exist in the system
 * @returns The created or found accounts
 */
export async function ensureAccountsExist() {
  try {
    const accounts = [];

    // Define the required accounts
    const requiredAccounts = [
      { code: "1000", name: "Cash", type: "ASSET" },
      { code: "1010", name: "Vodafone Cash", type: "ASSET" },
      { code: "1020", name: "Bank Account", type: "ASSET" },
      { code: "1030", name: "Visa", type: "ASSET" },
      { code: "1100", name: "Accounts Receivable", type: "ASSET" },
      { code: "1200", name: "Inventory", type: "ASSET" },
      { code: "2000", name: "Accounts Payable", type: "LIABILITY" },
      { code: "3000", name: "Owner's Equity", type: "EQUITY" },
      { code: "4000", name: "Sales Revenue", type: "REVENUE" },
      { code: "4100", name: "Sales Returns", type: "REVENUE" },
      { code: "5000", name: "Cost of Goods Sold", type: "EXPENSE" },
      { code: "5100", name: "Inventory Adjustment", type: "EXPENSE" },
      { code: "6000", name: "Operating Expenses", type: "EXPENSE" },
    ];

    // Ensure payment method settings exist
    await ensurePaymentMethodsExist();

    // Check if each account exists, create if it doesn't
    for (const account of requiredAccounts) {
      let existingAccount = await db.account.findFirst({
        where: {
          OR: [
            { code: account.code },
            { name: account.name, type: account.type },
          ],
        },
      });

      if (!existingAccount) {
        existingAccount = await db.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            balance: 0,
            isActive: true,
          },
        });
      }

      accounts.push(existingAccount);
    }

    return accounts;
  } catch (error) {
    console.error("Error ensuring accounts exist:", error);
    throw error;
  }
}

/**
 * Ensure that default payment methods exist in the system
 * @returns The created or found payment methods
 */
export async function ensurePaymentMethodsExist() {
  try {
    // Define the default payment methods
    const defaultPaymentMethods = [
      { code: "CASH", name: "Cash" },
      { code: "VODAFONE_CASH", name: "Vodafone Cash" },
      { code: "BANK_TRANSFER", name: "Bank Transfer" },
      { code: "VISA", name: "Visa" },
      { code: "CUSTOMER_ACCOUNT", name: "Customer Account" },
    ];

    // Check if each payment method exists, create if it doesn't
    for (const method of defaultPaymentMethods) {
      const existingMethod = await db.paymentMethodSettings.findFirst({
        where: {
          code: method.code,
        },
      });

      if (!existingMethod) {
        // Get the highest sequence number
        const highestSequence = await db.paymentMethodSettings.findFirst({
          orderBy: {
            sequence: 'desc',
          },
        });

        const sequence = highestSequence ? highestSequence.sequence + 1 : 1;

        // Create the payment method
        await db.paymentMethodSettings.create({
          data: {
            code: method.code,
            name: method.name,
            isActive: true,
            sequence,
            updatedAt: new Date(),
          },
        });
      }
    }
  } catch (error) {
    console.error("Error ensuring payment methods exist:", error);
    throw error;
  }
}
