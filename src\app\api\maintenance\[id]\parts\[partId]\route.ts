import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// PATCH /api/maintenance/[id]/parts/[partId] - Update a maintenance part
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string; partId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit maintenance services
    const canEditMaintenance = await hasPermission("edit_maintenance") || session.user.role === "ADMIN";

    if (!canEditMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to update maintenance parts" },
        { status: 403 }
      );
    }

    const { id, partId } = params;
    const data = await req.json();

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Check if part exists and belongs to the service
    const existingPart = await db.maintenancePart.findFirst({
      where: {
        id: partId,
        maintenanceServiceId: id,
      },
    });

    if (!existingPart) {
      return NextResponse.json(
        { error: "Maintenance part not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    // Only update fields that are provided
    if (data.partName !== undefined) updateData.partName = data.partName;
    if (data.quantity !== undefined) updateData.quantity = data.quantity;
    if (data.unitPrice !== undefined) updateData.unitPrice = data.unitPrice;

    // Calculate total price if quantity or unit price changed
    if (data.quantity !== undefined || data.unitPrice !== undefined) {
      const quantity = data.quantity !== undefined ? data.quantity : existingPart.quantity;
      const unitPrice = data.unitPrice !== undefined ? data.unitPrice : existingPart.unitPrice;
      updateData.totalPrice = quantity * unitPrice;
    }

    // Update the maintenance part
    const updatedPart = await db.maintenancePart.update({
      where: { id: partId },
      data: updateData,
    });

    // Update the estimated cost of the maintenance service
    const allParts = await db.maintenancePart.findMany({
      where: { maintenanceServiceId: id },
    });

    const totalCost = allParts.reduce((sum, part) => sum + part.totalPrice, 0);

    await db.maintenanceService.update({
      where: { id },
      data: {
        estimatedCost: totalCost,
      },
    });

    return NextResponse.json(updatedPart);
  } catch (error) {
    console.error("Error updating maintenance part:", error);
    return NextResponse.json(
      { error: "Failed to update maintenance part" },
      { status: 500 }
    );
  }
}

// DELETE /api/maintenance/[id]/parts/[partId] - Delete a maintenance part
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; partId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit maintenance services
    const canEditMaintenance = await hasPermission("edit_maintenance") || session.user.role === "ADMIN";

    if (!canEditMaintenance) {
      return NextResponse.json(
        { error: "You don't have permission to delete maintenance parts" },
        { status: 403 }
      );
    }

    const { id, partId } = params;

    // Check if maintenance service exists
    const existingService = await db.maintenanceService.findUnique({
      where: { id },
    });

    if (!existingService) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Check if part exists and belongs to the service
    const existingPart = await db.maintenancePart.findFirst({
      where: {
        id: partId,
        maintenanceServiceId: id,
      },
    });

    if (!existingPart) {
      return NextResponse.json(
        { error: "Maintenance part not found" },
        { status: 404 }
      );
    }

    // If part was from inventory, restore the inventory quantity
    if (existingPart.isFromInventory && existingPart.productId) {
      // Get the branch's warehouse
      const warehouse = await db.warehouse.findFirst({
        where: { branchId: existingService.branchId },
      });

      if (warehouse) {
        // Check if inventory exists for this product in the warehouse
        const inventory = await db.inventory.findFirst({
          where: {
            productId: existingPart.productId,
            warehouseId: warehouse.id,
          },
        });

        if (inventory) {
          // Update inventory quantity
          await db.inventory.update({
            where: { id: inventory.id },
            data: {
              quantity: {
                increment: existingPart.quantity,
              },
            },
          });
        }
      }
    }

    // Delete the maintenance part
    await db.maintenancePart.delete({
      where: { id: partId },
    });

    // Update the estimated cost of the maintenance service
    const allParts = await db.maintenancePart.findMany({
      where: { maintenanceServiceId: id },
    });

    const totalCost = allParts.reduce((sum, part) => sum + part.totalPrice, 0);

    await db.maintenanceService.update({
      where: { id },
      data: {
        estimatedCost: totalCost,
      },
    });

    return NextResponse.json({ message: "Maintenance part deleted successfully" });
  } catch (error) {
    console.error("Error deleting maintenance part:", error);
    return NextResponse.json(
      { error: "Failed to delete maintenance part" },
      { status: 500 }
    );
  }
}
