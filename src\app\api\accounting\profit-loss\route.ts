import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/profit-loss - Get profit & loss statement
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: "Start date and end date are required" },
        { status: 400 }
      );
    }
    
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    // Get all revenue accounts
    const revenueAccounts = await db.account.findMany({
      where: {
        type: "REVENUE",
        isActive: true,
      },
      orderBy: {
        code: "asc",
      },
    });
    
    // Get all expense accounts
    const expenseAccounts = await db.account.findMany({
      where: {
        type: "EXPENSE",
        isActive: true,
      },
      orderBy: {
        code: "asc",
      },
    });
    
    // Get all journal entries within the date range
    const journalEntries = await db.journalEntry.findMany({
      where: {
        date: {
          gte: startDateObj,
          lte: endDateObj,
        },
      },
    });
    
    // Calculate balances for revenue accounts
    const revenueWithBalances = revenueAccounts.map(account => {
      // For revenue accounts, credits increase the balance
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Net balance is credits minus debits for revenue accounts
      const balance = totalCredits - totalDebits;
      
      return {
        ...account,
        balance,
      };
    });
    
    // Calculate balances for expense accounts
    const expensesWithBalances = expenseAccounts.map(account => {
      // For expense accounts, debits increase the balance
      const debitEntries = journalEntries.filter(entry => entry.debitAccountId === account.id);
      const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      const creditEntries = journalEntries.filter(entry => entry.creditAccountId === account.id);
      const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);
      
      // Net balance is debits minus credits for expense accounts
      const balance = totalDebits - totalCredits;
      
      return {
        ...account,
        balance,
      };
    });
    
    // Filter out accounts with zero balance
    const nonZeroRevenue = revenueWithBalances.filter(account => account.balance !== 0);
    const nonZeroExpenses = expensesWithBalances.filter(account => account.balance !== 0);
    
    // Calculate totals
    const totalRevenue = nonZeroRevenue.reduce((sum, account) => sum + account.balance, 0);
    const totalExpenses = nonZeroExpenses.reduce((sum, account) => sum + account.balance, 0);
    const netIncome = totalRevenue - totalExpenses;
    
    return NextResponse.json({
      startDate: startDateObj,
      endDate: endDateObj,
      revenue: nonZeroRevenue,
      expenses: nonZeroExpenses,
      totalRevenue,
      totalExpenses,
      netIncome,
    });
  } catch (error: any) {
    console.error("Error generating profit & loss statement:", error);
    return NextResponse.json(
      { error: error.message || "Failed to generate profit & loss statement" },
      { status: 500 }
    );
  }
}
