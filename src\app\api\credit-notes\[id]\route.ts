import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// GET /api/credit-notes/[id] - Get a specific credit note
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const creditNoteId = params.id;

    // Get credit note with related data
    const creditNote = await db.creditNote.findUnique({
      where: { id: creditNoteId },
      include: {
        contact: true,
        branch: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          include: {
            product: true,
            warehouse: true,
          },
        },
        payments: true,
      },
    });

    if (!creditNote) {
      return NextResponse.json({ error: "Credit note not found" }, { status: 404 });
    }

    return NextResponse.json(creditNote);
  } catch (error: any) {
    console.error("Error fetching credit note:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch credit note" },
      { status: 500 }
    );
  }
}

// PUT /api/credit-notes/[id] - Update a credit note
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const creditNoteId = params.id;
    const data = await req.json();

    // Check if credit note exists
    const existingCreditNote = await db.creditNote.findUnique({
      where: { id: creditNoteId },
      include: {
        items: true,
      },
    });

    if (!existingCreditNote) {
      return NextResponse.json({ error: "Credit note not found" }, { status: 404 });
    }

    // Update credit note in a transaction
    const updatedCreditNote = await db.$transaction(async (tx) => {
      // Update credit note
      const creditNote = await tx.creditNote.update({
        where: { id: creditNoteId },
        data: {
          status: data.status,
          notes: data.notes,
          updatedAt: new Date(),
        },
      });

      return creditNote;
    });

    return NextResponse.json(updatedCreditNote);
  } catch (error: any) {
    console.error("Error updating credit note:", error);
    return NextResponse.json(
      { error: error.message || "Failed to update credit note" },
      { status: 500 }
    );
  }
}

// DELETE /api/credit-notes/[id] - Delete a credit note
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const creditNoteId = params.id;

    // Check if credit note exists
    const existingCreditNote = await db.creditNote.findUnique({
      where: { id: creditNoteId },
      include: {
        items: true,
      },
    });

    if (!existingCreditNote) {
      return NextResponse.json({ error: "Credit note not found" }, { status: 404 });
    }

    // Only allow deletion of PENDING credit notes
    if (existingCreditNote.status !== "PENDING") {
      return NextResponse.json(
        { error: "Only PENDING credit notes can be deleted" },
        { status: 400 }
      );
    }

    // Delete credit note and its items in a transaction
    await db.$transaction(async (tx) => {
      // Delete credit note items
      await tx.creditNoteItem.deleteMany({
        where: { creditNoteId },
      });

      // Delete credit note
      await tx.creditNote.delete({
        where: { id: creditNoteId },
      });
    });

    return NextResponse.json({ message: "Credit note deleted successfully" });
  } catch (error: any) {
    console.error("Error deleting credit note:", error);
    return NextResponse.json(
      { error: error.message || "Failed to delete credit note" },
      { status: 500 }
    );
  }
}
