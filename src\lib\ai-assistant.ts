import { db } from "@/lib/db";
import {
  getCustomerInfo,
  getProductInfo,
  getSalesSummary,
  getPaymentMethodBalances
} from "@/lib/ai-assistant-data";
import {
  getDatabaseSchema,
  getTableData,
  executeQuery,
  getTableRelationships,
  getDatabaseStats
} from "@/lib/ai-assistant-db-explorer";
import {
  getApplicationRoutes,
  testUIComponent,
  checkCommonUIIssues,
  testWorkflow
} from "@/lib/ai-assistant-ui-tester";
import {
  veroERPKnowledge,
  getModuleKnowledge,
  getTableKnowledge,
  getWorkflowKnowledge,
  searchKnowledgeBase
} from "@/lib/ai-assistant-knowledge-base";

interface User {
  id: string;
  name: string;
  email: string;
  role?: string;
}

interface Message {
  id: string;
  conversationId: string;
  content: string;
  role: "SYSTEM" | "USER" | "ASSISTANT";
  createdAt: Date;
}

interface SmartNotification {
  title: string;
  message: string;
  type: "INFO" | "WARNING" | "SUCCESS" | "ERROR";
  actionUrl?: string;
  actionLabel?: string;
}

interface Suggestion {
  id: string;
  title: string;
  description: string;
  actionUrl?: string;
  actionLabel?: string;
}

/**
 * Process a user message and generate an AI assistant response
 * This is a placeholder implementation that simulates AI responses
 * In a real implementation, this would call an external AI service like OpenAI
 */
export async function processAIAssistantMessage(
  message: string,
  conversationHistory: Message[],
  user: User
): Promise<string> {
  // For now, we'll use a simple rule-based system to generate responses
  // In a real implementation, this would call an external AI service

  // Convert message to lowercase for easier matching
  const lowerMessage = message.toLowerCase();

  // Get user information for personalized responses
  const userName = user.name;

  // Basic greeting detection
  if (
    lowerMessage.includes("مرحبا") ||
    lowerMessage.includes("السلام عليكم") ||
    lowerMessage.includes("صباح الخير") ||
    lowerMessage.includes("مساء الخير") ||
    lowerMessage.includes("اهلا")
  ) {
    return `مرحباً ${userName}! كيف يمكنني مساعدتك اليوم؟`;
  }

  // Check for customer information request
  const customerMatch = lowerMessage.match(/عميل\s+(.+)|معلومات\s+عن\s+عميل\s+(.+)|بيانات\s+عميل\s+(.+)/);
  if (customerMatch) {
    const customerQuery = customerMatch[1] || customerMatch[2] || customerMatch[3];
    if (customerQuery) {
      try {
        const { success, customer, message: errorMessage } = await getCustomerInfo(customerQuery);

        if (success && customer) {
          let response = `معلومات العميل: ${customer.name}\n`;
          response += `رقم الهاتف: ${customer.phone || "غير متوفر"}\n`;
          response += `العنوان: ${customer.address || "غير متوفر"}\n`;
          response += `الرصيد: ${customer.balance} جنيه مصري\n\n`;

          if (customer.recentSales && customer.recentSales.length > 0) {
            response += "آخر المبيعات:\n";
            customer.recentSales.forEach((sale, index) => {
              response += `${index + 1}. بتاريخ ${new Date(sale.date).toLocaleDateString("ar-EG")} - المبلغ: ${sale.total} جنيه - الحالة: ${sale.paymentStatus === "PAID" ? "مدفوع" : "غير مدفوع"}\n`;
            });
          } else {
            response += "لا توجد مبيعات سابقة لهذا العميل.";
          }

          return response;
        } else {
          return errorMessage || "لم أتمكن من العثور على معلومات لهذا العميل.";
        }
      } catch (error) {
        console.error("Error processing customer info request:", error);
        return "عذراً، حدث خطأ أثناء البحث عن معلومات العميل.";
      }
    }
  }

  // Check for product information request
  const productMatch = lowerMessage.match(/منتج\s+(.+)|معلومات\s+عن\s+منتج\s+(.+)|بيانات\s+منتج\s+(.+)/);
  if (productMatch) {
    const productQuery = productMatch[1] || productMatch[2] || productMatch[3];
    if (productQuery) {
      try {
        const { success, product, message: errorMessage } = await getProductInfo(productQuery);

        if (success && product) {
          let response = `معلومات المنتج: ${product.name}\n`;
          response += `الباركود: ${product.barcode || "غير متوفر"}\n`;
          response += `الوصف: ${product.description || "غير متوفر"}\n`;
          response += `السعر: ${product.price} جنيه مصري\n`;
          response += `التكلفة: ${product.cost} جنيه مصري\n`;
          response += `التصنيف: ${product.category}\n`;
          response += `إجمالي المخزون: ${product.totalInventory} قطعة\n\n`;

          if (product.inventoryByWarehouse && product.inventoryByWarehouse.length > 0) {
            response += "المخزون حسب المستودع:\n";
            product.inventoryByWarehouse.forEach((item, index) => {
              response += `${index + 1}. ${item.warehouse}: ${item.quantity} قطعة\n`;
            });
          }

          return response;
        } else {
          return errorMessage || "لم أتمكن من العثور على معلومات لهذا المنتج.";
        }
      } catch (error) {
        console.error("Error processing product info request:", error);
        return "عذراً، حدث خطأ أثناء البحث عن معلومات المنتج.";
      }
    }
  }

  // Check for sales summary request
  const salesSummaryMatch = lowerMessage.match(/ملخص\s+المبيعات|احصائيات\s+المبيعات|تقرير\s+المبيعات/);
  if (salesSummaryMatch) {
    let period: "day" | "week" | "month" | "year" = "month";

    if (lowerMessage.includes("يوم") || lowerMessage.includes("اليوم")) {
      period = "day";
    } else if (lowerMessage.includes("اسبوع") || lowerMessage.includes("أسبوع")) {
      period = "week";
    } else if (lowerMessage.includes("شهر")) {
      period = "month";
    } else if (lowerMessage.includes("سنة") || lowerMessage.includes("عام")) {
      period = "year";
    }

    try {
      const { success, summary, message: errorMessage } = await getSalesSummary(period);

      if (success && summary) {
        let periodText = "";
        switch (period) {
          case "day":
            periodText = "اليوم";
            break;
          case "week":
            periodText = "الأسبوع الماضي";
            break;
          case "month":
            periodText = "الشهر الماضي";
            break;
          case "year":
            periodText = "السنة الماضية";
            break;
        }

        let response = `ملخص المبيعات خلال ${periodText}:\n\n`;
        response += `إجمالي عدد المبيعات: ${summary.totalSales}\n`;
        response += `إجمالي قيمة المبيعات: ${summary.totalAmount} جنيه مصري\n\n`;
        response += `المبيعات المدفوعة: ${summary.paidSales} (${summary.paidAmount} جنيه مصري)\n`;
        response += `المبيعات غير المدفوعة: ${summary.unpaidSales} (${summary.unpaidAmount} جنيه مصري)\n`;

        return response;
      } else {
        return errorMessage || "لم أتمكن من الحصول على ملخص المبيعات.";
      }
    } catch (error) {
      console.error("Error processing sales summary request:", error);
      return "عذراً، حدث خطأ أثناء جلب ملخص المبيعات.";
    }
  }

  // Check for payment method balances request
  const paymentMethodsMatch = lowerMessage.match(/ارصدة\s+طرق\s+الدفع|رصيد\s+طرق\s+الدفع|اموال\s+الخزينة/);
  if (paymentMethodsMatch) {
    try {
      const { success, balances, message: errorMessage } = await getPaymentMethodBalances();

      if (success && balances) {
        let response = "أرصدة طرق الدفع:\n\n";

        balances.forEach((method, index) => {
          response += `${index + 1}. ${method.name}: ${method.balance} جنيه مصري\n`;
        });

        // Calculate total balance
        const totalBalance = balances.reduce((sum, method) => sum + method.balance, 0);
        response += `\nإجمالي الرصيد: ${totalBalance} جنيه مصري`;

        return response;
      } else {
        return errorMessage || "لم أتمكن من الحصول على أرصدة طرق الدفع.";
      }
    } catch (error) {
      console.error("Error processing payment methods request:", error);
      return "عذراً، حدث خطأ أثناء جلب أرصدة طرق الدفع.";
    }
  }

  // Help with sales
  if (
    lowerMessage.includes("مبيعات") ||
    lowerMessage.includes("فاتورة") ||
    lowerMessage.includes("بيع") ||
    lowerMessage.includes("عميل")
  ) {
    if (lowerMessage.includes("كيف") || lowerMessage.includes("طريقة")) {
      return `لإنشاء فاتورة مبيعات جديدة، اتبع الخطوات التالية:
1. انتقل إلى قسم المبيعات من القائمة الجانبية
2. انقر على "فاتورة جديدة"
3. اختر العميل من القائمة أو أضف عميل جديد
4. أضف المنتجات المطلوبة وحدد الكميات والأسعار
5. حدد طريقة الدفع
6. انقر على "حفظ" لإنشاء الفاتورة

هل تحتاج إلى مساعدة في أي خطوة محددة؟`;
    }

    if (lowerMessage.includes("تقرير") || lowerMessage.includes("احصائيات")) {
      return `يمكنك الوصول إلى تقارير المبيعات من خلال:
1. الانتقال إلى قسم "التقارير" من القائمة الجانبية
2. اختيار "تقارير المبيعات"
3. تحديد الفترة الزمنية المطلوبة (يوم، أسبوع، شهر، سنة)
4. يمكنك تصفية النتائج حسب العميل أو المنتج أو الفرع

هل تريد مساعدة في استخراج تقرير محدد؟`;
    }

    return `يمكنني مساعدتك في إدارة المبيعات. ماذا تريد تحديداً؟
- إنشاء فاتورة مبيعات جديدة
- البحث عن فواتير سابقة
- عرض تقارير المبيعات
- إدارة مرتجعات المبيعات
- إدارة العملاء`;
  }

  // Help with inventory
  if (
    lowerMessage.includes("مخزون") ||
    lowerMessage.includes("منتج") ||
    lowerMessage.includes("بضاعة") ||
    lowerMessage.includes("مستودع")
  ) {
    if (lowerMessage.includes("اضافة") || lowerMessage.includes("جديد")) {
      return `لإضافة منتج جديد إلى المخزون:
1. انتقل إلى قسم "المخزون" من القائمة الجانبية
2. انقر على "إضافة منتج"
3. أدخل بيانات المنتج (الاسم، الوصف، السعر، الفئة)
4. حدد المستودع والكمية
5. انقر على "حفظ"

هل تحتاج إلى مساعدة إضافية؟`;
    }

    if (lowerMessage.includes("جرد") || lowerMessage.includes("تحقق")) {
      return `لإجراء جرد المخزون:
1. انتقل إلى قسم "المخزون" من القائمة الجانبية
2. اختر "جرد المخزون"
3. حدد المستودع المطلوب
4. قم بإدخال الكميات الفعلية لكل منتج
5. انقر على "تحديث المخزون" لتسجيل الفروقات

يمكنك أيضاً طباعة تقرير الجرد أو تصديره كملف Excel.`;
    }

    return `يمكنني مساعدتك في إدارة المخزون. ماذا تريد تحديداً؟
- إضافة منتج جديد
- البحث عن منتج
- نقل مخزون بين المستودعات
- إجراء جرد المخزون
- عرض تقارير المخزون`;
  }

  // Help with accounting
  if (
    lowerMessage.includes("محاسبة") ||
    lowerMessage.includes("حساب") ||
    lowerMessage.includes("مالية") ||
    lowerMessage.includes("دفع")
  ) {
    return `يمكنني مساعدتك في الأمور المالية والمحاسبية. ماذا تريد تحديداً؟
- إنشاء سند قبض أو دفع
- عرض الحسابات
- عرض ميزان المراجعة
- عرض قائمة الدخل
- عرض الميزانية العمومية
- إدارة المصروفات`;
  }

  // Help with customers
  if (
    lowerMessage.includes("عملاء") ||
    lowerMessage.includes("زبائن") ||
    lowerMessage.includes("حساب عميل")
  ) {
    return `يمكنني مساعدتك في إدارة العملاء. ماذا تريد تحديداً؟
- إضافة عميل جديد
- البحث عن عميل
- عرض كشف حساب عميل
- إدارة برنامج الولاء
- متابعة الديون المستحقة`;
  }

  // Help with suppliers
  if (
    lowerMessage.includes("مورد") ||
    lowerMessage.includes("موردين") ||
    lowerMessage.includes("مشتريات")
  ) {
    return `يمكنني مساعدتك في إدارة الموردين والمشتريات. ماذا تريد تحديداً؟
- إضافة مورد جديد
- إنشاء أمر شراء
- استلام بضاعة
- عرض كشف حساب مورد
- متابعة المدفوعات للموردين`;
  }

  // Check for database schema request
  const dbSchemaMatch = lowerMessage.match(/هيكل\s+قاعدة\s+البيانات|بنية\s+قاعدة\s+البيانات|schema|database\s+structure/);
  if (dbSchemaMatch) {
    try {
      const { success, schema, error } = await getDatabaseSchema();

      if (success && schema) {
        let response = "هيكل قاعدة البيانات:\n\n";

        // Get the top 5 tables by record count
        const topTables = Object.entries(schema)
          .sort((a, b) => b[1].recordCount - a[1].recordCount)
          .slice(0, 5);

        response += "أهم الجداول في قاعدة البيانات:\n";
        topTables.forEach(([tableName, tableInfo], index) => {
          response += `${index + 1}. ${tableName}: ${tableInfo.recordCount} سجل\n`;
        });

        response += "\nيمكنك طلب معلومات تفصيلية عن جدول محدد مثل: 'عرض بيانات جدول المبيعات'";

        return response;
      } else {
        return error || "لم أتمكن من الحصول على هيكل قاعدة البيانات.";
      }
    } catch (error) {
      console.error("Error processing database schema request:", error);
      return "عذراً، حدث خطأ أثناء جلب هيكل قاعدة البيانات.";
    }
  }

  // Check for table data request
  const tableDataMatch = lowerMessage.match(/بيانات\s+جدول\s+(.+)|عرض\s+جدول\s+(.+)|عرض\s+بيانات\s+(.+)/);
  if (tableDataMatch) {
    const tableName = tableDataMatch[1] || tableDataMatch[2] || tableDataMatch[3];
    if (tableName) {
      try {
        // Map Arabic table names to actual table names
        const tableMap: Record<string, string> = {
          "المبيعات": "sale",
          "المشتريات": "purchase",
          "المنتجات": "product",
          "العملاء": "contact",
          "المخزون": "inventory",
          "المستخدمين": "user",
          "الفروع": "branch",
          "المستودعات": "warehouse",
        };

        const actualTableName = tableMap[tableName] || tableName;

        const { success, data, pagination, error } = await getTableData(actualTableName, 1, 5);

        if (success && data) {
          let response = `بيانات جدول ${tableName}:\n\n`;

          if (data.length === 0) {
            response += "لا توجد بيانات في هذا الجدول.";
          } else {
            // Show first 5 records
            data.forEach((record, index) => {
              response += `سجل ${index + 1}:\n`;

              // Show first 5 fields of each record
              const fields = Object.entries(record).slice(0, 5);
              fields.forEach(([key, value]) => {
                response += `- ${key}: ${value === null ? "غير متوفر" : value}\n`;
              });

              response += "\n";
            });

            response += `إجمالي السجلات: ${pagination.totalCount}\n`;
            response += `عدد الصفحات: ${pagination.totalPages}\n`;
          }

          return response;
        } else {
          return error || `لم أتمكن من الحصول على بيانات جدول ${tableName}.`;
        }
      } catch (error) {
        console.error("Error processing table data request:", error);
        return `عذراً، حدث خطأ أثناء جلب بيانات جدول ${tableName}.`;
      }
    }
  }

  // Check for UI issues request
  const uiIssuesMatch = lowerMessage.match(/مشاكل\s+واجهة\s+المستخدم|اختبار\s+الواجهة|فحص\s+المشاكل/);
  if (uiIssuesMatch) {
    try {
      const { success, issues, error } = await checkCommonUIIssues();

      if (success && issues) {
        let response = "المشاكل المكتشفة في واجهة المستخدم:\n\n";

        if (issues.length === 0) {
          response += "لم يتم اكتشاف أي مشاكل في واجهة المستخدم.";
        } else {
          issues.forEach((issue, index) => {
            response += `${index + 1}. ${issue.component} (${issue.path}):\n`;
            response += `   المشكلة: ${issue.error}\n`;
            response += `   الوصف: ${issue.description}\n\n`;
          });
        }

        return response;
      } else {
        return error || "لم أتمكن من فحص مشاكل واجهة المستخدم.";
      }
    } catch (error) {
      console.error("Error processing UI issues request:", error);
      return "عذراً، حدث خطأ أثناء فحص مشاكل واجهة المستخدم.";
    }
  }

  // Check for workflow test request
  const workflowMatch = lowerMessage.match(/اختبار\s+عملية\s+(.+)|فحص\s+عملية\s+(.+)|تجربة\s+عملية\s+(.+)/);
  if (workflowMatch) {
    const workflowName = workflowMatch[1] || workflowMatch[2] || workflowMatch[3];
    if (workflowName) {
      try {
        // Map Arabic workflow names to actual workflow names
        const workflowMap: Record<string, string> = {
          "المبيعات": "sales_invoice",
          "فاتورة المبيعات": "sales_invoice",
          "الفاتورة": "sales_invoice",
          "المخزون": "inventory_management",
          "إدارة المخزون": "inventory_management",
          "العملاء": "customer_management",
          "إدارة العملاء": "customer_management",
        };

        const actualWorkflowName = workflowMap[workflowName] || workflowName;

        const { success, steps, error } = await testWorkflow(actualWorkflowName);

        if (success && steps) {
          let response = `خطوات عملية ${workflowName}:\n\n`;

          steps.forEach((step, index) => {
            response += `${step.step}. ${step.description}\n`;
            if (step.path) {
              response += `   المسار: ${step.path}\n`;
            }
            if (step.component) {
              response += `   المكون: ${step.component}\n`;
            }
            response += "\n";
          });

          return response;
        } else {
          return error || `لم أتمكن من اختبار عملية ${workflowName}.`;
        }
      } catch (error) {
        console.error("Error processing workflow test request:", error);
        return `عذراً، حدث خطأ أثناء اختبار عملية ${workflowName}.`;
      }
    }
  }

  // Check for system knowledge request
  const systemKnowledgeMatch = lowerMessage.match(/معلومات\s+عن\s+النظام|معلومات\s+النظام|شرح\s+النظام|وصف\s+النظام/);
  if (systemKnowledgeMatch) {
    try {
      let response = `معلومات عن نظام VERO ERP:\n\n`;
      response += `الإصدار: ${veroERPKnowledge.version}\n`;
      response += `آخر تحديث: ${new Date(veroERPKnowledge.lastUpdated).toLocaleDateString("ar-EG")}\n\n`;

      response += "الوحدات الرئيسية في النظام:\n";
      veroERPKnowledge.modules.forEach((module, index) => {
        response += `${index + 1}. ${module.name}: ${module.description}\n`;
      });

      response += "\nالميزات العامة للنظام:\n";
      veroERPKnowledge.globalFeatures.slice(0, 5).forEach((feature, index) => {
        response += `${index + 1}. ${feature}\n`;
      });

      response += "\nيمكنك طلب معلومات تفصيلية عن أي وحدة مثل: 'معلومات عن وحدة المبيعات'";

      return response;
    } catch (error) {
      console.error("Error processing system knowledge request:", error);
      return "عذراً، حدث خطأ أثناء جلب معلومات النظام.";
    }
  }

  // Check for module knowledge request
  const moduleKnowledgeMatch = lowerMessage.match(/معلومات\s+عن\s+وحدة\s+(.+)|معلومات\s+وحدة\s+(.+)|شرح\s+وحدة\s+(.+)/);
  if (moduleKnowledgeMatch) {
    const moduleName = moduleKnowledgeMatch[1] || moduleKnowledgeMatch[2] || moduleKnowledgeMatch[3];
    if (moduleName) {
      try {
        const moduleInfo = getModuleKnowledge(moduleName);

        if (moduleInfo) {
          let response = `معلومات عن وحدة ${moduleInfo.name}:\n\n`;
          response += `${moduleInfo.description}\n\n`;

          response += "الميزات الرئيسية:\n";
          moduleInfo.features.forEach((feature, index) => {
            response += `${index + 1}. ${feature}\n`;
          });

          response += "\nالعمليات المتاحة:\n";
          moduleInfo.workflows.forEach((workflow, index) => {
            response += `${index + 1}. ${workflow.name}: ${workflow.description}\n`;
          });

          response += "\nالمشاكل الشائعة:\n";
          if (moduleInfo.commonIssues.length > 0) {
            moduleInfo.commonIssues.forEach((issue, index) => {
              response += `${index + 1}. ${issue.issue}: ${issue.description}\n`;
            });
          } else {
            response += "لا توجد مشاكل شائعة مسجلة لهذه الوحدة.\n";
          }

          response += "\nالوحدات ذات الصلة: " + moduleInfo.relatedModules.join("، ");

          return response;
        } else {
          return `لم أتمكن من العثور على معلومات عن وحدة ${moduleName}.`;
        }
      } catch (error) {
        console.error("Error processing module knowledge request:", error);
        return `عذراً، حدث خطأ أثناء جلب معلومات عن وحدة ${moduleName}.`;
      }
    }
  }

  // Check for workflow knowledge request
  const workflowKnowledgeMatch = lowerMessage.match(/معلومات\s+عن\s+عملية\s+(.+)|شرح\s+عملية\s+(.+)|كيفية\s+(.+)/);
  if (workflowKnowledgeMatch) {
    const workflowName = workflowKnowledgeMatch[1] || workflowKnowledgeMatch[2] || workflowKnowledgeMatch[3];
    if (workflowName) {
      try {
        const workflowInfo = getWorkflowKnowledge(workflowName);

        if (workflowInfo) {
          let response = `معلومات عن عملية ${workflowInfo.name}:\n\n`;
          response += `${workflowInfo.description}\n\n`;

          response += "خطوات العملية:\n";
          workflowInfo.steps.forEach((step, index) => {
            response += `${index + 1}. ${step}\n`;
          });

          response += "\nالصلاحيات المطلوبة: " + workflowInfo.requiredPermissions.join("، ");

          return response;
        } else {
          return `لم أتمكن من العثور على معلومات عن عملية ${workflowName}.`;
        }
      } catch (error) {
        console.error("Error processing workflow knowledge request:", error);
        return `عذراً، حدث خطأ أثناء جلب معلومات عن عملية ${workflowName}.`;
      }
    }
  }

  // Check for general knowledge search
  const knowledgeSearchMatch = lowerMessage.match(/ابحث\s+عن\s+(.+)|معلومات\s+عن\s+(.+)|ماذا\s+تعرف\s+عن\s+(.+)/);
  if (knowledgeSearchMatch) {
    const searchTerm = knowledgeSearchMatch[1] || knowledgeSearchMatch[2] || knowledgeSearchMatch[3];
    if (searchTerm) {
      try {
        const searchResults = searchKnowledgeBase(searchTerm);

        if (searchResults.length > 0) {
          let response = `نتائج البحث عن "${searchTerm}":\n\n`;

          // Group results by type
          const groupedResults: Record<string, any[]> = {
            module: [],
            workflow: [],
            issue: [],
            table: []
          };

          searchResults.forEach(result => {
            if (groupedResults[result.type]) {
              groupedResults[result.type].push(result);
            }
          });

          // Add modules
          if (groupedResults.module.length > 0) {
            response += "الوحدات:\n";
            groupedResults.module.slice(0, 3).forEach((result, index) => {
              response += `${index + 1}. ${result.data.name}: ${result.data.description}\n`;
            });
            response += "\n";
          }

          // Add workflows
          if (groupedResults.workflow.length > 0) {
            response += "العمليات:\n";
            groupedResults.workflow.slice(0, 3).forEach((result, index) => {
              response += `${index + 1}. ${result.data.name}: ${result.data.description} (في وحدة ${result.module})\n`;
            });
            response += "\n";
          }

          // Add issues
          if (groupedResults.issue.length > 0) {
            response += "المشاكل الشائعة:\n";
            groupedResults.issue.slice(0, 3).forEach((result, index) => {
              response += `${index + 1}. ${result.data.issue}: ${result.data.description} (في وحدة ${result.module})\n`;
            });
            response += "\n";
          }

          // Add tables
          if (groupedResults.table.length > 0) {
            response += "الجداول:\n";
            groupedResults.table.slice(0, 3).forEach((result, index) => {
              response += `${index + 1}. ${result.data.arabicName} (${result.data.name}): ${result.data.description}\n`;
            });
            response += "\n";
          }

          response += "يمكنك طلب معلومات تفصيلية عن أي من هذه العناصر.";

          return response;
        } else {
          return `لم أتمكن من العثور على معلومات عن "${searchTerm}".`;
        }
      } catch (error) {
        console.error("Error processing knowledge search request:", error);
        return `عذراً، حدث خطأ أثناء البحث عن "${searchTerm}".`;
      }
    }
  }

  // Default response
  return `أنا هنا لمساعدتك في استخدام نظام VERO ERP. يمكنني مساعدتك في:
- إدارة المبيعات والفواتير
- إدارة المشتريات
- إدارة المخزون والمنتجات
- إدارة العملاء والموردين
- الأمور المالية والمحاسبية
- إعدادات النظام
- فحص قاعدة البيانات وهيكلها
- اختبار واجهة المستخدم واكتشاف المشاكل
- اختبار عمليات النظام المختلفة
- تقديم معلومات شاملة عن النظام وكل وحداته

يمكنك أن تسألني عن:
- معلومات عن النظام
- معلومات عن وحدة معينة (مثل: معلومات عن وحدة المبيعات)
- شرح عملية معينة (مثل: كيفية إنشاء فاتورة مبيعات)
- البحث عن معلومات محددة (مثل: معلومات عن الفواتير)

ما الذي تحتاج المساعدة فيه تحديداً؟`;
}

/**
 * Get suggested actions based on user activity and system state
 */
export async function getSuggestedActions(userId: string): Promise<string[]> {
  try {
    // Get low stock products
    const lowStockProducts = await db.inventory.findMany({
      where: {
        quantity: {
          lt: 5, // Products with less than 5 items
        },
      },
      include: {
        product: true,
      },
      take: 3,
    });

    // Get unpaid invoices
    const unpaidInvoices = await db.sale.findMany({
      where: {
        paymentStatus: "UNPAID",
        date: {
          lte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Older than 7 days
        },
      },
      include: {
        contact: true,
      },
      take: 3,
    });

    const suggestions: string[] = [];

    if (lowStockProducts.length > 0) {
      suggestions.push(`هناك ${lowStockProducts.length} منتجات منخفضة المخزون تحتاج إلى إعادة الطلب.`);
    }

    if (unpaidInvoices.length > 0) {
      suggestions.push(`هناك ${unpaidInvoices.length} فواتير غير مدفوعة تحتاج إلى متابعة.`);
    }

    return suggestions;
  } catch (error) {
    console.error("Error getting suggested actions:", error);
    return [];
  }
}

/**
 * Generate smart notifications based on system state
 */
export async function generateSmartNotifications(userId: string): Promise<void> {
  try {
    // Check user settings first
    const userSettings = await db.aIAssistantSettings.findUnique({
      where: {
        userId,
      },
    });

    // If notifications are turned off, don't generate any
    if (userSettings && !userSettings.notificationsOn) {
      return;
    }

    const notifications: SmartNotification[] = [];

    // Check for low stock products
    const lowStockProducts = await db.inventory.findMany({
      where: {
        quantity: {
          lt: 5, // Products with less than 5 items
        },
      },
      include: {
        product: true,
      },
      take: 5,
    });

    if (lowStockProducts.length > 0) {
      // Check if we already have a notification for this
      const existingNotification = await db.aIAssistantNotification.findFirst({
        where: {
          userId,
          title: "منتجات منخفضة المخزون",
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Created in the last 24 hours
          },
        },
      });

      if (!existingNotification) {
        notifications.push({
          title: "منتجات منخفضة المخزون",
          message: `هناك ${lowStockProducts.length} منتجات منخفضة المخزون تحتاج إلى إعادة الطلب.`,
          type: "WARNING",
          actionUrl: "/dashboard/inventory",
          actionLabel: "عرض المخزون",
        });
      }
    }

    // Check for unpaid invoices
    const unpaidInvoices = await db.sale.findMany({
      where: {
        paymentStatus: "UNPAID",
        date: {
          lte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Older than 7 days
        },
      },
      include: {
        contact: true,
      },
      take: 5,
    });

    if (unpaidInvoices.length > 0) {
      // Check if we already have a notification for this
      const existingNotification = await db.aIAssistantNotification.findFirst({
        where: {
          userId,
          title: "فواتير غير مدفوعة",
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Created in the last 24 hours
          },
        },
      });

      if (!existingNotification) {
        notifications.push({
          title: "فواتير غير مدفوعة",
          message: `هناك ${unpaidInvoices.length} فواتير غير مدفوعة تحتاج إلى متابعة.`,
          type: "WARNING",
          actionUrl: "/dashboard/sales",
          actionLabel: "عرض الفواتير",
        });
      }
    }

    // Check for products that haven't been sold in a long time
    const inactiveProducts = await db.product.findMany({
      where: {
        SaleItem: {
          none: {
            sale: {
              date: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Sold in the last 30 days
              },
            },
          },
        },
      },
      take: 5,
    });

    if (inactiveProducts.length > 0) {
      // Check if we already have a notification for this
      const existingNotification = await db.aIAssistantNotification.findFirst({
        where: {
          userId,
          title: "منتجات غير نشطة",
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Created in the last 7 days
          },
        },
      });

      if (!existingNotification) {
        notifications.push({
          title: "منتجات غير نشطة",
          message: `هناك ${inactiveProducts.length} منتجات لم يتم بيعها منذ أكثر من 30 يوماً.`,
          type: "INFO",
          actionUrl: "/dashboard/inventory",
          actionLabel: "عرض المنتجات",
        });
      }
    }

    // Create notifications in the database
    for (const notification of notifications) {
      await db.aIAssistantNotification.create({
        data: {
          userId,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          actionUrl: notification.actionUrl,
          actionLabel: notification.actionLabel,
        },
      });
    }
  } catch (error) {
    console.error("Error generating smart notifications:", error);
  }
}

/**
 * Generate contextual suggestions based on the current path
 */
export async function generateContextualSuggestions(path: string, userId: string): Promise<Suggestion[]> {
  try {
    const suggestions: Suggestion[] = [];

    // Dashboard suggestions
    if (path === "/dashboard") {
      suggestions.push({
        id: "dashboard-overview",
        title: "استكشاف لوحة التحكم",
        description: "يمكنك الاطلاع على ملخص المبيعات والمشتريات والمخزون من لوحة التحكم الرئيسية.",
        actionUrl: "/dashboard",
        actionLabel: "استكشاف",
      });
    }

    // Sales suggestions
    if (path.includes("/dashboard/sales")) {
      // Check for unpaid invoices
      const unpaidInvoices = await db.sale.count({
        where: {
          paymentStatus: "UNPAID",
        },
      });

      if (unpaidInvoices > 0) {
        suggestions.push({
          id: "unpaid-invoices",
          title: "فواتير غير مدفوعة",
          description: `لديك ${unpaidInvoices} فواتير غير مدفوعة. هل ترغب في متابعتها؟`,
          actionUrl: "/dashboard/sales?filter=unpaid",
          actionLabel: "عرض الفواتير غير المدفوعة",
        });
      }

      // Suggest creating a new invoice
      suggestions.push({
        id: "create-invoice",
        title: "إنشاء فاتورة جديدة",
        description: "يمكنك إنشاء فاتورة مبيعات جديدة بسهولة.",
        actionUrl: "/dashboard/sales/new",
        actionLabel: "إنشاء فاتورة",
      });
    }

    // Inventory suggestions
    if (path.includes("/dashboard/inventory")) {
      // Check for low stock products
      const lowStockProducts = await db.inventory.count({
        where: {
          quantity: {
            lt: 5,
          },
        },
      });

      if (lowStockProducts > 0) {
        suggestions.push({
          id: "low-stock",
          title: "منتجات منخفضة المخزون",
          description: `لديك ${lowStockProducts} منتجات منخفضة المخزون. هل ترغب في إعادة الطلب؟`,
          actionUrl: "/dashboard/inventory?filter=low-stock",
          actionLabel: "عرض المنتجات منخفضة المخزون",
        });
      }

      // Suggest adding a new product
      suggestions.push({
        id: "add-product",
        title: "إضافة منتج جديد",
        description: "يمكنك إضافة منتج جديد إلى المخزون بسهولة.",
        actionUrl: "/dashboard/inventory/products/new",
        actionLabel: "إضافة منتج",
      });
    }

    // Customers suggestions
    if (path.includes("/dashboard/contacts")) {
      // Suggest adding a new customer
      suggestions.push({
        id: "add-customer",
        title: "إضافة عميل جديد",
        description: "يمكنك إضافة عميل جديد بسهولة.",
        actionUrl: "/dashboard/contacts/new",
        actionLabel: "إضافة عميل",
      });

      // Suggest viewing customer statements
      suggestions.push({
        id: "customer-statements",
        title: "كشوف حسابات العملاء",
        description: "يمكنك عرض كشوف حسابات العملاء لمتابعة المستحقات.",
        actionUrl: "/dashboard/finance/customer-statements",
        actionLabel: "عرض كشوف الحسابات",
      });
    }

    // Accounting suggestions
    if (path.includes("/dashboard/accounting")) {
      // Suggest viewing the general ledger
      suggestions.push({
        id: "general-ledger",
        title: "دفتر الأستاذ العام",
        description: "يمكنك عرض دفتر الأستاذ العام لمتابعة الحركات المالية.",
        actionUrl: "/dashboard/accounting/general-ledger",
        actionLabel: "عرض دفتر الأستاذ",
      });

      // Suggest creating a payment voucher
      suggestions.push({
        id: "payment-voucher",
        title: "إنشاء سند صرف",
        description: "يمكنك إنشاء سند صرف جديد بسهولة.",
        actionUrl: "/dashboard/accounting/payment-vouchers/new",
        actionLabel: "إنشاء سند صرف",
      });
    }

    // Limit to 2 suggestions maximum
    return suggestions.slice(0, 2);
  } catch (error) {
    console.error("Error generating contextual suggestions:", error);
    return [];
  }
}
