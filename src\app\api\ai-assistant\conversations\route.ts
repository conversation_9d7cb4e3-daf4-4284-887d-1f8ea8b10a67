import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// GET /api/ai-assistant/conversations - Get user's conversations
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get user's conversations
    const conversations = await db.aIAssistantConversation.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });
    
    return NextResponse.json({ conversations });
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return NextResponse.json(
      { error: "Failed to fetch conversations" },
      { status: 500 }
    );
  }
}

// POST /api/ai-assistant/conversations - Create a new conversation
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Set all existing conversations to inactive
    await db.aIAssistantConversation.updateMany({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      data: {
        isActive: false,
      },
    });
    
    // Create a new conversation
    const conversation = await db.aIAssistantConversation.create({
      data: {
        userId: session.user.id,
        isActive: true,
      },
    });
    
    // Create initial system message
    await db.aIAssistantMessage.create({
      data: {
        conversationId: conversation.id,
        role: "SYSTEM",
        content: "أنا المساعد الذكي لنظام VERO ERP. يمكنني مساعدتك في إدارة المبيعات والمشتريات والمخزون والعملاء والموردين والمحاسبة وغيرها من وظائف النظام.",
      },
    });
    
    return NextResponse.json(conversation);
  } catch (error) {
    console.error("Error creating conversation:", error);
    return NextResponse.json(
      { error: "Failed to create conversation" },
      { status: 500 }
    );
  }
}
