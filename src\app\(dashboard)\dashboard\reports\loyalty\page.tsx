"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from "recharts";
import { 
  Download, 
  Filter, 
  Search, 
  Award, 
  Gift, 
  Users,
  Calendar,
  TrendingUp,
  TrendingDown,
  Star,
  CreditCard,
  DollarSign,
  ShoppingBag
} from "lucide-react";
import { format, subDays, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns";

// Define types
interface LoyaltyCustomer {
  id: string;
  name: string;
  phone: string;
  loyaltyPoints: number;
  loyaltyTier: string;
  isVIP: boolean;
}

interface TierDistribution {
  tier: string;
  count: number;
}

interface PointsHistory {
  month: string;
  earned: number;
  redeemed: number;
  net: number;
}

interface BirthdayCustomer {
  id: string;
  name: string;
  phone: string;
  email: string;
  birthday: string;
  loyaltyPoints: number;
  loyaltyTier: string;
  isVIP: boolean;
  daysUntilBirthday: number;
}

export default function LoyaltyReportsPage() {
  // State for filters
  const [dateRange, setDateRange] = useState<[Date | undefined, Date | undefined]>([
    startOfMonth(new Date()),
    endOfMonth(new Date())
  ]);
  const [loyaltyTier, setLoyaltyTier] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [timeFrame, setTimeFrame] = useState<string>("month");
  
  // State for data
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [topCustomers, setTopCustomers] = useState<LoyaltyCustomer[]>([]);
  const [tiersDistribution, setTiersDistribution] = useState<TierDistribution[]>([]);
  const [pointsHistory, setPointsHistory] = useState<PointsHistory[]>([]);
  const [totalPoints, setTotalPoints] = useState<number>(0);
  const [pointValueInEGP, setPointValueInEGP] = useState<number>(0.01);
  const [upcomingBirthdays, setUpcomingBirthdays] = useState<BirthdayCustomer[]>([]);
  
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];
  const TIER_COLORS = {
    BRONZE: '#cd7f32',
    SILVER: '#C0C0C0',
    GOLD: '#FFD700',
    PLATINUM: '#E5E4E2'
  };
  
  // Update date range based on time frame
  useEffect(() => {
    const now = new Date();
    
    switch (timeFrame) {
      case "week":
        setDateRange([subDays(now, 7), now]);
        break;
      case "month":
        setDateRange([startOfMonth(now), endOfMonth(now)]);
        break;
      case "quarter":
        setDateRange([subDays(now, 90), now]);
        break;
      case "year":
        setDateRange([startOfYear(now), endOfYear(now)]);
        break;
      default:
        setDateRange([startOfMonth(now), endOfMonth(now)]);
    }
  }, [timeFrame]);
  
  // Fetch data when filters change
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      
      try {
        // In a real implementation, this would be an API call with filters
        // For now, we'll simulate data
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock top customers data
        const mockTopCustomers: LoyaltyCustomer[] = [
          { id: "1", name: "Ahmed Mohamed", phone: "01012345678", loyaltyPoints: 5200, loyaltyTier: "GOLD", isVIP: true },
          { id: "2", name: "Sara Ali", phone: "01023456789", loyaltyPoints: 3800, loyaltyTier: "SILVER", isVIP: false },
          { id: "3", name: "Mohamed Ibrahim", phone: "01034567890", loyaltyPoints: 2500, loyaltyTier: "SILVER", isVIP: true },
          { id: "4", name: "Fatima Hassan", phone: "01045678901", loyaltyPoints: 1800, loyaltyTier: "SILVER", isVIP: false },
          { id: "5", name: "Ali Mahmoud", phone: "01056789012", loyaltyPoints: 950, loyaltyTier: "BRONZE", isVIP: false },
          { id: "6", name: "Nour Ahmed", phone: "01067890123", loyaltyPoints: 750, loyaltyTier: "BRONZE", isVIP: false },
          { id: "7", name: "Hassan Ali", phone: "01078901234", loyaltyPoints: 650, loyaltyTier: "BRONZE", isVIP: false },
          { id: "8", name: "Laila Mohamed", phone: "01089012345", loyaltyPoints: 12500, loyaltyTier: "PLATINUM", isVIP: true },
          { id: "9", name: "Omar Ibrahim", phone: "01090123456", loyaltyPoints: 8700, loyaltyTier: "GOLD", isVIP: true },
          { id: "10", name: "Mariam Samir", phone: "01001234567", loyaltyPoints: 4200, loyaltyTier: "GOLD", isVIP: false }
        ];
        
        // Mock tiers distribution
        const mockTiersDistribution: TierDistribution[] = [
          { tier: "BRONZE", count: 145 },
          { tier: "SILVER", count: 78 },
          { tier: "GOLD", count: 32 },
          { tier: "PLATINUM", count: 12 }
        ];
        
        // Mock points history
        const mockPointsHistory: PointsHistory[] = [
          { month: "Jan", earned: 12500, redeemed: 8200, net: 4300 },
          { month: "Feb", earned: 14200, redeemed: 9500, net: 4700 },
          { month: "Mar", earned: 15800, redeemed: 10200, net: 5600 },
          { month: "Apr", earned: 13600, redeemed: 8900, net: 4700 },
          { month: "May", earned: 16200, redeemed: 11500, net: 4700 },
          { month: "Jun", earned: 18500, redeemed: 12800, net: 5700 },
          { month: "Jul", earned: 17200, redeemed: 11200, net: 6000 },
          { month: "Aug", earned: 19800, redeemed: 13500, net: 6300 },
          { month: "Sep", earned: 21500, redeemed: 14800, net: 6700 },
          { month: "Oct", earned: 23200, redeemed: 15900, net: 7300 },
          { month: "Nov", earned: 25800, redeemed: 17200, net: 8600 },
          { month: "Dec", earned: 28500, redeemed: 19500, net: 9000 }
        ];
        
        // Mock upcoming birthdays
        const mockUpcomingBirthdays: BirthdayCustomer[] = [
          { id: "1", name: "Ahmed Mohamed", phone: "01012345678", email: "<EMAIL>", birthday: "2023-12-15", loyaltyPoints: 5200, loyaltyTier: "GOLD", isVIP: true, daysUntilBirthday: 3 },
          { id: "2", name: "Sara Ali", phone: "01023456789", email: "<EMAIL>", birthday: "2023-12-18", loyaltyPoints: 3800, loyaltyTier: "SILVER", isVIP: false, daysUntilBirthday: 6 },
          { id: "3", name: "Mohamed Ibrahim", phone: "01034567890", email: "<EMAIL>", birthday: "2023-12-20", loyaltyPoints: 2500, loyaltyTier: "SILVER", isVIP: true, daysUntilBirthday: 8 },
          { id: "4", name: "Fatima Hassan", phone: "01045678901", email: "<EMAIL>", birthday: "2023-12-25", loyaltyPoints: 1800, loyaltyTier: "SILVER", isVIP: false, daysUntilBirthday: 13 },
          { id: "5", name: "Ali Mahmoud", phone: "01056789012", email: "<EMAIL>", birthday: "2023-12-28", loyaltyPoints: 950, loyaltyTier: "BRONZE", isVIP: false, daysUntilBirthday: 16 }
        ];
        
        // Filter customers based on search term
        const filteredCustomers = searchTerm 
          ? mockTopCustomers.filter(customer => 
              customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              customer.phone.includes(searchTerm)
            )
          : mockTopCustomers;
        
        // Filter by loyalty tier
        const tierFilteredCustomers = loyaltyTier === "all" 
          ? filteredCustomers 
          : filteredCustomers.filter(customer => customer.loyaltyTier === loyaltyTier);
        
        setTopCustomers(tierFilteredCustomers);
        setTiersDistribution(mockTiersDistribution);
        setPointsHistory(mockPointsHistory);
        setTotalPoints(267000);
        setPointValueInEGP(0.01);
        setUpcomingBirthdays(mockUpcomingBirthdays);
      } catch (error) {
        console.error("Error fetching loyalty data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [dateRange, loyaltyTier, searchTerm]);
  
  // Calculate total value of points
  const totalPointsValue = totalPoints * pointValueInEGP;
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Loyalty Program Reports</h1>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export Report
        </Button>
      </div>
      
      {/* Filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Filters</CardTitle>
            <Select value={timeFrame} onValueChange={setTimeFrame}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time frame" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Last 7 days</SelectItem>
                <SelectItem value="month">This month</SelectItem>
                <SelectItem value="quarter">Last 90 days</SelectItem>
                <SelectItem value="year">This year</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <CardDescription>
            Filter loyalty program data by various criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <div className="flex items-center space-x-2">
                <DatePicker
                  value={dateRange[0] ? new Date(dateRange[0]) : undefined}
                  onChange={(date) => setDateRange([date, dateRange[1]])}
                />
                <span>to</span>
                <DatePicker
                  value={dateRange[1] ? new Date(dateRange[1]) : undefined}
                  onChange={(date) => setDateRange([dateRange[0], date])}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Loyalty Tier</label>
              <Select value={loyaltyTier} onValueChange={setLoyaltyTier}>
                <SelectTrigger>
                  <SelectValue placeholder="Select tier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  <SelectItem value="BRONZE">Bronze</SelectItem>
                  <SelectItem value="SILVER">Silver</SelectItem>
                  <SelectItem value="GOLD">Gold</SelectItem>
                  <SelectItem value="PLATINUM">Platinum</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="Search customers..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Loyalty Points</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{totalPoints.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Points in circulation</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Gift className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Points Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{totalPointsValue.toLocaleString()} ج.م</p>
                <p className="text-sm text-gray-500">Total liability</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">VIP Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{topCustomers.filter(c => c.isVIP).length}</p>
                <p className="text-sm text-gray-500">Out of {topCustomers.length} top customers</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Star className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Upcoming Birthdays</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{upcomingBirthdays.length}</p>
                <p className="text-sm text-gray-500">In the next 30 days</p>
              </div>
              <div className="bg-amber-100 p-3 rounded-full">
                <Calendar className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Loyalty Tier Distribution</CardTitle>
            <CardDescription>
              Breakdown of customers by loyalty tier
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={tiersDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                  nameKey="tier"
                  label={({ tier, percent }) => `${tier}: ${(percent * 100).toFixed(0)}%`}
                >
                  {tiersDistribution.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={TIER_COLORS[entry.tier as keyof typeof TIER_COLORS] || COLORS[index % COLORS.length]} 
                    />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} customers`, 'Count']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Points History</CardTitle>
            <CardDescription>
              Monthly points earned and redeemed
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={pointsHistory}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value.toLocaleString()} points`, '']} />
                <Legend />
                <Area type="monotone" dataKey="earned" stackId="1" stroke="#8884d8" fill="#8884d8" name="Points Earned" />
                <Area type="monotone" dataKey="redeemed" stackId="2" stroke="#82ca9d" fill="#82ca9d" name="Points Redeemed" />
                <Line type="monotone" dataKey="net" stroke="#ff7300" name="Net Change" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
      
      {/* Tabs for detailed data */}
      <Tabs defaultValue="customers" className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="customers">Top Customers</TabsTrigger>
          <TabsTrigger value="birthdays">Upcoming Birthdays</TabsTrigger>
        </TabsList>
        
        <TabsContent value="customers">
          <Card>
            <CardHeader>
              <CardTitle>Top Loyalty Customers</CardTitle>
              <CardDescription>
                Customers with the highest loyalty points
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loyalty Tier</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VIP Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {isLoading ? (
                      <tr>
                        <td colSpan={6} className="px-4 py-4 text-center text-sm text-gray-500">Loading...</td>
                      </tr>
                    ) : topCustomers.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="px-4 py-4 text-center text-sm text-gray-500">No customer data found</td>
                      </tr>
                    ) : (
                      topCustomers.map((customer) => (
                        <tr key={customer.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">{customer.name}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{customer.phone}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">
                            <Badge 
                              style={{ 
                                backgroundColor: TIER_COLORS[customer.loyaltyTier as keyof typeof TIER_COLORS] || '#8884d8',
                                color: customer.loyaltyTier === 'BRONZE' || customer.loyaltyTier === 'PLATINUM' ? '#000' : '#fff'
                              }}
                            >
                              {customer.loyaltyTier}
                            </Badge>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-500">{customer.loyaltyPoints.toLocaleString()}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{(customer.loyaltyPoints * pointValueInEGP).toFixed(2)} ج.م</td>
                          <td className="px-4 py-4 text-sm text-gray-500">
                            {customer.isVIP ? (
                              <Badge className="bg-yellow-100 text-yellow-800">
                                <Star className="h-3 w-3 mr-1" />
                                VIP
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-gray-500">
                                Standard
                              </Badge>
                            )}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="birthdays">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Customer Birthdays</CardTitle>
              <CardDescription>
                Customers with birthdays in the next 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birthday</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Until</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loyalty Tier</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VIP Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {isLoading ? (
                      <tr>
                        <td colSpan={7} className="px-4 py-4 text-center text-sm text-gray-500">Loading...</td>
                      </tr>
                    ) : upcomingBirthdays.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="px-4 py-4 text-center text-sm text-gray-500">No upcoming birthdays found</td>
                      </tr>
                    ) : (
                      upcomingBirthdays.map((customer) => (
                        <tr key={customer.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">{customer.name}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{customer.phone}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{customer.email}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{format(new Date(customer.birthday), 'MMM d, yyyy')}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">
                            <Badge className={customer.daysUntilBirthday <= 7 ? "bg-red-100 text-red-800" : "bg-blue-100 text-blue-800"}>
                              {customer.daysUntilBirthday} days
                            </Badge>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-500">
                            <Badge 
                              style={{ 
                                backgroundColor: TIER_COLORS[customer.loyaltyTier as keyof typeof TIER_COLORS] || '#8884d8',
                                color: customer.loyaltyTier === 'BRONZE' || customer.loyaltyTier === 'PLATINUM' ? '#000' : '#fff'
                              }}
                            >
                              {customer.loyaltyTier}
                            </Badge>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-500">
                            {customer.isVIP ? (
                              <Badge className="bg-yellow-100 text-yellow-800">
                                <Star className="h-3 w-3 mr-1" />
                                VIP
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-gray-500">
                                Standard
                              </Badge>
                            )}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
