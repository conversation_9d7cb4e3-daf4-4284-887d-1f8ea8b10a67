# Configuración del Módulo de Contabilidad

Este documento explica cómo configurar el módulo de contabilidad en el sistema.

## Requisitos previos

Antes de configurar el módulo de contabilidad, asegúrese de que:

1. La base de datos PostgreSQL esté instalada y funcionando
2. La aplicación esté configurada correctamente con las variables de entorno necesarias
3. Tenga permisos de administrador en la base de datos

## Pasos para la configuración

### 1. Ejecutar el script de migración de contabilidad

Hemos creado un script que automatiza todo el proceso de configuración del módulo de contabilidad. Para ejecutarlo, simplemente use el siguiente comando:

```bash
npm run accounting:setup
```

Este script realizará las siguientes acciones:

1. Verificar la conexión a la base de datos
2. Solicitar los detalles de conexión si es necesario
3. Inicializar el módulo con cuentas contables predeterminadas
4. Crear diarios contables predeterminados para cada método de pago
5. Configurar el año fiscal actual y los períodos mensuales

> **Nota**: El script utiliza Prisma directamente para interactuar con la base de datos, por lo que no requiere herramientas externas como `psql`.

### 2. Verificar la configuración

Después de ejecutar el script, puede verificar que todo se haya configurado correctamente accediendo a:

1. **Cuentas contables**: Debería ver un plan de cuentas completo con cuentas de activo, pasivo, patrimonio, ingresos y gastos.
2. **Diarios contables**: Debería ver diarios para cada método de pago (efectivo, Vodafone Cash, transferencia bancaria, tarjeta de crédito) y un diario general.
3. **Año fiscal**: Debería ver el año fiscal actual configurado con períodos mensuales.

## Estructura del módulo de contabilidad

El módulo de contabilidad incluye las siguientes entidades:

### Cuentas (Accounts)

Las cuentas contables representan los diferentes elementos financieros de la empresa. Están organizadas en un plan de cuentas jerárquico con los siguientes tipos:

- **Activos (ASSET)**: Recursos que posee la empresa (efectivo, inventario, etc.)
- **Pasivos (LIABILITY)**: Obligaciones de la empresa (cuentas por pagar, préstamos, etc.)
- **Patrimonio (EQUITY)**: Capital y ganancias retenidas
- **Ingresos (REVENUE)**: Fuentes de ingresos (ventas, servicios, etc.)
- **Gastos (EXPENSE)**: Costos y gastos operativos

### Diarios (Journals)

Los diarios contables son utilizados para registrar transacciones financieras. Cada diario está asociado a un tipo específico de transacción:

- **Diario de efectivo (CASH)**: Para transacciones en efectivo
- **Diario de Vodafone Cash (VODAFONE_CASH)**: Para transacciones con Vodafone Cash
- **Diario de transferencia bancaria (BANK_TRANSFER)**: Para transacciones bancarias
- **Diario de tarjeta de crédito (VISA)**: Para transacciones con tarjeta de crédito
- **Diario de cuenta de cliente (CUSTOMER_ACCOUNT)**: Para transacciones a crédito con clientes
- **Diario general (GENERAL)**: Para otros tipos de transacciones

### Asientos contables (Journal Entries)

Los asientos contables registran movimientos financieros específicos. Cada asiento incluye:

- Una cuenta de débito
- Una cuenta de crédito
- Un monto
- Una descripción
- Una referencia (opcional)
- Una fecha

### Año fiscal y períodos

El módulo también incluye la gestión de años fiscales y períodos contables:

- **Año fiscal**: Representa un año financiero completo
- **Períodos fiscales**: Divisiones del año fiscal (generalmente meses)

## Integración con otros módulos

El módulo de contabilidad se integra con:

1. **Ventas**: Genera asientos contables automáticamente cuando se crea una factura de venta
2. **Compras**: Genera asientos contables automáticamente cuando se crea una factura de compra
3. **Notas de crédito**: Genera asientos contables automáticamente cuando se crea una nota de crédito

## Solución de problemas

Si encuentra algún problema durante la configuración, verifique:

1. **Conexión a la base de datos**: Asegúrese de que la cadena de conexión a la base de datos sea correcta
2. **Permisos**: Asegúrese de tener los permisos necesarios para crear tablas y modificar la base de datos
3. **Registros existentes**: Si ya existen registros en las tablas de contabilidad, el script no los sobrescribirá

Si necesita reiniciar completamente el módulo de contabilidad, puede eliminar todas las tablas relacionadas y volver a ejecutar el script de configuración.

## Próximos pasos

Después de configurar el módulo de contabilidad, puede:

1. Personalizar el plan de cuentas según las necesidades específicas de su empresa
2. Configurar reglas contables para diferentes tipos de transacciones
3. Generar informes financieros como el balance general y el estado de resultados
