import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/inventory/transfers - Get all inventory transfers
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view inventory
    const hasViewPermission = await hasPermission("view_products") ||
                             await hasPermission("view_inventory") ||
                             session.user.role === "ADMIN";
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view inventory transfers" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const warehouseId = url.searchParams.get("warehouseId");
    const productId = url.searchParams.get("productId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");

    // Build filter object
    const filter: any = {};

    // Add warehouse filter if provided
    if (warehouseId) {
      filter.OR = [
        { sourceWarehouseId: warehouseId },
        { destinationWarehouseId: warehouseId }
      ];
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      filter.date = {};
      if (startDate) {
        filter.date.gte = new Date(startDate);
      }
      if (endDate) {
        filter.date.lte = new Date(endDate);
      }
    }

    // Get transfers from database
    const transfers = await db.inventoryTransfer.findMany({
      where: filter,
      include: {
        sourceWarehouse: true,
        destinationWarehouse: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true
              }
            }
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // If productId is provided, filter the transfers to only include those with the specified product
    let filteredTransfers = transfers;
    if (productId) {
      filteredTransfers = transfers.filter(transfer => 
        transfer.items.some(item => item.productId === productId)
      );
    }

    // Format the response
    const formattedTransfers = filteredTransfers.map(transfer => ({
      id: transfer.id,
      referenceNumber: transfer.referenceNumber,
      date: transfer.date,
      sourceWarehouse: {
        id: transfer.sourceWarehouse.id,
        name: transfer.sourceWarehouse.name
      },
      destinationWarehouse: {
        id: transfer.destinationWarehouse.id,
        name: transfer.destinationWarehouse.name
      },
      user: transfer.user,
      notes: transfer.notes,
      items: transfer.items.map(item => ({
        id: item.id,
        product: {
          id: item.product.id,
          name: item.product.name,
          category: item.product.category.name
        },
        quantity: item.quantity
      })),
      createdAt: transfer.createdAt,
      updatedAt: transfer.updatedAt
    }));

    return NextResponse.json(formattedTransfers);
  } catch (error) {
    console.error("Error fetching inventory transfers:", error);
    return NextResponse.json(
      { error: "Failed to fetch inventory transfers" },
      { status: 500 }
    );
  }
}

// POST /api/inventory/transfers - Create a new inventory transfer
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to add inventory
    const hasAddPermission = await hasPermission("add_products") ||
                            session.user.role === "ADMIN";
    if (!hasAddPermission) {
      return NextResponse.json(
        { error: "You don't have permission to create inventory transfers" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.sourceWarehouseId) {
      return NextResponse.json(
        { error: "Source warehouse is required" },
        { status: 400 }
      );
    }

    if (!data.destinationWarehouseId) {
      return NextResponse.json(
        { error: "Destination warehouse is required" },
        { status: 400 }
      );
    }

    if (data.sourceWarehouseId === data.destinationWarehouseId) {
      return NextResponse.json(
        { error: "Source and destination warehouses cannot be the same" },
        { status: 400 }
      );
    }

    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { error: "At least one product is required" },
        { status: 400 }
      );
    }

    // Validate each item
    for (const item of data.items) {
      if (!item.productId) {
        return NextResponse.json(
          { error: "Product ID is required for each item" },
          { status: 400 }
        );
      }

      if (!item.quantity || item.quantity <= 0) {
        return NextResponse.json(
          { error: "Quantity must be greater than zero for each item" },
          { status: 400 }
        );
      }

      // Check if product exists
      const product = await db.product.findUnique({
        where: {
          id: item.productId
        }
      });

      if (!product) {
        return NextResponse.json(
          { error: `Product with ID ${item.productId} not found` },
          { status: 400 }
        );
      }

      // Check if there's enough inventory in the source warehouse
      const inventory = await db.inventory.findFirst({
        where: {
          productId: item.productId,
          warehouseId: data.sourceWarehouseId
        }
      });

      if (!inventory || inventory.quantity < item.quantity) {
        const productName = product.name;
        const availableQuantity = inventory ? inventory.quantity : 0;
        return NextResponse.json(
          { error: `Not enough inventory for product "${productName}". Available: ${availableQuantity}, Requested: ${item.quantity}` },
          { status: 400 }
        );
      }
    }

    // Generate reference number
    const referenceNumber = await generateReferenceNumber();

    // Create inventory transfer
    const inventoryTransfer = await db.inventoryTransfer.create({
      data: {
        referenceNumber,
        sourceWarehouseId: data.sourceWarehouseId,
        destinationWarehouseId: data.destinationWarehouseId,
        date: data.date ? new Date(data.date) : new Date(),
        notes: data.notes || "",
        userId: session.user.id,
        items: {
          create: data.items.map((item: any) => ({
            productId: item.productId,
            quantity: item.quantity
          }))
        }
      },
      include: {
        sourceWarehouse: true,
        destinationWarehouse: true,
        items: {
          include: {
            product: true
          }
        }
      }
    });

    // Update inventory quantities
    for (const item of data.items) {
      // Reduce quantity from source warehouse
      const sourceInventory = await db.inventory.findFirst({
        where: {
          productId: item.productId,
          warehouseId: data.sourceWarehouseId
        }
      });

      if (sourceInventory) {
        await db.inventory.update({
          where: {
            id: sourceInventory.id
          },
          data: {
            quantity: sourceInventory.quantity - item.quantity
          }
        });
      }

      // Add quantity to destination warehouse
      const destInventory = await db.inventory.findFirst({
        where: {
          productId: item.productId,
          warehouseId: data.destinationWarehouseId
        }
      });

      if (destInventory) {
        await db.inventory.update({
          where: {
            id: destInventory.id
          },
          data: {
            quantity: destInventory.quantity + item.quantity
          }
        });
      } else {
        // Create new inventory entry if it doesn't exist
        await db.inventory.create({
          data: {
            productId: item.productId,
            warehouseId: data.destinationWarehouseId,
            quantity: item.quantity,
            costPrice: sourceInventory?.costPrice || 0
          }
        });
      }
    }

    // Format the response
    const formattedTransfer = {
      id: inventoryTransfer.id,
      referenceNumber: inventoryTransfer.referenceNumber,
      date: inventoryTransfer.date,
      sourceWarehouse: {
        id: inventoryTransfer.sourceWarehouse.id,
        name: inventoryTransfer.sourceWarehouse.name
      },
      destinationWarehouse: {
        id: inventoryTransfer.destinationWarehouse.id,
        name: inventoryTransfer.destinationWarehouse.name
      },
      notes: inventoryTransfer.notes,
      items: inventoryTransfer.items.map(item => ({
        id: item.id,
        product: {
          id: item.product.id,
          name: item.product.name
        },
        quantity: item.quantity
      })),
      createdAt: inventoryTransfer.createdAt,
      updatedAt: inventoryTransfer.updatedAt
    };

    return NextResponse.json(formattedTransfer);
  } catch (error) {
    console.error("Error creating inventory transfer:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create inventory transfer" },
      { status: 500 }
    );
  }
}

// Helper function to generate a reference number
async function generateReferenceNumber() {
  // Get the count of existing transfers
  const count = await db.inventoryTransfer.count();
  
  // Generate a reference number in the format TR-00001
  const referenceNumber = `TR-${(count + 1).toString().padStart(5, '0')}`;
  
  return referenceNumber;
}
