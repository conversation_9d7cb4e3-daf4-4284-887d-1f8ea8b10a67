import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { exec } from "child_process";
import { promisify } from "util";

const execPromise = promisify(exec);

// POST /api/system/init-database - Initialize the database
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only allow admins to initialize the database
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only administrators can initialize the database" },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();
    const { action } = data;

    if (!action) {
      return NextResponse.json(
        { error: "Action is required" },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case "check":
        // Check database connection and schema
        try {
          const { stdout: checkOutput, stderr: checkError } = await execPromise("npx prisma db pull");
          result = {
            success: true,
            message: "Database connection successful",
            details: {
              output: checkOutput,
              error: checkError || null,
            },
          };
        } catch (error) {
          result = {
            success: false,
            message: "Database connection failed",
            details: {
              error: error instanceof Error ? error.message : "Unknown error",
            },
          };
        }
        break;

      case "migrate":
        // Run migrations
        try {
          const { stdout: migrateOutput, stderr: migrateError } = await execPromise("npx prisma migrate deploy");
          result = {
            success: true,
            message: "Database migrations applied successfully",
            details: {
              output: migrateOutput,
              error: migrateError || null,
            },
          };
        } catch (error) {
          result = {
            success: false,
            message: "Database migrations failed",
            details: {
              error: error instanceof Error ? error.message : "Unknown error",
            },
          };
        }
        break;

      case "reset":
        // Reset database (dangerous!)
        try {
          const { stdout: resetOutput, stderr: resetError } = await execPromise("npx prisma migrate reset --force");
          result = {
            success: true,
            message: "Database reset successfully",
            details: {
              output: resetOutput,
              error: resetError || null,
            },
          };
        } catch (error) {
          result = {
            success: false,
            message: "Database reset failed",
            details: {
              error: error instanceof Error ? error.message : "Unknown error",
            },
          };
        }
        break;

      case "seed":
        // Seed database with initial data
        try {
          const { stdout: seedOutput, stderr: seedError } = await execPromise("npx prisma db seed");
          result = {
            success: true,
            message: "Database seeded successfully",
            details: {
              output: seedOutput,
              error: seedError || null,
            },
          };
        } catch (error) {
          result = {
            success: false,
            message: "Database seeding failed",
            details: {
              error: error instanceof Error ? error.message : "Unknown error",
            },
          };
        }
        break;

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error initializing database:", error);
    return NextResponse.json(
      { error: "Failed to initialize database", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}

// GET /api/system/init-database - Get database status
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check database connection
    let databaseStatus = {
      connected: false,
      error: null,
      tables: [],
      migrations: [],
    };

    try {
      // Check if database is accessible
      const { stdout: dbPullOutput, stderr: dbPullError } = await execPromise("npx prisma db pull --print");
      
      databaseStatus.connected = true;
      
      // Extract table names from the output
      const tableMatches = dbPullOutput.match(/model\s+(\w+)\s+{/g);
      if (tableMatches) {
        databaseStatus.tables = tableMatches.map(match => {
          const tableName = match.match(/model\s+(\w+)\s+{/)?.[1];
          return tableName || "";
        }).filter(Boolean);
      }
      
      // Get migration status
      const { stdout: migrationOutput } = await execPromise("npx prisma migrate status");
      
      // Extract migration information
      const migrationMatches = migrationOutput.match(/(\d+_\w+)\s+\|\s+(\w+)/g);
      if (migrationMatches) {
        databaseStatus.migrations = migrationMatches.map(match => {
          const [migrationName, status] = match.split(/\s+\|\s+/);
          return { name: migrationName.trim(), status: status.trim() };
        });
      }
    } catch (error) {
      databaseStatus.error = error instanceof Error ? error.message : "Unknown error";
    }

    return NextResponse.json({
      success: databaseStatus.connected,
      status: databaseStatus,
      user: {
        email: session.user.email,
        role: session.user.role,
      },
    });
  } catch (error) {
    console.error("Error getting database status:", error);
    return NextResponse.json(
      { error: "Failed to get database status", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
