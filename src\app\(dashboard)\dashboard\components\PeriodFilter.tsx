"use client";

import { motion } from "framer-motion";

interface PeriodFilterProps {
  period: string;
  onChange: (period: string) => void;
}

export default function PeriodFilter({ period, onChange }: PeriodFilterProps) {
  const periods = [
    { id: "today", label: "Today" },
    { id: "week", label: "This Week" },
    { id: "month", label: "This Month" },
    { id: "year", label: "This Year" },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex space-x-2 mb-6"
    >
      {periods.map((p) => (
        <button
          key={p.id}
          onClick={() => onChange(p.id)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
            period === p.id
              ? "bg-indigo-600 text-white shadow-md"
              : "bg-white text-gray-700 hover:bg-gray-100"
          }`}
        >
          {p.label}
        </button>
      ))}
    </motion.div>
  );
}
