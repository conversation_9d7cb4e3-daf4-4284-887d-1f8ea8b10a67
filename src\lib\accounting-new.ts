import { db } from "@/lib/db";

// Transaction types
export enum TransactionType {
  SALE = "SALE",
  PURCHASE = "PURCHASE",
  CREDIT_NOTE = "CREDIT_NOTE",
  PAYMENT = "PAYMENT",
  RECEIPT = "RECEIPT",
  ADJUSTMENT = "ADJUSTMENT",
  MANUAL = "MANUAL"
}

// Payment info interface
export interface PaymentInfo {
  method: string;
  amount: number;
  date?: Date;
  journalId?: string;
}

// Transaction info interface
export interface TransactionInfo {
  transactionType: TransactionType;
  referenceId: string;
  referenceNumber: string;
  description: string;
  contactId?: string;
  contactName?: string;
  branchId?: string;
  date: Date;
}

/**
 * Get the journal for a payment method
 * @param methodCode The payment method code
 * @returns The journal for the payment method
 */
export async function getPaymentMethodJournal(methodCode: string) {
  try {
    console.log(`Getting journal for payment method: ${methodCode}`);

    // First, try to get the journal from PaymentMethodSettings
    const paymentMethodSetting = await db.paymentMethodSettings.findFirst({
      where: {
        code: methodCode,
        isActive: true,
      },
      include: {
        journal: true,
      },
    });

    console.log(`Payment method setting found:`, paymentMethodSetting);

    if (paymentMethodSetting?.journal) {
      console.log(`Using journal from payment method settings:`, paymentMethodSetting.journal);
      return paymentMethodSetting.journal;
    }

    console.log(`No journal found in payment method settings, falling back to default journals`);

    // Fallback to default journals
    let journal;
    switch (methodCode) {
      case "CASH":
        journal = await db.journal.findFirst({
          where: {
            code: "CASH",
          },
        });
        break;
      case "VODAFONE_CASH":
        journal = await db.journal.findFirst({
          where: {
            code: "VFCASH",
          },
        });
        break;
      case "BANK_TRANSFER":
        journal = await db.journal.findFirst({
          where: {
            code: "BANK",
          },
        });
        break;
      case "VISA":
      case "CREDIT_CARD":
        journal = await db.journal.findFirst({
          where: {
            code: "VISA",
          },
        });
        break;
      case "CUSTOMER_ACCOUNT":
        journal = await db.journal.findFirst({
          where: {
            code: "CUST",
          },
        });
        break;
      default:
        journal = await db.journal.findFirst({
          where: {
            code: "GENERAL",
          },
        });
    }

    if (!journal) {
      // Create a default journal if none exists
      journal = await db.journal.create({
        data: {
          name: `${methodCode} Journal`,
          code: methodCode.substring(0, 5),
          type: methodCode,
          isActive: true,
        },
      });
    }

    console.log(`Using journal for ${methodCode}:`, journal);
    return journal;
  } catch (error) {
    console.error(`Error getting journal for payment method ${methodCode}:`, error);
    throw error;
  }
}

/**
 * Get the account for a payment method
 * @param methodCode The payment method code
 * @returns The account for the payment method
 */
export async function getPaymentMethodAccount(methodCode: string) {
  try {
    console.log(`Getting account for payment method: ${methodCode}`);

    // First, try to get the account from PaymentMethodSettings
    const paymentMethodSetting = await db.paymentMethodSettings.findFirst({
      where: {
        code: methodCode,
        isActive: true,
      },
      include: {
        account: true,
      },
    });

    console.log(`Payment method setting found:`, paymentMethodSetting);

    if (paymentMethodSetting?.account) {
      console.log(`Using account from payment method settings:`, paymentMethodSetting.account);
      return paymentMethodSetting.account;
    }

    console.log(`No account found in payment method settings, falling back to hardcoded accounts`);

    // Fallback to hardcoded accounts for backward compatibility
    let account;
    switch (methodCode) {
      case "CASH":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1000", // Cash account
          },
        });
        break;
      case "VODAFONE_CASH":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1010", // Vodafone Cash account
          },
        });
        break;
      case "BANK_TRANSFER":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1020", // Bank account
          },
        });
        break;
      case "VISA":
      case "CREDIT_CARD":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1030", // Visa account
          },
        });
        break;
      case "CUSTOMER_ACCOUNT":
        account = await db.account.findFirst({
          where: {
            type: "ASSET",
            code: "1100", // Accounts Receivable account
          },
        });
        break;
      default:
        throw new Error(`Unsupported payment method: ${methodCode}`);
    }

    console.log(`Using hardcoded account for ${methodCode}:`, account);
    return account;
  } catch (error) {
    console.error(`Error getting account for payment method ${methodCode}:`, error);
    throw error;
  }
}

/**
 * Create journal entries for a sale
 * @param payments The payments for the sale
 * @param totalAmount The total amount of the sale
 * @param subtotalAmount The subtotal amount of the sale
 * @param transactionInfo The transaction information
 * @returns The created journal entries
 */
export async function createSaleJournalEntries(
  payments: PaymentInfo[],
  totalAmount: number,
  subtotalAmount: number,
  transactionInfo: TransactionInfo
) {
  try {
    console.log(`Creating sale journal entries for ${transactionInfo.referenceNumber}`);

    // Ensure required accounts exist
    await ensureAccountsExist();

    const journalEntries = [];

    // Get the sales revenue account
    const salesRevenueAccount = await db.account.findFirst({
      where: {
        type: "REVENUE",
        code: "4000", // Sales Revenue account
      },
    });

    if (!salesRevenueAccount) {
      throw new Error("Sales revenue account not found");
    }

    // Get the accounts receivable account
    const accountsReceivableAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1100", // Accounts Receivable account
      },
    });

    if (!accountsReceivableAccount) {
      throw new Error("Accounts Receivable account not found");
    }

    // Process each payment
    for (const payment of payments) {
      // Skip customer account payments as they're handled separately
      if (payment.method === "CUSTOMER_ACCOUNT") {
        continue;
      }

      // Get the payment method account
      const paymentMethodAccount = await getPaymentMethodAccount(payment.method);
      if (!paymentMethodAccount) {
        throw new Error(`Account for payment method ${payment.method} not found`);
      }

      // Get the payment method journal
      const journal = await getPaymentMethodJournal(payment.method);
      if (!journal) {
        throw new Error(`Journal for payment method ${payment.method} not found`);
      }

      // Create journal entry for the payment
      const journalEntry = await createJournalEntry({
        journalId: journal.id,
        description: `Payment for ${transactionInfo.description}`,
        debitAccountId: paymentMethodAccount.id, // Debit the payment method account (e.g., Cash)
        creditAccountId: salesRevenueAccount.id, // Credit the sales revenue account
        amount: payment.amount,
        date: payment.date || transactionInfo.date,
        reference: transactionInfo.referenceId,
        referenceType: transactionInfo.transactionType,
        contactId: transactionInfo.contactId,
      });

      journalEntries.push(journalEntry);
    }

    // Calculate total paid amount
    const totalPaid = payments.reduce((sum, payment) => {
      // Only include non-customer account payments
      if (payment.method !== "CUSTOMER_ACCOUNT") {
        return sum + payment.amount;
      }
      return sum;
    }, 0);

    // If there's an unpaid amount, create a journal entry for accounts receivable
    const unpaidAmount = totalAmount - totalPaid;
    if (unpaidAmount > 0) {
      // Get the general journal
      const generalJournal = await db.journal.findFirst({
        where: {
          code: "GENERAL",
        },
      });

      if (!generalJournal) {
        throw new Error("General journal not found");
      }

      // Create journal entry for accounts receivable
      const arJournalEntry = await createJournalEntry({
        journalId: generalJournal.id,
        description: `Accounts receivable for ${transactionInfo.description}`,
        debitAccountId: accountsReceivableAccount.id, // Debit accounts receivable
        creditAccountId: salesRevenueAccount.id, // Credit sales revenue
        amount: unpaidAmount,
        date: transactionInfo.date,
        reference: transactionInfo.referenceId,
        referenceType: transactionInfo.transactionType,
        contactId: transactionInfo.contactId,
      });

      journalEntries.push(arJournalEntry);
    }

    return journalEntries;
  } catch (error) {
    console.error("Error creating sale journal entries:", error);
    throw error;
  }
}

/**
 * Create a journal entry
 * @param data The journal entry data
 * @returns The created journal entry
 */
export async function createJournalEntry(data: {
  journalId: string;
  description: string;
  debitAccountId: string;
  creditAccountId: string;
  amount: number;
  date?: Date;
  reference?: string;
  referenceType?: string;
  contactId?: string;
}) {
  try {
    console.log(`Creating journal entry: ${data.description}`);

    // Get the journal
    const journal = await db.journal.findUnique({
      where: {
        id: data.journalId,
      },
    });

    if (!journal) {
      throw new Error(`Journal not found: ${data.journalId}`);
    }

    // Get the last entry number for this journal
    const lastEntry = await db.journalEntry.findFirst({
      where: {
        journalId: data.journalId,
      },
      orderBy: {
        entryNumber: 'desc',
      },
    });

    // Generate entry number
    let entryNumber = "1";
    if (lastEntry && lastEntry.entryNumber) {
      const lastEntryNumber = parseInt(lastEntry.entryNumber);
      if (!isNaN(lastEntryNumber)) {
        entryNumber = (lastEntryNumber + 1).toString();
      }
    }

    // Get current fiscal period
    const currentDate = data.date || new Date();
    const fiscalPeriod = await db.fiscalPeriod.findFirst({
      where: {
        startDate: { lte: currentDate },
        endDate: { gte: currentDate },
        isClosed: false,
      },
    });

    console.log(`Creating journal entry with data:`, {
      journalId: data.journalId,
      entryNumber,
      description: data.description,
      debitAccountId: data.debitAccountId,
      creditAccountId: data.creditAccountId,
      amount: data.amount,
      date: currentDate,
      fiscalPeriodId: fiscalPeriod?.id,
    });

    // Create the journal entry
    const journalEntry = await db.journalEntry.create({
      data: {
        journalId: data.journalId,
        entryNumber,
        date: currentDate,
        description: data.description,
        debitAccountId: data.debitAccountId,
        creditAccountId: data.creditAccountId,
        amount: data.amount,
        reference: data.reference || null,
        referenceType: data.referenceType || null,
        contactId: data.contactId || null,
        fiscalPeriodId: fiscalPeriod?.id || null,
        isPosted: true,
      },
    });

    console.log(`Journal entry created:`, journalEntry);

    // Update account balances
    await db.account.update({
      where: { id: data.debitAccountId },
      data: { balance: { increment: data.amount } },
    });

    await db.account.update({
      where: { id: data.creditAccountId },
      data: { balance: { decrement: data.amount } },
    });

    return journalEntry;
  } catch (error) {
    console.error("Error creating journal entry:", error);
    throw error;
  }
}

/**
 * Create journal entries for a purchase
 * @param payments The payments for the purchase
 * @param totalAmount The total amount of the purchase
 * @param subtotalAmount The subtotal amount of the purchase
 * @param transactionInfo The transaction information
 * @returns The created journal entries
 */
export async function createPurchaseJournalEntries(
  payments: PaymentInfo[],
  totalAmount: number,
  subtotalAmount: number,
  transactionInfo: TransactionInfo
) {
  try {
    console.log(`Creating purchase journal entries for ${transactionInfo.referenceNumber}`);

    // Ensure required accounts exist
    await ensureAccountsExist();

    const journalEntries = [];

    // Get the inventory account
    const inventoryAccount = await db.account.findFirst({
      where: {
        type: "ASSET",
        code: "1200", // Inventory account
      },
    });

    if (!inventoryAccount) {
      throw new Error("Inventory account not found");
    }

    // Get the accounts payable account
    const accountsPayableAccount = await db.account.findFirst({
      where: {
        type: "LIABILITY",
        code: "2000", // Accounts Payable account
      },
    });

    if (!accountsPayableAccount) {
      throw new Error("Accounts Payable account not found");
    }

    // Process each payment
    for (const payment of payments) {
      // Skip supplier account payments as they're handled separately
      if (payment.method === "SUPPLIER_ACCOUNT") {
        continue;
      }

      // Get the payment method account
      const paymentMethodAccount = await getPaymentMethodAccount(payment.method);
      if (!paymentMethodAccount) {
        throw new Error(`Account for payment method ${payment.method} not found`);
      }

      // Get the payment method journal
      const journal = await getPaymentMethodJournal(payment.method);
      if (!journal) {
        throw new Error(`Journal for payment method ${payment.method} not found`);
      }

      // Create journal entry for the payment
      const journalEntry = await createJournalEntry({
        journalId: journal.id,
        description: `Payment for ${transactionInfo.description}`,
        debitAccountId: inventoryAccount.id, // Debit the inventory account
        creditAccountId: paymentMethodAccount.id, // Credit the payment method account (e.g., Cash)
        amount: payment.amount,
        date: payment.date || transactionInfo.date,
        reference: transactionInfo.referenceId,
        referenceType: transactionInfo.transactionType,
        contactId: transactionInfo.contactId,
      });

      journalEntries.push(journalEntry);
    }

    // Calculate total paid amount
    const totalPaid = payments.reduce((sum, payment) => {
      // Only include non-supplier account payments
      if (payment.method !== "SUPPLIER_ACCOUNT") {
        return sum + payment.amount;
      }
      return sum;
    }, 0);

    // If there's an unpaid amount, create a journal entry for accounts payable
    const unpaidAmount = totalAmount - totalPaid;
    if (unpaidAmount > 0) {
      // Get the general journal
      const generalJournal = await db.journal.findFirst({
        where: {
          code: "GENERAL",
        },
      });

      if (!generalJournal) {
        throw new Error("General journal not found");
      }

      // Create journal entry for accounts payable
      const apJournalEntry = await createJournalEntry({
        journalId: generalJournal.id,
        description: `Accounts payable for ${transactionInfo.description}`,
        debitAccountId: inventoryAccount.id, // Debit inventory
        creditAccountId: accountsPayableAccount.id, // Credit accounts payable
        amount: unpaidAmount,
        date: transactionInfo.date,
        reference: transactionInfo.referenceId,
        referenceType: transactionInfo.transactionType,
        contactId: transactionInfo.contactId,
      });

      journalEntries.push(apJournalEntry);
    }

    return journalEntries;
  } catch (error) {
    console.error("Error creating purchase journal entries:", error);
    throw error;
  }
}

/**
 * Ensure that all required accounts exist in the system
 * @returns The created or found accounts
 */
export async function ensureAccountsExist() {
  try {
    const accounts = [];

    // Define the required accounts
    const requiredAccounts = [
      { code: "1000", name: "Cash", type: "ASSET" },
      { code: "1010", name: "Vodafone Cash", type: "ASSET" },
      { code: "1020", name: "Bank Account", type: "ASSET" },
      { code: "1030", name: "Visa", type: "ASSET" },
      { code: "1100", name: "Accounts Receivable", type: "ASSET" },
      { code: "1200", name: "Inventory", type: "ASSET" },
      { code: "2000", name: "Accounts Payable", type: "LIABILITY" },
      { code: "3000", name: "Owner's Equity", type: "EQUITY" },
      { code: "4000", name: "Sales Revenue", type: "REVENUE" },
      { code: "4100", name: "Sales Returns", type: "REVENUE" },
      { code: "5000", name: "Cost of Goods Sold", type: "EXPENSE" },
      { code: "5100", name: "Inventory Adjustment", type: "EXPENSE" },
      { code: "6000", name: "Operating Expenses", type: "EXPENSE" },
    ];

    // Check if each account exists, create if it doesn't
    for (const account of requiredAccounts) {
      let existingAccount = await db.account.findFirst({
        where: {
          OR: [
            { code: account.code },
            { name: account.name, type: account.type },
          ],
        },
      });

      if (!existingAccount) {
        existingAccount = await db.account.create({
          data: {
            code: account.code,
            name: account.name,
            type: account.type,
            balance: 0,
            isActive: true,
          },
        });
      }

      accounts.push(existingAccount);
    }

    // Ensure journals exist
    await ensureJournalsExist();

    return accounts;
  } catch (error) {
    console.error("Error ensuring accounts exist:", error);
    throw error;
  }
}

/**
 * Ensure that all required journals exist in the system
 * @returns The created or found journals
 */
export async function ensureJournalsExist() {
  try {
    const journals = [];

    // Define the required journals
    const requiredJournals = [
      { code: "CASH", name: "Cash Journal", type: "CASH" },
      { code: "VFCASH", name: "Vodafone Cash Journal", type: "VODAFONE_CASH" },
      { code: "BANK", name: "Bank Journal", type: "BANK_TRANSFER" },
      { code: "VISA", name: "Credit Card Journal", type: "VISA" },
      { code: "CUST", name: "Customer Account Journal", type: "CUSTOMER_ACCOUNT" },
      { code: "GENERAL", name: "General Journal", type: "GENERAL" },
    ];

    // Check if each journal exists, create if it doesn't
    for (const journal of requiredJournals) {
      let existingJournal = await db.journal.findFirst({
        where: {
          OR: [
            { code: journal.code },
            { name: journal.name, type: journal.type },
          ],
        },
      });

      if (!existingJournal) {
        existingJournal = await db.journal.create({
          data: {
            code: journal.code,
            name: journal.name,
            type: journal.type,
            isActive: true,
          },
        });
      }

      journals.push(existingJournal);
    }

    return journals;
  } catch (error) {
    console.error("Error ensuring journals exist:", error);
    throw error;
  }
}
