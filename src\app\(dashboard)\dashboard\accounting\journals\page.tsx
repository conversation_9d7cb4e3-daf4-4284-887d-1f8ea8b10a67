"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { DatePicker } from "@/components/ui/date-picker";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select-radix";
import {
  Loader2, Plus, Search, RefreshCw, Edit, Trash2,
  CreditCard, Wallet, Building, DollarSign, FileText,
  BarChart3, ArrowRight, ArrowDown, ArrowUp, Filter, X
} from "lucide-react";
import { format, subDays, startOfMonth } from "date-fns";
import JournalEntryForm from "./components/JournalEntryForm";
import { toast } from "sonner";

interface Journal {
  id: string;
  code: string;
  name: string;
  type: string;
  paymentMethod: string | null;
  isActive: boolean;
  balance?: number;
}

interface JournalEntry {
  id: string;
  date: string;
  description: string;
  amount: number;
  journal: {
    id: string;
    code: string;
    name: string;
  };
  debitAccount: {
    id: string;
    code: string;
    name: string;
  };
  creditAccount: {
    id: string;
    code: string;
    name: string;
  };
  reference: string | null;
  contact: {
    id: string;
    name: string;
  } | null;
}

interface JournalSummary {
  id: string;
  code: string;
  name: string;
  type: string;
  balance: number;
  entryCount: number;
  totalAmount: number;
}

export default function JournalsPage() {
  const [activeTab, setActiveTab] = useState("payment-journals");
  const [activeJournalType, setActiveJournalType] = useState<string | null>(null);
  const [journals, setJournals] = useState<Journal[]>([]);
  const [journalSummaries, setJournalSummaries] = useState<JournalSummary[]>([]);
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [isLoadingJournals, setIsLoadingJournals] = useState(false);
  const [isLoadingEntries, setIsLoadingEntries] = useState(false);
  const [isLoadingSummaries, setIsLoadingSummaries] = useState(false);
  const [selectedJournalId, setSelectedJournalId] = useState<string>("");
  const [startDate, setStartDate] = useState<Date | undefined>(startOfMonth(new Date())); // First day of current month
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [showEntryForm, setShowEntryForm] = useState(false);
  const [period, setPeriod] = useState<string>("month");
  const [isInitializingJournals, setIsInitializingJournals] = useState(false);

  // Fetch journals on component mount
  useEffect(() => {
    fetchJournals();
    fetchJournalSummaries();
  }, []);

  // Update date range when period changes
  useEffect(() => {
    const now = new Date();

    switch (period) {
      case "day":
        setStartDate(now);
        setEndDate(now);
        break;
      case "week":
        setStartDate(subDays(now, 7));
        setEndDate(now);
        break;
      case "month":
        setStartDate(startOfMonth(now));
        setEndDate(now);
        break;
      case "year":
        setStartDate(new Date(now.getFullYear(), 0, 1));
        setEndDate(now);
        break;
    }
  }, [period]);

  // Fetch journals
  const fetchJournals = async () => {
    setIsLoadingJournals(true);
    try {
      const response = await fetch("/api/accounting/journals");
      if (response.ok) {
        const data = await response.json();
        setJournals(data.data || []);
      }
    } catch (error) {
      console.error("Error fetching journals:", error);
    } finally {
      setIsLoadingJournals(false);
    }
  };

  // Fetch journal summaries
  const fetchJournalSummaries = async () => {
    setIsLoadingSummaries(true);
    try {
      const response = await fetch("/api/accounting/dashboard");
      if (response.ok) {
        const data = await response.json();
        setJournalSummaries(data.journalSummaries || []);
      }
    } catch (error) {
      console.error("Error fetching journal summaries:", error);
    } finally {
      setIsLoadingSummaries(false);
    }
  };

  // Fetch journal entries
  const fetchEntries = async () => {
    setIsLoadingEntries(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (selectedJournalId) {
        params.append("journalId", selectedJournalId);
      }

      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }

      if (endDate) {
        params.append("endDate", endDate.toISOString());
      }

      const response = await fetch(`/api/accounting/journals/entries?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setEntries(data.data || []);
      }
    } catch (error) {
      console.error("Error fetching journal entries:", error);
    } finally {
      setIsLoadingEntries(false);
    }
  };

  // Get journal type display name
  const getJournalTypeDisplay = (type: string) => {
    switch (type) {
      case "CASH":
        return "Cash / النقدية";
      case "VODAFONE_CASH":
        return "Vodafone Cash / فودافون كاش";
      case "BANK_TRANSFER":
        return "Bank Transfer / تحويل بنكي";
      case "VISA":
        return "Credit Card / بطاقة ائتمان";
      case "CUSTOMER_ACCOUNT":
        return "Customer Account / حساب العميل";
      case "GENERAL":
        return "General / عام";
      default:
        return type;
    }
  };

  // Get journal type icon
  const getJournalTypeIcon = (type: string) => {
    switch (type) {
      case "CASH":
        return <DollarSign className="h-6 w-6 text-green-600" />;
      case "VODAFONE_CASH":
        return <Wallet className="h-6 w-6 text-red-600" />;
      case "BANK_TRANSFER":
        return <Building className="h-6 w-6 text-blue-600" />;
      case "VISA":
        return <CreditCard className="h-6 w-6 text-purple-600" />;
      case "CUSTOMER_ACCOUNT":
        return <FileText className="h-6 w-6 text-amber-600" />;
      case "GENERAL":
        return <BarChart3 className="h-6 w-6 text-gray-600" />;
      default:
        return <FileText className="h-6 w-6 text-gray-600" />;
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Get payment journals
  const getPaymentJournals = () => {
    return journals.filter(journal =>
      ["CASH", "VODAFONE_CASH", "BANK_TRANSFER", "VISA"].includes(journal.type)
    );
  };

  // Get other journals
  const getOtherJournals = () => {
    return journals.filter(journal =>
      !["CASH", "VODAFONE_CASH", "BANK_TRANSFER", "VISA"].includes(journal.type)
    );
  };

  // Get journal summary
  const getJournalSummary = (journalId: string) => {
    return journalSummaries.find(summary => summary.id === journalId);
  };

  // Initialize journals
  const initializeJournals = async () => {
    setIsInitializingJournals(true);
    try {
      const response = await fetch("/api/accounting/journals/initialize", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        // Refresh journals list
        await fetchJournals();
        await fetchJournalSummaries();
        toast.success("Journals initialized successfully! / تم تهيئة اليوميات بنجاح!");
      } else {
        const error = await response.json();
        toast.error(`Error initializing journals: ${error.error}`);
      }
    } catch (error) {
      console.error("Error initializing journals:", error);
      toast.error("Failed to initialize journals. Please try again.");
    } finally {
      setIsInitializingJournals(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Accounting Journals</h1>
          <p className="text-gray-500 mt-1">يوميات المحاسبة</p>
        </div>
        <div className="flex space-x-2">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-md">
            <Button
              variant={period === "day" ? "default" : "ghost"}
              size="sm"
              onClick={() => setPeriod("day")}
              className={period === "day" ? "bg-white shadow-sm" : ""}
            >
              Day / يوم
            </Button>
            <Button
              variant={period === "week" ? "default" : "ghost"}
              size="sm"
              onClick={() => setPeriod("week")}
              className={period === "week" ? "bg-white shadow-sm" : ""}
            >
              Week / أسبوع
            </Button>
            <Button
              variant={period === "month" ? "default" : "ghost"}
              size="sm"
              onClick={() => setPeriod("month")}
              className={period === "month" ? "bg-white shadow-sm" : ""}
            >
              Month / شهر
            </Button>
            <Button
              variant={period === "year" ? "default" : "ghost"}
              size="sm"
              onClick={() => setPeriod("year")}
              className={period === "year" ? "bg-white shadow-sm" : ""}
            >
              Year / سنة
            </Button>
          </div>
          <Button
            variant="outline"
            onClick={initializeJournals}
            disabled={isInitializingJournals}
            className="mr-2 border-blue-200 text-blue-700 hover:bg-blue-50"
          >
            {isInitializingJournals ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Initialize Journals / تهيئة اليوميات
          </Button>
          <Button
            onClick={() => setShowEntryForm(!showEntryForm)}
            className={`bg-gradient-to-r ${showEntryForm ? 'from-red-500 to-red-700 hover:from-red-600 hover:to-red-800' : 'from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600'}`}
          >
            {showEntryForm ? (
              <>
                <X className="h-4 w-4 mr-2" />
                Hide Entry Form / إخفاء النموذج
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create Journal Entry / إنشاء قيد محاسبي
              </>
            )}
          </Button>
        </div>
      </div>

      {showEntryForm && (
        <JournalEntryForm />
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
        <TabsList className="grid w-full grid-cols-3 p-1 bg-gray-100 rounded-lg">
          <TabsTrigger
            value="payment-journals"
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <div className="flex flex-col items-center">
              <div className="flex items-center">
                <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                <span>Payment Journals</span>
              </div>
              <span className="text-xs text-gray-500">يوميات الدفع</span>
            </div>
          </TabsTrigger>
          <TabsTrigger
            value="other-journals"
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <div className="flex flex-col items-center">
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-1 text-amber-600" />
                <span>Other Journals</span>
              </div>
              <span className="text-xs text-gray-500">يوميات أخرى</span>
            </div>
          </TabsTrigger>
          <TabsTrigger
            value="entries"
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <div className="flex flex-col items-center">
              <div className="flex items-center">
                <BarChart3 className="h-4 w-4 mr-1 text-blue-600" />
                <span>Journal Entries</span>
              </div>
              <span className="text-xs text-gray-500">قيود اليومية</span>
            </div>
          </TabsTrigger>
        </TabsList>

        {/* Payment Journals Tab */}
        <TabsContent value="payment-journals" className="mt-4">
          {/* Payment Method Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {/* Cash Journal */}
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="p-3 bg-green-100 rounded-full">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-500">Cash / النقدية</p>
                    <h3 className="text-2xl font-bold mt-1">
                      {formatCurrency(
                        journals.find(j => j.type === "CASH")?.balance || 0
                      )}
                    </h3>
                  </div>
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      setActiveTab("entries");
                      setSelectedJournalId(journals.find(j => j.type === "CASH")?.id || "");
                      fetchEntries();
                    }}
                  >
                    View Transactions
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Vodafone Cash Journal */}
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="p-3 bg-red-100 rounded-full">
                    <Wallet className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-500">Vodafone Cash / فودافون كاش</p>
                    <h3 className="text-2xl font-bold mt-1">
                      {formatCurrency(
                        journals.find(j => j.type === "VODAFONE_CASH")?.balance || 0
                      )}
                    </h3>
                  </div>
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      setActiveTab("entries");
                      setSelectedJournalId(journals.find(j => j.type === "VODAFONE_CASH")?.id || "");
                      fetchEntries();
                    }}
                  >
                    View Transactions
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Bank Transfer Journal */}
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="p-3 bg-blue-100 rounded-full">
                    <Building className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-500">Bank Transfer / تحويل بنكي</p>
                    <h3 className="text-2xl font-bold mt-1">
                      {formatCurrency(
                        journals.find(j => j.type === "BANK_TRANSFER")?.balance || 0
                      )}
                    </h3>
                  </div>
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      setActiveTab("entries");
                      setSelectedJournalId(journals.find(j => j.type === "BANK_TRANSFER")?.id || "");
                      fetchEntries();
                    }}
                  >
                    View Transactions
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Visa Journal */}
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="p-3 bg-purple-100 rounded-full">
                    <CreditCard className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-500">Credit Card / بطاقة ائتمان</p>
                    <h3 className="text-2xl font-bold mt-1">
                      {formatCurrency(
                        journals.find(j => j.type === "VISA")?.balance || 0
                      )}
                    </h3>
                  </div>
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      setActiveTab("entries");
                      setSelectedJournalId(journals.find(j => j.type === "VISA")?.id || "");
                      fetchEntries();
                    }}
                  >
                    View Transactions
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Payment Journals / يوميات الدفع</CardTitle>
              <CardDescription>
                Manage cash, Vodafone Cash, bank transfers, and credit card journals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingJournals ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading journals...</span>
                </div>
              ) : (
                <div className="rounded-md border overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="bg-gray-100 border-b">
                          <th className="px-4 py-3 text-left">Code</th>
                          <th className="px-4 py-3 text-left">Name</th>
                          <th className="px-4 py-3 text-left">Type</th>
                          <th className="px-4 py-3 text-right">Balance</th>
                          <th className="px-4 py-3 text-center">Status</th>
                          <th className="px-4 py-3 text-center">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {getPaymentJournals().map(journal => {
                          const summary = getJournalSummary(journal.id);
                          return (
                            <tr key={journal.id} className="border-b hover:bg-gray-50">
                              <td className="px-4 py-3">{journal.code}</td>
                              <td className="px-4 py-3">{journal.name}</td>
                              <td className="px-4 py-3 flex items-center">
                                {getJournalTypeIcon(journal.type)}
                                <span className="ml-2">{getJournalTypeDisplay(journal.type)}</span>
                              </td>
                              <td className="px-4 py-3 text-right font-medium">
                                {formatCurrency(journal.balance || 0)}
                              </td>
                              <td className="px-4 py-3 text-center">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${journal.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                  {journal.isActive ? "Active" : "Inactive"}
                                </span>
                              </td>
                              <td className="px-4 py-3 text-center">
                                <div className="flex justify-center space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setActiveTab("entries");
                                      setSelectedJournalId(journal.id);
                                      fetchEntries();
                                    }}
                                  >
                                    View Entries
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Other Journals Tab */}
        <TabsContent value="other-journals" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Other Journals / يوميات أخرى</CardTitle>
              <CardDescription>
                Manage general journals and customer account journals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingJournals ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading journals...</span>
                </div>
              ) : (
                <div className="rounded-md border overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="bg-gray-100 border-b">
                          <th className="px-4 py-3 text-left">Code</th>
                          <th className="px-4 py-3 text-left">Name</th>
                          <th className="px-4 py-3 text-left">Type</th>
                          <th className="px-4 py-3 text-right">Balance</th>
                          <th className="px-4 py-3 text-center">Status</th>
                          <th className="px-4 py-3 text-center">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {getOtherJournals().map(journal => {
                          const summary = getJournalSummary(journal.id);
                          return (
                            <tr key={journal.id} className="border-b hover:bg-gray-50">
                              <td className="px-4 py-3">{journal.code}</td>
                              <td className="px-4 py-3">{journal.name}</td>
                              <td className="px-4 py-3 flex items-center">
                                {getJournalTypeIcon(journal.type)}
                                <span className="ml-2">{getJournalTypeDisplay(journal.type)}</span>
                              </td>
                              <td className="px-4 py-3 text-right font-medium">
                                {formatCurrency(journal.balance || 0)}
                              </td>
                              <td className="px-4 py-3 text-center">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${journal.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                  {journal.isActive ? "Active" : "Inactive"}
                                </span>
                              </td>
                              <td className="px-4 py-3 text-center">
                                <div className="flex justify-center space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setActiveTab("entries");
                                      setSelectedJournalId(journal.id);
                                      fetchEntries();
                                    }}
                                  >
                                    View Entries
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Journal Entries Tab */}
        <TabsContent value="entries" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Journal Entries / قيود اليومية</CardTitle>
              <CardDescription>
                View and search journal entries by journal, date range, and other filters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Card className="mb-4 border-blue-100">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center">
                    <Filter className="h-5 w-5 mr-2 text-blue-500" />
                    Filter Journal Entries / تصفية القيود المحاسبية
                  </CardTitle>
                  <CardDescription>
                    Select filters to view specific journal entries / اختر عوامل التصفية لعرض قيود محاسبية محددة
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Journal / اليومية</label>
                      <Select value={selectedJournalId} onValueChange={setSelectedJournalId}>
                        <SelectTrigger>
                          <SelectValue placeholder="All Journals / كل اليوميات" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Journals / كل اليوميات</SelectItem>
                          {journals.map(journal => (
                            <SelectItem key={journal.id} value={journal.id}>
                              {journal.code} - {journal.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Start Date / تاريخ البداية</label>
                      <DatePicker date={startDate} setDate={setStartDate} />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">End Date / تاريخ النهاية</label>
                      <DatePicker date={endDate} setDate={setEndDate} />
                    </div>

                    <div className="flex items-end">
                      <Button
                        onClick={fetchEntries}
                        disabled={isLoadingEntries}
                        className="w-full bg-gradient-to-r from-blue-500 to-blue-700"
                      >
                        {isLoadingEntries ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Search className="h-4 w-4 mr-2" />
                        )}
                        Search / بحث
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {isLoadingEntries ? (
                <div className="flex items-center justify-center p-8">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
                    <span className="text-gray-500 block">Loading journal entries...</span>
                    <span className="text-gray-500 block">جاري تحميل القيود المحاسبية...</span>
                  </div>
                </div>
              ) : entries.length > 0 ? (
                <div className="rounded-md border overflow-hidden shadow-sm">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="bg-gradient-to-r from-blue-50 to-green-50 border-b">
                          <th className="px-4 py-3 text-left font-medium text-gray-700">Date / التاريخ</th>
                          <th className="px-4 py-3 text-left font-medium text-gray-700">Journal / اليومية</th>
                          <th className="px-4 py-3 text-left font-medium text-gray-700">Description / الوصف</th>
                          <th className="px-4 py-3 text-left font-medium text-gray-700">Debit Account / حساب مدين</th>
                          <th className="px-4 py-3 text-left font-medium text-gray-700">Credit Account / حساب دائن</th>
                          <th className="px-4 py-3 text-right font-medium text-gray-700">Amount / المبلغ</th>
                          <th className="px-4 py-3 text-left font-medium text-gray-700">Reference / المرجع</th>
                        </tr>
                      </thead>
                      <tbody>
                        {entries.map((entry, index) => (
                          <tr
                            key={entry.id}
                            className={`border-b hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                          >
                            <td className="px-4 py-3">{format(new Date(entry.date), "yyyy-MM-dd")}</td>
                            <td className="px-4 py-3">
                              <div className="flex items-center">
                                {getJournalTypeIcon(journals.find(j => j.id === entry.journal.id)?.type || "")}
                                <span className="ml-2">{entry.journal.code}</span>
                              </div>
                            </td>
                            <td className="px-4 py-3 max-w-xs truncate" title={entry.description}>
                              {entry.description}
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                                <span>{entry.debitAccount.code} - {entry.debitAccount.name}</span>
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                                <span>{entry.creditAccount.code} - {entry.creditAccount.name}</span>
                              </div>
                            </td>
                            <td className="px-4 py-3 text-right font-medium">{formatCurrency(entry.amount)}</td>
                            <td className="px-4 py-3">{entry.reference || "-"}</td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className="bg-gray-100 font-medium">
                          <td colSpan={5} className="px-4 py-3 text-right">Total Entries:</td>
                          <td className="px-4 py-3 text-right">{entries.length}</td>
                          <td></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center p-8 bg-gray-50 rounded-md border border-dashed border-gray-300">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">No journal entries found</h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    Try adjusting your filters or create a new journal entry.
                    <br />
                    لم يتم العثور على قيود. حاول تعديل عوامل التصفية أو إنشاء قيد جديد.
                  </p>
                  <Button
                    className="mt-4"
                    variant="outline"
                    onClick={() => {
                      setSelectedJournalId("");
                      setStartDate(startOfMonth(new Date()));
                      setEndDate(new Date());
                      fetchEntries();
                    }}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset Filters
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
