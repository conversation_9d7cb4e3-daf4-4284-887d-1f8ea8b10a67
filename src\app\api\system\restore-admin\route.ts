import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import bcrypt from "bcryptjs";

// Function to check if user is admin
async function isAdmin(req: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return false;
  }

  // Check if user has admin role
  return session.user.role === "ADMIN";
}

// POST /api/system/restore-admin - Restore admin user
export async function POST(req: NextRequest) {
  try {
    // Get a secret token from the request headers
    const authHeader = req.headers.get('authorization');
    const secretToken = process.env.ADMIN_RESTORE_SECRET || 'vero-admin-restore-secret-token';

    // Check if user is admin or if the request includes the correct secret token
    const adminExists = await db.user.findFirst({
      where: {
        role: "ADMIN",
      },
    });

    // If admin exists, require admin authentication
    if (adminExists) {
      const isUserAdmin = await isAdmin(req);

      // If not admin, check for secret token
      if (!isUserAdmin) {
        // If no valid token, reject the request
        if (!authHeader || authHeader !== `Bearer ${secretToken}`) {
          return NextResponse.json(
            { error: "Unauthorized. Admin access required." },
            { status: 401 }
          );
        }

        // Log that the secret token was used
        console.log("Admin restore secret token was used to bypass authentication");
      }
    } else {
      // Even if no admin exists, still require the secret token for security
      if (!authHeader || authHeader !== `Bearer ${secretToken}`) {
        return NextResponse.json(
          { error: "Unauthorized. Secret token required for initial admin creation." },
          { status: 401 }
        );
      }

      console.log("Admin restore secret token was used for initial admin creation");
    }

    // Check if admin user exists
    const adminUser = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    });

    if (adminUser) {
      console.log("Admin user already exists");

      // Check if admin user has permissions
      const adminPermissions = await db.permission.findMany({
        where: {
          users: {
            some: {
              id: adminUser.id
            }
          }
        }
      });

      if (adminPermissions.length === 0) {
        console.log("Admin user has no permissions. Adding permissions...");

        // Get all permissions
        const allPermissions = await db.permission.findMany();

        // Connect all permissions to admin user
        await db.user.update({
          where: {
            id: adminUser.id,
          },
          data: {
            permissions: {
              connect: allPermissions.map(p => ({ id: p.id })),
            },
          },
        });

        console.log(`Added ${allPermissions.length} permissions to admin user`);
      } else {
        console.log(`Admin user has ${adminPermissions.length} permissions`);
      }

      return NextResponse.json({
        message: "Admin user verified and permissions updated if needed",
        action: "updated"
      });
    }

    console.log("Admin user not found. Creating a new admin user...");

    // Find a branch or create one
    let branch = await db.branch.findFirst();

    if (!branch) {
      console.log("No branch found. Creating a new branch...");
      branch = await db.branch.create({
        data: {
          name: "Main Branch",
          code: "A",
          address: "Cairo, Egypt",
          phone: "01000000000",
        },
      });
      console.log(`Created new branch: ${branch.name}`);
    }

    // Create default permissions
    const defaultPermissions = [
      { name: "view_users", description: "View users" },
      { name: "add_users", description: "Add new users" },
      { name: "edit_users", description: "Edit existing users" },
      { name: "delete_users", description: "Delete users" },
      { name: "view_branches", description: "View branches" },
      { name: "add_branches", description: "Add new branches" },
      { name: "edit_branches", description: "Edit existing branches" },
      { name: "delete_branches", description: "Delete branches" },
      { name: "view_warehouses", description: "View warehouses" },
      { name: "add_warehouses", description: "Add new warehouses" },
      { name: "edit_warehouses", description: "Edit existing warehouses" },
      { name: "delete_warehouses", description: "Delete warehouses" },
      { name: "view_products", description: "View products" },
      { name: "add_products", description: "Add new products" },
      { name: "edit_products", description: "Edit existing products" },
      { name: "delete_products", description: "Delete products" },
      { name: "view_inventory", description: "View inventory" },
      { name: "add_inventory", description: "Add inventory" },
      { name: "edit_inventory", description: "Edit inventory" },
      { name: "delete_inventory", description: "Delete inventory" },
      { name: "view_sales", description: "View sales" },
      { name: "add_sales", description: "Add new sales" },
      { name: "edit_sales", description: "Edit existing sales" },
      { name: "delete_sales", description: "Delete sales" },
      { name: "view_purchases", description: "View purchases" },
      { name: "add_purchases", description: "Add new purchases" },
      { name: "edit_purchases", description: "Edit existing purchases" },
      { name: "delete_purchases", description: "Delete purchases" },
      { name: "view_contacts", description: "View contacts" },
      { name: "add_contacts", description: "Add new contacts" },
      { name: "edit_contacts", description: "Edit existing contacts" },
      { name: "delete_contacts", description: "Delete contacts" },
      { name: "view_reports", description: "View reports" },
      { name: "view_settings", description: "View settings" },
      { name: "edit_settings", description: "Edit settings" },
    ];

    // Create permissions
    const createdPermissions = [];
    for (const permission of defaultPermissions) {
      // Check if permission already exists
      const existingPermission = await db.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        const newPermission = await db.permission.create({
          data: permission,
        });
        createdPermissions.push(newPermission);
        console.log(`Created permission: ${permission.name}`);
      } else {
        createdPermissions.push(existingPermission);
        console.log(`Permission already exists: ${permission.name}`);
      }
    }

    // Create a new admin user
    const hashedPassword = await bcrypt.hash("admin123", 10);
    const newAdmin = await db.user.create({
      data: {
        name: "Admin User",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "ADMIN",
        branchId: branch.id,
        isActive: true,
        permissions: {
          connect: createdPermissions.map(p => ({ id: p.id })),
        },
      },
    });

    // Create a warehouse if none exists
    const warehouse = await db.warehouse.findFirst({
      where: {
        branchId: branch.id,
      },
    });

    if (!warehouse) {
      const newWarehouse = await db.warehouse.create({
        data: {
          name: "Main Warehouse",
          branchId: branch.id,
          isActive: true,
        },
      });

      // Connect admin to warehouse
      await db.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: newWarehouse.id,
        },
      });
    } else {
      // Connect admin to existing warehouse
      await db.userWarehouse.create({
        data: {
          userId: newAdmin.id,
          warehouseId: warehouse.id,
        },
      });
    }

    console.log("Admin user created successfully");
    console.log("Email: <EMAIL>");
    console.log("Password: admin123");

    return NextResponse.json({
      message: "Admin user created successfully",
      action: "created",
      credentials: {
        email: "<EMAIL>",
        password: "admin123"
      }
    });
  } catch (error) {
    console.error("Error restoring admin user:", error);
    return NextResponse.json(
      { error: "Failed to restore admin user" },
      { status: 500 }
    );
  }
}
