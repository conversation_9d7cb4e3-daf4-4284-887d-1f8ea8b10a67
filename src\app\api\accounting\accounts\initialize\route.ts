import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import { ensureAccountsExist } from "@/lib/accounting";

// POST /api/accounting/accounts/initialize - Initialize basic accounts
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // For now, allow any authenticated user to initialize accounts
    // Later, we can add proper permission checks
    // const hasManagePermission = await hasPermission("manage_accounts");
    // if (!hasManagePermission) {
    //   return NextResponse.json(
    //     { error: "You don't have permission to initialize accounts" },
    //     { status: 403 }
    //   );
    // }

    // Initialize accounts
    const accounts = await ensureAccountsExist();

    return NextResponse.json({
      success: true,
      message: "Accounts initialized successfully",
      data: accounts,
    });
  } catch (error: any) {
    console.error("Error initializing accounts:", error);
    return NextResponse.json(
      { error: error.message || "Failed to initialize accounts" },
      { status: 500 }
    );
  }
}
