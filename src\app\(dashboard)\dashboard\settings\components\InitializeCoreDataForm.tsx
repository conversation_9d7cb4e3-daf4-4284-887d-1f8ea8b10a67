"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, RefreshCw } from "lucide-react";
import { toast } from "sonner";

export default function InitializeCoreDataForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleInitializeCoreData = async () => {
    if (!confirm("Are you sure you want to initialize core data? This will ensure all required data exists in the system.")) {
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch("/api/system/initialize-core-data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to initialize core data");
      }

      setResults(data.results);
      toast.success(data.message || "Core data initialized successfully");
    } catch (error) {
      console.error("Error initializing core data:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
      toast.error("Failed to initialize core data");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Initialize Core Data</CardTitle>
        <CardDescription>
          Ensure all required data exists in the system for proper functionality.
          This will create missing branches, warehouses, permissions, users, journals, and payment methods.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Button
              onClick={handleInitializeCoreData}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Initializing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Initialize Core Data
                </>
              )}
            </Button>
          </div>

          {error && (
            <div className="p-4 bg-red-50 text-red-800 rounded-md">
              <p className="font-medium">Error:</p>
              <p>{error}</p>
            </div>
          )}

          {results && (
            <div className="p-4 bg-green-50 text-green-800 rounded-md">
              <p className="font-medium">Success! The following data was initialized:</p>
              <ul className="mt-2 space-y-1 list-disc list-inside">
                <li>
                  Branches: {results.branch.created} created, {results.branch.existing} already existed
                </li>
                <li>
                  Warehouses: {results.warehouse.created} created, {results.warehouse.existing} already existed
                </li>
                <li>
                  Permissions: {results.permissions.created} created, {results.permissions.existing} already existed
                </li>
                <li>
                  Admin User: {results.admin.created} created, {results.admin.existing} already existed, {results.admin.updated} updated
                </li>
                <li>
                  Journals: {results.journals.created} created, {results.journals.existing} already existed
                </li>
                <li>
                  Payment Methods: {results.paymentMethods.created} created, {results.paymentMethods.existing} already existed
                </li>
              </ul>
            </div>
          )}

          <div className="text-sm text-gray-500 mt-4">
            <p>
              <strong>Note:</strong> This tool is safe to run multiple times. It will only create data that doesn't already exist.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
