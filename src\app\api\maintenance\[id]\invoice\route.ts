import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { generateInvoiceNumber } from "@/lib/utils";

// Helper function to find or create a default product for custom services
async function findOrCreateDefaultProduct(db: any, userId: string, branchId: string): Promise<string> {
  // Try to find an existing default product for custom services
  const defaultProduct = await db.product.findFirst({
    where: {
      name: "خدمة صيانة",
      description: "خدمات الصيانة (إيرادات أخرى)",
    },
  });

  if (defaultProduct) {
    return defaultProduct.id;
  }

  // If no default product exists, create one
  try {
    // First, find or create a default category for services
    let serviceCategory = await db.category.findFirst({
      where: {
        name: "خدما<PERSON>",
      },
    });

    if (!serviceCategory) {
      serviceCategory = await db.category.create({
        data: {
          name: "خدما<PERSON>",
          description: "خدمات الصيانة والإصلاح (إيرادات أخرى)",
        },
      });
    }

    // Create the default product
    const newProduct = await db.product.create({
      data: {
        name: "خدمة صيانة",
        description: "خدمات الصيانة (إيرادات أخرى)",
        basePrice: 0,
        costPrice: 0,
        categoryId: serviceCategory.id,
      },
    });

    // No need to create inventory for service products
    // Services are not physical items and don't need inventory

    return newProduct.id;
  } catch (error) {
    console.error("Error creating default product:", error);
    throw new Error("Failed to create default product for custom services");
  }
}

// No longer needed as we don't create inventory for service products

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const maintenanceId = params.id;

    // Check if maintenance service exists
    const service = await db.maintenanceService.findUnique({
      where: { id: maintenanceId },
      include: {
        contact: true,
        branch: true,
        parts: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!service) {
      return NextResponse.json(
        { error: "Maintenance service not found" },
        { status: 404 }
      );
    }

    // Check if invoice already exists
    if (service.invoiceId) {
      return NextResponse.json(
        { error: "Invoice already exists for this service" },
        { status: 400 }
      );
    }

    // Generate invoice number
    const invoiceNumber = await generateInvoiceNumber(service.branchId);

    // Calculate subtotal from all parts
    const subtotalAmount = service.parts.reduce(
      (sum, part) => sum + part.totalPrice,
      0
    );

    // Separate parts into those with productId and those without
    const partsWithProductId = service.parts.filter(part => part.productId);
    const customParts = service.parts.filter(part => !part.productId);

    // Get default warehouse for the branch
    const defaultWarehouse = await db.warehouse.findFirst({
      where: { branchId: service.branchId },
    });

    if (!defaultWarehouse && partsWithProductId.length > 0) {
      return NextResponse.json(
        { error: "No warehouse found for this branch" },
        { status: 400 }
      );
    }

    // Use finalCost if available, otherwise use subtotal
    const totalAmount = service.finalCost || subtotalAmount;

    // Check if there are parts
    if (!service.parts || service.parts.length === 0) {
      return NextResponse.json(
        { error: "Cannot generate invoice: No parts added to this service" },
        { status: 400 }
      );
    }

    // We'll allow custom parts without productId, but we need to handle them differently

    // Create sale (invoice)
    try {
      const sale = await db.sale.create({
        data: {
          userId: session.user.id,
          branchId: service.branchId,
          contactId: service.contactId,
          date: new Date(),
          status: "COMPLETED",
          invoiceNumber,
          subtotalAmount,
          totalAmount,
          applyTax: false,
          taxAmount: 0,
          taxRate: 0,
          discountAmount: 0,
          paymentMethod: service.paymentMethod || "CASH",
          paymentStatus: service.paymentStatus || "UNPAID",
          notes: `Generated from maintenance service ${service.serviceNumber}. Note: Maintenance services are categorized as "Other Income" (إيرادات أخرى) while parts from inventory are "Sales Income" (إيراد مبيعات).`,
          items: {
            create: [
              // Parts with productId
              ...partsWithProductId.map(part => ({
                productId: part.productId,
                quantity: part.quantity,
                unitPrice: part.unitPrice,
                totalPrice: part.totalPrice,
                warehouseId: defaultWarehouse?.id, // Use default warehouse for all products
              })),

              // For custom parts (services) without productId, create separate line items for each service
              // These will be categorized as "Other Income" (إيرادات أخرى)
              ...(await Promise.all(customParts.map(async (part) => {
                const serviceProductId = await findOrCreateDefaultProduct(db, session.user.id, service.branchId);
                return {
                  productId: serviceProductId,
                  quantity: part.quantity,
                  unitPrice: part.unitPrice,
                  totalPrice: part.totalPrice,
                  isCustomized: true,
                  specifications: part.partName, // Use the part name as the specification
                };
              }))),
            ],
          },
        },
      });

      // Update maintenance service with invoice reference
      await db.maintenanceService.update({
        where: { id: maintenanceId },
        data: {
          invoiceId: sale.id,
          invoiceNumber: sale.invoiceNumber,
        },
      });

      return NextResponse.json({
        success: true,
        invoiceId: sale.id,
        invoiceNumber: sale.invoiceNumber,
      });
    } catch (err: any) {
      console.error("Error creating sale or updating maintenance service:", err);
      return NextResponse.json(
        { error: `Failed to generate invoice: ${err.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Error generating invoice:", error);
    return NextResponse.json(
      { error: `Failed to generate invoice: ${error.message || "Unknown error"}` },
      { status: 500 }
    );
  }
}
