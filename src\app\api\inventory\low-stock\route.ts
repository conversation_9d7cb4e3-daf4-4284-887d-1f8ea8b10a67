import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/inventory/low-stock - Get low stock items
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view inventory
    const hasViewPermission = await hasPermission("view_inventory") || 
                             await hasPermission("view_products") ||
                             session.user.role === "ADMIN";
    
    if (!hasViewPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view inventory" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const warehouseId = url.searchParams.get("warehouseId");
    const categoryId = url.searchParams.get("categoryId");
    const threshold = parseInt(url.searchParams.get("threshold") || "5");
    
    // Build the query
    const query: any = {
      where: {
        quantity: {
          lte: threshold
        }
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            basePrice: true,
            costPrice: true,
            categoryId: true,
            category: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        warehouse: {
          select: {
            id: true,
            name: true,
            branchId: true,
            branch: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        quantity: 'asc'
      }
    };

    // Add warehouse filter if provided
    if (warehouseId) {
      query.where.warehouseId = warehouseId;
    }

    // Add category filter if provided
    if (categoryId) {
      query.where.product = {
        categoryId: categoryId
      };
    }

    // Get low stock items
    const lowStockItems = await db.inventory.findMany(query);

    // Format the response
    const formattedItems = lowStockItems.map(item => ({
      id: item.id,
      productId: item.productId,
      productName: item.product.name,
      warehouseId: item.warehouseId,
      warehouseName: item.warehouse.name,
      branchId: item.warehouse.branchId,
      branchName: item.warehouse.branch.name,
      quantity: item.quantity,
      categoryId: item.product.categoryId,
      categoryName: item.product.category?.name || "Uncategorized",
      costPrice: item.costPrice || item.product.costPrice,
      basePrice: item.product.basePrice,
    }));

    // Group by warehouse for better organization
    const groupedByWarehouse = formattedItems.reduce((acc, item) => {
      if (!acc[item.warehouseName]) {
        acc[item.warehouseName] = {
          warehouseId: item.warehouseId,
          warehouseName: item.warehouseName,
          branchName: item.branchName,
          items: []
        };
      }
      
      acc[item.warehouseName].items.push(item);
      return acc;
    }, {});

    return NextResponse.json({
      lowStockItems: formattedItems,
      groupedByWarehouse: Object.values(groupedByWarehouse),
      count: formattedItems.length,
      threshold
    });
  } catch (error) {
    console.error("Error fetching low stock items:", error);
    return NextResponse.json(
      { error: "Failed to fetch low stock items" },
      { status: 500 }
    );
  }
}
