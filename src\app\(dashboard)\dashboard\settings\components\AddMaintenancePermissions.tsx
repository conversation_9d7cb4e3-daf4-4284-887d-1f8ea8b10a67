"use client";

import { useState } from "react";

export default function AddMaintenancePermissions() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleAddMaintenancePermissions = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch("/api/system/add-maintenance-permissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        try {
          const data = await response.json();

          if (response.ok) {
            setResult({
              success: true,
              message: data.message || "Maintenance permissions added successfully",
            });
          } else {
            setResult({
              success: false,
              message: data.error || "Failed to add maintenance permissions",
            });
          }
        } catch (jsonError) {
          console.error("Error parsing JSON:", jsonError);
          setResult({
            success: false,
            message: "Error parsing server response",
          });
        }
      } else {
        // Handle non-JSON response
        const textResponse = await response.text();
        console.error("Non-JSON response:", textResponse);
        setResult({
          success: false,
          message: "Server returned an invalid response format",
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: "An error occurred while adding maintenance permissions",
      });
      console.error("Error adding maintenance permissions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">
          Maintenance Permissions
        </h3>
        <div className="mt-2 max-w-xl text-sm text-gray-500">
          <p>
            Add maintenance permissions to the system. This will create all necessary permissions
            for the maintenance module and assign them to admin users.
          </p>
        </div>
        <div className="mt-5">
          <button
            type="button"
            onClick={handleAddMaintenancePermissions}
            disabled={isLoading}
            className={`inline-flex items-center justify-center px-4 py-2 border border-transparent font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
              isLoading ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isLoading ? "Adding Permissions..." : "Add Maintenance Permissions"}
          </button>
        </div>

        {result && (
          <div
            className={`mt-4 p-4 rounded-md ${
              result.success ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="flex">
              <div className="flex-shrink-0">
                {result.success ? (
                  <svg
                    className="h-5 w-5 text-green-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="h-5 w-5 text-red-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p
                  className={`text-sm font-medium ${
                    result.success ? "text-green-800" : "text-red-800"
                  }`}
                >
                  {result.message}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
