import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/payment-methods/:id - Get payment method by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounting data
    const hasViewPermission = await hasPermission("view_accounting");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view payment method details" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Get payment method with associated journal and account
    const paymentMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id,
      },
      include: {
        journal: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
          },
        },
        account: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
            balance: true,
          },
        },
      },
    });

    if (!paymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: paymentMethod,
    });
  } catch (error) {
    console.error("Error fetching payment method:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch payment method" },
      { status: 500 }
    );
  }
}

// PUT /api/accounting/payment-methods/:id - Update payment method
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to update payment methods" },
        { status: 403 }
      );
    }

    const id = params.id;
    const data = await req.json();

    // Check if payment method exists
    const existingPaymentMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id,
      },
    });

    if (!existingPaymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    // Update payment method
    const updatedPaymentMethod = await db.paymentMethodSettings.update({
      where: {
        id,
      },
      data: {
        name: data.name || existingPaymentMethod.name,
        code: data.code || existingPaymentMethod.code,
        iconName: data.iconName || existingPaymentMethod.iconName,
        color: data.color || existingPaymentMethod.color,
        isActive: data.isActive !== undefined ? data.isActive : existingPaymentMethod.isActive,
        accountId: data.accountId || existingPaymentMethod.accountId,
        journalId: data.journalId || existingPaymentMethod.journalId,
      },
      include: {
        journal: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
          },
        },
        account: {
          select: {
            id: true,
            code: true,
            name: true,
            type: true,
            balance: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedPaymentMethod,
    });
  } catch (error) {
    console.error("Error updating payment method:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update payment method" },
      { status: 500 }
    );
  }
}

// DELETE /api/accounting/payment-methods/:id - Delete payment method
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to delete payment methods" },
        { status: 403 }
      );
    }

    const id = params.id;

    // Check if payment method exists
    const existingPaymentMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id,
      },
    });

    if (!existingPaymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    // Delete payment method
    await db.paymentMethodSettings.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Payment method deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting payment method:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to delete payment method" },
      { status: 500 }
    );
  }
}
