# Product Customization Components

This directory contains components for customizing products in the sales module. These components provide a user-friendly interface for configuring customizable products like computers and laptops.

## Components Overview

### ProductCustomizationWizard

A step-by-step wizard for customizing products with multiple components.

**Features:**
- Multi-step interface for organized product configuration
- Component selection for different product parts (CPU, RAM, storage, etc.)
- Real-time price calculation
- Support for custom pricing
- Preview of the final customized product

**Props:**
- `isOpen`: <PERSON><PERSON><PERSON> to control the visibility of the wizard
- `onClose`: Function to call when the wizard is closed
- `product`: Product object with base information and available components
- `onConfirm`: Function to call when customization is confirmed

**Usage:**
```tsx
<ProductCustomizationWizard
  isOpen={isWizardModalOpen}
  onClose={() => setIsWizardModalOpen(false)}
  product={productToCustomize}
  onConfirm={handleCustomizedProduct}
/>
```

### ProductPreview

A visual preview of the customized product with 3D rotation effect.

**Features:**
- Interactive 3D-like rotation effect on mouse movement
- Display of selected components
- Automatic calculation of total RAM capacity
- Display of storage configurations
- Responsive design for different screen sizes

**Props:**
- `product`: Base product information
- `selectedComponents`: Object containing the selected components
- `totalPrice`: The calculated total price
- `className`: Optional CSS class for styling

**Usage:**
```tsx
<ProductPreview
  product={product}
  selectedComponents={selectedComponents}
  totalPrice={totalPrice}
  className="mt-4"
/>
```

### DraggableComponentSelector

A drag-and-drop interface for selecting components.

**Features:**
- Drag-and-drop functionality for intuitive component selection
- Visual feedback during drag operations
- Display of component details (price, specifications)
- Indication of out-of-stock components
- Easy removal of selected components

**Props:**
- `componentType`: Type of component being selected (e.g., "CPU", "RAM")
- `components`: Array of available components
- `selectedComponent`: Currently selected component (if any)
- `onSelect`: Function to call when a component is selected or removed
- `className`: Optional CSS class for styling

**Usage:**
```tsx
<DraggableComponentSelector
  componentType="CPU"
  components={availableComponents.CPU}
  selectedComponent={selectedComponents.CPU}
  onSelect={(component) => handleComponentChange("CPU", component)}
  className="mb-4"
/>
```

## Type Definitions

### Component

Represents a product component like CPU, RAM, etc.

```typescript
interface Component {
  id: string;
  name: string;
  type: ComponentType;
  price: number;
  stock: number;
  capacity?: string; // For storage and RAM
  speed?: string; // For RAM and CPU
  description?: string;
  count?: number; // For multiple RAM sticks
  imageUrl?: string; // For component image
}
```

### Product

Represents a customizable product.

```typescript
interface Product {
  id: string;
  name: string;
  basePrice: number;
  costPrice: number;
  isCustomizable: boolean;
  imageUrl?: string; // For product image
  availableComponents?: {
    [key in ComponentType]?: Component[];
  };
}
```

### CustomizedProduct

Represents a product after customization.

```typescript
interface CustomizedProduct {
  productId: string;
  productName: string;
  basePrice: number;
  costPrice: number;
  totalPrice: number;
  selectedComponents: SelectedComponentsMap;
  editingItemId?: string;
}
```

## Performance Considerations

- The components use React's `useState` and `useEffect` hooks for efficient state management
- Framer Motion is used for smooth animations with minimal performance impact
- Components are designed to handle large numbers of available components efficiently
- Type guards are implemented to ensure type safety and prevent runtime errors

## Testing

Each component has corresponding test files:
- `ProductCustomizationWizard.test.tsx`
- `ProductPreview.test.tsx`
- `DraggableComponentSelector.test.tsx`

Run tests with:
```bash
npm test
```

## Future Improvements

- Add support for more component types
- Implement component compatibility checking
- Add more detailed product visualization
- Support for saving and loading customization templates
- Performance optimizations for very large component catalogs
