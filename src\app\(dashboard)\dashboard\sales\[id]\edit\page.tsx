'use client';

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Save } from 'lucide-react';
import { format } from 'date-fns';

// Import components separately to avoid any potential issues
import CustomizeProductModal from "../../components/CustomizeProductModal";
import type { CustomizedProduct } from "../../components/CustomizeProductModal";
import ProductCustomizationWizard from "../../components/ProductCustomizationWizard";

// Define types for data
interface Contact {
  id: string;
  name: string;
  phone: string;
  isCustomer: boolean;
}

interface Branch {
  id: string;
  name: string;
  code: string;
}

interface Specification {
  id: string;
  name: string;
  value: string;
}

interface InventoryItem {
  warehouseId: string;
  warehouseName: string;
  quantity: number;
}

interface Product {
  id: string;
  name: string;
  basePrice: number;
  costPrice: number;
  isCustomizable: boolean;
  specifications: Specification[];
  inventory: InventoryItem[];
}

interface Warehouse {
  id: string;
  name: string;
  branchId: string;
}

interface SaleItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  costPrice?: number;
  total: number;
  warehouseId: string;
  warehouseName: string;
  specifications?: any;
  isCustomized?: boolean;
  customizedComponents?: any;
  components?: any[];
}

interface Sale {
  id: string;
  invoiceNumber: string;
  date: string;
  contactId: string;
  branchId: string;
  status: string;
  paymentMethod: string;
  paymentStatus: string;
  subtotalAmount: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  notes: string | null;
  currency: string;
  items: SaleItem[];
  payments?: any[];
}

export default function EditSalePage() {
  const params = useParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sale, setSale] = useState<Sale | null>(null);

  // Form fields
  const [selectedContact, setSelectedContact] = useState("");
  const [selectedBranch, setSelectedBranch] = useState("");
  const [items, setItems] = useState<SaleItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [unitPrice, setUnitPrice] = useState(0);
  const [notes, setNotes] = useState("");
  const [availableWarehouses, setAvailableWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState("");
  const [isCustomizeModalOpen, setIsCustomizeModalOpen] = useState(false);
  const [isWizardModalOpen, setIsWizardModalOpen] = useState(false);
  const [productToCustomize, setProductToCustomize] = useState<any>(null);
  const [applyTax, setApplyTax] = useState(false);
  const [discount, setDiscount] = useState(0);
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [status, setStatus] = useState<'PENDING' | 'COMPLETED' | 'CANCELLED'>('COMPLETED');
  const [paymentMethod, setPaymentMethod] = useState<string>('CASH');
  const [paymentStatus, setPaymentStatus] = useState<'PAID' | 'PARTIALLY_PAID' | 'UNPAID'>('PAID');
  const [taxRate, setTaxRate] = useState(14); // Default tax rate is 14%

  // Payment methods
  interface PaymentMethodSetting {
    id: string;
    name: string;
    code: string;
    isActive: boolean;
  }

  interface PaymentDetail {
    method: string;
    amount: number;
  }

  const [paymentMethods, setPaymentMethods] = useState<PaymentDetail[]>([]);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState<PaymentMethodSetting[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('CASH');
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentMethodsLoading, setPaymentMethodsLoading] = useState(false);

  // State for real data
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      setPaymentMethodsLoading(true);
      const response = await fetch('/api/settings/payment-methods');

      if (response.ok) {
        const data = await response.json();
        // Filter only active payment methods
        const activeMethods = data.filter((method: PaymentMethodSetting) =>
          method.isActive && method.code !== 'SUPPLIER_ACCOUNT'
        );

        setAvailablePaymentMethods(activeMethods);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    } finally {
      setPaymentMethodsLoading(false);
    }
  };

  // Fetch real data from API
  useEffect(() => {
    const fetchData = async () => {
      setDataLoading(true);
      try {
        // Make sure params.id is available
        if (!params.id) {
          console.error("Sale ID is undefined");
          setError("Sale ID is missing");
          setDataLoading(false);
          return;
        }

        // Fetch sale
        console.log(`Fetching sale with ID: ${params.id}`);
        const saleResponse = await fetch(`/api/sales/${params.id}`);

        if (!saleResponse.ok) {
          let errorMessage = 'Failed to fetch sale';

          try {
            const errorData = await saleResponse.json();
            console.error("Error response:", errorData);

            if (errorData && errorData.error) {
              errorMessage = errorData.error;
            }

            // If sale not found, redirect to sales list
            if (saleResponse.status === 404) {
              console.error("Sale not found, redirecting to sales list");
              router.push('/dashboard/sales');
              return;
            }
          } catch (parseError) {
            console.error("Error parsing error response:", parseError);
          }

          throw new Error(errorMessage);
        }

        const saleData = await saleResponse.json();

        // Validate sale data
        if (!saleData || !saleData.id) {
          console.error("Invalid sale data received:", saleData);
          setError("Invalid sale data received");
          router.push('/dashboard/sales');
          return;
        }
        setSale(saleData);

        // Set form fields
        setInvoiceNumber(saleData.invoiceNumber);
        setDate(format(new Date(saleData.date), 'yyyy-MM-dd'));
        setSelectedContact(saleData.contactId);
        setSelectedBranch(saleData.branchId);
        setStatus(saleData.status || 'COMPLETED');
        setPaymentStatus(saleData.paymentStatus || 'PAID');
        setPaymentMethod(saleData.paymentMethod || 'CASH');
        setNotes(saleData.notes || '');
        setApplyTax(saleData.taxAmount > 0);
        setDiscount(saleData.discountAmount || 0);
        setTaxRate(saleData.taxRate ? saleData.taxRate * 100 : 14);

        // Set payment methods if available
        if (saleData.payments && saleData.payments.length > 0) {
          // Filter out CUSTOMER_ACCOUNT payments for display
          const payments = saleData.payments
            .filter((p: any) => p.method !== 'CUSTOMER_ACCOUNT')
            .map((p: any) => ({
              method: p.method,
              amount: p.amount
            }));

          setPaymentMethods(payments);

          // If there are no payments or only CUSTOMER_ACCOUNT payment, set payment method to CUSTOMER_ACCOUNT
          if (payments.length === 0) {
            setPaymentMethod('CUSTOMER_ACCOUNT');
          } else {
            // Otherwise, set to the first payment method
            setPaymentMethod(payments[0].method);
          }
        } else {
          // If no payments, set to CUSTOMER_ACCOUNT
          setPaymentMethod('CUSTOMER_ACCOUNT');
        }

        // Fetch customers (contacts that are customers)
        const contactsResponse = await fetch('/api/contacts?type=customer');
        if (contactsResponse.ok) {
          const contactsData = await contactsResponse.json();
          setContacts(contactsData);
        }

        // Fetch branches
        const branchesResponse = await fetch('/api/branches');
        if (branchesResponse.ok) {
          const branchesData = await branchesResponse.json();
          setBranches(branchesData);
        }

        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          setProducts(productsData);
        }

        // Fetch warehouses
        let warehousesData = [];
        const warehousesResponse = await fetch('/api/warehouses');
        if (warehousesResponse.ok) {
          warehousesData = await warehousesResponse.json();
          setWarehouses(warehousesData);
        }

        // Transform items to include product name and warehouse name
        const transformedItems = saleData.items.map((item: any) => {
          console.log("Processing item:", item);

          // Parse customizedComponents if it's a string
          let parsedComponents = null;
          if (item.customizedComponents) {
            try {
              parsedComponents = typeof item.customizedComponents === 'string'
                ? JSON.parse(item.customizedComponents)
                : item.customizedComponents;
              console.log("Parsed customizedComponents:", parsedComponents);
            } catch (e) {
              console.error("Error parsing customizedComponents:", e);
            }
          }

          // If we have components from the database, convert them to the format expected by the UI
          if (item.components && item.components.length > 0) {
            console.log(`Item has ${item.components.length} components from database`);

            // If parsedComponents is null, initialize it as an empty object
            if (!parsedComponents) {
              parsedComponents = {};
            }

            // Group components by type
            for (const comp of item.components) {
              // Use componentType from the component itself
              const type = comp.componentType || "Component";

              // Add to parsedComponents
              parsedComponents[type] = {
                id: comp.componentId,
                name: comp.componentName,
                price: comp.unitPrice || 0,
                count: comp.quantity || 1,
                // Add other properties as needed
                stock: 999 // Placeholder for stock
              };
            }

            console.log("Created parsedComponents from database components:", parsedComponents);
          }

          // Parse specifications if it's a string
          let parsedSpecifications = null;
          if (item.specifications) {
            try {
              parsedSpecifications = typeof item.specifications === 'string'
                ? JSON.parse(item.specifications)
                : item.specifications;
              console.log("Parsed specifications:", parsedSpecifications);
            } catch (e) {
              console.error("Error parsing specifications:", e);
            }
          }

          // If we have components but no specifications, create specifications from components
          if (parsedComponents && (!parsedSpecifications || parsedSpecifications.length === 0)) {
            parsedSpecifications = Object.entries(parsedComponents).map(([type, component]: [string, any]) => {
              const count = component.count || 1;
              return {
                id: component.id,
                name: type,
                value: count > 1 ? `${component.name} (${count} sticks)` : component.name,
                count: count
              };
            });

            console.log("Created specifications from components:", parsedSpecifications);
          }

          // Find the warehouse name from the warehouses list
          const warehouse = warehousesData.find(w => w.id === item.warehouseId);
          const warehouseName = warehouse ? warehouse.name : 'Unknown Warehouse';

          return {
            id: item.id,
            productId: item.productId,
            productName: item.product?.name || 'Unknown Product',
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            costPrice: item.product?.costPrice || 0,
            total: item.totalPrice || (item.unitPrice * item.quantity),
            warehouseId: item.warehouseId,
            warehouseName: warehouseName,
            specifications: parsedSpecifications,
            isCustomized: item.isCustomized || false,
            customizedComponents: parsedComponents,
            // Add components array for inventory tracking
            components: parsedComponents ? Object.entries(parsedComponents).map(([type, component]: [string, any]) => {
              const count = component.count || 1;
              return {
                id: component.id,
                type,
                name: component.name,
                price: component.price,
                count: count,
                totalCount: count * item.quantity
              };
            }) : [],
          };
        });

        setItems(transformedItems);

        // Fetch payment methods
        await fetchPaymentMethods();
      } catch (error) {
        console.error("Error fetching data:", error);
        // Provide more detailed error message
        if (error instanceof Error) {
          setError(`Failed to load sale data: ${error.message}`);
        } else {
          setError('Failed to load sale data');
        }
      } finally {
        setDataLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  // Update available warehouses when branch changes
  useEffect(() => {
    if (selectedBranch) {
      const branchWarehouses = warehouses.filter(wh => wh.branchId === selectedBranch);
      setAvailableWarehouses(branchWarehouses);
      if (branchWarehouses.length > 0 && !selectedWarehouse) {
        setSelectedWarehouse(branchWarehouses[0].id);
      }
    } else {
      setAvailableWarehouses([]);
      setSelectedWarehouse("");
    }
  }, [selectedBranch, warehouses, selectedWarehouse]);

  // Update unit price when product changes
  useEffect(() => {
    if (selectedProduct) {
      const product = products.find(p => p.id === selectedProduct);
      if (product) {
        // Ensure we never set a zero or negative price
        if (product.basePrice > 0) {
          setUnitPrice(product.basePrice);
        } else {
          // If product has a zero or negative base price, set a default minimum price
          setUnitPrice(1);
        }
      }
    } else {
      // When no product is selected, reset the unit price field
      setUnitPrice(0);
    }
  }, [selectedProduct, products]);

  // Calculate total
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

  // Apply tax first
  const taxAmount = applyTax ? subtotal * (taxRate / 100) : 0;
  const subtotalWithTax = subtotal + taxAmount;

  // Then apply discount
  const discountAmount = discount > 0 ? (discount > subtotalWithTax ? subtotalWithTax : discount) : 0;
  const total = subtotalWithTax - discountAmount;

  // Calculate total paid amount and remaining amount
  const totalPaid = paymentMethods.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingAmount = Math.max(0, total - totalPaid);

  // Add payment method
  const addPaymentMethod = () => {
    // Special handling for Customer Account
    if (selectedPaymentMethod === 'CUSTOMER_ACCOUNT') {
      // For Customer Account, we set the entire remaining amount
      // We don't actually add it to the payment methods list as it's handled automatically

      // Remove any existing Customer Account payments
      const filteredPayments = paymentMethods.filter(p => p.method !== 'CUSTOMER_ACCOUNT');
      setPaymentMethods(filteredPayments);

      // Set payment amount to remaining amount
      setPaymentAmount(remainingAmount);

      return;
    }

    // For other payment methods
    if (selectedPaymentMethod && paymentAmount > 0 && paymentAmount <= remainingAmount) {
      // Check if this payment method already exists
      const existingPaymentIndex = paymentMethods.findIndex(p => p.method === selectedPaymentMethod);

      if (existingPaymentIndex >= 0) {
        // Update existing payment method
        const updatedPaymentMethods = [...paymentMethods];
        updatedPaymentMethods[existingPaymentIndex].amount += paymentAmount;
        setPaymentMethods(updatedPaymentMethods);
      } else {
        // Add new payment method
        setPaymentMethods([...paymentMethods, { method: selectedPaymentMethod, amount: paymentAmount }]);
      }

      // Reset payment amount
      setPaymentAmount(remainingAmount > 0 ? remainingAmount : 0);
    }
  };

  // Remove payment method
  const removePaymentMethod = (index: number) => {
    const updatedPaymentMethods = [...paymentMethods];
    updatedPaymentMethods.splice(index, 1);
    setPaymentMethods(updatedPaymentMethods);
  };

  // Determine payment status based on total paid amount
  const getPaymentStatus = () => {
    if (totalPaid === 0) return 'UNPAID';
    if (totalPaid < total) return 'PARTIALLY_PAID';
    return 'PAID';
  };

  // Get payment status display text
  const getPaymentStatusText = () => {
    if (totalPaid === 0) return 'Unpaid (Customer Account)';
    if (totalPaid < total) return 'Partially Paid (Remaining to Customer Account)';
    return 'Paid';
  };

  // Update payment amount when total or payment methods change
  useEffect(() => {
    // Set payment amount to remaining amount
    setPaymentAmount(remainingAmount);
  }, [total, paymentMethods.length, remainingAmount]);

  // Add item to sale
  const addItem = () => {
    if (!selectedProduct || !selectedWarehouse) return;

    // Ensure quantity and unit price are valid
    let finalQuantity = quantity;
    let finalUnitPrice = unitPrice;

    if (finalQuantity <= 0) {
      finalQuantity = 1;
      setQuantity(1);
    }

    // Get the product
    const product = products.find(p => p.id === selectedProduct);
    if (!product) return;

    // Allow zero unit prices
    // If unit price is 0 or negative, we'll still use the product's base price as a default
    if (finalUnitPrice <= 0) {
      finalUnitPrice = product.basePrice;
      setUnitPrice(product.basePrice);

      // But if the product's base price is also 0 or negative, we'll just use 0
      if (finalUnitPrice <= 0) {
        finalUnitPrice = 0;
        setUnitPrice(0);
      }
    }

    // Check if product is in stock
    const inventoryItem = product.inventory.find(inv => inv.warehouseId === selectedWarehouse);
    if (!inventoryItem || inventoryItem.quantity < finalQuantity) {
      alert("Not enough stock available!");
      return;
    }

    // If product is customizable, open the customize wizard
    if (product.isCustomizable) {
      console.log("Opening customize wizard for product:", product);

      // Create a clean copy of the product without any existing components
      const cleanProduct = {
        ...product,
        // Make sure we don't have any leftover components from previous edits
        currentComponents: {},
        editingItemId: undefined
      };

      setProductToCustomize(cleanProduct);

      // Use the new wizard interface
      setIsWizardModalOpen(true);
      return;
    } else {
      console.log("Product is not customizable:", product);
    }

    // Check if this product from this warehouse already exists in the items list
    const existingItemIndex = items.findIndex(
      item => item.productId === product.id && item.warehouseId === selectedWarehouse && !item.isCustomized
    );

    if (existingItemIndex >= 0) {
      // Update existing item instead of adding a new one
      const updatedItems = [...items];
      const existingItem = updatedItems[existingItemIndex];

      updatedItems[existingItemIndex] = {
        ...existingItem,
        quantity: existingItem.quantity + finalQuantity,
        total: (existingItem.quantity + finalQuantity) * existingItem.unitPrice
      };

      setItems(updatedItems);
    } else {
      // Create a new item
      const newItem = {
        id: `item-${Date.now()}`,
        productId: product.id,
        productName: product.name,
        unitPrice: finalUnitPrice,
        costPrice: product.costPrice || 0,
        quantity: finalQuantity,
        total: finalUnitPrice * finalQuantity,
        warehouseId: selectedWarehouse,
        warehouseName: inventoryItem.warehouseName,
        specifications: product.specifications,
        isCustomized: false,
      };

      setItems([...items, newItem]);
    }
    setSelectedProduct("");
    setQuantity(1);
    setUnitPrice(0);
  };

  // Handle customized product confirmation
  const handleCustomizedProduct = (customizedProduct: CustomizedProduct, existingItemId?: string) => {
    // If no existingItemId was passed directly, check if it's in the customizedProduct object
    const itemId = existingItemId || (customizedProduct as any).editingItemId;

    console.log("Handling customized product:", customizedProduct);
    console.log("Existing item ID:", itemId);

    // Find the product
    const product = products.find(p => p.id === customizedProduct.productId);
    if (!product) {
      console.error("Product not found:", customizedProduct.productId);
      return;
    }

    // If we're editing an existing item
    if (itemId) {
      // Find the existing item
      const existingItem = items.find(item => item.id === itemId);
      if (!existingItem) {
        console.error("Existing item not found:", itemId);
        return;
      }

      console.log("Found existing item:", existingItem);
      console.log("Selected components:", customizedProduct.selectedComponents);

      // Create specifications array from selected components
      const selectedSpecs = Object.entries(customizedProduct.selectedComponents).map(([type, component]: [string, any]) => {
        const count = component.count || 1;
        return {
          id: component.id,
          name: type,
          value: count > 1 ? `${component.name} (${count} sticks)` : component.name,
          count: count
        };
      });

      // Create components array for inventory tracking
      const componentsArray = Object.entries(customizedProduct.selectedComponents).map(([type, component]: [string, any]) => {
        const count = component.count || 1;
        return {
          id: component.id,
          type,
          name: component.name,
          price: component.price,
          count: count,
          totalCount: count * existingItem.quantity
        };
      });

      // Update the existing item
      const updatedItems = items.map(item => {
        if (item.id === itemId) { // This is the item we're editing
          return {
            ...item,
            unitPrice: customizedProduct.totalPrice,
            costPrice: customizedProduct.costPrice,
            total: customizedProduct.totalPrice * item.quantity,
            specifications: selectedSpecs,
            customizedComponents: customizedProduct.selectedComponents,
            isCustomized: true,
            // Keep the original warehouse information
            warehouseId: item.warehouseId,
            warehouseName: item.warehouseName,
            // Add components array for inventory tracking
            components: componentsArray,
          };
        }
        return item;
      });

      console.log("Updated items with components for inventory tracking:", updatedItems);
      setItems(updatedItems);
      return;
    }

    // For new items, check if product is in stock
    const inventoryItem = product.inventory.find(inv => inv.warehouseId === selectedWarehouse);
    if (!inventoryItem || inventoryItem.quantity < quantity) {
      alert("Not enough stock available!");
      return;
    }

    // Check if all components have enough stock for the requested quantity
    // For components with count (like RAM), we need to check if there's enough stock for count * quantity
    let insufficientComponents = [];

    for (const [type, component] of Object.entries(customizedProduct.selectedComponents) as [string, any][]) {
      const componentCount = component.count || 1;
      const totalNeeded = componentCount * quantity;

      if (totalNeeded > component.stock) {
        insufficientComponents.push({
          name: component.name,
          type,
          available: component.stock,
          needed: totalNeeded
        });
      }
    }

    if (insufficientComponents.length > 0) {
      const message = insufficientComponents.map(c =>
        `${c.name} (${c.type}): Need ${c.needed}, but only ${c.available} available`
      ).join('\n');

      alert(`Not enough components in stock:\n${message}`);
      return;
    }

    // Create specifications array from selected components
    const selectedSpecs = Object.entries(customizedProduct.selectedComponents).map(([type, component]: [string, any]) => {
      const count = component.count || 1;
      return {
        id: component.id,
        name: type,
        value: count > 1 ? `${component.name} (${count} sticks)` : component.name,
        count: count
      };
    });

    // Check if this product from this warehouse already exists in the items list with the same components
    // For customized products, we need to create a new item since the components might be different
    // We don't want to merge different customizations

    // Create components array for inventory tracking
    const componentsArray = Object.entries(customizedProduct.selectedComponents).map(([type, component]: [string, any]) => {
      const count = component.count || 1;
      return {
        id: component.id,
        type,
        name: component.name,
        price: component.price,
        count: count,
        totalCount: count * quantity
      };
    });

    // Create a new item
    const newItem = {
      id: `item-${Date.now()}`,
      productId: product.id,
      productName: product.name,
      unitPrice: customizedProduct.totalPrice,
      costPrice: customizedProduct.costPrice,
      quantity,
      total: customizedProduct.totalPrice * quantity,
      warehouseId: selectedWarehouse,
      warehouseName: inventoryItem.warehouseName,
      specifications: selectedSpecs,
      isCustomized: true,
      customizedComponents: customizedProduct.selectedComponents,
      // Add components array for inventory tracking
      components: componentsArray,
    };

    setItems([...items, newItem]);
    setSelectedProduct("");
    setQuantity(1);
  };

  // Remove item from sale
  const removeItem = (itemId: string) => {
    setItems(items.filter(item => item.id !== itemId));
  };

  // Save sale
  const saveSale = async () => {
    if (!selectedContact || !selectedBranch || items.length === 0) {
      alert('Please fill in all required fields and add at least one item');
      return;
    }

    try {
      setSaving(true);

      // Prepare items with component details for inventory deduction
      const processedItems = items.map(item => {
        console.log("Processing item for save:", item);

        // For customized items, include component details
        if (item.isCustomized && item.customizedComponents) {
          console.log("Item is customized with components:", item.customizedComponents);

          // Extract component details for inventory deduction
          let components = [];

          // Handle both object and array formats of customizedComponents
          if (typeof item.customizedComponents === 'object') {
            if (Array.isArray(item.customizedComponents)) {
              // If it's an array, use it directly
              components = item.customizedComponents;
            } else {
              // If it's an object with type keys, convert to array
              components = Object.entries(item.customizedComponents).map(([type, component]: [string, any]) => {
                const count = component.count || 1;
                return {
                  id: component.id,
                  type,
                  name: component.name,
                  price: component.price,
                  count: count, // Include count for components like RAM
                  totalCount: count * item.quantity // Total count needed (count per unit × quantity)
                };
              });
            }
          }

          console.log("Processed components from customizedComponents:", components);

          return {
            ...item,
            components: components,
          };
        }

        // If item already has components array, use it
        if (item.components && Array.isArray(item.components) && item.components.length > 0) {
          console.log("Item already has components array:", item.components);

          // Make sure each component has the required fields for inventory tracking
          const enhancedComponents = item.components.map((component: any) => {
            return {
              ...component,
              // Ensure these fields are present
              id: component.id,
              type: component.type || 'COMPONENT',
              name: component.name,
              price: component.price || 0,
              count: component.count || 1,
              totalCount: component.totalCount || (component.count || 1) * item.quantity
            };
          });

          console.log("Enhanced components for inventory tracking:", enhancedComponents);

          return {
            ...item,
            components: enhancedComponents,
          };
        }

        // For non-customized items
        return item;
      });

      // Prepare sale data
      const saleData = {
        invoiceNumber,
        date: new Date(date).toISOString(),
        contactId: selectedContact,
        branchId: selectedBranch,
        status,
        paymentStatus: getPaymentStatus(),
        paymentMethod,
        items: processedItems.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total,
          warehouseId: item.warehouseId,
          components: item.components || [],
        })),
        subtotal,
        discountAmount,
        taxAmount,
        taxRate: taxRate / 100,
        applyTax,
        totalAmount: total,
        // If there are payment methods, include them and add any remaining amount to customer account
        // If no payment methods, the entire amount goes to customer account
        payments: paymentMethods.length > 0
          ? (remainingAmount > 0
              ? [...paymentMethods, { method: 'CUSTOMER_ACCOUNT', amount: remainingAmount }]
              : paymentMethods)
          : [{ method: 'CUSTOMER_ACCOUNT', amount: total }],
        notes,
        currency: "EGP", // Egyptian Pound
      };

      console.log("Sale data:", saleData);

      // Send data to API
      const response = await fetch(`/api/sales/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(saleData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update sale');
      }

      // Redirect to sales detail page
      router.push(`/dashboard/sales/${params.id}`);
    } catch (err: any) {
      console.error('Error updating sale:', err);
      alert(`Error updating sale: ${err.message || "Unknown error"}`);
    } finally {
      setSaving(false);
    }
  };

  // Update unit price when product changes
  useEffect(() => {
    if (selectedProduct) {
      const product = products.find(p => p.id === selectedProduct);
      if (product) {
        setUnitPrice(product.basePrice);
      }
    } else {
      setUnitPrice(0);
    }
  }, [selectedProduct, products]);

  if (dataLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-700">Loading sale data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !sale) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          <p>{error || 'Sale not found'}</p>
          <Link href="/dashboard/sales" className="text-red-600 underline mt-2 inline-block">
            Return to sales
          </Link>
        </div>
      </div>
    );
  }

  // Render function
  if (dataLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-700">Loading sale data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !sale) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          <p>{error || 'Sale not found'}</p>
          <Link href="/dashboard/sales" className="text-red-600 underline mt-2 inline-block">
            Return to sales
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link href={`/dashboard/sales/${params.id}`} className="mr-4">
            <ArrowLeft className="h-5 w-5 text-black" />
          </Link>
          <h1 className="text-2xl font-bold text-black">Edit Sale</h1>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={async () => {
              // When canceling, we need to restore any components that were deducted from inventory
              try {
                // Call the API to restore inventory for this sale
                const response = await fetch(`/api/sales/${params.id}/restore-inventory`, {
                  method: 'POST',
                });

                if (!response.ok) {
                  console.error("Failed to restore inventory:", await response.text());
                } else {
                  console.log("Inventory restored successfully");
                }

                // Navigate back to the sale detail page
                router.push(`/dashboard/sales/${params.id}`);
              } catch (error) {
                console.error("Error restoring inventory:", error);
                // Still navigate back even if there's an error
                router.push(`/dashboard/sales/${params.id}`);
              }
            }}
            className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            onClick={saveSale}
            disabled={saving}
            className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-black mb-1">Invoice Number</label>
            <input
              type="text"
              value={invoiceNumber}
              onChange={(e) => setInvoiceNumber(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Date</label>
            <input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Customer</label>
            <select
              value={selectedContact}
              onChange={(e) => setSelectedContact(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            >
              <option value="">Select a customer</option>
              {contacts.map((contact) => (
                <option key={contact.id} value={contact.id}>
                  {contact.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Branch</label>
            <select
              value={selectedBranch}
              onChange={(e) => setSelectedBranch(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              required
            >
              <option value="">Select a branch</option>
              {branches.map((branch) => (
                <option key={branch.id} value={branch.id}>
                  {branch.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Status</label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value as 'PENDING' | 'COMPLETED' | 'CANCELLED')}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
            >
              <option value="PENDING">Pending</option>
              <option value="COMPLETED">Completed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Payment Status</label>
            <select
              value={paymentStatus}
              onChange={(e) => setPaymentStatus(e.target.value as 'PAID' | 'PARTIALLY_PAID' | 'UNPAID')}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
            >
              <option value="PAID">Paid</option>
              <option value="PARTIALLY_PAID">Partially Paid</option>
              <option value="UNPAID">Unpaid</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Payment Method</label>
            <select
              value={paymentMethod}
              onChange={(e) => setPaymentMethod(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
            >
              {paymentMethodsLoading ? (
                <option value="">Loading payment methods...</option>
              ) : (
                <>
                  {availablePaymentMethods.map((method) => (
                    <option key={method.id} value={method.code}>{method.name}</option>
                  ))}
                  <option value="CUSTOMER_ACCOUNT">Customer Account</option>
                </>
              )}
            </select>
          </div>
        </div>

        <h2 className="text-lg font-medium text-black mb-4">Items</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-black mb-1">Product</label>
            <select
              value={selectedProduct}
              onChange={(e) => setSelectedProduct(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
            >
              <option value="">Select a product</option>
              {products.map((product) => (
                <option key={product.id} value={product.id}>
                  {product.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Warehouse</label>
            <select
              value={selectedWarehouse}
              onChange={(e) => setSelectedWarehouse(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
            >
              <option value="">Select a warehouse</option>
              {warehouses.map((warehouse) => (
                <option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Unit Price</label>
            <input
              type="number"
              value={unitPrice}
              onChange={(e) => setUnitPrice(parseFloat(e.target.value) || 0)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              min="0"
              step="0.01"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-1">Quantity</label>
            <div className="flex">
              <input
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
                className="w-full p-2 border border-gray-300 rounded-l-md text-black"
                min="1"
              />
              <button
                onClick={addItem}
                disabled={!selectedProduct || !selectedWarehouse}
                className="bg-blue-600 text-white px-4 rounded-r-md hover:bg-blue-700 disabled:bg-blue-300"
              >
                Add
              </button>
            </div>
          </div>
        </div>

        {items.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Warehouse</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Unit Price</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Quantity</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Total</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-black uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {items.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-black">{item.productName}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-black">{item.warehouseName}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-black text-right">{item.unitPrice.toFixed(2)} ج.م</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-black text-right">{item.quantity}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-black text-right">{item.total.toFixed(2)} ج.م</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {/* Show Edit/Add Components button for all products */}
                        <button
                          onClick={() => {
                            // Find the product
                            const product = products.find(p => p.id === item.productId);
                            if (product) {
                              console.log("Editing components for item:", item);
                              console.log("Customized components:", item.customizedComponents);

                              // Create a copy of the product with additional properties
                              const productWithComponents = {
                                ...product,
                                currentComponents: item.customizedComponents || {},
                                editingItemId: item.id,
                                warehouseId: item.warehouseId // Add warehouseId for inventory restoration
                              };
                              setProductToCustomize(productWithComponents);
                              // Use the new wizard interface instead of the old modal
                              setIsWizardModalOpen(true);
                            }
                          }}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          {item.isCustomized ? 'Edit Components' : 'Add Components'}
                        </button>
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Remove
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-4 bg-gray-50 rounded-md">
            <p className="text-gray-500">No items added yet</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div>
            <label className="block text-sm font-medium text-black mb-1">Notes</label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-black"
              rows={4}
            ></textarea>
          </div>

          <div>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex justify-between py-2 text-base">
                <span className="font-bold text-black">Subtotal:</span>
                <span className="text-black font-bold">{subtotal.toFixed(2)} ج.م</span>
              </div>

              {/* Tax */}
              <div className="py-2">
                <div className="flex justify-between text-sm mb-1">
                  <div className="flex items-center">
                    <input
                      id="apply-tax"
                      name="apply-tax"
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      checked={applyTax}
                      onChange={(e) => setApplyTax(e.target.checked)}
                    />
                    <label htmlFor="apply-tax" className="ml-2 font-medium text-black">
                      Apply Tax (14%):
                    </label>
                  </div>
                  <span className="text-black font-medium">{taxAmount.toFixed(2)} ج.م</span>
                </div>
                {applyTax && (
                  <div className="text-xs text-indigo-600 ml-6">
                    14% of {subtotal.toFixed(2)} ج.م
                  </div>
                )}
              </div>

              {/* Subtotal with Tax */}
              <div className="flex justify-between py-2 text-sm border-t border-gray-200 mt-2 pt-2">
                <span className="font-medium text-black">Subtotal with Tax:</span>
                <span className="text-black font-medium">{subtotalWithTax.toFixed(2)} ج.م</span>
              </div>

              {/* Discount */}
              <div className="py-2">
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium text-black">Discount:</span>
                  <span className="text-black font-medium">{discountAmount.toFixed(2)} ج.م</span>
                </div>
                <div className="flex items-center bg-white border border-gray-300 rounded-md">
                  <div className="flex-grow relative">
                    <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                      <span className="text-black text-xs">ج.م</span>
                    </div>
                    <input
                      type="number"
                      min="0"
                      max={subtotalWithTax}
                      step="0.01"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-8 pr-8 py-1 text-sm font-medium text-black border-0"
                      value={discount}
                      onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
                    />
                    {discount > 0 && (
                      <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                        <button
                          type="button"
                          className="text-black hover:text-gray-600 focus:outline-none"
                          onClick={() => setDiscount(0)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                {discount > 0 && (
                  <div className="mt-1 text-xs text-green-600">
                    {((discountAmount / subtotalWithTax) * 100).toFixed(1)}% off
                  </div>
                )}
              </div>
              <div className="flex justify-between py-3 text-lg font-bold border-t-2 border-gray-300 mt-3 pt-3 bg-gray-50 rounded-lg p-3">
                <span className="text-black">Total:</span>
                <span className="text-black text-xl">{total.toFixed(2)} ج.م</span>
              </div>

              {/* Payment Methods */}
              <div className="py-3 mt-4 border-t border-gray-200">
                <h4 className="text-base font-bold text-black mb-2">Payment Methods:</h4>

                {/* Payment Method Selection */}
                {paymentMethodsLoading ? (
                  <div className="flex justify-center items-center py-4">
                    <svg className="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="ml-2 text-gray-600">Loading payment methods...</span>
                  </div>
                ) : availablePaymentMethods.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    No payment methods available. Please add payment methods in settings.
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {availablePaymentMethods.map((method) => (
                      <div
                        key={method.id}
                        className={`p-2 border rounded-md cursor-pointer flex items-center ${selectedPaymentMethod === method.code ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedPaymentMethod(method.code)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        <span className="font-medium text-black">{method.name}</span>
                      </div>
                    ))}
                    <div
                      className={`p-2 border rounded-md cursor-pointer flex items-center ${selectedPaymentMethod === 'CUSTOMER_ACCOUNT' ? 'bg-indigo-50 border-indigo-500' : 'border-gray-300'}`}
                      onClick={() => setSelectedPaymentMethod('CUSTOMER_ACCOUNT')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                      <span className="font-medium text-black">Customer Account</span>
                    </div>
                  </div>
                )}

                {/* Payment Amount Input */}
                <div className="flex items-center mb-4">
                  <div className="flex-1 mr-2">
                    <label htmlFor="paymentAmount" className="block text-sm font-medium text-black mb-1">
                      Payment Amount
                    </label>
                    <div className="relative rounded-md shadow-sm">
                      <input
                        type="number"
                        id="paymentAmount"
                        className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-3 pr-12 sm:text-sm border-gray-300 rounded-md text-black"
                        placeholder="0.00"
                        min="0"
                        max={remainingAmount}
                        step="0.01"
                        value={paymentAmount}
                        onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <span className="text-black sm:text-sm">ج.م</span>
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    onClick={addPaymentMethod}
                    disabled={!selectedPaymentMethod || paymentAmount <= 0 || paymentAmount > remainingAmount}
                  >
                    Add Payment
                  </button>
                </div>

                {/* Payment Methods List */}
                {paymentMethods.length > 0 && (
                  <div className="mt-4 border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                            Method
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                            Amount
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {paymentMethods.map((payment, index) => (
                          <tr key={index}>
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-black">
                              {payment.method === 'CUSTOMER_ACCOUNT' ? 'Customer Account' :
                                availablePaymentMethods.find(m => m.code === payment.method)?.name || payment.method}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                              {payment.amount.toFixed(2)} ج.م
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-black">
                              <button
                                type="button"
                                onClick={() => removePaymentMethod(index)}
                                className="text-red-600 hover:text-red-900"
                              >
                                Remove
                              </button>
                            </td>
                          </tr>
                        ))}
                        <tr className="bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                            Total Paid
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                            {totalPaid.toFixed(2)} ج.م
                          </td>
                          <td></td>
                        </tr>
                        <tr>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                            Remaining
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                            {remainingAmount.toFixed(2)} ج.م
                          </td>
                          <td></td>
                        </tr>
                        {remainingAmount > 0 && (
                          <tr className="bg-yellow-50">
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black">
                              Payment Status
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-black" colSpan={2}>
                              {getPaymentStatusText()}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Render modals */}
      {isCustomizeModalOpen && productToCustomize && (
        <CustomizeProductModal
          isOpen={isCustomizeModalOpen}
          onClose={() => setIsCustomizeModalOpen(false)}
          product={productToCustomize}
          onConfirm={handleCustomizedProduct}
        />
      )}

      {isWizardModalOpen && productToCustomize && (
        <ProductCustomizationWizard
          isOpen={isWizardModalOpen}
          onClose={() => setIsWizardModalOpen(false)}
          product={productToCustomize}
          onConfirm={handleCustomizedProduct}
        />
      )}
    </div>
  );
}