import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/permissions - Get all permissions
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admins and users with manage_users permission can view permissions
    const isAdmin = await db.user.findFirst({
      where: {
        email: session.user?.email,
        role: "ADMIN",
      },
    });

    const hasManageUsersPermission = await hasPermission("edit_users");

    if (!isAdmin && !hasManageUsersPermission) {
      return NextResponse.json(
        { error: "You don't have permission to view permissions" },
        { status: 403 }
      );
    }

    const permissions = await db.permission.findMany({
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json(permissions);
  } catch (error) {
    console.error("Error fetching permissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch permissions" },
      { status: 500 }
    );
  }
}

// POST /api/permissions - Create a new permission
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only admins can create permissions
    const isAdmin = await db.user.findFirst({
      where: {
        email: session.user?.email,
        role: "ADMIN",
      },
    });

    if (!isAdmin) {
      return NextResponse.json(
        { error: "Only administrators can create permissions" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: "Permission name is required" },
        { status: 400 }
      );
    }

    // Create the permission
    const permission = await db.permission.create({
      data: {
        name: data.name,
        description: data.description || null,
      },
    });

    return NextResponse.json(permission);
  } catch (error) {
    console.error("Error creating permission:", error);
    return NextResponse.json(
      { error: "Failed to create permission" },
      { status: 500 }
    );
  }
}
