"use client";

import { useState, useEffect, useRef } from "react";
import { Bell, Check, X, Gift, ShoppingCart, Package, Star, Calendar, Info } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import { useSession } from "next-auth/react";

interface Notification {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  link?: string;
  read: boolean;
  metadata?: string;
  createdAt: string;
  updatedAt: string;
}

export function NotificationCenter() {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const notificationRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { data: session } = useSession();

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!session?.user) return;

    setIsLoading(true);
    try {
      // Use the dummy API for testing
      const response = await fetch(`/api/notifications/dummy`);
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications);
        setUnreadCount(data.unreadCount);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (id: string) => {
    try {
      // For testing, just update the state locally without API call
      setNotifications(
        notifications.map((notification) =>
          notification.id === id
            ? { ...notification, read: true }
            : notification
        )
      );
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      // For testing, just update the state locally without API call
      setNotifications(
        notifications.map((notification) => ({
          ...notification,
          read: true,
        }))
      );
      setUnreadCount(0);
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }

    if (notification.link) {
      router.push(notification.link);
    }

    setIsOpen(false);
  };

  // Get filtered notifications based on active tab
  const getFilteredNotifications = () => {
    switch (activeTab) {
      case "unread":
        return notifications.filter((n) => !n.read);
      case "loyalty":
        return notifications.filter((n) => n.type === "LOYALTY" || n.type === "BIRTHDAY");
      case "sales":
        return notifications.filter((n) => n.type === "SALE" || n.type === "CREDIT_NOTE");
      case "system":
        return notifications.filter((n) => n.type === "SYSTEM");
      default:
        return notifications;
    }
  };

  // Get icon for notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "LOYALTY":
        return <Star className="h-5 w-5 text-yellow-500" />;
      case "BIRTHDAY":
        return <Gift className="h-5 w-5 text-pink-500" />;
      case "SALE":
        return <ShoppingCart className="h-5 w-5 text-green-500" />;
      case "CREDIT_NOTE":
        return <Package className="h-5 w-5 text-red-500" />;
      case "SYSTEM":
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  // Close notification center when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch notifications on mount and when session changes
  useEffect(() => {
    if (session?.user) {
      fetchNotifications();
    }
  }, [session]);

  // Refresh notifications every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (session?.user) {
        fetchNotifications();
      }
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [session]);

  return (
    <div className="relative" ref={notificationRef}>
      <button
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Notifications"
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <Badge
            className="absolute -top-1 -right-1 px-1.5 py-0.5 text-xs bg-red-500 text-white"
            variant="destructive"
          >
            {unreadCount > 99 ? "99+" : unreadCount}
          </Badge>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 sm:w-96 bg-white rounded-md shadow-lg overflow-hidden z-50">
          <div className="p-4 bg-gray-50 border-b flex items-center justify-between">
            <h3 className="text-lg font-medium">Notifications</h3>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                disabled={unreadCount === 0}
                className="text-xs"
              >
                <Check className="h-4 w-4 mr-1" />
                Mark all as read
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-xs"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
            <div className="px-2 pt-2 bg-gray-50 border-b">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
                <TabsTrigger value="unread" className="text-xs">
                  Unread
                  {unreadCount > 0 && (
                    <Badge variant="destructive" className="ml-1 px-1 py-0 text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="loyalty" className="text-xs">Loyalty</TabsTrigger>
                <TabsTrigger value="sales" className="text-xs">Sales</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value={activeTab} className="max-h-[70vh] overflow-y-auto">
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : getFilteredNotifications().length === 0 ? (
                <div className="flex flex-col items-center justify-center p-8 text-gray-500">
                  <Bell className="h-12 w-12 text-gray-300 mb-2" />
                  <p>No notifications</p>
                </div>
              ) : (
                <div className="divide-y">
                  {getFilteredNotifications().map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 hover:bg-gray-50 cursor-pointer ${
                        !notification.read ? "bg-blue-50" : ""
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex">
                        <div className="flex-shrink-0 mr-3">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <p className={`text-sm font-medium ${!notification.read ? "text-blue-600" : "text-gray-900"}`}>
                              {notification.title}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                            </p>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                          {notification.link && (
                            <p className="text-xs text-blue-600 mt-1">Click to view</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="p-2 bg-gray-50 border-t text-center">
            <Button
              variant="link"
              size="sm"
              className="text-xs text-gray-600"
              onClick={() => {
                router.push("/dashboard/notifications");
                setIsOpen(false);
              }}
            >
              View all notifications
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
