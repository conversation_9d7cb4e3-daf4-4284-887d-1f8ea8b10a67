import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/accounting/journal-entries - Get journal entries by IDs
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const ids = url.searchParams.get("ids");
    
    if (!ids) {
      return NextResponse.json(
        { error: "Missing required parameter: ids" },
        { status: 400 }
      );
    }
    
    // Split the comma-separated IDs
    const entryIds = ids.split(",");
    
    // Get journal entries
    const entries = await db.journalEntry.findMany({
      where: {
        id: {
          in: entryIds,
        },
      },
      include: {
        journal: true,
        debitAccount: true,
        creditAccount: true,
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        fiscalPeriod: {
          include: {
            fiscalYear: true,
          },
        },
      },
      orderBy: [
        { date: "desc" },
        { createdAt: "desc" },
      ],
    });
    
    return NextResponse.json({
      success: true,
      data: entries,
    });
  } catch (error: any) {
    console.error("Error fetching journal entries by IDs:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch journal entries" },
      { status: 500 }
    );
  }
}
