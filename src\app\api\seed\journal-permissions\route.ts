import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// POST /api/seed/journal-permissions - Seed journal permissions
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Only admins can create permissions
    const isAdmin = await db.user.findFirst({
      where: {
        email: session.user?.email,
        role: "ADMIN",
      },
    });
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Only administrators can create permissions" },
        { status: 403 }
      );
    }
    
    // Define journal permissions
    const journalPermissions = [
      { name: 'view_journals', description: 'View accounting journals' },
      { name: 'add_journals', description: 'Add new accounting journals' },
      { name: 'edit_journals', description: 'Edit existing accounting journals' },
      { name: 'delete_journals', description: 'Delete accounting journals' },
      { name: 'search_journals', description: 'Search in accounting journals' },
    ];
    
    const createdPermissions = [];
    const existingPermissions = [];
    
    // Create permissions
    for (const permission of journalPermissions) {
      // Check if permission already exists
      const existingPermission = await db.permission.findFirst({
        where: {
          name: permission.name,
        },
      });
      
      if (!existingPermission) {
        const newPermission = await db.permission.create({
          data: permission,
        });
        createdPermissions.push(newPermission);
      } else {
        existingPermissions.push(existingPermission);
      }
    }
    
    // Assign new permissions to admin users
    if (createdPermissions.length > 0) {
      const adminUsers = await db.user.findMany({
        where: {
          role: 'ADMIN',
        },
      });
      
      for (const admin of adminUsers) {
        await db.user.update({
          where: {
            id: admin.id,
          },
          data: {
            permissions: {
              connect: createdPermissions.map(p => ({ id: p.id })),
            },
          },
        });
      }
    }
    
    return NextResponse.json({
      message: "Journal permissions processed successfully",
      created: createdPermissions,
      existing: existingPermissions,
    });
  } catch (error) {
    console.error("Error creating journal permissions:", error);
    return NextResponse.json(
      { error: "Failed to create journal permissions" },
      { status: 500 }
    );
  }
}
