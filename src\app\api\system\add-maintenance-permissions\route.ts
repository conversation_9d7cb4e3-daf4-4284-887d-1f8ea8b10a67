import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// POST /api/system/add-maintenance-permissions - Add maintenance permissions
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only administrators can add maintenance permissions" },
        { status: 403 }
      );
    }

    console.log("Adding maintenance permissions...");
    
    // Define maintenance permissions
    const maintenancePermissions = [
      { name: "view_maintenance", description: "View maintenance services" },
      { name: "add_maintenance", description: "Add new maintenance services" },
      { name: "edit_maintenance", description: "Edit existing maintenance services" },
      { name: "delete_maintenance", description: "Delete maintenance services" },
    ];

    // Create permissions if they don't exist
    const results = {
      created: 0,
      existing: 0,
      adminUpdated: false,
    };

    for (const permission of maintenancePermissions) {
      // Check if permission already exists
      const existingPermission = await db.permission.findFirst({
        where: {
          name: permission.name,
        },
      });

      if (!existingPermission) {
        // Create new permission
        await db.permission.create({
          data: permission,
        });
        console.log(`Created permission: ${permission.name}`);
        results.created++;
      } else {
        console.log(`Permission already exists: ${permission.name}`);
        results.existing++;
      }
    }

    // Add permissions to admin user
    const adminUser = await db.user.findFirst({
      where: {
        role: "ADMIN",
      },
      include: {
        permissions: true,
      },
    });

    if (adminUser) {
      // Get all maintenance permissions
      const allMaintenancePermissions = await db.permission.findMany({
        where: {
          name: {
            in: maintenancePermissions.map(p => p.name),
          },
        },
      });

      // Find permissions that admin doesn't have
      const adminPermissionIds = adminUser.permissions.map(p => p.id);
      const missingPermissions = allMaintenancePermissions.filter(
        p => !adminPermissionIds.includes(p.id)
      );

      if (missingPermissions.length > 0) {
        // Add missing permissions to admin
        await db.user.update({
          where: { id: adminUser.id },
          data: {
            permissions: {
              connect: missingPermissions.map(p => ({ id: p.id })),
            },
          },
        });
        console.log(`Added ${missingPermissions.length} permissions to admin user`);
        results.adminUpdated = true;
      }
    }

    return NextResponse.json({
      success: true,
      message: "Maintenance permissions added successfully",
      results,
    });
  } catch (error) {
    console.error("Error adding maintenance permissions:", error);
    return NextResponse.json(
      {
        success: false,
        error: "An error occurred while adding maintenance permissions",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
