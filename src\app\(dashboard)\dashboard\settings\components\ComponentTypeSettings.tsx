"use client";

import { useState, useEffect } from "react";
import ComponentTypeForm from "./ComponentTypeForm";
import ComponentTypeList from "./ComponentTypeList";

interface ComponentType {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export default function ComponentTypeSettings() {
  const [componentTypes, setComponentTypes] = useState<ComponentType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingComponentType, setEditingComponentType] = useState<ComponentType | null>(null);

  // Fetch component types
  const fetchComponentTypes = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/component-types");
      if (!response.ok) {
        throw new Error("Failed to fetch component types");
      }
      const data = await response.json();
      setComponentTypes(data);
    } catch (error) {
      console.error("Error fetching component types:", error);
      setError("Failed to load component types. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchComponentTypes();
  }, []);

  // Handle edit component type
  const handleEditComponentType = (componentType: ComponentType) => {
    setEditingComponentType(componentType);
    setShowForm(true);
  };

  // Handle delete component type
  const handleDeleteComponentType = async (componentTypeId: string) => {
    try {
      const response = await fetch(`/api/component-types/${componentTypeId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete component type");
      }

      // Remove the deleted component type from the state
      setComponentTypes(componentTypes.filter((componentType) => componentType.id !== componentTypeId));
    } catch (error: any) {
      setError(error.message);
    }
  };

  // Handle form close
  const handleFormClose = () => {
    setShowForm(false);
    setEditingComponentType(null);
  };

  // Handle form success
  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingComponentType(null);
    fetchComponentTypes();
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h4 className="text-lg font-semibold text-gray-900">Component Types</h4>
        {!showForm && (
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Add Component Type
          </button>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {showForm ? (
        <div className="mb-6">
          <ComponentTypeForm
            componentType={editingComponentType || undefined}
            onClose={handleFormClose}
            onSuccess={handleFormSuccess}
          />
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ComponentTypeList
            componentTypes={componentTypes}
            onEdit={handleEditComponentType}
            onDelete={handleDeleteComponentType}
            isLoading={isLoading}
          />
        </div>
      )}
    </div>
  );
}
