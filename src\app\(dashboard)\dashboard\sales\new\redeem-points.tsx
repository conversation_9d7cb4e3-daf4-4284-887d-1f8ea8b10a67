"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Gift, 
  DollarSign, 
  Minus, 
  Plus, 
  Check, 
  X,
  AlertTriangle
} from "lucide-react";

interface RedeemPointsProps {
  contactId: string;
  subtotal: number;
  onPointsRedeemed: (amount: number) => void;
}

interface LoyaltyInfo {
  currentPoints: number;
  availableValue: number;
  tier: string;
  isVIP: boolean;
}

export default function RedeemPoints({
  contactId,
  subtotal,
  onPointsRedeemed
}: RedeemPointsProps) {
  const [loyaltyInfo, setLoyaltyInfo] = useState<LoyaltyInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pointsToRedeem, setPointsToRedeem] = useState<number>(0);
  const [redeemAmount, setRedeemAmount] = useState<number>(0);
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [redeemSuccess, setRedeemSuccess] = useState(false);
  
  // Fetch loyalty data
  useEffect(() => {
    const fetchLoyaltyData = async () => {
      if (!contactId) return;
      
      setIsLoading(true);
      try {
        const response = await fetch(`/api/loyalty?contactId=${contactId}`);
        if (response.ok) {
          const data = await response.json();
          setLoyaltyInfo(data.loyaltyInfo);
        } else {
          setError("Failed to fetch loyalty data");
        }
      } catch (error) {
        console.error("Error fetching loyalty data:", error);
        setError("Error loading loyalty information");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchLoyaltyData();
  }, [contactId]);
  
  // Calculate redeem amount when points change
  useEffect(() => {
    if (!loyaltyInfo) return;
    
    const pointValue = 0.01; // 1 point = 0.01 EGP
    const calculatedAmount = pointsToRedeem * pointValue;
    
    // Ensure redeem amount doesn't exceed subtotal
    const finalAmount = Math.min(calculatedAmount, subtotal);
    setRedeemAmount(finalAmount);
  }, [pointsToRedeem, subtotal, loyaltyInfo]);
  
  // Handle redeeming points
  const handleRedeemPoints = async () => {
    if (!loyaltyInfo || pointsToRedeem <= 0 || pointsToRedeem > loyaltyInfo.currentPoints) return;
    
    setIsRedeeming(true);
    try {
      // In a real implementation, this would be an API call to redeem points
      // For now, we'll simulate success
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Call the parent callback with the redeemed amount
      onPointsRedeemed(redeemAmount);
      setRedeemSuccess(true);
      
      // Reset after success
      setTimeout(() => {
        setRedeemSuccess(false);
        setPointsToRedeem(0);
      }, 3000);
    } catch (error) {
      console.error("Error redeeming points:", error);
      setError("Failed to redeem points");
    } finally {
      setIsRedeeming(false);
    }
  };
  
  // Set maximum points
  const setMaxPoints = () => {
    if (!loyaltyInfo) return;
    
    const pointValue = 0.01; // 1 point = 0.01 EGP
    const maxPointsForSubtotal = Math.floor(subtotal / pointValue);
    const pointsToUse = Math.min(maxPointsForSubtotal, loyaltyInfo.currentPoints);
    setPointsToRedeem(pointsToUse);
  };
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loyalty Points</CardTitle>
          <CardDescription>Loading loyalty information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (error || !loyaltyInfo) {
    return null; // Don't show anything if there's an error or no loyalty info
  }
  
  // Don't show if customer has no points
  if (loyaltyInfo.currentPoints <= 0) {
    return null;
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Gift className="h-5 w-5 mr-2 text-purple-500" />
          Redeem Loyalty Points
        </CardTitle>
        <CardDescription>
          Use loyalty points to get a discount on this purchase
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Gift className="h-4 w-4 mr-1 text-purple-500" />
              <span className="text-sm font-medium">Available Points:</span>
            </div>
            <span className="font-bold">{loyaltyInfo.currentPoints}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 mr-1 text-green-500" />
              <span className="text-sm font-medium">Points Value:</span>
            </div>
            <span className="font-bold">{loyaltyInfo.availableValue.toFixed(2)} ج.م</span>
          </div>
          
          <Separator />
          
          <div>
            <Label htmlFor="points-to-redeem">Points to Redeem</Label>
            <div className="flex mt-1">
              <Button
                variant="outline"
                size="icon"
                className="rounded-r-none"
                onClick={() => setPointsToRedeem(Math.max(0, pointsToRedeem - 100))}
                disabled={pointsToRedeem <= 0}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Input
                id="points-to-redeem"
                type="number"
                min="0"
                max={loyaltyInfo.currentPoints}
                value={pointsToRedeem || ''}
                onChange={(e) => setPointsToRedeem(parseInt(e.target.value) || 0)}
                className="rounded-none text-center"
              />
              <Button
                variant="outline"
                size="icon"
                className="rounded-l-none"
                onClick={() => setPointsToRedeem(Math.min(loyaltyInfo.currentPoints, pointsToRedeem + 100))}
                disabled={pointsToRedeem >= loyaltyInfo.currentPoints}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex justify-between mt-1">
              <p className="text-xs text-gray-500">
                Value: {redeemAmount.toFixed(2)} ج.م
              </p>
              <Button
                variant="link"
                size="sm"
                className="h-auto p-0 text-xs"
                onClick={setMaxPoints}
              >
                Use Maximum
              </Button>
            </div>
          </div>
          
          {pointsToRedeem > loyaltyInfo.currentPoints && (
            <div className="p-2 bg-yellow-50 border border-yellow-200 rounded text-sm flex items-start">
              <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1 mt-0.5" />
              <p className="text-yellow-700">
                You can't redeem more points than you have available.
              </p>
            </div>
          )}
          
          {redeemAmount > subtotal && (
            <div className="p-2 bg-blue-50 border border-blue-200 rounded text-sm flex items-start">
              <AlertTriangle className="h-4 w-4 text-blue-500 mr-1 mt-0.5" />
              <p className="text-blue-700">
                The redemption value exceeds the subtotal. Only {Math.floor(subtotal / 0.01)} points ({subtotal.toFixed(2)} ج.م) will be used.
              </p>
            </div>
          )}
          
          {redeemSuccess && (
            <div className="p-2 bg-green-50 border border-green-200 rounded text-sm flex items-start">
              <Check className="h-4 w-4 text-green-500 mr-1 mt-0.5" />
              <p className="text-green-700">
                Successfully redeemed {pointsToRedeem} points for {redeemAmount.toFixed(2)} ج.م discount!
              </p>
            </div>
          )}
          
          <Button
            className="w-full"
            onClick={handleRedeemPoints}
            disabled={
              isRedeeming || 
              pointsToRedeem <= 0 || 
              pointsToRedeem > loyaltyInfo.currentPoints ||
              redeemSuccess
            }
          >
            {isRedeeming ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Redeeming...
              </>
            ) : (
              <>
                <Gift className="h-4 w-4 mr-2" />
                Redeem Points
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
