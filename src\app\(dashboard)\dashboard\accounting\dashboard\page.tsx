"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Loader2, 
  ArrowRight, 
  Wallet, 
  CreditCard, 
  Building2, 
  Smartphone, 
  RefreshCw, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  FileText, 
  BarChart4, 
  PieChart, 
  Calendar, 
  Clock, 
  Plus, 
  Download
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { format } from "date-fns";

// Dashboard component
export default function AccountingDashboardPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState({
    cashBalance: 0,
    vodafoneCashBalance: 0,
    bankBalance: 0,
    visaBalance: 0,
    totalReceivables: 0,
    totalPayables: 0,
    recentTransactions: [],
    todaysSales: 0,
    todaysPurchases: 0,
    todaysExpenses: 0,
    todaysReceipts: 0,
    pendingPaymentVouchers: 0,
    pendingReceiptVouchers: 0,
    pendingSalesInvoices: 0,
    pendingPurchaseInvoices: 0
  });

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/accounting/dashboard");
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      } else {
        toast.error("Failed to load accounting dashboard data");
      }
    } catch (error) {
      console.error("Error fetching accounting dashboard data:", error);
      toast.error("Failed to load accounting dashboard data");
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh dashboard data
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      await fetchDashboardData();
      toast.success("Dashboard data refreshed");
    } catch (error) {
      console.error("Error refreshing dashboard data:", error);
      toast.error("Failed to refresh dashboard data");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Get payment method icon
  const getPaymentMethodIcon = (code: string) => {
    switch (code?.toUpperCase()) {
      case 'CASH':
        return <Wallet className="h-5 w-5" />;
      case 'VODAFONE_CASH':
        return <Smartphone className="h-5 w-5" />;
      case 'BANK_TRANSFER':
        return <Building2 className="h-5 w-5" />;
      case 'VISA':
        return <CreditCard className="h-5 w-5" />;
      default:
        return <DollarSign className="h-5 w-5" />;
    }
  };

  // Fetch data on mount
  useEffect(() => {
    fetchDashboardData();
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">المحاسبة / Accounting</h1>
          <p className="text-gray-500">لوحة تحكم المحاسبة المبسطة</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={refreshData} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
            تحديث
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">جاري تحميل البيانات...</span>
        </div>
      ) : (
        <>
          {/* Payment Methods Balance Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <Wallet className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-medium ml-2">الخزينة / Cash</h3>
                  </div>
                  <Link href="/dashboard/accounting/payment-journals?method=CASH">
                    <Button variant="ghost" size="sm">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="text-2xl font-bold text-blue-700">
                  {formatCurrency(dashboardData.cashBalance)}
                </div>
                <div className="text-sm text-gray-500 mt-2">
                  آخر تحديث: {format(new Date(), 'dd/MM/yyyy HH:mm')}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-white">
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <Smartphone className="h-6 w-6 text-purple-600" />
                    </div>
                    <h3 className="text-lg font-medium ml-2">فودافون كاش</h3>
                  </div>
                  <Link href="/dashboard/accounting/payment-journals?method=VODAFONE_CASH">
                    <Button variant="ghost" size="sm">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="text-2xl font-bold text-purple-700">
                  {formatCurrency(dashboardData.vodafoneCashBalance)}
                </div>
                <div className="text-sm text-gray-500 mt-2">
                  آخر تحديث: {format(new Date(), 'dd/MM/yyyy HH:mm')}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-white">
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <div className="bg-green-100 p-2 rounded-full">
                      <Building2 className="h-6 w-6 text-green-600" />
                    </div>
                    <h3 className="text-lg font-medium ml-2">البنك / Bank</h3>
                  </div>
                  <Link href="/dashboard/accounting/payment-journals?method=BANK_TRANSFER">
                    <Button variant="ghost" size="sm">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="text-2xl font-bold text-green-700">
                  {formatCurrency(dashboardData.bankBalance)}
                </div>
                <div className="text-sm text-gray-500 mt-2">
                  آخر تحديث: {format(new Date(), 'dd/MM/yyyy HH:mm')}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-amber-50 to-white">
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <div className="bg-amber-100 p-2 rounded-full">
                      <CreditCard className="h-6 w-6 text-amber-600" />
                    </div>
                    <h3 className="text-lg font-medium ml-2">فيزا / Visa</h3>
                  </div>
                  <Link href="/dashboard/accounting/payment-journals?method=VISA">
                    <Button variant="ghost" size="sm">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="text-2xl font-bold text-amber-700">
                  {formatCurrency(dashboardData.visaBalance)}
                </div>
                <div className="text-sm text-gray-500 mt-2">
                  آخر تحديث: {format(new Date(), 'dd/MM/yyyy HH:mm')}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>العمليات السريعة / Quick Actions</CardTitle>
              <CardDescription>إجراءات محاسبية سريعة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Link href="/dashboard/accounting/payment-vouchers/new">
                  <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2">
                    <TrendingDown className="h-6 w-6 text-red-500" />
                    <span>إذن صرف جديد</span>
                  </Button>
                </Link>
                <Link href="/dashboard/accounting/receipt-vouchers/new">
                  <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2">
                    <TrendingUp className="h-6 w-6 text-green-500" />
                    <span>إذن استلام جديد</span>
                  </Button>
                </Link>
                <Link href="/dashboard/accounting/journals">
                  <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2">
                    <FileText className="h-6 w-6 text-blue-500" />
                    <span>دفتر اليومية</span>
                  </Button>
                </Link>
                <Link href="/dashboard/accounting/reports">
                  <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2">
                    <BarChart4 className="h-6 w-6 text-purple-500" />
                    <span>التقارير</span>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Today's Summary */}
          <Card>
            <CardHeader>
              <CardTitle>ملخص اليوم / Today's Summary</CardTitle>
              <CardDescription>ملخص العمليات المالية لليوم</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 p-4 rounded-md">
                  <div className="flex items-center mb-2">
                    <TrendingUp className="h-5 w-5 text-green-500 mr-2" />
                    <h3 className="text-sm font-medium">المبيعات / Sales</h3>
                  </div>
                  <p className="text-xl font-bold text-green-700">{formatCurrency(dashboardData.todaysSales)}</p>
                </div>
                <div className="bg-blue-50 p-4 rounded-md">
                  <div className="flex items-center mb-2">
                    <TrendingDown className="h-5 w-5 text-blue-500 mr-2" />
                    <h3 className="text-sm font-medium">المشتريات / Purchases</h3>
                  </div>
                  <p className="text-xl font-bold text-blue-700">{formatCurrency(dashboardData.todaysPurchases)}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-md">
                  <div className="flex items-center mb-2">
                    <TrendingDown className="h-5 w-5 text-red-500 mr-2" />
                    <h3 className="text-sm font-medium">المصروفات / Expenses</h3>
                  </div>
                  <p className="text-xl font-bold text-red-700">{formatCurrency(dashboardData.todaysExpenses)}</p>
                </div>
                <div className="bg-amber-50 p-4 rounded-md">
                  <div className="flex items-center mb-2">
                    <TrendingUp className="h-5 w-5 text-amber-500 mr-2" />
                    <h3 className="text-sm font-medium">المقبوضات / Receipts</h3>
                  </div>
                  <p className="text-xl font-bold text-amber-700">{formatCurrency(dashboardData.todaysReceipts)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pending Items */}
          <Card>
            <CardHeader>
              <CardTitle>العناصر المعلقة / Pending Items</CardTitle>
              <CardDescription>العناصر التي تحتاج إلى متابعة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Link href="/dashboard/accounting/payment-vouchers?status=PENDING">
                  <div className="border rounded-md p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">أذونات صرف معلقة</h3>
                      <Badge variant="outline" className="bg-red-50 text-red-700">
                        {dashboardData.pendingPaymentVouchers}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500">أذونات صرف تحتاج إلى متابعة</p>
                  </div>
                </Link>
                <Link href="/dashboard/accounting/receipt-vouchers?status=PENDING">
                  <div className="border rounded-md p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">أذونات استلام معلقة</h3>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        {dashboardData.pendingReceiptVouchers}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500">أذونات استلام تحتاج إلى متابعة</p>
                  </div>
                </Link>
                <Link href="/dashboard/sales?status=UNPAID">
                  <div className="border rounded-md p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">فواتير مبيعات غير مدفوعة</h3>
                      <Badge variant="outline" className="bg-amber-50 text-amber-700">
                        {dashboardData.pendingSalesInvoices}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500">فواتير مبيعات تحتاج إلى تحصيل</p>
                  </div>
                </Link>
                <Link href="/dashboard/purchases?status=UNPAID">
                  <div className="border rounded-md p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">فواتير مشتريات غير مدفوعة</h3>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        {dashboardData.pendingPurchaseInvoices}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500">فواتير مشتريات تحتاج إلى سداد</p>
                  </div>
                </Link>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
