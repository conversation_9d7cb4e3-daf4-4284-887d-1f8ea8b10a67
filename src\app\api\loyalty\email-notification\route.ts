import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { sendLoyaltyNotificationEmail, sendBirthdayGiftEmail } from "@/lib/email-service";

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const data = await req.json();
    
    if (!data.contactId) {
      return NextResponse.json(
        { error: "Contact ID is required" },
        { status: 400 }
      );
    }
    
    // Check if contact exists
    const contact = await prisma.contact.findUnique({
      where: { id: data.contactId },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        loyaltyPoints: true,
        loyaltyTier: true,
        isVIP: true
      }
    });
    
    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }
    
    // Check if contact has email
    if (!contact.email) {
      return NextResponse.json(
        { error: "Contact does not have an email address" },
        { status: 400 }
      );
    }
    
    // Handle different notification types
    switch (data.type) {
      case "LOYALTY_POINTS":
        if (!data.points) {
          return NextResponse.json(
            { error: "Points value is required" },
            { status: 400 }
          );
        }
        
        const emailSent = await sendLoyaltyNotificationEmail(
          contact.email,
          contact.name,
          data.points,
          contact.loyaltyPoints,
          data.reason || "",
          contact.loyaltyTier || "BRONZE"
        );
        
        if (emailSent) {
          // Create notification record
          await prisma.notification.create({
            data: {
              userId: session.user.id,
              type: "LOYALTY",
              title: `Email Sent: ${contact.name}`,
              message: `Loyalty notification email sent to ${contact.name} about ${data.points > 0 ? "earning" : "redeeming"} ${Math.abs(data.points)} points.`,
              read: false,
              metadata: JSON.stringify({
                contactId: contact.id,
                points: data.points,
                reason: data.reason
              })
            }
          });
          
          return NextResponse.json({
            success: true,
            message: `Loyalty notification email sent to ${contact.email}`
          });
        } else {
          return NextResponse.json(
            { error: "Failed to send email" },
            { status: 500 }
          );
        }
        
      case "BIRTHDAY_GIFT":
        if (!data.points) {
          return NextResponse.json(
            { error: "Points value is required" },
            { status: 400 }
          );
        }
        
        const birthdayEmailSent = await sendBirthdayGiftEmail(
          contact.email,
          contact.name,
          data.points,
          contact.loyaltyPoints,
          contact.loyaltyTier || "BRONZE",
          contact.isVIP
        );
        
        if (birthdayEmailSent) {
          // Create notification record
          await prisma.notification.create({
            data: {
              userId: session.user.id,
              type: "BIRTHDAY",
              title: `Birthday Email Sent: ${contact.name}`,
              message: `Birthday gift notification email sent to ${contact.name} about receiving ${data.points} points.`,
              read: false,
              metadata: JSON.stringify({
                contactId: contact.id,
                points: data.points
              })
            }
          });
          
          return NextResponse.json({
            success: true,
            message: `Birthday gift notification email sent to ${contact.email}`
          });
        } else {
          return NextResponse.json(
            { error: "Failed to send email" },
            { status: 500 }
          );
        }
        
      default:
        return NextResponse.json(
          { error: "Invalid notification type" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error sending notification email:", error);
    return NextResponse.json(
      { error: "Failed to send notification email" },
      { status: 500 }
    );
  }
}
