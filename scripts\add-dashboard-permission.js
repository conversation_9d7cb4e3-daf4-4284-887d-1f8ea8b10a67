// This script adds the view_dashboard permission to the database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking for view_dashboard permission...');

    // Check if view_dashboard permission exists
    const existingPermission = await prisma.permission.findFirst({
      where: {
        name: "view_dashboard",
      },
    });

    if (existingPermission) {
      console.log("view_dashboard permission already exists");
      return;
    }

    console.log("view_dashboard permission not found. Creating...");

    // Create the view_dashboard permission
    const newPermission = await prisma.permission.create({
      data: {
        name: "view_dashboard",
        description: "View dashboard",
      },
    });

    console.log(`Created permission: ${newPermission.name}`);

    // Add this permission to all users
    const users = await prisma.user.findMany();
    
    for (const user of users) {
      await prisma.user.update({
        where: {
          id: user.id,
        },
        data: {
          permissions: {
            connect: {
              id: newPermission.id,
            },
          },
        },
      });
      console.log(`Added view_dashboard permission to user: ${user.name}`);
    }

    console.log('Successfully added view_dashboard permission to all users');
  } catch (error) {
    console.error('Error adding view_dashboard permission:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
