import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";

// GET /api/categories/[id] - Get a specific category
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const categoryId = params.id;

    // Get category from database
    const category = await db.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(category);
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      { error: "Failed to fetch category" },
      { status: 500 }
    );
  }
}

// PATCH /api/categories/[id] - Update a category
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to edit categories
    const hasEditPermission = await hasPermission("edit_products");
    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to edit categories" },
        { status: 403 }
      );
    }

    const categoryId = params.id;
    const data = await req.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await db.category.findUnique({
      where: { id: categoryId },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if another category with the same name already exists
    const duplicateCategory = await db.category.findFirst({
      where: {
        name: {
          equals: data.name,
          mode: "insensitive",
        },
        id: {
          not: categoryId,
        },
      },
    });

    if (duplicateCategory) {
      return NextResponse.json(
        { error: "Another category with this name already exists" },
        { status: 400 }
      );
    }

    // Update the category
    const updatedCategory = await db.category.update({
      where: { id: categoryId },
      data: {
        name: data.name,
        description: data.description || "",
        type: data.type || "PRODUCT",
      },
    });

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update category" },
      { status: 500 }
    );
  }
}

// DELETE /api/categories/[id] - Delete a category
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to delete categories
    const hasDeletePermission = await hasPermission("delete_products");
    if (!hasDeletePermission) {
      return NextResponse.json(
        { error: "You don't have permission to delete categories" },
        { status: 403 }
      );
    }

    const categoryId = params.id;

    // Check if category exists
    const existingCategory = await db.category.findUnique({
      where: { id: categoryId },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if category is being used by any products
    const productsUsingCategory = await db.product.count({
      where: { categoryId },
    });

    if (productsUsingCategory > 0) {
      return NextResponse.json(
        { error: "Cannot delete category because it is being used by products" },
        { status: 400 }
      );
    }

    // Delete the category
    await db.category.delete({
      where: { id: categoryId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to delete category" },
      { status: 500 }
    );
  }
}
