import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { exec } from "child_process";
import { promisify } from "util";

const execPromise = promisify(exec);

// Function to check if user is admin
async function isAdmin(req: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return false;
  }

  // Check if user has admin role
  return session.user.role === "ADMIN";
}

// POST /api/system/add-real-data - Add real sample data
export async function POST(req: NextRequest) {
  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }

    // Run the add-real-data script
    const { stdout, stderr } = await execPromise("npm run data:add-real");

    if (stderr && !stderr.includes("npm WARN")) {
      console.error("Error adding real data:", stderr);
      return NextResponse.json(
        { error: "Failed to add real data", details: stderr },
        { status: 500 }
      );
    }

    console.log("Real data added successfully:", stdout);

    return NextResponse.json({ 
      message: "Real sample data added successfully",
      details: stdout
    });
  } catch (error) {
    console.error("Error adding real data:", error);
    return NextResponse.json(
      { error: "Failed to add real data" },
      { status: 500 }
    );
  }
}
