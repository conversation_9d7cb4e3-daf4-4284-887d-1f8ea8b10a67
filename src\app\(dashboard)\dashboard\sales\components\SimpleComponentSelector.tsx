"use client";

import React from "react";
import {
  Cpu,
  HardDrive,
  Save,
  DollarSign,
  X
} from "lucide-react";

// Component types
type ComponentType = string;

// Component interface
interface Component {
  id: string;
  name: string;
  type: ComponentType;
  price: number;
  stock: number;
  capacity?: string;
  speed?: string;
  description?: string;
  imageUrl?: string;
}

interface SimpleComponentSelectorProps {
  componentType: ComponentType;
  components: Component[];
  selectedComponent?: Component;
  onSelect: (component: Component | undefined) => void;
  className?: string;
}

export default function SimpleComponentSelector({
  componentType,
  components,
  selectedComponent,
  onSelect,
  className = ""
}: SimpleComponentSelectorProps) {
  // Get icon based on component type
  const getComponentIcon = (type: ComponentType) => {
    switch (type) {
      case "CPU":
        return <Cpu className="h-5 w-5 text-blue-500" />;
      case "RAM":
        return <Save className="h-5 w-5 text-green-500" />;
      case "SSD":
      case "HDD":
      case "NVMe":
        return <HardDrive className="h-5 w-5 text-purple-500" />;
      default:
        return <DollarSign className="h-5 w-5 text-gray-500" />;
    }
  };

  // Handle component selection
  const handleComponentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const componentId = e.target.value;
    
    if (componentId === "") {
      onSelect(undefined);
      return;
    }
    
    const component = components.find(c => c.id === componentId);
    if (component) {
      onSelect(component);
    }
  };

  // Handle remove selected component
  const handleRemove = () => {
    onSelect(undefined);
  };

  return (
    <div className={`${className}`}>
      <div className="mb-2 flex justify-between items-center">
        <h3 className="text-sm font-medium text-gray-700 flex items-center">
          {getComponentIcon(componentType)}
          <span className="ml-2">{componentType}</span>
        </h3>

        {selectedComponent && (
          <button
            type="button"
            className="text-xs text-red-500 hover:text-red-700 flex items-center"
            onClick={handleRemove}
          >
            <X className="h-3 w-3 mr-1" />
            Remove
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4">
        {/* Component Selection Dropdown */}
        <div>
          <select
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md bg-white text-black font-medium"
            value={selectedComponent?.id || ""}
            onChange={handleComponentChange}
          >
            <option value="" className="text-black font-medium">Select {componentType}</option>
            {components.map((component) => (
              <option
                key={component.id}
                value={component.id}
                disabled={component.stock <= 0}
                className="text-black font-medium"
              >
                {component.name} - {component.price.toFixed(2)} ج.م {component.stock <= 0 ? "(Out of Stock)" : ""}
              </option>
            ))}
          </select>
        </div>

        {/* Selected Component Details */}
        {selectedComponent && (
          <div className="border-2 border-blue-500 bg-blue-50 rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium text-sm">{selectedComponent.name}</h4>
                {selectedComponent.capacity && (
                  <p className="text-xs text-gray-500 mt-1">Capacity: {selectedComponent.capacity}</p>
                )}
                {selectedComponent.speed && (
                  <p className="text-xs text-gray-500">Speed: {selectedComponent.speed}</p>
                )}
              </div>
              <span className="text-sm font-medium">{selectedComponent.price.toFixed(2)} ج.م</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
