import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { hasPermission } from "@/lib/permissions";
import { sendEmail } from "@/lib/email";

// POST /api/purchases/:id/reminder - Send a reminder for a purchase
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Check if user has permission to send reminders
    const hasEditPermission = await hasPermission("edit_purchases");
    if (!hasEditPermission) {
      return NextResponse.json(
        { error: "You don't have permission to send reminders" },
        { status: 403 }
      );
    }
    
    const id = params.id;
    
    // Handle "all" case - send reminders for all overdue invoices
    if (id === "all") {
      const today = new Date();
      
      // Find all unpaid or partially paid purchases with due dates in the past
      const overduePurchases = await prisma.purchase.findMany({
        where: {
          paymentStatus: {
            in: ["UNPAID", "PARTIALLY_PAID"]
          },
          dueDate: {
            lt: today
          },
          reminderSent: false
        },
        include: {
          contact: true,
          payments: true
        }
      });
      
      // Send reminders for each purchase
      const results = await Promise.allSettled(
        overduePurchases.map(async (purchase) => {
          // Calculate remaining amount
          const totalPaid = purchase.payments.reduce((sum, payment) => sum + payment.amount, 0);
          const remainingAmount = purchase.totalAmount - totalPaid;
          
          // Send email reminder
          if (purchase.contact.email) {
            await sendEmail({
              to: purchase.contact.email,
              subject: `Payment Reminder: Invoice ${purchase.invoiceNumber}`,
              text: `Dear ${purchase.contact.name},\n\nThis is a friendly reminder that invoice ${purchase.invoiceNumber} for ${remainingAmount.toFixed(2)} EGP is overdue. The payment was due on ${purchase.dueDate?.toLocaleDateString()}.\n\nPlease make the payment at your earliest convenience.\n\nThank you,\nYour Company Name`,
              html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Payment Reminder</h2>
                  <p>Dear ${purchase.contact.name},</p>
                  <p>This is a friendly reminder that the following invoice is overdue:</p>
                  <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <tr style="background-color: #f2f2f2;">
                      <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Invoice Number</th>
                      <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Due Date</th>
                      <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Amount Due</th>
                    </tr>
                    <tr>
                      <td style="padding: 10px; border: 1px solid #ddd;">${purchase.invoiceNumber}</td>
                      <td style="padding: 10px; border: 1px solid #ddd;">${purchase.dueDate?.toLocaleDateString()}</td>
                      <td style="padding: 10px; border: 1px solid #ddd;">${remainingAmount.toFixed(2)} EGP</td>
                    </tr>
                  </table>
                  <p>Please make the payment at your earliest convenience.</p>
                  <p>Thank you,<br>Your Company Name</p>
                </div>
              `
            });
          }
          
          // Update purchase record
          return await prisma.purchase.update({
            where: { id: purchase.id },
            data: {
              reminderSent: true,
              lastReminderDate: today
            }
          });
        })
      );
      
      // Count successful reminders
      const successCount = results.filter(result => result.status === "fulfilled").length;
      
      return NextResponse.json({
        success: true,
        message: `Sent ${successCount} reminders out of ${overduePurchases.length} overdue invoices`
      });
    }
    
    // Handle single purchase case
    const purchase = await prisma.purchase.findUnique({
      where: { id },
      include: {
        contact: true,
        payments: true
      }
    });
    
    if (!purchase) {
      return NextResponse.json(
        { error: "Purchase not found" },
        { status: 404 }
      );
    }
    
    // Calculate remaining amount
    const totalPaid = purchase.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = purchase.totalAmount - totalPaid;
    
    // Send email reminder
    if (purchase.contact.email) {
      await sendEmail({
        to: purchase.contact.email,
        subject: `Payment Reminder: Invoice ${purchase.invoiceNumber}`,
        text: `Dear ${purchase.contact.name},\n\nThis is a friendly reminder that invoice ${purchase.invoiceNumber} for ${remainingAmount.toFixed(2)} EGP is ${purchase.dueDate && purchase.dueDate < new Date() ? 'overdue' : 'due soon'}. ${purchase.dueDate ? `The payment was due on ${purchase.dueDate.toLocaleDateString()}.` : ''}\n\nPlease make the payment at your earliest convenience.\n\nThank you,\nYour Company Name`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Payment Reminder</h2>
            <p>Dear ${purchase.contact.name},</p>
            <p>This is a friendly reminder that the following invoice is ${purchase.dueDate && purchase.dueDate < new Date() ? 'overdue' : 'due soon'}:</p>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
              <tr style="background-color: #f2f2f2;">
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Invoice Number</th>
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Due Date</th>
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Amount Due</th>
              </tr>
              <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">${purchase.invoiceNumber}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${purchase.dueDate?.toLocaleDateString() || 'Not specified'}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${remainingAmount.toFixed(2)} EGP</td>
              </tr>
            </table>
            <p>Please make the payment at your earliest convenience.</p>
            <p>Thank you,<br>Your Company Name</p>
          </div>
        `
      });
    }
    
    // Update purchase record
    const updatedPurchase = await prisma.purchase.update({
      where: { id },
      data: {
        reminderSent: true,
        lastReminderDate: new Date()
      }
    });
    
    return NextResponse.json(updatedPurchase);
  } catch (error) {
    console.error("Error sending reminder:", error);
    return NextResponse.json(
      { error: "Failed to send reminder" },
      { status: 500 }
    );
  }
}
