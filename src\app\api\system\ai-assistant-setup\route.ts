import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { isAdmin } from "@/lib/auth-utils";
import { execPromise } from "@/lib/exec-promise";

// POST /api/system/ai-assistant-setup - Set up AI assistant
export async function POST(req: NextRequest) {
  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }

    // Run the AI assistant setup script
    const { stdout, stderr } = await execPromise("npm run ai-assistant:setup");

    if (stderr && !stderr.includes("npm WARN")) {
      console.error("Error setting up AI assistant:", stderr);
      return NextResponse.json(
        { error: "Failed to set up AI assistant", details: stderr },
        { status: 500 }
      );
    }

    console.log("AI assistant set up successfully:", stdout);

    return NextResponse.json({ 
      message: "AI assistant set up successfully",
      details: stdout
    });
  } catch (error) {
    console.error("Error setting up AI assistant:", error);
    return NextResponse.json(
      { 
        error: "Failed to set up AI assistant",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
