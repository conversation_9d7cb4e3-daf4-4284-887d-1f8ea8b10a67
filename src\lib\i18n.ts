/**
 * Internationalization (i18n) configuration and utilities
 */

export type Language = 'ar' | 'en';

export interface TranslationKeys {
  // Navigation
  dashboard: string;
  sales: string;
  purchases: string;
  inventory: string;
  products: string;
  contacts: string;
  customers: string;
  suppliers: string;
  branches: string;
  warehouses: string;
  pos: string;
  accounting: string;
  reports: string;
  settings: string;
  
  // Common actions
  add: string;
  edit: string;
  delete: string;
  save: string;
  cancel: string;
  search: string;
  filter: string;
  export: string;
  print: string;
  
  // Sales
  newInvoice: string;
  salesOrders: string;
  creditNotes: string;
  salesReports: string;
  
  // Purchases
  newPurchase: string;
  purchaseOrders: string;
  purchaseReturns: string;
  purchaseReports: string;
  
  // Inventory
  productList: string;
  addProduct: string;
  stockMovements: string;
  warehouseTransfers: string;
  
  // Accounting
  customerStatements: string;
  cashMovements: string;
  installments: string;
  profitLoss: string;
  expenses: string;
  
  // User interface
  welcome: string;
  logout: string;
  profile: string;
  language: string;
  darkMode: string;
  lightMode: string;
  currentBranch: string;
  currentWarehouse: string;
  
  // Forms
  name: string;
  description: string;
  price: string;
  quantity: string;
  total: string;
  date: string;
  status: string;
  
  // Status
  active: string;
  inactive: string;
  paid: string;
  unpaid: string;
  pending: string;
  completed: string;
  
  // Messages
  success: string;
  error: string;
  warning: string;
  info: string;
  loading: string;
  noData: string;
  confirmDelete: string;
}

export const translations: Record<Language, TranslationKeys> = {
  ar: {
    // Navigation
    dashboard: 'لوحة التحكم',
    sales: 'المبيعات',
    purchases: 'المشتريات',
    inventory: 'المخزون',
    products: 'المنتجات',
    contacts: 'جهات الاتصال',
    customers: 'العملاء',
    suppliers: 'الموردين',
    branches: 'الفروع',
    warehouses: 'المستودعات',
    pos: 'نقاط البيع',
    accounting: 'المحاسبة',
    reports: 'التقارير',
    settings: 'الإعدادات',
    
    // Common actions
    add: 'إضافة',
    edit: 'تعديل',
    delete: 'حذف',
    save: 'حفظ',
    cancel: 'إلغاء',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    print: 'طباعة',
    
    // Sales
    newInvoice: 'فاتورة جديدة',
    salesOrders: 'طلبات المبيعات',
    creditNotes: 'إشعارات الخصم',
    salesReports: 'تقارير المبيعات',
    
    // Purchases
    newPurchase: 'مشتريات جديدة',
    purchaseOrders: 'طلبات الشراء',
    purchaseReturns: 'مرتجعات المشتريات',
    purchaseReports: 'تقارير المشتريات',
    
    // Inventory
    productList: 'قائمة المنتجات',
    addProduct: 'إضافة منتج',
    stockMovements: 'حركات المخزون',
    warehouseTransfers: 'النقل بين المستودعات',
    
    // Accounting
    customerStatements: 'كشوف حساب العملاء',
    cashMovements: 'حركات النقدية',
    installments: 'الأقساط',
    profitLoss: 'الأرباح والخسائر',
    expenses: 'المصروفات',
    
    // User interface
    welcome: 'مرحباً',
    logout: 'تسجيل الخروج',
    profile: 'الملف الشخصي',
    language: 'اللغة',
    darkMode: 'الوضع المظلم',
    lightMode: 'الوضع الفاتح',
    currentBranch: 'الفرع الحالي',
    currentWarehouse: 'المستودع الحالي',
    
    // Forms
    name: 'الاسم',
    description: 'الوصف',
    price: 'السعر',
    quantity: 'الكمية',
    total: 'الإجمالي',
    date: 'التاريخ',
    status: 'الحالة',
    
    // Status
    active: 'نشط',
    inactive: 'غير نشط',
    paid: 'مدفوع',
    unpaid: 'غير مدفوع',
    pending: 'معلق',
    completed: 'مكتمل',
    
    // Messages
    success: 'تم بنجاح',
    error: 'حدث خطأ',
    warning: 'تحذير',
    info: 'معلومات',
    loading: 'جاري التحميل...',
    noData: 'لا توجد بيانات',
    confirmDelete: 'هل أنت متأكد من الحذف؟',
  },
  en: {
    // Navigation
    dashboard: 'Dashboard',
    sales: 'Sales',
    purchases: 'Purchases',
    inventory: 'Inventory',
    products: 'Products',
    contacts: 'Contacts',
    customers: 'Customers',
    suppliers: 'Suppliers',
    branches: 'Branches',
    warehouses: 'Warehouses',
    pos: 'POS',
    accounting: 'Accounting',
    reports: 'Reports',
    settings: 'Settings',
    
    // Common actions
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    print: 'Print',
    
    // Sales
    newInvoice: 'New Invoice',
    salesOrders: 'Sales Orders',
    creditNotes: 'Credit Notes',
    salesReports: 'Sales Reports',
    
    // Purchases
    newPurchase: 'New Purchase',
    purchaseOrders: 'Purchase Orders',
    purchaseReturns: 'Purchase Returns',
    purchaseReports: 'Purchase Reports',
    
    // Inventory
    productList: 'Product List',
    addProduct: 'Add Product',
    stockMovements: 'Stock Movements',
    warehouseTransfers: 'Warehouse Transfers',
    
    // Accounting
    customerStatements: 'Customer Statements',
    cashMovements: 'Cash Movements',
    installments: 'Installments',
    profitLoss: 'Profit & Loss',
    expenses: 'Expenses',
    
    // User interface
    welcome: 'Welcome',
    logout: 'Logout',
    profile: 'Profile',
    language: 'Language',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    currentBranch: 'Current Branch',
    currentWarehouse: 'Current Warehouse',
    
    // Forms
    name: 'Name',
    description: 'Description',
    price: 'Price',
    quantity: 'Quantity',
    total: 'Total',
    date: 'Date',
    status: 'Status',
    
    // Status
    active: 'Active',
    inactive: 'Inactive',
    paid: 'Paid',
    unpaid: 'Unpaid',
    pending: 'Pending',
    completed: 'Completed',
    
    // Messages
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    loading: 'Loading...',
    noData: 'No data available',
    confirmDelete: 'Are you sure you want to delete?',
  },
};

/**
 * Get translation for a key
 */
export function t(key: keyof TranslationKeys, language: Language = 'ar'): string {
  return translations[language][key] || key;
}

/**
 * Get current language direction
 */
export function getLanguageDirection(language: Language): 'ltr' | 'rtl' {
  return language === 'ar' ? 'rtl' : 'ltr';
}

/**
 * Get opposite language
 */
export function getOppositeLanguage(language: Language): Language {
  return language === 'ar' ? 'en' : 'ar';
}
