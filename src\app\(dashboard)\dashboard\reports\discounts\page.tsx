"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts";
import { 
  Download, 
  Filter, 
  Search, 
  Percent, 
  DollarSign, 
  Tag, 
  ShoppingBag, 
  Users,
  Calendar,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import { format, subDays, startOfMonth, endOf<PERSON>onth, startOfYear, endOfYear } from "date-fns";

// Define types
interface DiscountSummary {
  totalDiscounts: number;
  totalAmount: number;
  invoiceDiscounts: number;
  invoiceDiscountAmount: number;
  itemDiscounts: number;
  itemDiscountAmount: number;
  customerDiscounts: number;
  customerDiscountAmount: number;
  percentageDiscounts: number;
  percentageDiscountAmount: number;
  fixedDiscounts: number;
  fixedDiscountAmount: number;
}

interface DiscountUsage {
  id: string;
  name: string;
  type: string;
  scope: string;
  usageCount: number;
  totalAmount: number;
  averageAmount: number;
}

interface TopCustomer {
  id: string;
  name: string;
  discountCount: number;
  totalAmount: number;
  averageDiscount: number;
}

export default function DiscountReportsPage() {
  // State for filters
  const [dateRange, setDateRange] = useState<[Date | undefined, Date | undefined]>([
    startOfMonth(new Date()),
    endOfMonth(new Date())
  ]);
  const [discountType, setDiscountType] = useState<string>("all");
  const [discountScope, setDiscountScope] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [timeFrame, setTimeFrame] = useState<string>("month");
  
  // State for data
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [summary, setSummary] = useState<DiscountSummary>({
    totalDiscounts: 0,
    totalAmount: 0,
    invoiceDiscounts: 0,
    invoiceDiscountAmount: 0,
    itemDiscounts: 0,
    itemDiscountAmount: 0,
    customerDiscounts: 0,
    customerDiscountAmount: 0,
    percentageDiscounts: 0,
    percentageDiscountAmount: 0,
    fixedDiscounts: 0,
    fixedDiscountAmount: 0
  });
  const [discountUsage, setDiscountUsage] = useState<DiscountUsage[]>([]);
  const [topCustomers, setTopCustomers] = useState<TopCustomer[]>([]);
  
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];
  
  // Update date range based on time frame
  useEffect(() => {
    const now = new Date();
    
    switch (timeFrame) {
      case "week":
        setDateRange([subDays(now, 7), now]);
        break;
      case "month":
        setDateRange([startOfMonth(now), endOfMonth(now)]);
        break;
      case "quarter":
        setDateRange([subDays(now, 90), now]);
        break;
      case "year":
        setDateRange([startOfYear(now), endOfYear(now)]);
        break;
      default:
        setDateRange([startOfMonth(now), endOfMonth(now)]);
    }
  }, [timeFrame]);
  
  // Fetch data when filters change
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      
      try {
        // In a real implementation, this would be an API call with filters
        // For now, we'll simulate data
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockSummary: DiscountSummary = {
          totalDiscounts: 156,
          totalAmount: 12450.75,
          invoiceDiscounts: 78,
          invoiceDiscountAmount: 7890.50,
          itemDiscounts: 45,
          itemDiscountAmount: 2560.25,
          customerDiscounts: 33,
          customerDiscountAmount: 2000.00,
          percentageDiscounts: 98,
          percentageDiscountAmount: 8750.25,
          fixedDiscounts: 58,
          fixedDiscountAmount: 3700.50
        };
        
        const mockDiscountUsage: DiscountUsage[] = [
          { id: "1", name: "Summer Sale", type: "PERCENTAGE", scope: "INVOICE", usageCount: 45, totalAmount: 3500.75, averageAmount: 77.79 },
          { id: "2", name: "New Customer", type: "PERCENTAGE", scope: "CUSTOMER", usageCount: 33, totalAmount: 2000.00, averageAmount: 60.61 },
          { id: "3", name: "Bulk Purchase", type: "FIXED_AMOUNT", scope: "INVOICE", usageCount: 28, totalAmount: 2800.00, averageAmount: 100.00 },
          { id: "4", name: "Clearance", type: "PERCENTAGE", scope: "ITEM", usageCount: 25, totalAmount: 1250.50, averageAmount: 50.02 },
          { id: "5", name: "Holiday Special", type: "FIXED_AMOUNT", scope: "ITEM", usageCount: 15, totalAmount: 1500.00, averageAmount: 100.00 },
          { id: "6", name: "Loyalty Reward", type: "PERCENTAGE", scope: "CUSTOMER", usageCount: 10, totalAmount: 1400.50, averageAmount: 140.05 }
        ];
        
        const mockTopCustomers: TopCustomer[] = [
          { id: "1", name: "Ahmed Mohamed", discountCount: 12, totalAmount: 1500.75, averageDiscount: 125.06 },
          { id: "2", name: "Sara Ali", discountCount: 8, totalAmount: 980.50, averageDiscount: 122.56 },
          { id: "3", name: "Mohamed Ibrahim", discountCount: 7, totalAmount: 850.25, averageDiscount: 121.46 },
          { id: "4", name: "Fatima Hassan", discountCount: 6, totalAmount: 720.00, averageDiscount: 120.00 },
          { id: "5", name: "Ali Mahmoud", discountCount: 5, totalAmount: 600.50, averageDiscount: 120.10 }
        ];
        
        // Filter discount usage based on search term
        const filteredDiscountUsage = searchTerm 
          ? mockDiscountUsage.filter(d => d.name.toLowerCase().includes(searchTerm.toLowerCase()))
          : mockDiscountUsage;
        
        // Filter by discount type
        const typeFilteredDiscountUsage = discountType === "all" 
          ? filteredDiscountUsage 
          : filteredDiscountUsage.filter(d => d.type === discountType);
        
        // Filter by discount scope
        const scopeFilteredDiscountUsage = discountScope === "all" 
          ? typeFilteredDiscountUsage 
          : typeFilteredDiscountUsage.filter(d => d.scope === discountScope);
        
        setSummary(mockSummary);
        setDiscountUsage(scopeFilteredDiscountUsage);
        setTopCustomers(mockTopCustomers);
      } catch (error) {
        console.error("Error fetching discount data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [dateRange, discountType, discountScope, searchTerm]);
  
  // Prepare data for charts
  const discountTypeData = [
    { name: "Percentage", value: summary.percentageDiscounts },
    { name: "Fixed Amount", value: summary.fixedDiscounts }
  ];
  
  const discountScopeData = [
    { name: "Invoice", value: summary.invoiceDiscounts },
    { name: "Item", value: summary.itemDiscounts },
    { name: "Customer", value: summary.customerDiscounts }
  ];
  
  const discountAmountData = [
    { name: "Invoice", amount: summary.invoiceDiscountAmount },
    { name: "Item", amount: summary.itemDiscountAmount },
    { name: "Customer", amount: summary.customerDiscountAmount }
  ];
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Discount Reports</h1>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export Report
        </Button>
      </div>
      
      {/* Filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Filters</CardTitle>
            <Select value={timeFrame} onValueChange={setTimeFrame}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time frame" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Last 7 days</SelectItem>
                <SelectItem value="month">This month</SelectItem>
                <SelectItem value="quarter">Last 90 days</SelectItem>
                <SelectItem value="year">This year</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <CardDescription>
            Filter discount data by various criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <div className="flex items-center space-x-2">
                <DatePicker
                  value={dateRange[0] ? new Date(dateRange[0]) : undefined}
                  onChange={(date) => setDateRange([date, dateRange[1]])}
                />
                <span>to</span>
                <DatePicker
                  value={dateRange[1] ? new Date(dateRange[1]) : undefined}
                  onChange={(date) => setDateRange([dateRange[0], date])}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Discount Type</label>
              <Select value={discountType} onValueChange={setDiscountType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                  <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Discount Scope</label>
              <Select value={discountScope} onValueChange={setDiscountScope}>
                <SelectTrigger>
                  <SelectValue placeholder="Select scope" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Scopes</SelectItem>
                  <SelectItem value="INVOICE">Invoice</SelectItem>
                  <SelectItem value="ITEM">Item</SelectItem>
                  <SelectItem value="CUSTOMER">Customer</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="Search discounts..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Discounts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{summary.totalDiscounts}</p>
                <p className="text-sm text-gray-500">Applied discounts</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Tag className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Discount Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{summary.totalAmount.toFixed(2)} ج.م</p>
                <p className="text-sm text-gray-500">Discount value</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Percentage Discounts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{summary.percentageDiscounts}</p>
                <p className="text-sm text-gray-500">{summary.percentageDiscountAmount.toFixed(2)} ج.م</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Percent className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Fixed Amount Discounts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-end">
              <div>
                <p className="text-3xl font-bold">{summary.fixedDiscounts}</p>
                <p className="text-sm text-gray-500">{summary.fixedDiscountAmount.toFixed(2)} ج.م</p>
              </div>
              <div className="bg-amber-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Discount Distribution by Type</CardTitle>
            <CardDescription>
              Breakdown of percentage vs. fixed amount discounts
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={discountTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {discountTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} discounts`, 'Count']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Discount Amount by Scope</CardTitle>
            <CardDescription>
              Total discount amount by invoice, item, and customer
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={discountAmountData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value.toFixed(2)} ج.م`, 'Amount']} />
                <Legend />
                <Bar dataKey="amount" fill="#3895e7" name="Discount Amount" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
      
      {/* Tabs for detailed data */}
      <Tabs defaultValue="usage" className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="usage">Discount Usage</TabsTrigger>
          <TabsTrigger value="customers">Top Customers</TabsTrigger>
        </TabsList>
        
        <TabsContent value="usage">
          <Card>
            <CardHeader>
              <CardTitle>Discount Usage Details</CardTitle>
              <CardDescription>
                Detailed breakdown of discount usage and amounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount Name</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scope</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage Count</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Amount</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {isLoading ? (
                      <tr>
                        <td colSpan={6} className="px-4 py-4 text-center text-sm text-gray-500">Loading...</td>
                      </tr>
                    ) : discountUsage.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="px-4 py-4 text-center text-sm text-gray-500">No discount data found</td>
                      </tr>
                    ) : (
                      discountUsage.map((discount) => (
                        <tr key={discount.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">{discount.name}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">
                            <Badge variant={discount.type === "PERCENTAGE" ? "secondary" : "outline"}>
                              {discount.type === "PERCENTAGE" ? (
                                <Percent className="h-3 w-3 mr-1" />
                              ) : (
                                <DollarSign className="h-3 w-3 mr-1" />
                              )}
                              {discount.type === "PERCENTAGE" ? "Percentage" : "Fixed Amount"}
                            </Badge>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-500">
                            <Badge variant="outline">
                              {discount.scope === "INVOICE" ? (
                                <ShoppingBag className="h-3 w-3 mr-1" />
                              ) : discount.scope === "ITEM" ? (
                                <Tag className="h-3 w-3 mr-1" />
                              ) : (
                                <Users className="h-3 w-3 mr-1" />
                              )}
                              {discount.scope === "INVOICE" ? "Invoice" : 
                               discount.scope === "ITEM" ? "Item" : "Customer"}
                            </Badge>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-500">{discount.usageCount}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{discount.totalAmount.toFixed(2)} ج.م</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{discount.averageAmount.toFixed(2)} ج.م</td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="customers">
          <Card>
            <CardHeader>
              <CardTitle>Top Customers by Discount Usage</CardTitle>
              <CardDescription>
                Customers who received the most discounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount Count</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Discount</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {isLoading ? (
                      <tr>
                        <td colSpan={4} className="px-4 py-4 text-center text-sm text-gray-500">Loading...</td>
                      </tr>
                    ) : topCustomers.length === 0 ? (
                      <tr>
                        <td colSpan={4} className="px-4 py-4 text-center text-sm text-gray-500">No customer data found</td>
                      </tr>
                    ) : (
                      topCustomers.map((customer) => (
                        <tr key={customer.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">{customer.name}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{customer.discountCount}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{customer.totalAmount.toFixed(2)} ج.م</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{customer.averageDiscount.toFixed(2)} ج.م</td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
