import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { isAdmin } from "@/lib/auth-utils";
import { 
  getDatabaseSchema, 
  getTableData, 
  executeQuery,
  getTableRelationships,
  getDatabaseStats
} from "@/lib/ai-assistant-db-explorer";

// GET /api/ai-assistant/db-explorer - Get database schema or table data
export async function GET(req: NextRequest) {
  try {
    // Check if user is admin
    if (!await isAdmin(req)) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 401 }
      );
    }
    
    const url = new URL(req.url);
    const action = url.searchParams.get("action") || "schema";
    const table = url.searchParams.get("table");
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10");
    const query = url.searchParams.get("query");
    
    let result;
    
    switch (action) {
      case "schema":
        result = await getDatabaseSchema();
        break;
      case "table":
        if (!table) {
          return NextResponse.json(
            { error: "Table parameter is required" },
            { status: 400 }
          );
        }
        result = await getTableData(table, page, pageSize);
        break;
      case "relationships":
        result = await getTableRelationships();
        break;
      case "stats":
        result = await getDatabaseStats();
        break;
      case "query":
        if (!query) {
          return NextResponse.json(
            { error: "Query parameter is required" },
            { status: 400 }
          );
        }
        result = await executeQuery(query);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in database explorer:", error);
    return NextResponse.json(
      { error: "Failed to execute database explorer action" },
      { status: 500 }
    );
  }
}
