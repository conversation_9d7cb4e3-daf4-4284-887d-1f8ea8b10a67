"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Save, Calendar, Tag, Percent, DollarSign, Users, ShoppingBag, Layers } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";

// Define types
interface Category {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  categoryId: string;
}

interface Contact {
  id: string;
  name: string;
  phone: string;
  isCustomer: boolean;
  isVIP: boolean;
}

export default function NewDiscountPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [customers, setCustomers] = useState<Contact[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  
  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [discountType, setDiscountType] = useState<"PERCENTAGE" | "FIXED_AMOUNT">("PERCENTAGE");
  const [discountValue, setDiscountValue] = useState<number>(0);
  const [discountScope, setDiscountScope] = useState<"ITEM" | "INVOICE" | "CUSTOMER">("INVOICE");
  const [minAmount, setMinAmount] = useState<number | null>(null);
  const [maxAmount, setMaxAmount] = useState<number | null>(null);
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [isActive, setIsActive] = useState(true);
  const [categoryId, setCategoryId] = useState<string>("");
  const [productId, setProductId] = useState<string>("");
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [applyToVIP, setApplyToVIP] = useState(false);
  
  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/categories');
        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories');
        }
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData);
        
        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products');
        }
        const productsData = await productsResponse.json();
        setProducts(productsData);
        setFilteredProducts(productsData);
        
        // Fetch customers
        const customersResponse = await fetch('/api/contacts?type=customer');
        if (!customersResponse.ok) {
          throw new Error('Failed to fetch customers');
        }
        const customersData = await customersResponse.json();
        setCustomers(customersData.filter((c: Contact) => c.isCustomer));
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: "Error",
          description: "Failed to load required data",
          variant: "destructive",
        });
      }
    };
    
    fetchData();
  }, [toast]);
  
  // Filter products by category
  useEffect(() => {
    if (categoryId) {
      setFilteredProducts(products.filter(product => product.categoryId === categoryId));
    } else {
      setFilteredProducts(products);
    }
  }, [categoryId, products]);
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!name) {
      toast({
        title: "Validation Error",
        description: "Discount name is required",
        variant: "destructive",
      });
      return;
    }
    
    if (discountValue <= 0) {
      toast({
        title: "Validation Error",
        description: "Discount value must be greater than zero",
        variant: "destructive",
      });
      return;
    }
    
    if (discountType === "PERCENTAGE" && discountValue > 100) {
      toast({
        title: "Validation Error",
        description: "Percentage discount cannot exceed 100%",
        variant: "destructive",
      });
      return;
    }
    
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      toast({
        title: "Validation Error",
        description: "End date must be after start date",
        variant: "destructive",
      });
      return;
    }
    
    if (discountScope === "ITEM" && !categoryId && !productId) {
      toast({
        title: "Validation Error",
        description: "Please select a category or product for item discount",
        variant: "destructive",
      });
      return;
    }
    
    if (discountScope === "CUSTOMER" && !applyToVIP && selectedCustomers.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one customer or apply to VIP customers",
        variant: "destructive",
      });
      return;
    }
    
    setLoading(true);
    
    try {
      // Prepare discount data
      const discountData = {
        name,
        description: description || null,
        type: discountType,
        value: discountValue,
        scope: discountScope,
        minAmount: minAmount || null,
        maxAmount: maxAmount || null,
        startDate: startDate ? new Date(startDate).toISOString() : null,
        endDate: endDate ? new Date(endDate).toISOString() : null,
        isActive,
        categoryId: discountScope === "ITEM" && categoryId ? categoryId : null,
        productId: discountScope === "ITEM" && productId ? productId : null,
        applyToVIP: discountScope === "CUSTOMER" ? applyToVIP : false,
        customerIds: discountScope === "CUSTOMER" ? selectedCustomers : [],
      };
      
      // Send data to API
      const response = await fetch('/api/discounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(discountData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create discount');
      }
      
      toast({
        title: "Success",
        description: "Discount created successfully",
      });
      
      // Redirect to discounts list
      router.push('/dashboard/sales/discounts');
    } catch (error) {
      console.error('Error creating discount:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create discount",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex items-center mb-6">
        <Link href="/dashboard/sales/discounts" className="mr-4">
          <ArrowLeft className="h-5 w-5 text-gray-500" />
        </Link>
        <h1 className="text-2xl font-bold">Create New Discount</h1>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Discount Information</CardTitle>
                <CardDescription>Basic information about the discount</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Discount Name <span className="text-red-500">*</span></Label>
                    <Input
                      id="name"
                      placeholder="Summer Sale, VIP Discount, etc."
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="status"
                        checked={isActive}
                        onCheckedChange={setIsActive}
                      />
                      <Label htmlFor="status">{isActive ? 'Active' : 'Inactive'}</Label>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Provide details about this discount"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Discount Type <span className="text-red-500">*</span></Label>
                    <RadioGroup
                      value={discountType}
                      onValueChange={(value) => setDiscountType(value as "PERCENTAGE" | "FIXED_AMOUNT")}
                      className="flex space-x-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="PERCENTAGE" id="percentage" />
                        <Label htmlFor="percentage" className="flex items-center">
                          <Percent className="h-4 w-4 mr-1" />
                          Percentage
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="FIXED_AMOUNT" id="fixed" />
                        <Label htmlFor="fixed" className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1" />
                          Fixed Amount
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="value">
                      Discount Value <span className="text-red-500">*</span>
                      {discountType === "PERCENTAGE" && " (%)"}
                      {discountType === "FIXED_AMOUNT" && " (EGP)"}
                    </Label>
                    <Input
                      id="value"
                      type="number"
                      min={0}
                      max={discountType === "PERCENTAGE" ? 100 : undefined}
                      step={discountType === "PERCENTAGE" ? 1 : 0.01}
                      value={discountValue}
                      onChange={(e) => setDiscountValue(parseFloat(e.target.value) || 0)}
                      required
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">Start Date</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="endDate">End Date</Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minAmount">Minimum Amount (EGP)</Label>
                    <Input
                      id="minAmount"
                      type="number"
                      min={0}
                      step={0.01}
                      value={minAmount !== null ? minAmount : ''}
                      onChange={(e) => setMinAmount(e.target.value ? parseFloat(e.target.value) : null)}
                      placeholder="Optional"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="maxAmount">Maximum Amount (EGP)</Label>
                    <Input
                      id="maxAmount"
                      type="number"
                      min={0}
                      step={0.01}
                      value={maxAmount !== null ? maxAmount : ''}
                      onChange={(e) => setMaxAmount(e.target.value ? parseFloat(e.target.value) : null)}
                      placeholder="Optional"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Discount Scope</CardTitle>
                <CardDescription>Define where this discount applies</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Scope <span className="text-red-500">*</span></Label>
                  <RadioGroup
                    value={discountScope}
                    onValueChange={(value) => setDiscountScope(value as "ITEM" | "INVOICE" | "CUSTOMER")}
                    className="grid grid-cols-1 md:grid-cols-3 gap-4"
                  >
                    <div className="flex items-center space-x-2 border rounded-md p-3 hover:bg-gray-50">
                      <RadioGroupItem value="ITEM" id="item" />
                      <Label htmlFor="item" className="flex items-center cursor-pointer">
                        <Tag className="h-4 w-4 mr-2" />
                        Item Discount
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 border rounded-md p-3 hover:bg-gray-50">
                      <RadioGroupItem value="INVOICE" id="invoice" />
                      <Label htmlFor="invoice" className="flex items-center cursor-pointer">
                        <ShoppingBag className="h-4 w-4 mr-2" />
                        Invoice Discount
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 border rounded-md p-3 hover:bg-gray-50">
                      <RadioGroupItem value="CUSTOMER" id="customer" />
                      <Label htmlFor="customer" className="flex items-center cursor-pointer">
                        <Users className="h-4 w-4 mr-2" />
                        Customer Discount
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
                
                {discountScope === "ITEM" && (
                  <div className="space-y-4 p-4 border rounded-md">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select value={categoryId} onValueChange={setCategoryId}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Categories</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="product">Product</Label>
                      <Select 
                        value={productId} 
                        onValueChange={setProductId}
                        disabled={filteredProducts.length === 0}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={
                            categoryId && filteredProducts.length === 0 
                              ? "No products in this category" 
                              : "Select a product"
                          } />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Products</SelectItem>
                          {filteredProducts.map((product) => (
                            <SelectItem key={product.id} value={product.id}>
                              {product.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
                
                {discountScope === "CUSTOMER" && (
                  <div className="space-y-4 p-4 border rounded-md">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="vip"
                          checked={applyToVIP}
                          onCheckedChange={setApplyToVIP}
                        />
                        <Label htmlFor="vip">Apply to all VIP customers</Label>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <Label>Select Specific Customers</Label>
                      <div className="max-h-60 overflow-y-auto border rounded-md p-2">
                        {customers.length === 0 ? (
                          <p className="text-sm text-gray-500 p-2">No customers found</p>
                        ) : (
                          customers.map((customer) => (
                            <div key={customer.id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md">
                              <input
                                type="checkbox"
                                id={`customer-${customer.id}`}
                                checked={selectedCustomers.includes(customer.id)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedCustomers([...selectedCustomers, customer.id]);
                                  } else {
                                    setSelectedCustomers(selectedCustomers.filter(id => id !== customer.id));
                                  }
                                }}
                                className="rounded"
                              />
                              <label htmlFor={`customer-${customer.id}`} className="flex-grow cursor-pointer">
                                <div className="flex justify-between">
                                  <span>{customer.name}</span>
                                  <span className="text-sm text-gray-500">{customer.phone}</span>
                                </div>
                                {customer.isVIP && (
                                  <span className="text-xs text-blue-600">VIP Customer</span>
                                )}
                              </label>
                            </div>
                          ))
                        )}
                      </div>
                      <p className="text-xs text-gray-500">
                        {selectedCustomers.length} customer(s) selected
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Discount Type:</span>
                    <span className="text-sm">
                      {discountType === "PERCENTAGE" ? "Percentage" : "Fixed Amount"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Value:</span>
                    <span className="text-sm">
                      {discountType === "PERCENTAGE" 
                        ? `${discountValue}%` 
                        : `${discountValue.toFixed(2)} EGP`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Scope:</span>
                    <span className="text-sm">
                      {discountScope === "ITEM" 
                        ? "Item Discount" 
                        : discountScope === "INVOICE" 
                          ? "Invoice Discount" 
                          : "Customer Discount"}
                    </span>
                  </div>
                  {startDate && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Start Date:</span>
                      <span className="text-sm">{new Date(startDate).toLocaleDateString()}</span>
                    </div>
                  )}
                  {endDate && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">End Date:</span>
                      <span className="text-sm">{new Date(endDate).toLocaleDateString()}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Status:</span>
                    <span className="text-sm">{isActive ? "Active" : "Inactive"}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Create Discount
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Help</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Discount Types</h4>
                  <p className="text-xs text-gray-500">
                    <strong>Percentage:</strong> Applies a percentage discount to the price.
                  </p>
                  <p className="text-xs text-gray-500">
                    <strong>Fixed Amount:</strong> Applies a fixed amount discount to the price.
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Discount Scopes</h4>
                  <p className="text-xs text-gray-500">
                    <strong>Item:</strong> Applies to specific products or categories.
                  </p>
                  <p className="text-xs text-gray-500">
                    <strong>Invoice:</strong> Applies to the entire invoice total.
                  </p>
                  <p className="text-xs text-gray-500">
                    <strong>Customer:</strong> Applies to specific customers or VIPs.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
