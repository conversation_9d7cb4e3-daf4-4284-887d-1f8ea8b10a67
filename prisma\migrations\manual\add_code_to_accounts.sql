-- Add code column to Account table
ALTER TABLE "Account" ADD COLUMN IF NOT EXISTS "code" TEXT;

-- Make code column unique
CREATE UNIQUE INDEX IF NOT EXISTS "Account_code_key" ON "Account"("code");

-- Add isDefault column to Account table
ALTER TABLE "Account" ADD COLUMN IF NOT EXISTS "isDefault" BOOLEAN NOT NULL DEFAULT false;

-- Add indexes
CREATE INDEX IF NOT EXISTS "Account_code_idx" ON "Account"("code");
CREATE INDEX IF NOT EXISTS "Account_type_idx" ON "Account"("type");
CREATE INDEX IF NOT EXISTS "Account_isActive_idx" ON "Account"("isActive");
CREATE INDEX IF NOT EXISTS "Account_branchId_idx" ON "Account"("branchId");

-- Update existing accounts to have a code
-- This is a temporary solution until we can properly set codes for all accounts
UPDATE "Account" SET "code" = 'ACC-' || "id" WHERE "code" IS NULL;
