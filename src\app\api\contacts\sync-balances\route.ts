import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// POST /api/contacts/sync-balances - Synchronize contact balances
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage finance
    const hasManageFinancePermission = await hasPermission("manage_finance");
    const isAdmin = session.user.role === "ADMIN";

    if (!hasManageFinancePermission && !isAdmin) {
      return NextResponse.json(
        { error: "You don't have permission to synchronize contact balances" },
        { status: 403 }
      );
    }

    // Get all contacts
    const contacts = await db.contact.findMany({
      where: {
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        openingBalance: true,
        isCustomer: true,
        isSupplier: true,
      },
    });

    const results = [];

    // Process each contact
    for (const contact of contacts) {
      try {
        // Calculate the correct balance for this contact
        const balance = await calculateContactBalance(contact.id);

        // Update the contact with the correct balance
        await db.contact.update({
          where: {
            id: contact.id,
          },
          data: {
            balance,
          },
        });

        results.push({
          id: contact.id,
          name: contact.name,
          oldBalance: contact.openingBalance,
          newBalance: balance,
          success: true,
        });
      } catch (error) {
        console.error(`Error syncing balance for contact ${contact.id}:`, error);
        results.push({
          id: contact.id,
          name: contact.name,
          error: error instanceof Error ? error.message : "Unknown error",
          success: false,
        });
      }
    }

    return NextResponse.json({
      message: "Contact balances synchronized successfully",
      results,
    });
  } catch (error) {
    console.error("Error synchronizing contact balances:", error);
    return NextResponse.json(
      { error: "Failed to synchronize contact balances" },
      { status: 500 }
    );
  }
}

// Helper function to calculate the correct balance for a contact
async function calculateContactBalance(contactId: string): Promise<number> {
  // Get the contact
  const contact = await db.contact.findUnique({
    where: {
      id: contactId,
    },
  });

  if (!contact) {
    throw new Error("Contact not found");
  }

  // Start with opening balance
  let balance = contact.openingBalance;

  // Get all sales for this contact
  const sales = await db.sale.findMany({
    where: {
      contactId,
    },
    include: {
      payments: true,
    },
  });

  // Get all purchases for this contact
  const purchases = await db.purchase.findMany({
    where: {
      contactId,
    },
    include: {
      payments: true,
    },
  });

  // Get all transactions for this contact
  const transactions = await db.transaction.findMany({
    where: {
      contactId,
    },
  });

  // Calculate balance from sales
  for (const sale of sales) {
    if (contact.isCustomer) {
      // For customers, unpaid sales increase their balance (they owe us money)
      const paidAmount = sale.payments.reduce((sum, payment) => sum + payment.amount, 0);
      const unpaidAmount = sale.totalAmount - paidAmount;
      
      if (unpaidAmount > 0) {
        balance += unpaidAmount;
      }
    }
  }

  // Calculate balance from purchases
  for (const purchase of purchases) {
    if (contact.isSupplier) {
      // For suppliers, unpaid purchases increase their balance (we owe them money)
      const paidAmount = purchase.payments.reduce((sum, payment) => sum + payment.amount, 0);
      const unpaidAmount = purchase.totalAmount - paidAmount;
      
      if (unpaidAmount > 0) {
        balance += unpaidAmount;
      }
    }
  }

  // Adjust for any other transactions not covered by sales and purchases
  for (const transaction of transactions) {
    // Skip transactions that are already accounted for in sales and purchases
    if (
      (transaction.referenceType === "SALE" && sales.some(s => s.id === transaction.referenceId)) ||
      (transaction.referenceType === "PURCHASE" && purchases.some(p => p.id === transaction.referenceId))
    ) {
      continue;
    }

    // Handle other transactions based on type
    if (transaction.type === "DEBIT") {
      balance += transaction.amount;
    } else if (transaction.type === "CREDIT") {
      balance -= transaction.amount;
    }
  }

  return balance;
}
