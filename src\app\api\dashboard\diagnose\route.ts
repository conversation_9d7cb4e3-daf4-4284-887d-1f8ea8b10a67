import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// GET /api/dashboard/diagnose - Diagnose dashboard issues
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const period = url.searchParams.get("period") || "today"; // today, week, month, year

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case "today":
        startDate.setHours(0, 0, 0, 0);
        break;
      case "week":
        startDate.setDate(now.getDate() - 7);
        break;
      case "month":
        startDate.setMonth(now.getMonth() - 1);
        break;
      case "year":
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setHours(0, 0, 0, 0); // Default to today
    }

    // Run a series of diagnostic tests
    const diagnosticResults = {
      session: {
        user: session.user,
        success: true,
      },
      database: {
        success: false,
        error: null,
        details: {},
      },
      queries: {
        sales: { success: false, error: null, count: 0 },
        purchases: { success: false, error: null, count: 0 },
        products: { success: false, error: null, count: 0 },
        contacts: { success: false, error: null, count: 0 },
        inventory: { success: false, error: null, count: 0 },
        maintenance: { success: false, error: null, count: 0 },
      },
    };

    // Test database connection
    try {
      // Simple query to test connection
      const userCount = await db.user.count();
      diagnosticResults.database.success = true;
      diagnosticResults.database.details = {
        userCount,
      };
    } catch (dbError) {
      diagnosticResults.database.success = false;
      diagnosticResults.database.error = dbError instanceof Error ? dbError.message : "Unknown database error";
    }

    // Test sales query
    try {
      const salesCount = await db.sale.count({
        where: {
          date: {
            gte: startDate,
          },
        },
      });
      diagnosticResults.queries.sales.success = true;
      diagnosticResults.queries.sales.count = salesCount;
    } catch (error) {
      diagnosticResults.queries.sales.success = false;
      diagnosticResults.queries.sales.error = error instanceof Error ? error.message : "Unknown error";
    }

    // Test purchases query
    try {
      const purchasesCount = await db.purchase.count({
        where: {
          date: {
            gte: startDate,
          },
        },
      });
      diagnosticResults.queries.purchases.success = true;
      diagnosticResults.queries.purchases.count = purchasesCount;
    } catch (error) {
      diagnosticResults.queries.purchases.success = false;
      diagnosticResults.queries.purchases.error = error instanceof Error ? error.message : "Unknown error";
    }

    // Test products query
    try {
      const productsCount = await db.product.count();
      diagnosticResults.queries.products.success = true;
      diagnosticResults.queries.products.count = productsCount;
    } catch (error) {
      diagnosticResults.queries.products.success = false;
      diagnosticResults.queries.products.error = error instanceof Error ? error.message : "Unknown error";
    }

    // Test contacts query
    try {
      const contactsCount = await db.contact.count();
      diagnosticResults.queries.contacts.success = true;
      diagnosticResults.queries.contacts.count = contactsCount;
    } catch (error) {
      diagnosticResults.queries.contacts.success = false;
      diagnosticResults.queries.contacts.error = error instanceof Error ? error.message : "Unknown error";
    }

    // Test inventory query
    try {
      const inventoryCount = await db.inventory.count();
      diagnosticResults.queries.inventory.success = true;
      diagnosticResults.queries.inventory.count = inventoryCount;
    } catch (error) {
      diagnosticResults.queries.inventory.success = false;
      diagnosticResults.queries.inventory.error = error instanceof Error ? error.message : "Unknown error";
    }

    // Test maintenance query (if exists)
    try {
      const maintenanceCount = await db.maintenanceService.count();
      diagnosticResults.queries.maintenance.success = true;
      diagnosticResults.queries.maintenance.count = maintenanceCount;
    } catch (error) {
      diagnosticResults.queries.maintenance.success = false;
      diagnosticResults.queries.maintenance.error = error instanceof Error ? error.message : "Unknown error";
    }

    return NextResponse.json(diagnosticResults);
  } catch (error) {
    console.error("Error running dashboard diagnostics:", error);
    return NextResponse.json(
      { 
        error: "Failed to run dashboard diagnostics",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
