// This file contains additional database indexes to improve query performance
// These indexes should be added to your main schema.prisma file

// Sales related indexes
model Sale {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([invoiceNumber])
  @@index([contactId])
  @@index([branchId])
  @@index([date])
  @@index([status])
  @@index([paymentStatus])
}

model SaleItem {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([saleId])
  @@index([productId])
}

// Purchase related indexes
model Purchase {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([invoiceNumber])
  @@index([contactId])
  @@index([branchId])
  @@index([date])
  @@index([status])
  @@index([paymentStatus])
}

model PurchaseItem {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([purchaseId])
  @@index([productId])
}

// Inventory related indexes
model Inventory {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([productId])
  @@index([warehouseId])
  @@index([quantity])
}

// Contact related indexes
model Contact {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([phone])
  @@index([name])
  @@index([isCustomer])
  @@index([isSupplier])
  @@index([loyaltyTier])
}

// Product related indexes
model Product {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([name])
  @@index([sku])
  @@index([barcode])
  @@index([categoryId])
  @@index([isActive])
}

// Loyalty related indexes
model LoyaltyTransaction {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([loyaltyAccountId])
  @@index([type])
  @@index([createdAt])
}

// Discount related indexes
model Discount {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([code])
  @@index([type])
  @@index([isActive])
  @@index([startDate])
  @@index([endDate])
}

// Credit Note related indexes
model CreditNote {
  // Existing fields...

  // Add indexes for frequently queried fields
  @@index([referenceNumber])
  @@index([saleId])
  @@index([contactId])
  @@index([date])
  @@index([status])
  @@index([paymentStatus])
}
