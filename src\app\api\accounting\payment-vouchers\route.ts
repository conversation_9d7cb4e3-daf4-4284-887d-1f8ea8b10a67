import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";
import { createJournalEntry } from "@/lib/accounting";
import {
  getPaymentMethodAccount,
  getPaymentMethodJournal,
  ensureAccountsExist
} from "@/lib/accounting-integration";

// GET /api/accounting/payment-vouchers - Get payment vouchers
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view accounting data
    const hasViewPermission = await hasPermission("view_accounting");
    if (!hasViewPermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view payment vouchers" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const search = url.searchParams.get("search");
    const status = url.searchParams.get("status");
    const paymentMethodId = url.searchParams.get("paymentMethodId");
    const contactId = url.searchParams.get("contactId");
    const limit = parseInt(url.searchParams.get("limit") || "100");
    const page = parseInt(url.searchParams.get("page") || "1");

    // Build filter
    const filter: any = {};

    if (startDate && endDate) {
      filter.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      filter.date = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      filter.date = {
        lte: new Date(endDate),
      };
    }

    if (search) {
      filter.OR = [
        { voucherNumber: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      filter.status = status;
    }

    if (paymentMethodId) {
      filter.paymentMethodId = paymentMethodId;
    }

    if (contactId) {
      filter.contactId = contactId;
    }

    // Get payment vouchers
    const vouchers = await db.paymentVoucher.findMany({
      where: filter,
      include: {
        paymentMethod: {
          select: {
            id: true,
            code: true,
            name: true,
          },
        },
        contact: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
      take: limit,
      skip: (page - 1) * limit,
    });

    // Get total count for pagination
    const totalCount = await db.paymentVoucher.count({
      where: filter,
    });

    return NextResponse.json({
      success: true,
      data: vouchers,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching payment vouchers:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch payment vouchers" },
      { status: 500 }
    );
  }
}

// POST /api/accounting/payment-vouchers - Create a new payment voucher
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to manage accounting
    const hasManagePermission = await hasPermission("manage_accounting");
    if (!hasManagePermission && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to create payment vouchers" },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Validate required fields
    if (!data.amount || !data.description || !data.paymentMethodId || !data.expenseAccountId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the payment method
    const paymentMethod = await db.paymentMethodSettings.findUnique({
      where: {
        id: data.paymentMethodId,
      },
      include: {
        account: true,
        journal: true,
      },
    });

    if (!paymentMethod) {
      return NextResponse.json(
        { error: "Payment method not found" },
        { status: 404 }
      );
    }

    if (!paymentMethod.accountId) {
      return NextResponse.json(
        { error: "Payment method has no associated account" },
        { status: 400 }
      );
    }

    // Get the user's branch
    const user = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      include: {
        branch: true,
      },
    });

    if (!user?.branchId) {
      return NextResponse.json(
        { error: "User has no associated branch" },
        { status: 400 }
      );
    }

    // Generate voucher number
    const lastVoucher = await db.paymentVoucher.findFirst({
      where: {
        branchId: user.branchId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    let voucherNumber = `PV-${user.branch?.code || "MAIN"}-1`;
    if (lastVoucher && lastVoucher.voucherNumber) {
      const lastNumber = parseInt(lastVoucher.voucherNumber.split("-").pop() || "0");
      if (!isNaN(lastNumber)) {
        voucherNumber = `PV-${user.branch?.code || "MAIN"}-${lastNumber + 1}`;
      }
    }

    // Ensure accounts exist
    await ensureAccountsExist();

    // Create the payment voucher and journal entry in a transaction
    const result = await db.$transaction(async (tx) => {
      // Create the payment voucher
      const voucher = await tx.paymentVoucher.create({
        data: {
          voucherNumber,
          date: data.date ? new Date(data.date) : new Date(),
          amount: data.amount,
          description: data.description,
          paymentMethodId: data.paymentMethodId,
          contactId: data.contactId || null,
          userId: session.user.id,
          branchId: user.branchId,
          status: "COMPLETED",
          reference: data.reference || null,
          referenceType: data.referenceType || "PAYMENT_VOUCHER",
        },
        include: {
          paymentMethod: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
          contact: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
            },
          },
          branch: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
      });

      // Get the selected expense account
      const expenseAccount = await tx.account.findUnique({
        where: {
          id: data.expenseAccountId,
        },
      });

      if (!expenseAccount) {
        throw new Error(`Expense account not found with ID: ${data.expenseAccountId}`);
      }

      // Verify that it's an expense account
      if (expenseAccount.type !== "EXPENSE") {
        throw new Error(`Selected account is not an expense account: ${expenseAccount.name}`);
      }

      // Get payment method account and journal
      const paymentMethodAccount = paymentMethod.accountId
        ? { id: paymentMethod.accountId }
        : await getPaymentMethodAccount(paymentMethod.code);

      if (!paymentMethodAccount) {
        throw new Error(`No account found for payment method ${paymentMethod.name}`);
      }

      // Get journal for the payment voucher
      const journal = paymentMethod.journalId
        ? { id: paymentMethod.journalId }
        : await getPaymentMethodJournal(paymentMethod.code);

      if (!journal) {
        throw new Error(`No journal found for payment method ${paymentMethod.name}`);
      }

      // Create journal entry
      const journalEntry = await createJournalEntry({
        journalId: journal.id,
        description: `Payment voucher: ${voucherNumber} - ${data.description}`,
        debitAccountId: expenseAccount.id, // Debit the expense account
        creditAccountId: paymentMethodAccount.id, // Credit the payment method account
        amount: data.amount,
        date: data.date ? new Date(data.date) : new Date(),
        reference: voucher.id,
        referenceType: "PAYMENT_VOUCHER",
        contactId: data.contactId || null,
      });

      // Update the voucher with the journal entry ID
      await tx.paymentVoucher.update({
        where: {
          id: voucher.id,
        },
        data: {
          journalEntryId: journalEntry.id,
        },
      });

      return voucher;
    });

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error creating payment voucher:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create payment voucher" },
      { status: 500 }
    );
  }
}
