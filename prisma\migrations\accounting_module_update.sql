-- Accounting Module Update Migration

-- Create enum types if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'journaltype') THEN
        CREATE TYPE "JournalType" AS ENUM ('CASH', 'VODAFONE_CASH', 'BANK_TRANSFER', 'VISA', 'CUSTOMER_ACCOUNT', 'GENERAL');
    END IF;
END
$$;

-- Update Account table
ALTER TABLE "Account" 
ADD COLUMN IF NOT EXISTS "code" TEXT,
ADD COLUMN IF NOT EXISTS "parentId" TEXT,
ADD COLUMN IF NOT EXISTS "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN IF NOT EXISTS "branchId" TEXT;

-- Add unique constraint to code if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'Account_code_key' AND conrelid = 'public."Account"'::regclass
    ) THEN
        ALTER TABLE "Account" ADD CONSTRAINT "Account_code_key" UNIQUE ("code");
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist, do nothing
END
$$;

-- Create Journal table if it doesn't exist
CREATE TABLE IF NOT EXISTS "Journal" (
  "id" TEXT NOT NULL,
  "code" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "type" "JournalType" NOT NULL,
  "paymentMethod" TEXT,
  "branchId" TEXT,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "Journal_pkey" PRIMARY KEY ("id")
);

-- Add unique constraint to code if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'Journal_code_key' AND conrelid = 'public."Journal"'::regclass
    ) THEN
        ALTER TABLE "Journal" ADD CONSTRAINT "Journal_code_key" UNIQUE ("code");
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist, do nothing
END
$$;

-- Create JournalEntry table if it doesn't exist
CREATE TABLE IF NOT EXISTS "JournalEntry" (
  "id" TEXT NOT NULL,
  "journalId" TEXT NOT NULL,
  "entryNumber" TEXT NOT NULL,
  "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "description" TEXT NOT NULL,
  "debitAccountId" TEXT NOT NULL,
  "creditAccountId" TEXT NOT NULL,
  "amount" DOUBLE PRECISION NOT NULL,
  "contactId" TEXT,
  "reference" TEXT,
  "referenceType" TEXT,
  "isPosted" BOOLEAN NOT NULL DEFAULT false,
  "fiscalPeriodId" TEXT,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "JournalEntry_pkey" PRIMARY KEY ("id")
);

-- Add unique constraint to entryNumber and journalId if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'JournalEntry_entryNumber_journalId_key' AND conrelid = 'public."JournalEntry"'::regclass
    ) THEN
        ALTER TABLE "JournalEntry" ADD CONSTRAINT "JournalEntry_entryNumber_journalId_key" UNIQUE ("entryNumber", "journalId");
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist, do nothing
END
$$;

-- Create FiscalYear table if it doesn't exist
CREATE TABLE IF NOT EXISTS "FiscalYear" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "startDate" TIMESTAMP(3) NOT NULL,
  "endDate" TIMESTAMP(3) NOT NULL,
  "isClosed" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "FiscalYear_pkey" PRIMARY KEY ("id")
);

-- Create FiscalPeriod table if it doesn't exist
CREATE TABLE IF NOT EXISTS "FiscalPeriod" (
  "id" TEXT NOT NULL,
  "fiscalYearId" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "startDate" TIMESTAMP(3) NOT NULL,
  "endDate" TIMESTAMP(3) NOT NULL,
  "isClosed" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "FiscalPeriod_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints
ALTER TABLE "Account" 
ADD CONSTRAINT IF NOT EXISTS "Account_parentId_fkey" 
FOREIGN KEY ("parentId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Account" 
ADD CONSTRAINT IF NOT EXISTS "Account_branchId_fkey" 
FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Journal" 
ADD CONSTRAINT IF NOT EXISTS "Journal_branchId_fkey" 
FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_journalId_fkey" 
FOREIGN KEY ("journalId") REFERENCES "Journal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_debitAccountId_fkey" 
FOREIGN KEY ("debitAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_creditAccountId_fkey" 
FOREIGN KEY ("creditAccountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_contactId_fkey" 
FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "JournalEntry" 
ADD CONSTRAINT IF NOT EXISTS "JournalEntry_fiscalPeriodId_fkey" 
FOREIGN KEY ("fiscalPeriodId") REFERENCES "FiscalPeriod"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "FiscalPeriod" 
ADD CONSTRAINT IF NOT EXISTS "FiscalPeriod_fiscalYearId_fkey" 
FOREIGN KEY ("fiscalYearId") REFERENCES "FiscalYear"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "Account_code_idx" ON "Account"("code");
CREATE INDEX IF NOT EXISTS "Account_type_idx" ON "Account"("type");
CREATE INDEX IF NOT EXISTS "Account_parentId_idx" ON "Account"("parentId");
CREATE INDEX IF NOT EXISTS "Account_isActive_idx" ON "Account"("isActive");

CREATE INDEX IF NOT EXISTS "Journal_code_idx" ON "Journal"("code");
CREATE INDEX IF NOT EXISTS "Journal_type_idx" ON "Journal"("type");
CREATE INDEX IF NOT EXISTS "Journal_isActive_idx" ON "Journal"("isActive");

CREATE INDEX IF NOT EXISTS "JournalEntry_journalId_idx" ON "JournalEntry"("journalId");
CREATE INDEX IF NOT EXISTS "JournalEntry_date_idx" ON "JournalEntry"("date");
CREATE INDEX IF NOT EXISTS "JournalEntry_debitAccountId_idx" ON "JournalEntry"("debitAccountId");
CREATE INDEX IF NOT EXISTS "JournalEntry_creditAccountId_idx" ON "JournalEntry"("creditAccountId");
CREATE INDEX IF NOT EXISTS "JournalEntry_isPosted_idx" ON "JournalEntry"("isPosted");
CREATE INDEX IF NOT EXISTS "JournalEntry_contactId_idx" ON "JournalEntry"("contactId");

CREATE INDEX IF NOT EXISTS "FiscalYear_isClosed_idx" ON "FiscalYear"("isClosed");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_fiscalYearId_idx" ON "FiscalPeriod"("fiscalYearId");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_isClosed_idx" ON "FiscalPeriod"("isClosed");
CREATE INDEX IF NOT EXISTS "FiscalPeriod_startDate_endDate_idx" ON "FiscalPeriod"("startDate", "endDate");

-- Add payment methods to Sale and Purchase tables
ALTER TABLE "Sale" 
ADD COLUMN IF NOT EXISTS "payments" JSONB;

ALTER TABLE "Purchase" 
ADD COLUMN IF NOT EXISTS "payments" JSONB;
