import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

interface OpeningBalanceEntry {
  accountCode: string;
  openingBalance: number;
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // For now, allow any authenticated user to import opening balances
    // Later, we can add proper permission checks
    // const hasPermission = await hasPermission("manage_accounts");
    // if (!hasPermission) {
    //   return NextResponse.json(
    //     { error: "You don't have permission to import opening balances" },
    //     { status: 403 }
    //   );
    // }

    const body = await request.json();
    const { entries } = body as { entries: OpeningBalanceEntry[] };

    if (!entries || !Array.isArray(entries) || entries.length === 0) {
      return NextResponse.json(
        { error: "No entries provided for opening balances" },
        { status: 400 }
      );
    }

    // Validate entries
    for (const entry of entries) {
      if (!entry.accountCode || entry.openingBalance === undefined) {
        return NextResponse.json(
          { error: "Each entry must have an accountCode and openingBalance" },
          { status: 400 }
        );
      }
    }

    // Calculate total debits and credits
    const totalDebits = entries
      .filter(entry => entry.openingBalance > 0)
      .reduce((sum, entry) => sum + entry.openingBalance, 0);
    
    const totalCredits = entries
      .filter(entry => entry.openingBalance < 0)
      .reduce((sum, entry) => sum + Math.abs(entry.openingBalance), 0);
    
    // Check if debits equal credits
    const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;
    
    if (!isBalanced) {
      return NextResponse.json(
        { error: `Opening balances are not balanced. Total debits: ${totalDebits.toFixed(2)}, Total credits: ${totalCredits.toFixed(2)}` },
        { status: 400 }
      );
    }

    // Process opening balances in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Get the general journal
      const generalJournal = await tx.journal.findFirst({
        where: {
          type: "GENERAL",
        },
      });

      if (!generalJournal) {
        throw new Error("General journal not found. Please initialize journals first.");
      }

      // Create a journal entry for opening balances
      const journalEntry = await tx.journalEntry.create({
        data: {
          date: new Date(),
          description: "Opening Balances / الأرصدة الافتتاحية",
          journalId: generalJournal.id,
          reference: `OB-${new Date().toISOString().split("T")[0]}`,
        },
      });

      // Process each account's opening balance
      const processedEntries = [];
      const errors = [];

      for (const entry of entries) {
        try {
          // Skip entries with zero balance
          if (Math.abs(entry.openingBalance) < 0.01) {
            continue;
          }

          // Find the account
          const account = await tx.account.findFirst({
            where: {
              code: entry.accountCode,
            },
          });

          if (!account) {
            errors.push({
              accountCode: entry.accountCode,
              error: "Account not found",
            });
            continue;
          }

          // Create journal entry line
          const entryLine = await tx.journalEntryLine.create({
            data: {
              journalEntryId: journalEntry.id,
              accountId: account.id,
              type: entry.openingBalance > 0 ? "DEBIT" : "CREDIT",
              amount: Math.abs(entry.openingBalance),
            },
          });

          // Update account balance
          await tx.account.update({
            where: {
              id: account.id,
            },
            data: {
              balance: {
                increment: entry.openingBalance,
              },
            },
          });

          processedEntries.push({
            accountCode: entry.accountCode,
            amount: entry.openingBalance,
            entryLineId: entryLine.id,
          });
        } catch (error: any) {
          errors.push({
            accountCode: entry.accountCode,
            error: error.message || "Failed to process opening balance",
          });
        }
      }

      return {
        journalEntryId: journalEntry.id,
        processedEntries,
        errors,
      };
    });

    return NextResponse.json({
      success: true,
      journalEntryId: result.journalEntryId,
      processed: result.processedEntries.length,
      errors: result.errors,
    });
  } catch (error: any) {
    console.error("Error importing opening balances:", error);
    return NextResponse.json(
      { error: error.message || "Failed to import opening balances" },
      { status: 500 }
    );
  }
}
