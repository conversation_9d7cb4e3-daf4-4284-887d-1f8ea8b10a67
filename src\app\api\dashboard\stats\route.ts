import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hasPermission } from "@/lib/permissions";

// GET /api/dashboard/stats - Get dashboard statistics
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user has permission to view dashboard
    // Skip permission check for now - all users should be able to view dashboard
    // We'll add the view_dashboard permission later if needed
    const isAdmin = session.user.role === "ADMIN";

    // Always allow access to dashboard for all authenticated users
    // This ensures the dashboard works even if the view_dashboard permission doesn't exist

    // Get query parameters
    const url = new URL(req.url);
    const period = url.searchParams.get("period") || "today"; // today, week, month, year
    const branchId = url.searchParams.get("branchId") || "all";

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case "today":
        startDate.setHours(0, 0, 0, 0);
        break;
      case "week":
        startDate.setDate(now.getDate() - 7);
        break;
      case "month":
        startDate.setMonth(now.getMonth() - 1);
        break;
      case "year":
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setHours(0, 0, 0, 0); // Default to today
    }

    // Build branch filter
    const branchFilter = branchId !== "all" ? { branchId } : {};

    // Get sales statistics
    const salesStats = await db.sale.aggregate({
      where: {
        date: {
          gte: startDate,
        },
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
      _count: true,
    });

    // Get paid sales
    const paidSalesStats = await db.sale.aggregate({
      where: {
        date: {
          gte: startDate,
        },
        paymentStatus: "PAID",
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
      _count: true,
    });

    // Get purchases statistics
    const purchasesStats = await db.purchase.aggregate({
      where: {
        date: {
          gte: startDate,
        },
        ...branchFilter,
      },
      _sum: {
        totalAmount: true,
      },
      _count: true,
    });

    // Get products count
    const productsCount = await db.product.count({
      where: {
        isComponent: false,
      },
    });

    // Get customers count
    const customersCount = await db.contact.count({
      where: {
        isCustomer: true,
      },
    });

    // Get suppliers count
    const suppliersCount = await db.contact.count({
      where: {
        isSupplier: true,
      },
    });

    // Get low stock products
    const lowStockProducts = await db.inventory.findMany({
      where: {
        quantity: {
          lte: 5, // Consider products with 5 or fewer items as low stock
        },
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            basePrice: true,
          },
        },
        warehouse: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      take: 5, // Limit to 5 products
    });

    // Get recent sales
    const recentSales = await db.sale.findMany({
      where: {
        ...branchFilter,
      },
      include: {
        contact: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
      take: 5,
    });

    // Get recent purchases
    const recentPurchases = await db.purchase.findMany({
      where: {
        ...branchFilter,
      },
      include: {
        contact: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
      take: 5,
    });

    // Get customers with overdue payments
    const overdueCustomers = await db.contact.findMany({
      where: {
        isCustomer: true,
        balance: {
          gt: 0,
        },
      },
      orderBy: {
        balance: "desc",
      },
      take: 5,
    });

    // Get maintenance statistics
    let maintenanceCount = 0;
    let overdueMaintenanceCount = 0;
    let maintenanceByStatus = [];

    try {
      // Check if maintenance tables exist by trying to count
      maintenanceCount = await db.maintenanceService.count();

      // Get overdue maintenance services (more than 15 days)
      const fifteenDaysAgo = new Date();
      fifteenDaysAgo.setDate(now.getDate() - 15);

      overdueMaintenanceCount = await db.maintenanceService.count({
        where: {
          receivedDate: {
            lte: fifteenDaysAgo,
          },
          status: {
            notIn: ["DELIVERED", "CANCELLED"],
          },
        },
      });

      // Get maintenance services by status
      maintenanceByStatus = await db.maintenanceService.groupBy({
        by: ["status"],
        _count: {
          id: true,
        },
      });
    } catch (error) {
      console.log("Maintenance module tables may not exist yet:", error);
      // Continue with default values if tables don't exist
    }

    // Get all contacts with non-zero balances
    let contactsWithBalance = [];

    try {
      // First try to get contacts with non-zero balances directly from the database
      contactsWithBalance = await db.contact.findMany({
        where: {
          AND: [
            {
              OR: [
                { balance: { gt: 0 } },  // Positive balance
                { balance: { lt: 0 } }   // Negative balance
              ]
            },
            {
              OR: [
                { isCustomer: true },    // Is a customer
                { isSupplier: true }     // Is a supplier
              ]
            }
          ]
        },
        select: {
          id: true,
          name: true,
          phone: true,
          balance: true,
          isCustomer: true,
          isSupplier: true,
        },
        orderBy: [
          {
            isCustomer: "desc",      // Show customers first
          },
          {
            balance: "desc",         // Then sort by balance
          }
        ],
        take: 20, // Limit to 20 contacts for performance
      });

      // If we don't have any contacts with non-zero balances, get some contacts anyway
      if (contactsWithBalance.length === 0) {
        contactsWithBalance = await db.contact.findMany({
          where: {
            OR: [
              { isCustomer: true },
              { isSupplier: true }
            ]
          },
          select: {
            id: true,
            name: true,
            phone: true,
            balance: true,
            isCustomer: true,
            isSupplier: true,
          },
          take: 20,
        });
      }
    } catch (error) {
      console.error("Error fetching contacts with balance:", error);
      // Return an empty array if there's an error
      contactsWithBalance = [];
    }

    // Get payment methods balances from the new API
    let paymentMethodsBalances = {
      total: 0,
      cash: 0,
      vodafoneCash: 0,
      bankTransfer: 0,
      creditCard: 0,
      customerAccount: 0
    };

    try {
      // Create a server-side fetch request to our own API
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:8070';
      const response = await fetch(`${baseUrl}/api/finance/payment-methods-balances`, {
        headers: {
          // Pass the session cookie to authenticate the request
          cookie: req.headers.get('cookie') || '',
        },
      });

      if (response.ok) {
        paymentMethodsBalances = await response.json();
      } else {
        console.error('Failed to fetch payment methods balances:', await response.text());
      }
    } catch (error) {
      console.error('Error fetching payment methods balances:', error);

      // Fallback to the old method if the new API fails
      const cashPayments = await db.sale.aggregate({
        where: {
          date: {
            gte: startDate,
          },
          paymentStatus: {
            in: ["PAID", "PARTIALLY_PAID"]
          },
          paymentMethod: "CASH",
          ...branchFilter,
        },
        _sum: {
          totalAmount: true,
        },
      });

      const vodafoneCashPayments = await db.sale.aggregate({
        where: {
          date: {
            gte: startDate,
          },
          paymentStatus: {
            in: ["PAID", "PARTIALLY_PAID"]
          },
          paymentMethod: "VODAFONE_CASH",
          ...branchFilter,
        },
        _sum: {
          totalAmount: true,
        },
      });

      const bankTransferPayments = await db.sale.aggregate({
        where: {
          date: {
            gte: startDate,
          },
          paymentStatus: {
            in: ["PAID", "PARTIALLY_PAID"]
          },
          paymentMethod: "BANK_TRANSFER",
          ...branchFilter,
        },
        _sum: {
          totalAmount: true,
        },
      });

      const creditCardPayments = await db.sale.aggregate({
        where: {
          date: {
            gte: startDate,
          },
          paymentStatus: {
            in: ["PAID", "PARTIALLY_PAID"]
          },
          paymentMethod: "CREDIT_CARD",
          ...branchFilter,
        },
        _sum: {
          totalAmount: true,
        },
      });

      // Get customer account balances (unpaid amounts)
      const customerAccountPayments = await db.sale.aggregate({
        where: {
          date: {
            gte: startDate,
          },
          paymentStatus: "UNPAID",
          paymentMethod: "CUSTOMER_ACCOUNT",
          ...branchFilter,
        },
        _sum: {
          totalAmount: true,
        },
      });

      // Calculate total for all payment methods
      paymentMethodsBalances = {
        total: (cashPayments._sum.totalAmount || 0) +
               (vodafoneCashPayments._sum.totalAmount || 0) +
               (bankTransferPayments._sum.totalAmount || 0) +
               (creditCardPayments._sum.totalAmount || 0),
        cash: cashPayments._sum.totalAmount || 0,
        vodafoneCash: vodafoneCashPayments._sum.totalAmount || 0,
        bankTransfer: bankTransferPayments._sum.totalAmount || 0,
        creditCard: creditCardPayments._sum.totalAmount || 0,
        customerAccount: customerAccountPayments._sum.totalAmount || 0
      };
    }

    return NextResponse.json({
      sales: {
        total: salesStats._sum.totalAmount || 0,
        count: salesStats._count || 0,
        paid: paidSalesStats._sum.totalAmount || 0,
        paidCount: paidSalesStats._count || 0,
      },
      purchases: {
        total: purchasesStats._sum.totalAmount || 0,
        count: purchasesStats._count || 0,
      },
      products: {
        count: productsCount,
        lowStock: lowStockProducts,
      },
      customers: {
        count: customersCount,
        overdue: overdueCustomers,
      },
      suppliers: {
        count: suppliersCount,
      },
      contacts: {
        withBalance: contactsWithBalance,
      },
      maintenance: {
        count: maintenanceCount,
        overdueCount: overdueMaintenanceCount,
        byStatus: maintenanceByStatus,
      },
      paymentMethods: paymentMethodsBalances,
      recentActivity: {
        sales: recentSales,
        purchases: recentPurchases,
      },
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);

    // Return a more detailed error message for debugging
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : "Unknown error";

    return NextResponse.json(
      {
        error: "Failed to fetch dashboard statistics",
        details: errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
