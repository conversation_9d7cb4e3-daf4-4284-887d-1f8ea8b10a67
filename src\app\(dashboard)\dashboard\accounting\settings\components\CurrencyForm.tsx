"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, Save } from "lucide-react";

interface CurrencyFormProps {
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export default function CurrencyForm({ initialData, onSubmit, onCancel }: CurrencyFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    symbol: "",
    exchangeRate: 1,
    isBaseCurrency: false,
    isActive: true,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load initial data if editing
  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name || "",
        code: initialData.code || "",
        symbol: initialData.symbol || "",
        exchangeRate: initialData.exchangeRate || 1,
        isBaseCurrency: initialData.isBaseCurrency || false,
        isActive: initialData.isActive !== undefined ? initialData.isActive : true,
      });
    }
  }, [initialData]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = parseFloat(value);
    
    setFormData(prev => ({
      ...prev,
      [name]: isNaN(numValue) ? 0 : numValue,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Currency name is required";
    }

    if (!formData.code.trim()) {
      newErrors.code = "Currency code is required";
    } else if (formData.code.length !== 3) {
      newErrors.code = "Currency code should be 3 characters (ISO format)";
    }

    if (!formData.symbol.trim()) {
      newErrors.symbol = "Currency symbol is required";
    }

    if (formData.exchangeRate <= 0) {
      newErrors.exchangeRate = "Exchange rate must be greater than 0";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        ...formData,
        id: initialData?.id,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Currency Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Egyptian Pound"
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="code">Currency Code (ISO)</Label>
          <Input
            id="code"
            name="code"
            value={formData.code}
            onChange={handleChange}
            placeholder="EGP"
            maxLength={3}
            className={`uppercase ${errors.code ? "border-red-500" : ""}`}
          />
          {errors.code && <p className="text-red-500 text-sm">{errors.code}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="symbol">Currency Symbol</Label>
          <Input
            id="symbol"
            name="symbol"
            value={formData.symbol}
            onChange={handleChange}
            placeholder="ج.م"
            className={errors.symbol ? "border-red-500" : ""}
          />
          {errors.symbol && <p className="text-red-500 text-sm">{errors.symbol}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="exchangeRate">Exchange Rate</Label>
          <Input
            id="exchangeRate"
            name="exchangeRate"
            type="number"
            step="0.0001"
            min="0.0001"
            value={formData.exchangeRate}
            onChange={handleNumberChange}
            placeholder="1.0"
            className={errors.exchangeRate ? "border-red-500" : ""}
          />
          {errors.exchangeRate && <p className="text-red-500 text-sm">{errors.exchangeRate}</p>}
        </div>
      </div>

      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="isBaseCurrency"
            checked={formData.isBaseCurrency}
            onCheckedChange={(checked) => handleCheckboxChange("isBaseCurrency", checked as boolean)}
          />
          <Label htmlFor="isBaseCurrency">Base Currency</Label>
        </div>
        <p className="text-sm text-gray-500 ml-6">
          If checked, this will be the default currency for the system. All other currencies will be converted to this one for reporting.
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => handleCheckboxChange("isActive", checked as boolean)}
        />
        <Label htmlFor="isActive">Active</Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Save Currency
        </Button>
      </div>
    </form>
  );
}
