"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Loader2, Download, Search, RefreshCw } from "lucide-react";
import { format } from "date-fns";

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
  balance: number;
}

interface ProfitLossData {
  startDate: string;
  endDate: string;
  revenue: Account[];
  expenses: Account[];
  totalRevenue: number;
  totalExpenses: number;
  netIncome: number;
}

export default function ProfitLossPage() {
  const [data, setData] = useState<ProfitLossData | null>(null);
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().getFullYear(), 0, 1)); // January 1st of current year
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState(false);

  // Fetch profit & loss data
  const fetchProfitLoss = async () => {
    if (!startDate || !endDate) {
      return;
    }

    setIsLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("startDate", startDate.toISOString());
      params.append("endDate", endDate.toISOString());

      const response = await fetch(`/api/accounting/profit-loss?${params.toString()}`);
      if (response.ok) {
        const responseData = await response.json();
        setData(responseData);
      }
    } catch (error) {
      console.error("Error fetching profit & loss:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    if (!data) return;

    // Create CSV content
    let csvContent = "Profit & Loss Statement\n";
    csvContent += `Period: ${format(new Date(data.startDate), "MMMM d, yyyy")} to ${format(new Date(data.endDate), "MMMM d, yyyy")}\n\n`;
    
    // Revenue section
    csvContent += "REVENUE\n";
    data.revenue.forEach(account => {
      csvContent += `${account.code},${account.name},${account.balance.toFixed(2)}\n`;
    });
    csvContent += `Total Revenue,,${data.totalRevenue.toFixed(2)}\n\n`;
    
    // Expenses section
    csvContent += "EXPENSES\n";
    data.expenses.forEach(account => {
      csvContent += `${account.code},${account.name},${account.balance.toFixed(2)}\n`;
    });
    csvContent += `Total Expenses,,${data.totalExpenses.toFixed(2)}\n\n`;
    
    // Net Income
    csvContent += `NET INCOME,,${data.netIncome.toFixed(2)}\n`;
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `profit_loss_${format(new Date(), "yyyyMMdd")}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Profit & Loss Statement</h1>
        <Button variant="outline" onClick={exportToCSV} disabled={!data || isLoading}>
          <Download className="h-4 w-4 mr-2" />
          Export to CSV
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <DatePicker date={startDate} setDate={setStartDate} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <DatePicker date={endDate} setDate={setEndDate} />
            </div>

            <div className="flex items-end">
              <Button onClick={fetchProfitLoss} disabled={!startDate || !endDate || isLoading}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {data && (
        <Card>
          <CardHeader>
            <CardTitle>
              Profit & Loss Statement
              <span className="block text-sm font-normal mt-1">
                {format(new Date(data.startDate), "MMMM d, yyyy")} to {format(new Date(data.endDate), "MMMM d, yyyy")}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Revenue Section */}
              <div>
                <h3 className="text-lg font-medium mb-3">Revenue</h3>
                <div className="rounded-md border overflow-hidden">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Account Code</th>
                        <th className="px-4 py-3 text-left">Account Name</th>
                        <th className="px-4 py-3 text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.revenue.map(account => (
                        <tr key={account.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{account.code}</td>
                          <td className="px-4 py-3">{account.name}</td>
                          <td className="px-4 py-3 text-right">{account.balance.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-100 font-medium">
                        <td colSpan={2} className="px-4 py-3 text-right">Total Revenue</td>
                        <td className="px-4 py-3 text-right">{data.totalRevenue.toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              {/* Expenses Section */}
              <div>
                <h3 className="text-lg font-medium mb-3">Expenses</h3>
                <div className="rounded-md border overflow-hidden">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gray-100 border-b">
                        <th className="px-4 py-3 text-left">Account Code</th>
                        <th className="px-4 py-3 text-left">Account Name</th>
                        <th className="px-4 py-3 text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.expenses.map(account => (
                        <tr key={account.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-3">{account.code}</td>
                          <td className="px-4 py-3">{account.name}</td>
                          <td className="px-4 py-3 text-right">{account.balance.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-100 font-medium">
                        <td colSpan={2} className="px-4 py-3 text-right">Total Expenses</td>
                        <td className="px-4 py-3 text-right">{data.totalExpenses.toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              {/* Net Income */}
              <div className="rounded-md border overflow-hidden">
                <table className="w-full text-sm">
                  <tbody>
                    <tr className={`font-bold text-lg ${data.netIncome >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                      <td className="px-4 py-4 text-right">Net Income</td>
                      <td className="px-4 py-4 text-right w-1/4">
                        {data.netIncome.toFixed(2)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
