'use client';

import { useState } from "react";
import CustomizeProductModal, { CustomizedProduct } from "../../components/CustomizeProductModal";
import ProductCustomizationWizard from "../../components/ProductCustomizationWizard";

export default function TestComponent() {
  const [isCustomizeModalOpen, setIsCustomizeModalOpen] = useState(false);
  const [isWizardModalOpen, setIsWizardModalOpen] = useState(false);
  const [productToCustomize, setProductToCustomize] = useState<any>(null);

  const handleCustomizedProduct = (customizedProduct: CustomizedProduct) => {
    console.log(customizedProduct);
  };

  return (
    <div className="p-6">
      <button 
        onClick={() => {
          setProductToCustomize({ id: '1', name: 'Test Product' });
          setIsCustomizeModalOpen(true);
        }}
        className="bg-blue-500 text-white px-4 py-2 rounded"
      >
        Open CustomizeProductModal
      </button>

      <button 
        onClick={() => {
          setProductToCustomize({ id: '1', name: 'Test Product' });
          setIsWizardModalOpen(true);
        }}
        className="bg-green-500 text-white px-4 py-2 rounded ml-4"
      >
        Open ProductCustomizationWizard
      </button>

      {productToCustomize && (
        <>
          {isCustomizeModalOpen && (
            <CustomizeProductModal
              isOpen={isCustomizeModalOpen}
              onClose={() => setIsCustomizeModalOpen(false)}
              product={productToCustomize}
              onConfirm={handleCustomizedProduct}
            />
          )}
          
          {isWizardModalOpen && (
            <ProductCustomizationWizard
              isOpen={isWizardModalOpen}
              onClose={() => setIsWizardModalOpen(false)}
              product={productToCustomize}
              onConfirm={handleCustomizedProduct}
            />
          )}
        </>
      )}
    </div>
  );
}
